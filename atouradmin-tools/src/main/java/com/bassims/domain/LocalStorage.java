/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR> Jie
* @date 2019-09-05
*/
@Getter
@Setter
@TableName(value = "s_tool_local_storage")
public class LocalStorage implements Serializable {


    @TableId(value = "storage_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "模板组主键")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "真实文件名")
    @TableField(value = "real_name")
    private String realName;

    @ApiModelProperty(value = "文件名")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(value = "后缀")
    @TableField(value = "suffix")
    private String suffix;

    @ApiModelProperty(value = "路径")
    @TableField(value = "path")
    private String path;

    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private String type;

    @ApiModelProperty(value = "大小")
    @TableField(value = "size")
    private String size;

    @ApiModelProperty(value = "节点id")
    @TableField(value = "node_id")
    private String nodeId;

    @ApiModelProperty(value = "code")
    @TableField(value = "code")
    private String code;

    @ApiModelProperty(value = "flag")
    @TableField(value = "flag")
    private String flag;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time")
    private Timestamp updateTime;


    /**消息   任务名称（不适用）； */
    @TableField(exist = false)
    private String msg;

    public LocalStorage(){}
    public LocalStorage(String realName,String name, String suffix, String path, String type, String size,String nodeId,String code) {
        this.realName = realName;
        this.name = name;
        this.suffix = suffix;
        this.path = path;
        this.type = type;
        this.size = size;
        this.nodeId = nodeId;
        this.code = code;
    }

    public LocalStorage(String realName,String name, String suffix, String path, String type, String size) {
        this.realName = realName;
        this.name = name;
        this.suffix = suffix;
        this.path = path;
        this.type = type;
        this.size = size;
    }

    public void copy(LocalStorage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}