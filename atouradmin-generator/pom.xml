<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>atouradmin</artifactId>
        <groupId>com.bassims</groupId>
        <version>2.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>atouradmin-generator</artifactId>
    <name>代码生成模块</name>

    <properties>
        <configuration.version>1.9</configuration.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bassims</groupId>
            <artifactId>atouradmin-common</artifactId>
            <version>2.6</version>
        </dependency>

        <!--模板引擎-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-configuration/commons-configuration -->
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${configuration.version}</version>
        </dependency>
    </dependencies>
</project>