/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.mnt.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-21
**/
@Entity
@Getter
@Setter
@Table(name="t_project_communication_info")
public class ProjectCommunication implements Serializable {
    @Id
    @Column(name = "communication_id")
    @ApiModelProperty(value = "图审详情主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long communicationId;

    @TableField(value = "communication_type")
    @ApiModelProperty(value = "0:代表新增图，1：代表新增审批问题，2代表回复")
    private String communicationType;

    @TableField(value = "parent_communication_id")
    @ApiModelProperty(value = "回复的审批问题ID")
    private Long parentCommunicationId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "node_id")
    @ApiModelProperty(value = "审批对应的节点id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;

    @TableField(value = "approve_user_role_code")
    @ApiModelProperty(value = "审批人角色编码")
    private String approveUserRoleCode;

    @TableField(value = "approve_submitter_role_code")
    @ApiModelProperty(value = "被审批人角色编码")
    private String approveSubmitterRoleCode;

    @TableField(value = "communication_text")
    @ApiModelProperty(value = "发送的文字")
    private String communicationText;

    @TableField(value = "communication_img")
    @ApiModelProperty(value = "发送的图片id，多个用，隔开")
    private String communicationImg;

    @TableField(value = "communication_img_cad")
    @ApiModelProperty(value = "发送的cad图片id，多个用，隔开")
    private String communicationImgCad;

    @TableField(value = "read_user")
    @ApiModelProperty(value = "已读人角色")
    private String readUser;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @CreationTimestamp
    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd")
    private Timestamp createTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "role_name",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "角色名称")
    private String roleName;


    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    @CreationTimestamp
    private Timestamp updateTime;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "是否拥有未读内容:0:沒有未读内容，1：有未读内容")
    @Transient
    private String isRead;

    @ApiModelProperty(value = "回复内容列表")
    @Transient
    private List<ProjectCommunication> list;


    @TableField(value = "drawing_status")
    @ApiModelProperty(value = "图纸状态")
    private String drawingStatus;

    @TableField(value = "describes")
    @ApiModelProperty(value = "描述")
    private String describes;

    @TableField(value = "is_approved")
    @ApiModelProperty(value = "是否被审批（0未被提交（审批）、1被提交（审批））")
    private String isApproved;


    public void copy(ProjectCommunication source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}