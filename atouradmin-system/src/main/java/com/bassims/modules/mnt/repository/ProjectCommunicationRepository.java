/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.mnt.repository;

import com.bassims.modules.mnt.domain.ProjectCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-28
**/
@Repository
public interface ProjectCommunicationRepository extends JpaRepository<ProjectCommunication, Long>, JpaSpecificationExecutor<ProjectCommunication> {
    /**
     * 根据项目ID和nodeID查询当前项目的所有图审内容
     * @param projectId
     * @param nodeId
     * @return
     */
    @Query(value = "SELECT *  FROM  t_project_communication_info  WHERE  is_delete = 0  and  project_id  = ?1    and node_id = ?2  ", nativeQuery = true)
    List<ProjectCommunication> findByProjectIdAndNodeId(Long projectId,Long nodeId);

    /**
     * 根据项目ID和nodeID查询当前项目的所有图审内容
     * @param projectId
     * @param nodeId
     * @return
     */
    List<ProjectCommunication> findByProjectIdAndNodeIdAndCommunicationTypeAndApproveSubmitterRoleCode(Long projectId,Long nodeId,String communicationType,String approveSubmitterRoleCode);

    /**
     * 根据项目ID和nodeID查询当前项目的所有问题和回复内容
     * @param projectId
     * @param nodeId
     * @return
     */
    List<ProjectCommunication> findByProjectIdAndNodeIdAndParentCommunicationId(Long projectId,Long nodeId,Long parent_communication_id);

    /**
     * 查询该审批问题下该角色所未读的回复
     * @param communicationId
     * @return
     */
    @Query(value = "select * from t_project_communication_info where parent_communication_id=?1 and read_user not like concat('%',?2,'%') and is_delete = 0 ", nativeQuery = true)
    List<ProjectCommunication> findByParentCommunicationIdIsRead(Long communicationId,String role_code);

    @Query(value = "SELECT *  FROM  t_project_communication_info  WHERE communication_type =  0  and  project_id  = ?1  and is_delete = 0  and node_id = ?2  order by create_time desc  limit 1", nativeQuery = true)
    ProjectCommunication  getCommunicationMax(Long projectId,Long nodeId);

    @Query(value = "SELECT *  FROM  t_project_communication_info  WHERE communication_id = ?1   limit 1", nativeQuery = true)
    ProjectCommunication  getById(Long id);


    @Modifying
    @Transactional
    @Query(nativeQuery=true, value="update t_project_communication_info a set a.is_delete= 1 where a.communication_id=?1")
    void updateById(Long id);

    @Modifying
    @Transactional
    @Query(value = "update t_project_communication_info a set a.is_approved= 1 where a.node_id in ?1 ", nativeQuery = true)
    void upByNodeId(List<Long> nodeId);
}