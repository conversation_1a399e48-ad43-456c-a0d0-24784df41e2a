/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-24
**/
@Data
@TableName(value="t_project_safe_civilized_construction")
public class ProjectSafeCivilizedConstruction implements Serializable {

    @TableId(value = "project_management_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "项目证照管理id")
    private Long projectManagementId;

    @TableField(value = "management_name")
    @ApiModelProperty(value = "证照类型名称")
    private String managementName;

    @TableField(value = "management_code")
    @ApiModelProperty(value = "证照类型code")
    private String managementCode;

    @TableField(value = "remark")
    @ApiModelProperty(value = "remark")
    private String remark;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "isDelete")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_edit")
    @ApiModelProperty(value = "是否可以编辑")
    private String isEdit;

    @TableField(value = "added_version")
    @ApiModelProperty(value = "用户添加的版本")
    private Integer addedVersion;

    @TableField(value = "owner_upload_time")
    @ApiModelProperty(value = "业主上传时间")
    private Timestamp ownerUploadTime;

    @TableField(value = "license_review_time")
    @ApiModelProperty(value = "证照审核时间")
    private Timestamp licenseReviewTime;

    @TableField(value = "certificate_review_status")
    @ApiModelProperty(value = "证照审核状态")
    private String certificateReviewStatus;

    @TableField(value = "completion_status")
    @ApiModelProperty(value = "完成状态")
    private String completionStatus;

    @TableField(value = "license_completion_time")
    @ApiModelProperty(value = "证照完成时间")
    private Timestamp licenseCompletionTime;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @TableField(value = "relevancy_node_code")
    @ApiModelProperty(value = "关联三级节点的编码")
    private String relevancyNodeCode;

    @TableField(value = "relevancy_node_name")
    @ApiModelProperty(value = "关联三级节点的名称")
    private String relevancyNodeName;

    public void copy(ProjectSafeCivilizedConstruction source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}