/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.AccountInterfaceInfo;
import com.bassims.modules.atour.service.dto.AccountInterfaceInfoDto;
import com.bassims.modules.atour.service.dto.AccountInterfaceInfoQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-11-24
**/
public interface AccountInterfaceInfoService extends BaseService<AccountInterfaceInfo> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(AccountInterfaceInfoQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<AccountInterfaceInfoDto>
    */
    List<AccountInterfaceInfoDto> queryAll(AccountInterfaceInfoQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return AccountInterfaceInfoDto
     */
    AccountInterfaceInfoDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return AccountInterfaceInfoDto
    */
    AccountInterfaceInfoDto create(AccountInterfaceInfo resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(AccountInterfaceInfo resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<AccountInterfaceInfoDto> all, HttpServletResponse response) throws IOException;

    /**
     * 费用核算部门与合同归属名称、合同归属编码存库
     * @return
     */
    Boolean getAccountInfo();

    List<AccountInterfaceInfoDto> getAccountListByName(String orgName);

    /**
     * 通过orgtype和orgcode 查询
     * @param orgtype
     * @param orgcode
     * @return
     */
    AccountInterfaceInfo findByOrgtypeAndOrgcode(Integer orgtype,String orgcode);
}