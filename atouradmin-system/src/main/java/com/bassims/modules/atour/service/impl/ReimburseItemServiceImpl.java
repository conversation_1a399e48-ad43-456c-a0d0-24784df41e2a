/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.ReimburseItem;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ReimburseItemRepository;
import com.bassims.modules.atour.service.ReimburseItemService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ReimburseItemDto;
import com.bassims.modules.atour.service.dto.ReimburseItemQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ReimburseItemMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-12-21
**/
@Service
public class ReimburseItemServiceImpl extends BaseServiceImpl<ReimburseItemRepository,ReimburseItem> implements ReimburseItemService {

    private static final Logger logger = LoggerFactory.getLogger(ReimburseItemServiceImpl.class);

    @Autowired
    private ReimburseItemRepository reimburseItemRepository;
    @Autowired
    private ReimburseItemMapper reimburseItemMapper;

    @Override
    public Map<String,Object> queryAll(ReimburseItemQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ReimburseItem> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ReimburseItem.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", reimburseItemMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ReimburseItemDto> queryAll(ReimburseItemQueryCriteria criteria){
        return reimburseItemMapper.toDto(list(QueryHelpPlus.getPredicate(ReimburseItem.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReimburseItemDto findById(Long reimburseItemId) {
        ReimburseItem reimburseItem = Optional.ofNullable(getById(reimburseItemId)).orElseGet(ReimburseItem::new);
        ValidationUtil.isNull(reimburseItem.getReimburseItemId(),getEntityClass().getSimpleName(),"reimburseItemId",reimburseItemId);
        return reimburseItemMapper.toDto(reimburseItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReimburseItemDto create(ReimburseItem resources) {
        save(resources);
        return findById(resources.getReimburseItemId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReimburseItem resources) {
        ReimburseItem reimburseItem = Optional.ofNullable(getById(resources.getReimburseItemId())).orElseGet(ReimburseItem::new);
        ValidationUtil.isNull( reimburseItem.getReimburseItemId(),"ReimburseItem","id",resources.getReimburseItemId());
        reimburseItem.copy(resources);
        updateById(reimburseItem);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long reimburseItemId : ids) {
            reimburseItemRepository.deleteById(reimburseItemId);
        }
    }

    @Override
    public void download(List<ReimburseItemDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ReimburseItemDto reimburseItem : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("费用报销合计id", reimburseItem.getReimburseId());
            map.put("费用一级", reimburseItem.getCostFirstCode());
            map.put("费用二级", reimburseItem.getCostSecondCode());
            map.put("费用三级", reimburseItem.getCostThirdCode());
            map.put("费用承担部门", reimburseItem.getExpenseDept());
            map.put("费用承担部门code", reimburseItem.getExpenseDeptCode());
            map.put("报销金额", reimburseItem.getExpenseFee());
            map.put("税率", reimburseItem.getTaxRatio());
            map.put("税额", reimburseItem.getTaxFee());
            map.put("不含税金额", reimburseItem.getExpenseToTaxFee());
            map.put("创建时间", reimburseItem.getCreateTime());
            map.put("更新时间", reimburseItem.getUpdateTime());
            map.put("创建人", reimburseItem.getCreateBy());
            map.put("更新人", reimburseItem.getUpdateBy());
            map.put("是否删除", reimburseItem.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}