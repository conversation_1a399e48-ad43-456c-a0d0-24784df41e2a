/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectApprove;
import com.bassims.modules.atour.domain.ProjectCompletionReceipt;
import com.bassims.modules.atour.service.dto.*;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2024-01-26
 **/
public interface ProjectCompletionReceiptService extends BaseService<ProjectCompletionReceipt> {

    /**
     * 模板下载
     */
    void downloadTemplate(ProjectCompletionReceiptQueryCriteria criteria, HttpServletResponse response);

    void downloadTemplateOld(ProjectCompletionReceiptQueryCriteria criteria, HttpServletResponse response);

    /**
     * 查询文件下载状态
     * @param projectId
     */
    String queryUnqualifiedDownloadState(Long projectId);
    /**
     * 删除已经生成的文件
     * @param projectId
     */
    String removeUnqualified(Long projectId);

    /**
     * 查询项目竣工验收单的总项数据
     *
     * @param criteria 条件参数
     * @return List<ProjectCompletionReceiptDto>
     */
    List<ProjectCompletionReceiptSubItemDto> getTotalItemList(ProjectCompletionReceiptQueryCriteria criteria);

    /**
     * 根据总项数据 查询项目竣工验收单的分项数据
     *
     * @param criteria 条件参数
     * @return List<ProjectCompletionReceiptDto>
     */
    List<ProjectCompletionReceiptSubItemDto> getSubItemList(ProjectCompletionReceiptQueryCriteria criteria);


    /**
     * 查询项目竣工验收单的列表数据
     *
     * @param criteria 条件参数
     * @return List<ProjectCompletionReceiptDto>
     */
    ProjectCompletionReceiptListDto getListBySubItem(ProjectCompletionReceiptQueryCriteria criteria);

    /**
     * @description: 计算单行竣工验收验收单数据
     */
    ProjectCompletionReceipt getScoreCalculation(ProjectCompletionReceipt resources);

    /**
     * 保存竣工验收验收单项目表
     */
    void updateCompletionReceipt(ProjectCompletionReceiptListDto resources);


    /**
     * 查询验收汇总
     */
    ProjectCompletionReceiptSummaryDto getAcceptanceSummary(ProjectCompletionReceiptQueryCriteria criteria);

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ProjectCompletionReceiptQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ProjectCompletionReceiptDto>
     */
    List<ProjectCompletionReceiptDto> queryAll(ProjectCompletionReceiptQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param projectReceiptId ID
     * @return ProjectCompletionReceiptDto
     */
    ProjectCompletionReceiptDto findById(Long projectReceiptId);

    /**
     * 创建
     *
     * @param resources /
     * @return ProjectCompletionReceiptDto
     */
    ProjectCompletionReceiptDto create(ProjectCompletionReceipt resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ProjectCompletionReceipt resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ProjectCompletionReceiptDto> all, HttpServletResponse response) throws IOException;

    List<ProjectCompletionReceipt> listByUnqualified(ProjectCompletionReceiptQueryCriteria criteria);

    Map groupApprovalStatus(Long projectId);

    Boolean updateByUnqualified(List<ProjectCompletionReceipt> resources);

    void updateRemarks(ProjectCompletionReceipt projectCompletionReceipt);

    void keepRecordsAuditComments(ProjectApprove projectApprove );
}