package com.bassims.modules.atour.service.impl;

import com.alibaba.excel.EasyExcel;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ProjectInputDetailReport;
import com.bassims.modules.atour.repository.ProjectInputDetailReportRepository;
import com.bassims.modules.atour.service.ProjectInputDetailReportService;
import com.bassims.modules.atour.service.dto.ProjectInputDetailReportDto;
import com.bassims.modules.atour.service.dto.ProjectInputDetailReportQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectInputDetailReportMapper;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.utils.PageUtil;
import com.bassims.utils.QueryHelpPlus;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProjectInputDetailReportServiceImpl extends BaseServiceImpl<ProjectInputDetailReportRepository, ProjectInputDetailReport> implements ProjectInputDetailReportService {

    @Autowired
    private ProjectInputDetailReportMapper projectInputDetailReportMapper;

    @Autowired
    private DictDetailService dictDetailService;

    @Override
    public void download(HttpServletResponse response, ProjectInputDetailReportQueryCriteria criteria) throws IOException {
        String fileName = "工程投入明细报表_" + System.currentTimeMillis() + ".xlsx";

        OutputStream outputStream = response.getOutputStream();
        String fileNameURL = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
        response.setContentType("application/msexcel;charset=UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String sheetName = "工程投入明细报表";
        List<ProjectInputDetailReportDto> data = projectInputDetailReportMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectInputDetailReport.class,criteria)));
        int index = 1;
        for (ProjectInputDetailReportDto datum : data) {
            datum.setIndexNo(index++);
        }
        EasyExcel.write(outputStream, ProjectInputDetailReportDto.class).sheet(sheetName).doWrite(data);
    }

    @Override
    public Object queryTime(ProjectInputDetailReportQueryCriteria criteria, Pageable pageable) {
        List<ProjectInputDetailReport> finalList=list(QueryHelpPlus.getPredicate(ProjectInputDetailReport.class,criteria));
        PageInfo<ProjectInputDetailReport> page = new PageInfo<>();
        if(finalList.size()>0){
            List list1 = PageUtil.toPage(pageable.getPageNumber()==0?1:(pageable.getPageNumber()-1), pageable.getPageSize(), finalList);
            page = new PageInfo<ProjectInputDetailReport>(list1);
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", page.getList());
        map.put("totalElements", finalList.size());
        return map;
    }
}
