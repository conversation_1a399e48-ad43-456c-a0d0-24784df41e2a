/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.ProjectTemplateDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateQueryCriteria;
import com.bassims.modules.atour.service.dto.TemplateGroupDto;
import com.bassims.modules.atour.service.mapstruct.ProjectTemplateMapper;
import com.bassims.modules.atour.service.mapstruct.TemplateGroupMapper;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-22
 **/
@Service
public class ProjectTemplateServiceImpl extends BaseServiceImpl<ProjectTemplateRepository, ProjectTemplate> implements ProjectTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTemplateServiceImpl.class);

    @Autowired
    private ProjectTemplateRepository projectTemplateRepository;
    @Autowired
    private ProjectTemplateMapper projectTemplateMapper;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private NoticeTemplateService noticeTemplateService;
    @Autowired
    private ApproveTemplateService approveTemplateService;
    @Autowired
    private ScheduleDetailRepository scheduleDetailRepository;
    @Autowired
    private TemplateGroupService templateGroupService;
    @Autowired
    private TemplateGroupMapper templateGroupMapper;
    @Autowired
    private StoreTemplateRelationService storeTemplateRelationService;
    @Autowired
    private ScheduleDetailService scheduleDetailService;
    @Autowired
    private DictDetailService dictDetailService;
    @Autowired
    private ProjectTemplateService projectTemplateService;
    @Autowired
    private TemplateQueueService templateQueueService;
    @Autowired
    private TemplateJointTaskOnfigurationRepository taskOnfigurationRepository;
    @Autowired
    private TemplateOutputRelationRepository templateOutputRelationRepository;
    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private TemplateQueueRepository templateQueueRepository;


    @Override
    public Map<String, Object> queryAll(ProjectTemplateQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        criteria.setParentIdForJava(1L);
        PageInfo<ProjectTemplate> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectTemplate.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectTemplateMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectTemplateDto> queryAll(ProjectTemplateQueryCriteria criteria) {
        return projectTemplateMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectTemplate.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTemplateDto findById(Long templateId) {
        ProjectTemplate projectTemplate = Optional.ofNullable(getById(templateId)).orElseGet(ProjectTemplate::new);
        ValidationUtil.isNull(projectTemplate.getTemplateId(), getEntityClass().getSimpleName(), "templateId", templateId);
        return projectTemplateMapper.toDto(projectTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTemplateDto create(ProjectTemplate resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTemplateId(snowflake.nextId());
        save(resources);
        return findById(resources.getTemplateId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectTemplate resources) {
        ProjectTemplate projectTemplate = Optional.ofNullable(getById(resources.getTemplateId())).orElseGet(ProjectTemplate::new);
        ValidationUtil.isNull(projectTemplate.getTemplateId(), "ProjectTemplate", "id", resources.getTemplateId());
        projectTemplate.copy(resources);
        updateById(projectTemplate);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateId : ids) {
            projectTemplateRepository.deleteById(templateId);
        }
    }

    @Override
    public void download(List<ProjectTemplateDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectTemplateDto projectTemplate : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("父节点", projectTemplate.getParentId());
            map.put("名称", projectTemplate.getNodeName());
            map.put(" templateCode", projectTemplate.getTemplateCode());
            map.put("节点编码", projectTemplate.getNodeCode());
            map.put("子节点下标", projectTemplate.getNodeIndex());
            map.put("节点序号", projectTemplate.getNodeWbs());
            map.put("前置任务配置", projectTemplate.getFrontWbsConfig());
            map.put("是否是关键节点", projectTemplate.getIsKey());
            map.put("关键节点前置任务", projectTemplate.getKeyFrontWbs());
            map.put("计划需要完成天数", projectTemplate.getPlanDay());
            map.put("节点等级", projectTemplate.getNodeLevel());
            map.put("节点类型", projectTemplate.getNodeType());
            map.put("提醒天数", projectTemplate.getNoticeDay());
            map.put("是否可用", projectTemplate.getIsEnabled());
            map.put("是否删除", projectTemplate.getIsDelete());
            map.put("创建时间", projectTemplate.getCreateTime());
            map.put("更新时间", projectTemplate.getUpdateTime());
            map.put(" createBy", projectTemplate.getCreateBy());
            map.put(" updateBy", projectTemplate.getUpdateBy());
            map.put("总工期", projectTemplate.getTotalDay());
            map.put("开始标志（甘特图）", projectTemplate.getStartSign());
            map.put("结束标志（甘特图）", projectTemplate.getEndSign());
            map.put("干系人角色名称", projectTemplate.getJobCode());
            map.put("下拉列表角色名称", projectTemplate.getDownCode());
            map.put("是否手机端", projectTemplate.getIsMobile());
            map.put("关联nodecode", projectTemplate.getRelationCode());
            map.put("关联的类型", projectTemplate.getRelationType());
            map.put("使用场景", projectTemplate.getUseCase());
            map.put("角色code", projectTemplate.getRoleCode());
            map.put("是否可以编辑", projectTemplate.getIsEdit());
            map.put("占位", projectTemplate.getSeat());
            map.put("remark", projectTemplate.getRemark());
            map.put("小程序标志", projectTemplate.getIcon());
            map.put("公式", projectTemplate.getFormula());
            map.put("影响的code", projectTemplate.getFormulaCode());

            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public boolean batchInsert(List<ProjectTemplate> template) {
        return this.saveBatch(template);
    }

    @Override
    public List<Tree<String>> getTemplateTree(Long templateId) {
        LambdaQueryWrapper<ProjectTemplate> template = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateId, templateId);
        ProjectTemplateDto parent = projectTemplateMapper.toDto(getOne(template));
        LambdaQueryWrapper<ProjectTemplate> query = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateCode, parent.getTemplateCode());
        List<ProjectTemplateDto> dtos = projectTemplateMapper.toDto(list(query));
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectTemplateDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(4);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, templateId.toString(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    @Override
    public List<ProjectTemplateDto> getParentNode(ProjectTemplateQueryCriteria criteria) {
        List<ProjectTemplateDto> projectTemplateList = projectTemplateRepository.getParentNode(criteria);
        return projectTemplateList;
    }

    @Override
    public ProjectTemplateDto getParentNodeInfo(Long templateId) {
        ProjectTemplateDto parentNodeInfo = projectTemplateRepository.getParentNodeInfo(templateId);
        return parentNodeInfo;
    }

    @Override
    public List<Tree<String>> getTemplateTaskTree(Long templateId) {
        LambdaQueryWrapper<ProjectTemplate> template = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateId, templateId);
        ProjectTemplateDto parent = projectTemplateMapper.toDto(getOne(template));
        LambdaQueryWrapper<ProjectTemplate> query = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateCode, parent.getTemplateCode());
        List<ProjectTemplateDto> dtos = projectTemplateMapper.toDto(list(query));
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            String templateIdCh = dto.getTemplateId();
            //查询当前节点所关联的模板
            LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
            ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, templateIdCh).eq(ProjectTemplateApproveRelation::getIsDelete, 0);
            ProjectTemplateApproveRelation relation = projectTemplateApproveRelationService.getOne(ptarLambdaQueryWrapper);
            if (relation != null) {
                //查找关联关系
                if (relation.getNoticeId() != null) {
                    //查找消息模板
                    LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                    noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeId, relation.getNoticeId());
                    NoticeTemplate noticeTemplate = noticeTemplateService.getOne(noticeTemplateLambdaQueryWrapper);
                    dto.setNoticeId(noticeTemplate.getNoticeId());
                    dto.setNoticeName(noticeTemplate.getNoticeName());
                }
                if (relation.getApproveMatrixId() != null) {
                    //查找审批模板
                    LambdaQueryWrapper<ApproveTemplate> approveTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ApproveTemplate.class);
                    approveTemplateLambdaQueryWrapper.eq(ApproveTemplate::getApproveTemplateId, relation.getApproveMatrixId());
                    ApproveTemplate approveTemplate = approveTemplateService.getOne(approveTemplateLambdaQueryWrapper);
                    dto.setApproveMatrixId(approveTemplate.getApproveTemplateId());
                    dto.setAppTemplateName(approveTemplate.getAppTemplateName());
                }
            }
            Class<? extends ProjectTemplateDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(12);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(2);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, templateId.toString(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    @Override
    public List<Tree<String>> getTemplateTaskTreeOther(Long templateId) {
        LambdaQueryWrapper<TemplateGroup> template = Wrappers.lambdaQuery(TemplateGroup.class).eq(null != templateId, TemplateGroup::getTemplateId, templateId);
        TemplateGroupDto parent = templateGroupMapper.toDto(templateGroupService.getOne(template));
        LambdaQueryWrapper<TemplateGroup> query = Wrappers.lambdaQuery(TemplateGroup.class).eq(null != templateId, TemplateGroup::getTemplateCode, parent.getTemplateCode());
        List<TemplateGroupDto> dtos = templateGroupMapper.toDto(templateGroupService.list(query));
        //查询当前节点所关联的模板
        LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapperl = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
        ptarLambdaQueryWrapperl.eq(ProjectTemplateApproveRelation::getIsDelete, 0);
        List<ProjectTemplateApproveRelation> relationList = projectTemplateApproveRelationService.list(ptarLambdaQueryWrapperl);
        List<TemplateGroupDto> projectTemplateDtoList = new LinkedList<>();
        dtos.stream().forEach(temp -> {
            projectTemplateDtoList.add(temp);
            String templateIdCh = temp.getTemplateId();
            String templateGroupId = temp.getTemplateGroupId();
            List<ProjectTemplateApproveRelation> collect = relationList.stream().filter(relation -> (relation.getTemplateId().equals(temp.getTemplateId()) && relation.getTemplateGroupId().equals(templateGroupId))).collect(Collectors.toList());
            if (collect.size() > 0) {
                ProjectTemplateApproveRelation projectTemplateApproveRelation = collect.get(0);
                //有子节点，进行下一步操作
                if (projectTemplateApproveRelation.getApproveMatrixId() != null) {
                    //查找审批模板
                    LambdaQueryWrapper<ApproveTemplate> approveTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ApproveTemplate.class);
                    approveTemplateLambdaQueryWrapper.eq(ApproveTemplate::getApproveTemplateId, projectTemplateApproveRelation.getApproveMatrixId());
                    ApproveTemplate approveTemplate = approveTemplateService.getOne(approveTemplateLambdaQueryWrapper);
                    TemplateGroupDto approveTemplateDto = new TemplateGroupDto();
                    approveTemplateDto.setTemplateId(approveTemplate.getApproveTemplateId().toString());
                    approveTemplateDto.setParentId(temp.getParentId());
                    approveTemplateDto.setNodeName(approveTemplate.getAppTemplateName());
                    approveTemplateDto.setNodeCode(approveTemplate.getAppTempleteCode());
                    approveTemplateDto.setNodeIndex(temp.getNodeIndex() + 1);
                    projectTemplateDtoList.add(approveTemplateDto);
                }
            }
        });
        //转换treeNode
        List<TreeNode<String>> treeNodes = projectTemplateDtoList.stream().map(dto -> {
            if (dto.getNodeLevel() == null || dto.getNodeLevel() < 3) {
                String templateIdCh = dto.getTemplateId();
                String templateGroupId = dto.getTemplateGroupId();
                //查询当前节点所关联的模板
                LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
                ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, templateIdCh).eq(ProjectTemplateApproveRelation::getTemplateGroupId, templateGroupId).eq(ProjectTemplateApproveRelation::getIsDelete, 0);
                List<ProjectTemplateApproveRelation> relations = projectTemplateApproveRelationService.list(ptarLambdaQueryWrapper);
                if (ObjectUtils.isNotEmpty(relations) && relations.size() > 0) {
                    //查找关联关系
                    ProjectTemplateApproveRelation relation = relations.get(0);
                    if (relation.getNoticeId() != null) {
                        //查找消息模板
                        LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                        noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeId, relation.getNoticeId());
                        NoticeTemplate noticeTemplate = noticeTemplateService.getOne(noticeTemplateLambdaQueryWrapper);
                        dto.setNoticeId(noticeTemplate.getNoticeId());
                        dto.setNoticeName(noticeTemplate.getNoticeName());
                    }
                    if (relation.getApproveMatrixId() != null) {

                        //查找审批模板
                        LambdaQueryWrapper<ApproveTemplate> approveTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ApproveTemplate.class);
                        approveTemplateLambdaQueryWrapper.eq(ApproveTemplate::getApproveTemplateId, relation.getApproveMatrixId());
                        ApproveTemplate approveTemplate = approveTemplateService.getOne(approveTemplateLambdaQueryWrapper);
                        dto.setApproveMatrixId(approveTemplate.getApproveTemplateId());
                        dto.setAppTemplateName(approveTemplate.getAppTemplateName());
                    }
                }
            }

            Class<? extends TemplateGroupDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(12);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(2);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, templateId.toString(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    @Override
    public void updatePlanDay(List<ProjectTemplateDto> resources) {
        if (CollectionUtils.isNotEmpty(resources)) {
            for (ProjectTemplateDto dto : resources) {
                ProjectTemplate projectTemplate = Optional.ofNullable(getById(dto.getTemplateId())).orElseGet(ProjectTemplate::new);
                ValidationUtil.isNull(projectTemplate.getTemplateId(), "ProjectTemplate", "id", dto.getTemplateId());
                projectTemplate.setPlanDay(dto.getPlanDay());
                projectTemplate.setTotalDay(dto.getTotalDay());
                projectTemplate.setNoticeDay(dto.getNoticeDay());
                updateById(projectTemplate);
                if (CollectionUtils.isNotEmpty(dto.getChildren())) {
                    updatePlanDay(dto.getChildren());
                }
            }
        }
    }

    @Override
    public List<Tree<String>> getJobTemplateNode(Long jobId, Long templateId) {
        LambdaQueryWrapper<TemplateGroup> template = Wrappers.lambdaQuery(TemplateGroup.class).eq(null != templateId, TemplateGroup::getTemplateGroupId, templateId);
        TemplateGroupDto parent = templateGroupMapper.toDto(templateGroupService.getOne(template));
        List<TemplateGroupDto> dtos = projectTemplateRepository.getJobNodeTemplate(jobId, parent.getTemplateCode());
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends TemplateGroupDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(2);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, templateId.toString(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    @Override
    public List<ProjectTemplateDto> getTemplateParentList(ProjectTemplateDto projectTemplateDto) {
        List<ProjectTemplateDto> templateParentList = projectTemplateRepository.getTemplateParentList(projectTemplateDto);
        return templateParentList;
    }

    @Override
    public List<Tree<String>> getTemplateTreeBySchedule(Long templateId, String scheduleType) {
        LambdaQueryWrapper<ProjectTemplate> template = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateId, templateId);
        ProjectTemplateDto parent = projectTemplateMapper.toDto(getOne(template));
        LambdaQueryWrapper<ProjectTemplate> query = Wrappers.lambdaQuery(ProjectTemplate.class).eq(null != templateId, ProjectTemplate::getTemplateCode, parent.getTemplateCode());
        List<ProjectTemplateDto> dtos = projectTemplateRepository.getTemplateBySchedule(parent.getTemplateCode(), scheduleType);
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectTemplateDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(4);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, templateId.toString(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    @Override
    public String updatePlanDayByTemplateGroup(List<ProjectTemplateDto> resources, String front) {
        if (CollectionUtils.isNotEmpty(resources)) {
            String templateId = resources.get(0).getTemplateId();
            String scheduleType = resources.get(0).getScheduleType();
            //String front="";
            for (ProjectTemplateDto dto : resources) {
                LambdaUpdateWrapper<TemplateGroup> updateWrapper = Wrappers.<TemplateGroup>lambdaUpdate()
                        .eq(TemplateGroup::getTemplateId, dto.getTemplateId());

                TemplateGroup templateGroup = new TemplateGroup();
                templateGroup.setPlanDay(dto.getPlanDay());
                templateGroup.setTotalDay(dto.getTotalDay());
                if (ObjectUtils.isNotEmpty(dto.getChildren())) {
                    int sum = dto.getChildren().stream().mapToInt(ProjectTemplateDto::getPlanDay).sum();
                    templateGroup.setPlanDay(ObjectUtils.isNotEmpty(dto.getPlanDay()) ? dto.getPlanDay() : sum);

                    Integer totalDay = dto.getChildren().stream().max(Comparator.comparing(ProjectTemplateDto::getTotalDay)).get().getTotalDay();
                    templateGroup.setTotalDay(ObjectUtils.isNotEmpty(dto.getTotalDay()) ? dto.getTotalDay() : totalDay);
                }

                templateGroup.setNoticeDay(dto.getNoticeDay());
                templateGroup.setIsKey(dto.getIsKey());
                if (dto.getIsKey() != null && dto.getIsKey()) {
                    //更新紧前
                    if (front != "") {
                        templateGroup.setKeyFrontWbs(front + "FS");

                    }
                    front = dto.getNodeCode();
                    logger.info("-----------" + front);
                }
                templateGroupRepository.update(templateGroup, updateWrapper);
                if (CollectionUtils.isNotEmpty(dto.getChildren())) {
                    front = updatePlanDayByTemplateGroup(dto.getChildren(), front);
                }
            }
            return front;

        }
        return front;

    }

    @Override
    public boolean batchInsertBySchedule(List<ProjectTemplateDto> template) {

        return false;
    }

    public static String incrementNumber2(String input) {
        Pattern pattern = Pattern.compile("(.*-)(\\d+)");
        Matcher matcher = pattern.matcher(input);

        if (matcher.matches()) {
            String prefix = matcher.group(1);
            String numberStr = matcher.group(2);
            int number = Integer.parseInt(numberStr);
            int newNumber = number + 1;
            String incrementedNumber = String.format("%0" + numberStr.length() + "d", newNumber);
            return prefix + incrementedNumber;
        } else {
            return input; // Return unchanged if pattern doesn't match
        }
    }

    @Override
    public List<ProjectTemplateDto> selectThreeLevel(Long templateId) {
//        List<ProjectTemplate> list = projectTemplateRepository.selectThreeLevel(templateId);
//        //根据nodeIndex排序
//        list.sort(Comparator.comparing(ProjectTemplate::getNodeIndex));
//
//        List<ProjectTemplateDto> dto = projectTemplateMapper.toDto(list);
//        return dto;
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateThreeLevel(TemplateQueueReq req) {
        //先更新template_group表
        TemplateQueue templateQueue = new TemplateQueue();
        BeanUtils.copyProperties(req, templateQueue);
        templateQueueService.updateById(templateQueue);

        //再更新project_template表
        //查询project_template对应的记录
        ProjectTemplate projectTemplate = projectTemplateRepository.selectProjectTemplate(req.getTemplateCode(), req.getTemplateId());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplate.setNodeType(req.getNodeType());
        projectTemplate.setIsEdit(req.getIsEdit());
        projectTemplate.setRelationCode(req.getRelationCode());
        projectTemplate.setEndSign(req.getEndSign());
        projectTemplateService.updateById(projectTemplate);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object deleteThreeLevel(String templateCode, Long templateId) {

        //删除template_queue表
        projectTemplateRepository.deleteQueue(templateId);

        //删除project_template表
        projectTemplateRepository.deleteProject(templateId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveTemplate(SaveTemplateReq req) {
        //TODO 模版和品牌类型的配置

        //查询是否已有这个模板
        StoreTemplateRelation isexitCode = projectTemplateRepository.selectByTemplateCode(req.getTemplateCode());
        if (isexitCode != null) {
            throw new BadRequestException("已存在该code");
        }

        StoreTemplateRelation isexitType = projectTemplateRepository.selectByTemplateType(req.getStoreType());
        if (isexitType != null) {
            throw new BadRequestException("已存在该模版类型");
        }
        //保存第零级
//        ProjectTemplate projectTemplate = new ProjectTemplate();
//        projectTemplate.setTemplateCode(req.getStoreType());
//        projectTemplate.setNodeName(req.getNodeName());
//        if (req.getRemark() != null) {
//            projectTemplate.setRemark(req.getRemark());
//        }
//        projectTemplate.setNodeIndex(0);
//        projectTemplate.setIsEdit("1");
//        projectTemplate.setSeat("1");
//        projectTemplate.setIsEnabled(true);
//        projectTemplate.setIsDelete(false);
//        projectTemplate.setIsMobile(true);
//
//        save(projectTemplate);

        TemplateGroup templateGroup = new TemplateGroup();
        templateGroup.setTemplateCode(req.getStoreType());
        templateGroup.setNodeName(req.getNodeName());
        if (req.getRemark() != null) {
            templateGroup.setRemark(req.getRemark());
        }
        templateGroup.setNodeCode(req.getStoreType() + "-00");
        templateGroup.setNodeLevel(0);
        templateGroup.setIsEnabled(true);
        templateGroup.setIsDelete(false);
        templateGroup.setUseCase("1");
        templateGroup.setTemplateType(req.getTemplateType());
        templateGroupService.save(templateGroup);

        templateGroup.setTemplateId(templateGroup.getTemplateGroupId());
        templateGroupService.updateById(templateGroup);

        //保存schedule表
        scheduleDetailService.createTemplateSchedule(templateGroup.getTemplateId(), req.getStoreType(), templateGroup.getTemplateCode());

//        保存模板表
        StoreTemplateRelation storeTemplateRelation = new StoreTemplateRelation();
        storeTemplateRelation.setStoreType(req.getStoreType());
        storeTemplateRelation.setTemplateId(templateGroup.getTemplateId());
        storeTemplateRelation.setTemplateCode(req.getStoreType());
        storeTemplateRelation.setIsEnabled(true);
        storeTemplateRelation.setIsDelete(false);
        storeTemplateRelationService.save(storeTemplateRelation);
//
//        //保存字典表
        DictResp dict = projectTemplateRepository.getDictByName("store_type");
        DictDetail dictDetail = dictDetailService.getByDictId(dict.getDictId());
        Integer dictSort = dictDetail.getDictSort();
        int dictId = dictSort + 1;
        projectTemplateRepository.saveDictDetail(dict.getDictId(), req.getNodeName(), req.getStoreType(), dictId);
//        return projectTemplate.getTemplateId();

        //清理dict缓存
        dictDetailService.delCaches(dictDetail);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateTemplate(UpdateTemplateReq req) {
        TemplateGroup templateGroup = projectTemplateRepository.getByTemplateId(req);
        templateGroup.setNodeName(req.getNodeName());
        templateGroup.setTemplateType(req.getTemplateType());
        templateGroupService.update(templateGroup);

//        //查询这个temp对应的字典表
//        DictDetailResp dictDetail = projectTemplateRepository.getByTemplateCode(req.getTemplateCode());
//        if (ObjectUtils.isNotEmpty(dictDetail)) {
//            //修改字典表
//            projectTemplateRepository.updateDictDetail(dictDetail.getDetailId(), req.getNodeName(), req.getTemplateCode());
//        }

        return templateGroup.getTemplateId();
//        StoreTemplateRelation storeTemplateRelation = storeTemplateRelationService.getByTemplateId(req.getTemplateId());
//        storeTemplateRelation.setStoreType(req.getStoreType());
//        storeTemplateRelation.setTemplateCode(req.getStoreType());
//        storeTemplateRelationService.update(storeTemplateRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOneLevel(TemplateGroup req) {

        List<TemplateGroup> templateGroups = projectTemplateRepository.selectOneLevelByTemplateCode(req.getTemplateCode());

        //查询是否之前有节点
        TemplateGroup templateGroup = projectTemplateRepository.selectOneLevel(req.getTemplateCode(), req.getNodeLevel());

        //如果没有之前没有，则手动创建一个node_code
        if (templateGroup == null) {
            //查询0级
            ProjectTemplate projectTemplate = projectTemplateRepository.selectZeroLevel(req.getTemplateCode());
            req.setNodeCode(projectTemplate.getNodeCode() + "1");
        } else {
            //如果之前有，则自动创建一个node_code
            String nodeCode = incrementNumber2(templateGroup.getNodeCode());
            req.setNodeCode(nodeCode);
        }

        //先保存进project_template表
        ProjectTemplate projectTemplate = new ProjectTemplate();
        projectTemplate.setTemplateCode(req.getTemplateCode());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplate.setNodeCode(req.getNodeCode());
        projectTemplate.setNodeIndex(templateGroups.size() + 1);
        projectTemplate.setNodeWbs(templateGroups.size() + 1);
        projectTemplate.setNodeLevel(req.getNodeLevel());
        projectTemplate.setIsEdit("1");
        projectTemplate.setSeat("1");
        projectTemplate.setIsEnabled(true);
        projectTemplate.setIsDelete(false);

        projectTemplateService.save(projectTemplate);

        //再保存进template_group表
        req.setNodeIndex(templateGroups.size() + 1);
        req.setNodeWbs(templateGroups.size() + 1);
        req.setRoleCode(req.getRoleCode());
        req.setTemplateId(projectTemplate.getTemplateId());
        req.setUseCase("1");
        req.setIsEnabled(true);
        req.setIsDelete(false);
        templateGroupService.save(req);

        return true;

    }

    @Override
    public List<TemplateGroup> selectTemplateLevel() {
        List<TemplateGroup> templateGroups = projectTemplateRepository.selectTemplateLevel();
        return templateGroups;
    }

    @Override
    public List<TemplateGroup> selectOneLevelByTemplateCode(String templateCode) {
        List<TemplateGroup> templateGroups = projectTemplateRepository.selectOneLevelByTemplateCode(templateCode);
        return templateGroups;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveTwoLevel(TemplateGroup req) {
        //查询是否之前有二级节点
        TemplateGroup templateGroup = projectTemplateRepository.selectTwoLevel(req.getTemplateCode(), req.getParentId());

        //如果没有之前没有二级，则手动创建一个node_code
        if (templateGroup == null) {
            //查询父级
            ProjectGroup projectGroup = projectTemplateRepository.selectParentId(req.getParentId());
            req.setNodeCode(projectGroup.getNodeCode() + "01");
        } else {
            //如果之前有二级，则自动创建一个node_code
            String nodeCode = incrementNumber2(templateGroup.getNodeCode());
            req.setNodeCode(nodeCode);
        }

        //先保存进project_template表
        ProjectTemplate projectTemplate = new ProjectTemplate();
        projectTemplate.setTemplateCode(req.getTemplateCode());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplate.setNodeCode(req.getNodeCode());
        projectTemplate.setNodeIndex(2);
        projectTemplate.setNodeWbs(2);
        projectTemplate.setNodeLevel(2);
        projectTemplate.setIsEdit("1");
        projectTemplate.setSeat("1");
        projectTemplate.setIsEnabled(true);
        projectTemplate.setIsDelete(false);
        projectTemplateService.save(projectTemplate);

        //保存template_queue表
        TemplateQueue templateQueue = new TemplateQueue();
        templateQueue.setTemplateId(projectTemplate.getTemplateId());
        templateQueue.setTemplateCode(req.getTemplateCode());
        templateQueue.setNodeName(req.getNodeName());
        templateQueue.setNodeCode(req.getNodeCode());
        templateQueue.setNodeLevel(2);
        templateQueue.setIsEnabled(true);
        templateQueue.setIsDelete(false);
        templateQueueService.save(templateQueue);


        //再保存进template_group表
        req.setTemplateQueueId(templateQueue.getTemplateQueueId());
        req.setTemplateId(projectTemplate.getTemplateId());
        req.setUseCase("1");
        req.setIsEnabled(true);
        req.setIsDelete(false);
        templateGroupService.save(req);

        return true;
    }

    @Override
    public List<TemplateGroupOneAndTwo> selectOneAndTwoLevelByTemplateCode(String templateCode) {
        //先查询出所有一级节点
        List<TemplateGroupOneAndTwo> templateGroupOne = projectTemplateRepository.selectLevelByTemplateCode(templateCode);

        for (TemplateGroupOneAndTwo templateGroup : templateGroupOne) {
            //查询出所有二级节点
            List<TemplateGroupOneAndTwo> templateGroupTwo = projectTemplateRepository.selectTwoLevelByTemplateCode(templateCode, templateGroup.getTemplateId());
            templateGroup.setTemplateGroupOneAndTwos(templateGroupTwo);
        }
        return templateGroupOne;
    }

    @Override
    public Object selectThreeLevelByTemplateCode(String templateCode, Long templateId) {
        List<TemplateQueueReq> templateQueues = projectTemplateRepository.selectThreeLevelByTemplateCode(templateCode, templateId);
        return templateQueues;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateOneLevel(TemplateGroup req) {
        //先更新template_group表
        templateGroupService.updateById(req);

        //再更新project_template表
        //查询project_template对应的记录
        ProjectTemplate projectTemplate = projectTemplateRepository.selectProjectTemplate(req.getTemplateCode(), req.getTemplateId());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplateService.updateById(projectTemplate);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateTwoLevel(TemplateGroup req) {
        //先更新project_template表
        templateGroupService.updateById(req);

        //再更新template_queue表
        //查询queue对应的记录
        TemplateQueue templateQueue = projectTemplateRepository.selectTemplateQueue(req);
        templateQueue.setNodeName(req.getNodeName());
        templateQueueService.updateById(templateQueue);

        //再更新project_template表
        //查询project_template对应的记录
        ProjectTemplate projectTemplate = projectTemplateRepository.selectProjectTemplate(req.getTemplateCode(), req.getTemplateId());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplateService.updateById(projectTemplate);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object deleteTemplate(String templateCode) {
        //删除store_template_relation表
        projectTemplateRepository.deleteStoreTemplateRelation(templateCode);

        //删除template_group表
        projectTemplateRepository.deleteTemplate(templateCode);

        //删除template_queue表
        projectTemplateRepository.deleteTemplateQueue(templateCode);

        //删除project_template表
        projectTemplateRepository.deleteProjectTemplate(templateCode);

        //删除s_sys_dict_detail
        projectTemplateRepository.deleteSysDictDetail(templateCode);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object deleteOneLevel(String templateCode, Long templateId) {


        //查询这个一级下的所有二级
        List<TemplateGroup> templateGroups = projectTemplateRepository.selectTwoLevelbyTemplateId(templateCode, templateId);

        for (TemplateGroup templateGroup : templateGroups) {
            //查询这二级下的所有三级
            List<TemplateQueueReq> templateQueues = projectTemplateRepository.selectThreeLevelByTemplateCode(templateCode, templateGroup.getTemplateId());
            for (TemplateQueueReq queue : templateQueues) {
                //删除三级template_queue表
                projectTemplateRepository.deleteQueue(queue.getTemplateId());
                //删除三级project_template表
                projectTemplateRepository.deleteProject(queue.getTemplateId());
            }
            //删除二级template_queue表
            projectTemplateRepository.deleteQueue(templateGroup.getTemplateId());
            //删除二级project_template表
            projectTemplateRepository.deleteProject(templateGroup.getTemplateId());
            //删除二级template_group表
            projectTemplateRepository.deleteGroup(templateGroup.getTemplateId());
        }
        //删除一级template_queue表
        projectTemplateRepository.deleteQueue(templateId);
        //删除一级project_template表
        projectTemplateRepository.deleteProject(templateId);
        //删除一级template_group表
        projectTemplateRepository.deleteGroup(templateId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object deleteTwoLevel(String templateCode, Long templateId) {

        //查询这个二级下的所有三级
        List<TemplateQueueReq> templateQueues = projectTemplateRepository.selectThreeLevelByTemplateCode(templateCode, templateId);

        for (TemplateQueueReq templateQueue : templateQueues) {
            //删除三级template_queue表
            projectTemplateRepository.deleteQueue(templateQueue.getTemplateId());
            //删除三级project_template表
            projectTemplateRepository.deleteProject(templateId);
        }

        //删除二级template_queue表
        projectTemplateRepository.deleteQueue(templateId);

        //删除二级project_template表
        projectTemplateRepository.deleteProject(templateId);

        //删除二级template_group表
        projectTemplateRepository.deleteGroup(templateId);

        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveThreeLevel(TemplateQueueReq req) {
        //查询是否之前有三级节点
        TemplateGroup templateGroup = projectTemplateRepository.selectThreeLevel(req.getTemplateCode(), req.getParentId());

        //如果没有之前没有三级，则手动创建一个node_code
        if (templateGroup == null) {
            //查询父级
            ProjectGroup projectGroup = projectTemplateRepository.selectParentId(req.getParentId());
            req.setNodeCode(projectGroup.getNodeCode() + "001");
        } else {
            //如果之前有三级，则自动创建一个node_code
            String nodeCode = incrementNumber2(templateGroup.getNodeCode());
            req.setNodeCode(nodeCode);
        }

        //先保存进project_template表
        ProjectTemplate projectTemplate = new ProjectTemplate();
        projectTemplate.setTemplateCode(req.getTemplateCode());
        projectTemplate.setNodeName(req.getNodeName());
        projectTemplate.setNodeCode(req.getNodeCode());
        projectTemplate.setNodeType(req.getNodeType());
        projectTemplate.setNodeIndex(1);
        projectTemplate.setNodeWbs(2);
        projectTemplate.setNodeLevel(3);
        projectTemplate.setIsEdit("1");
        projectTemplate.setSeat("1");
        projectTemplate.setIsEnabled(true);
        projectTemplate.setIsDelete(false);
        projectTemplateService.save(projectTemplate);

        //保存template_queue表
        TemplateQueue templateQueue = new TemplateQueue();
        req.setTemplateId(projectTemplate.getTemplateId());
        req.setTemplateCode(req.getTemplateCode());
        req.setNodeName(req.getNodeName());
        req.setNodeCode(req.getNodeCode());
        req.setNodeLevel(3);
        req.setIsEnabled(true);
        req.setIsDelete(false);
        BeanUtils.copyProperties(req, templateQueue);

        templateQueueService.save(templateQueue);

        return true;
    }

    @Override
    public TemplateGroup selectTwo(String templateCode, String nodeCode) {
        LambdaQueryWrapper<TemplateGroup> next = Wrappers.lambdaQuery(TemplateGroup.class)
                .eq(TemplateGroup::getNodeCode, nodeCode)
                .eq(TemplateGroup::getTemplateCode, templateCode)
                .last("limit 1");
        TemplateGroup groups = templateGroupRepository.selectOne(next);
        return groups;
    }

    @Override
    public List<TemplateGroupOneAndTwo> selectTwoLevelByTemplateCode(String templateCode, String nodeCode) {

        //先查询出所有一级节点
        List<TemplateGroupOneAndTwo> templateGroupOne = projectTemplateRepository.selectLevelByTemplateCode(templateCode);

        for (TemplateGroupOneAndTwo templateGroup : templateGroupOne) {
            //查询出所有二级节点
            List<TemplateGroupOneAndTwo> templateGroupTwo = projectTemplateRepository.selectTwoLevelByTemplateCode(templateCode, templateGroup.getTemplateId());

            //查询出该二级节点是否已经和nodeCode节点配置过
            for (TemplateGroupOneAndTwo record : templateGroupTwo) {
                LambdaQueryWrapper<TemplateJointTaskOnfiguration> queueLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateJointTaskOnfiguration.class)
                        .eq(TemplateJointTaskOnfiguration::getNodeCode, nodeCode)
                        .eq(TemplateJointTaskOnfiguration::getRelevanceNodeCode, record.getNodeCode());
                Long aLong = taskOnfigurationRepository.selectCount(queueLambdaQueryWrapper);
                //判断是否已经配置过联合任务
                if (null != aLong && aLong != 0L) {
                    record.setIsAssociation(true);
                } else {
                    record.setIsAssociation(false);
                }
            }
            templateGroup.setTemplateGroupOneAndTwos(templateGroupTwo);
        }
        return templateGroupOne;
    }

    @Override
    public List<TemplateGroupOneAndTwo> selectTwoByTemplateCode(String templateCode, String nodeCode) {
        //先查询出所有一级节点
        List<TemplateGroupOneAndTwo> templateGroupOne = projectTemplateRepository.selectLevelByTemplateCode(templateCode);
        for (TemplateGroupOneAndTwo templateGroup : templateGroupOne) {
            //查询出所有二级节点
            List<TemplateGroupOneAndTwo> templateGroupTwo = projectTemplateRepository.selectTwoLevelByTemplateCode(templateCode, templateGroup.getTemplateId());
            //查询出该二级节点是否已经和nodeCode节点配置过
            for (TemplateGroupOneAndTwo record : templateGroupTwo) {
                List<TemplateQueueReq> queueReqs = projectTemplateRepository.selectThreeLevelByTemplateCode(templateCode, record.getTemplateId());
                for (TemplateQueueReq queueReq : queueReqs) {
                    LambdaQueryWrapper<TemplateOutputRelation> queueLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateOutputRelation.class)
                            .eq(TemplateOutputRelation::getNodeCode, record.getNodeCode())
                            .eq(TemplateOutputRelation::getRelevanceNodeCode, queueReq.getNodeCode());
                    Long aLong = templateOutputRelationRepository.selectCount(queueLambdaQueryWrapper);
                    //判断是否已经配置过联合任务
                    if (null != aLong && aLong != 0L) {
                        record.setIsAssociation(true);
                    } else {
                        record.setIsAssociation(false);
                    }
                }
                record.setQueueReqs(queueReqs);
            }
            templateGroup.setTemplateGroupOneAndTwos(templateGroupTwo);
        }
        return templateGroupOne;
    }

    @Override
    public Object selectThreeLevelUniteByTemplateCode(String templateCode, Long templateId, String jointCode) {
        List<TemplateQueueReq> templateQueues = projectTemplateRepository.selectThreeLevelUniteByTemplateCode(templateCode, templateId, jointCode);
        return templateQueues;
    }

    @Override
    public Object getThreeLevelByNodeCode(String nodeCode) {
        LambdaQueryWrapper<ProjectTemplate> wrapper = Wrappers.lambdaQuery(ProjectTemplate.class)
                .eq(ProjectTemplate::getNodeCode, nodeCode)
                .eq(ProjectTemplate::getNodeType, JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST);
        ProjectTemplate projectTemplate = projectTemplateRepository.selectOne(wrapper);
        if (ObjectUtils.isEmpty(projectTemplate)) {
            throw new BadRequestException("请确认当前三级节点是否是动态表格类型");
        }
        return projectTemplate;
    }


    @Override
    @Transactional
    public List<ProjectTemplate> saveSecondaryTemplate(List<TemplateGroup> templateGroup) {
//        for (int i = 1; i < 14; i++) {
        Integer count = 1;  //查询当前模版已经分了几个模版，默认为1
        final ArrayList<ProjectTemplate> listA = new ArrayList<>();
        final ArrayList<TemplateQueue> listB = new ArrayList<>();
        final ArrayList<TemplateGroup> listC = new ArrayList<>();
        final ArrayList<Integer> results = new ArrayList<>();
        final LambdaQueryWrapper<TemplateGroup> next = Wrappers.lambdaQuery(TemplateGroup.class).like(TemplateGroup::getNodeCode, "next");
        final List<TemplateGroup> groups = templateGroupRepository.selectList(next);
        if (ObjectUtils.isNotEmpty(groups)) {
            groups.stream().filter(e -> e.getNodeCode().contains("next")).forEach(one -> {
                int index = one.getNodeCode().indexOf("next");
                if (index != -1) {
                    String result = one.getNodeCode().substring(index + "next".length());
                    results.add(Integer.valueOf(result));
                }
            });
            final Integer max = Collections.max(results.stream().distinct().collect(Collectors.toList()));
            count = max + 1; //存在分模版的话+1
        }
        Integer finalCount = count;
        templateGroup.stream().forEach(group -> {
            LambdaQueryWrapper<TemplateGroup> next1 = Wrappers.lambdaQuery(TemplateGroup.class)
                    .eq(TemplateGroup::getNodeCode, group.getNodeCode())
                    .eq(TemplateGroup::getTemplateCode, group.getTemplateCode())
                    .last("limit 1");
            TemplateGroup templateGroup1 = templateGroupRepository.selectOne(next1);
            templateGroup1.setTemplateGroupId(null);

//                TemplateGroup templateGroup1 = new TemplateGroup();
//                BeanUtil.copyProperties(templateGroup1, group, CopyOptions.create().setIgnoreNullValue(true));

            //生成次级的project_template
            final LambdaQueryWrapper<ProjectTemplate> eq = Wrappers.lambdaQuery(ProjectTemplate.class)
                    .like(ProjectTemplate::getNodeCode, group.getNodeCode())
                    .notLike(ProjectTemplate::getNodeCode, "next");
            final List<ProjectTemplate> projectTemplates = projectTemplateRepository.selectList(eq);
            projectTemplates.stream().forEach(projectTemplate -> {
                if (ObjectUtils.isEmpty(projectTemplate.getRelationCode())) {
                    projectTemplate.setRelationCode(projectTemplate.getNodeCode());
                }
                projectTemplate.setNodeCode(projectTemplate.getNodeCode() + "_next" + finalCount);
                //设置模板等级为分模板
                projectTemplate.setStencilLevel("split_plate");
                projectTemplate.setTemplateId(IdUtil.getSnowflake(1, 1).nextId());
                //赋值当前任务关联的 一二级任务
                projectTemplate.setOneTemplateId(group.getParentId());
                projectTemplate.setTwoTemplateId(group.getTemplateId());
                listA.add(projectTemplate);
            });
            projectTemplateService.saveBatch(listA);
            listA.clear();

            //生成次级的template_queue
            final LambdaQueryWrapper<TemplateQueue> like = Wrappers.lambdaQuery(TemplateQueue.class)
                    .like(TemplateQueue::getNodeCode, group.getNodeCode())
                    .notLike(TemplateQueue::getNodeCode, "next");
            final List<TemplateQueue> queueList = templateQueueRepository.selectList(like);
            queueList.stream().forEach(queue -> {
                queue.setNodeCode(queue.getNodeCode() + "_next" + finalCount);
                final LambdaQueryWrapper<ProjectTemplate> likeA = Wrappers.lambdaQuery(ProjectTemplate.class).eq(ProjectTemplate::getNodeCode, queue.getNodeCode());
                final ProjectTemplate template = projectTemplateRepository.selectOne(likeA);
                if (ObjectUtils.isNotEmpty(template) && ObjectUtils.isNotEmpty(template.getTemplateId())) {
                    queue.setTemplateId(template.getTemplateId());
                }
                if (ObjectUtils.isNotEmpty(queue.getParentId()) && ObjectUtils.isNotEmpty(queue.getNodeLevel()) && queue.getNodeLevel() == 3) {
                    final LambdaQueryWrapper<ProjectTemplate> eqA = Wrappers.lambdaQuery(ProjectTemplate.class)
                            .eq(ProjectTemplate::getNodeCode, group.getNodeCode() + "_next" + finalCount);
                    final ProjectTemplate two = projectTemplateRepository.selectOne(eqA);
                    if (ObjectUtils.isNotEmpty(two)) {
                        queue.setParentId(two.getTemplateId());
                    }
                }
                queue.setStencilLevel("split_plate");
                queue.setTemplateQueueId(IdUtil.getSnowflake(1, 1).nextId());
                listB.add(queue);
            });
            templateQueueService.saveBatch(listB);
            listB.clear();

            //生成次级的template_group
            // 赋值当前任务关联的 一二级任务
            templateGroup1.setOneTemplateId(group.getParentId());
            templateGroup1.setTwoTemplateId(group.getTemplateId());

            final LambdaQueryWrapper<TemplateQueue> wrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getNodeLevel, 2)
                    .like(TemplateQueue::getNodeCode, "_next" + finalCount);
            final List<TemplateQueue> queues = templateQueueRepository.selectList(wrapper);
            queues.stream().forEach(q -> {
                if (ObjectUtils.isNotEmpty(q.getTemplateQueueId()) && q.getNodeCode().equals(group.getNodeCode() + "_next" + finalCount)) {
                    templateGroup1.setTemplateQueueId(q.getTemplateQueueId());
                    templateGroup1.setTemplateId(q.getTemplateId());
                }
            });
            templateGroup1.setNodeCode(group.getNodeCode() + "_next" + finalCount);
            templateGroup1.setNodeIndex(group.getNodeIndex()); //分模版的顺序为原模版+分模版数（小数）
            templateGroup1.setStencilLevel("split_plate");
            templateGroup1.setTemplateGroupId(IdUtil.getSnowflake(1, 1).nextId());
            listC.add(templateGroup1);
        });
        templateGroupService.saveBatch(listC);
        listC.clear();
        System.out.println("执行完毕");
//        }
//        final LambdaQueryWrapper<ProjectTemplate> like = Wrappers.lambdaQuery(ProjectTemplate.class).like(ProjectTemplate::getNodeCode, "_next" + finalCount);
        return null;
    }

}