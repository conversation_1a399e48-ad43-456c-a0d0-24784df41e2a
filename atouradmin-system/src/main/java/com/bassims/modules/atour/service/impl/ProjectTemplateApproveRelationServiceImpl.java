/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ProjectTemplateApproveRelation;
import com.bassims.modules.atour.repository.ProjectTemplateApproveRelationRepository;
import com.bassims.modules.atour.service.ProjectTemplateApproveRelationService;
import com.bassims.modules.atour.service.dto.ProjectTemplateApproveRelationDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateApproveRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectTemplateApproveRelationMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.SnowFlakeUtil;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-30
 **/
@Service
public class ProjectTemplateApproveRelationServiceImpl extends BaseServiceImpl<ProjectTemplateApproveRelationRepository, ProjectTemplateApproveRelation> implements ProjectTemplateApproveRelationService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTemplateApproveRelationServiceImpl.class);

    @Autowired
    private ProjectTemplateApproveRelationRepository projectTemplateApproveRelationRepository;
    @Autowired
    private ProjectTemplateApproveRelationMapper projectTemplateApproveRelationMapper;

    @Override
    public Map<String, Object> queryAll(ProjectTemplateApproveRelationQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectTemplateApproveRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectTemplateApproveRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectTemplateApproveRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectTemplateApproveRelationDto> queryAll(ProjectTemplateApproveRelationQueryCriteria criteria) {
        return projectTemplateApproveRelationMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectTemplateApproveRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTemplateApproveRelationDto findById(Long templateRelationId) {
        ProjectTemplateApproveRelation projectTemplateApproveRelation = Optional.ofNullable(getById(templateRelationId)).orElseGet(ProjectTemplateApproveRelation::new);
        ValidationUtil.isNull(projectTemplateApproveRelation.getTemplateRelationId(), getEntityClass().getSimpleName(), "templateRelationId", templateRelationId);
        return projectTemplateApproveRelationMapper.toDto(projectTemplateApproveRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTemplateApproveRelationDto create(ProjectTemplateApproveRelation resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTemplateRelationId(snowflake.nextId());
        save(resources);
        return findById(resources.getTemplateRelationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectTemplateApproveRelation resources) {
        ProjectTemplateApproveRelation projectTemplateApproveRelation = Optional.ofNullable(getById(resources.getTemplateRelationId())).orElseGet(ProjectTemplateApproveRelation::new);
        ValidationUtil.isNull(projectTemplateApproveRelation.getTemplateRelationId(), "ProjectTemplateApproveRelation", "id", resources.getTemplateRelationId());
        projectTemplateApproveRelation.copy(resources);
        updateById(projectTemplateApproveRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateRelationId : ids) {
            projectTemplateApproveRelationRepository.deleteById(templateRelationId);
        }
    }

    @Override
    public void download(List<ProjectTemplateApproveRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectTemplateApproveRelationDto projectTemplateApproveRelation : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("审批矩阵id", projectTemplateApproveRelation.getApproveMatrixId());
            map.put("模板group", projectTemplateApproveRelation.getTemplateGroupId());
            map.put(" createTime", projectTemplateApproveRelation.getCreateTime());
            map.put(" updateTime", projectTemplateApproveRelation.getUpdateTime());
            map.put(" createBy", projectTemplateApproveRelation.getCreateBy());
            map.put(" updateBy", projectTemplateApproveRelation.getUpdateBy());
            map.put("节点编码", projectTemplateApproveRelation.getNodeCode());
            map.put("模板id", projectTemplateApproveRelation.getTemplateId());
            map.put("消息id", projectTemplateApproveRelation.getNoticeId());
            map.put("是否可用", projectTemplateApproveRelation.getIsEnabled());
            map.put("是否删除", projectTemplateApproveRelation.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean saveAppRelation(ProjectTemplateApproveRelationDto relationDto) {
        boolean save = false;
        //查询是否有旧的关联关系
        LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
        ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, relationDto.getTemplateId())
                .eq(ProjectTemplateApproveRelation::getTemplateGroupId, relationDto.getTemplateGroupId())
                .eq(ProjectTemplateApproveRelation::getIsDelete, 0);
        ProjectTemplateApproveRelation projectTemplateApproveRelation = projectTemplateApproveRelationRepository.selectOne(ptarLambdaQueryWrapper);
        if (projectTemplateApproveRelation != null) {
            save = this.removeById(projectTemplateApproveRelation.getTemplateRelationId());
        }
        if (relationDto.getUpdateType() != null && "update".equals(relationDto.getUpdateType())) {
            relationDto.setTemplateRelationId(SnowFlakeUtil.getInstance().nextLongId());
            relationDto.setIsDelete(false);
            ProjectTemplateApproveRelation projectTemplateApproveRelation1 = projectTemplateApproveRelationMapper.toEntity(relationDto);
            save = this.save(projectTemplateApproveRelation1);
        }
        return save;
    }

    @Override
    public Boolean saveNotiRelation(List<ProjectTemplateApproveRelationDto> resources) {
        boolean save = false;
        for (ProjectTemplateApproveRelationDto relationDto : resources) {
            if ("save".equals(relationDto.getUpdateType())) {
                //查询是否有旧的关联关系
                LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
                ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, relationDto.getTemplateId())
                        .eq(ProjectTemplateApproveRelation::getIsDelete, 0);
                ProjectTemplateApproveRelation projectTemplateApproveRelation = projectTemplateApproveRelationRepository.selectOne(ptarLambdaQueryWrapper);
                if (projectTemplateApproveRelation != null) {
                    if (relationDto.getApproveMatrixId() != null) {
                        projectTemplateApproveRelation.setNoticeId(relationDto.getNoticeId());
                    }

                    save = this.updateById(projectTemplateApproveRelation);
                } else {
                    relationDto.setTemplateRelationId(SnowFlakeUtil.getInstance().nextLongId());
                    relationDto.setIsDelete(false);
                    ProjectTemplateApproveRelation projectTemplateApproveRelation1 = projectTemplateApproveRelationMapper.toEntity(relationDto);
                    save = this.save(projectTemplateApproveRelation1);
                }
            }
            if ("delete".equals(relationDto.getUpdateType())) {
                //删除
                LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
                ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, relationDto.getTemplateId())
                        .eq(ProjectTemplateApproveRelation::getIsDelete, 0);
                ProjectTemplateApproveRelation projectTemplateApproveRelation = projectTemplateApproveRelationRepository.selectOne(ptarLambdaQueryWrapper);
                if (projectTemplateApproveRelation != null) {
                    projectTemplateApproveRelation.setIsDelete(false);
                    save = this.updateById(projectTemplateApproveRelation);
                }
            }
        }

        return save;
    }

    @Override
    public List<ProjectTemplateApproveRelationDto> getAppRelation(Long templateId, Long templateGroupId) {
        LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
        ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getTemplateId, templateId)
                .eq(ProjectTemplateApproveRelation::getTemplateGroupId, templateGroupId)
                .eq(ProjectTemplateApproveRelation::getIsDelete, 0);
        List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = projectTemplateApproveRelationRepository.selectList(ptarLambdaQueryWrapper);
        List<ProjectTemplateApproveRelationDto> relationDto = projectTemplateApproveRelationMapper.toDto(projectTemplateApproveRelations);
        return relationDto;
    }
}