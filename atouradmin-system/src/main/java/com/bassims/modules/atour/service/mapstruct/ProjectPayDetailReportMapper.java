package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectPayDetailReport;
import com.bassims.modules.atour.service.dto.ProjectPayDetailReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程付款明细报表
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectPayDetailReportMapper extends BaseMapper<ProjectPayDetailReportDto, ProjectPayDetailReport> {

}
