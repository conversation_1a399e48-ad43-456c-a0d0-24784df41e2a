/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectTemplateNoticeRelation;
import com.bassims.modules.atour.service.ProjectTemplateNoticeRelationService;
import com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-04-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_project_template_notice_relation管理")
@RequestMapping("/api/projectTemplateNoticeRelation")
public class ProjectTemplateNoticeRelationController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTemplateNoticeRelationController.class);

    private final ProjectTemplateNoticeRelationService projectTemplateNoticeRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectTemplateNoticeRelationQueryCriteria criteria) throws IOException {
        projectTemplateNoticeRelationService.download(projectTemplateNoticeRelationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ProjectTemplateNoticeRelationDto>>}
    */
    @GetMapping("/list")
//    @Log("查询t_project_template_notice_relation")
    @ApiOperation("查询t_project_template_notice_relation")
    public ResponseEntity<Object> query(ProjectTemplateNoticeRelationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectTemplateNoticeRelationService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ProjectTemplateNoticeRelationDto>}
    */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询t_project_template_notice_relation")
    @ApiOperation("查询t_project_template_notice_relation")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectTemplateNoticeRelationService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_project_template_notice_relation")
    @ApiOperation("新增t_project_template_notice_relation")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectTemplateNoticeRelation resources){
        return new ResponseEntity<>(projectTemplateNoticeRelationService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_project_template_notice_relation")
    @ApiOperation("修改t_project_template_notice_relation")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectTemplateNoticeRelation resources){
        projectTemplateNoticeRelationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_project_template_notice_relation")
    @ApiOperation("删除t_project_template_notice_relation")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectTemplateNoticeRelationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}