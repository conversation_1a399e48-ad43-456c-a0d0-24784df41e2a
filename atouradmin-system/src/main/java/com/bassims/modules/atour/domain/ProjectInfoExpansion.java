/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2023-10-30
 **/
@Data
@TableName(value = "t_project_info_expansion")
public class ProjectInfoExpansion implements Serializable {

    @TableId(value = "project_expansion_id")
    @ApiModelProperty(value = "项目拓展表的ID")
    private Long projectExpansionId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @TableField(value = "hotel_id")
    @ApiModelProperty(value = "酒店ID")
    private Integer hotelId;

    @TableField(value = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌")
    private Long brand;

    @TableField(value = "brand_name")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "product")
    @ApiModelProperty(value = "产品")
    private String product;

    @TableField(value = "product_name")
    @ApiModelProperty(value = "产品名称")
    private String productName;


    @TableField(value = "legal_agreement_content")
    @ApiModelProperty(value = "法务约定工程内容")
    private String legalAgreementContent;

    @TableField(value = "construct_special_remark")
    @ApiModelProperty(value = "营建备忘录特殊事项描述")
    private String constructSpecialRemark;

    @TableField(value = "rooms_number")
    @ApiModelProperty(value = "签约房间数")
    private Integer roomsNumber;

    @TableField(value = "effective_date")
    @ApiModelProperty(value = "生效日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date effectiveDate;

    @TableField(value = "red_line_description")
    @ApiModelProperty(value = "经营地址【红线描述】")
    private String redLineDescription;


    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号 ")
    private String projectVersion;


    @TableField(value = "construct_partition")
    @ApiModelProperty(value = "项目所属大区")
    private String constructPartition;

    @TableField(value = "construct_partition_desc")
    @ApiModelProperty(value = "项目所属大区 中文描述")
    private String constructPartitionDesc;

    @TableField(value = "project_nature")
    @ApiModelProperty(value = "项目性质: 1特许,2直营")
    private String projectNature;

    @TableField(value = "city")
    @ApiModelProperty(value = "城市名称")
    private String city;

    @TableField(value = "city_ad_code")
    @ApiModelProperty(value = "城市adcode")
    private String cityAdCode;

    @TableField(value = "is_flip_card")
    @ApiModelProperty(value = "是否翻牌 1-是 0-否")
    private String isFlipCard;

    @TableField(value = "operate_area")
    @ApiModelProperty(value = "运营城区")
    private String operateArea;

    @TableField(value = "effective_name")
    @ApiModelProperty(value = "生效定名")
    private String effectiveName;


    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "owner_project_manager")
    @ApiModelProperty(value = "业主项目经理(业主方-特许商对接人信息)")
    private String ownerProjectManager;

    @TableField(value = "red_line_image")
    @ApiModelProperty(value = "红线图")
    private String redLineImage;
    @TableField(value = "memorandum")
    @ApiModelProperty(value = "营建备忘录")
    private String memorandum;
    @TableField(value = "concession")
    @ApiModelProperty(value = "特许之附件A")
    private String concession;
    @TableField(value = "notice_preparation")
    @ApiModelProperty(value = "筹建告知书")
    private String noticePreparation;
    @TableField(value = "decision_report")
    @ApiModelProperty(value = "XXX决策报告")
    private String decisionReport;
    @TableField(value = "renovation_list")
    @ApiModelProperty(value = "翻牌项目改造清单")
    private String renovationList;

    @TableField(value = "meeting_state")
    @ApiModelProperty(value = "项目决策状态(会议结论)- 1:未决策 2:有条件通过 3:待定 4:通过 5:否决")
    private Integer meetingState;

    @TableField(value = " meeting_state_desc")
    @ApiModelProperty(value = "项目决策状态(会议结论)")
    private String meetingStateDesc;


    @TableField(value = " construction_area_leader")
    @ApiModelProperty(value = "营建区域负责人")
    private Long constructionAreaLeader;

    @TableField(value = "develope_manager_id")
    @ApiModelProperty(value = "开发经理（花名)")
    private String developeManagerId;


    @TableField(value = "security_project")
    @ApiModelProperty(value = "是否为保密项目 1=是 0=否 默认为0")
    private Integer securityProject;


    @TableField(value = "number_floor_levels")
    @ApiModelProperty(value = "楼层数")
    private Integer numberFloorLevels;

    /*项目启动时间*/
    @TableField(value = "project_start_time")
    @ApiModelProperty(value = "项目启动时间")
    private Date projectStartTime;


    /*项目类型*/
    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    public void copy(ProjectInfoExpansion source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}