package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bassims.modules.atour.domain.ProjectCompletionReceiptSummary;
import com.bassims.modules.atour.repository.ProjectCompletionReceiptSummaryMapper;
import com.bassims.modules.atour.service.ProjectCompletionReceiptSummaryService;
import org.springframework.stereotype.Service;

@Service
public class ProjectCompletionReceiptSummaryServiceImpl extends ServiceImpl<ProjectCompletionReceiptSummaryMapper, ProjectCompletionReceiptSummary> implements ProjectCompletionReceiptSummaryService {


    @Override
    public Page findPage(Page page) {
        return page(page);
    }

    @Override
    public void insert(ProjectCompletionReceiptSummary tProjectCompletionReceiptSummary) {
        save(tProjectCompletionReceiptSummary);
    }

    @Override
    public void update(ProjectCompletionReceiptSummary tProjectCompletionReceiptSummary) {
        updateById(tProjectCompletionReceiptSummary);
    }

    @Override
    public void delById(Integer id) {
        removeById(id);
    }

    @Override
    public ProjectCompletionReceiptSummary selectByProjectId(Long projectId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id",projectId);
        return getOne(queryWrapper);
    }

    @Override
    public synchronized void insertOrUpdate(ProjectCompletionReceiptSummary projectCompletionReceiptSummary) {
        ProjectCompletionReceiptSummary projectCompletionReceiptSummaryInSQL = selectByProjectId(projectCompletionReceiptSummary.getProjectId());
        if (projectCompletionReceiptSummaryInSQL == null){
            save(projectCompletionReceiptSummary);
        }else {
            projectCompletionReceiptSummary.setId(projectCompletionReceiptSummaryInSQL.getId());
            updateById(projectCompletionReceiptSummary);
        }
    }
}
