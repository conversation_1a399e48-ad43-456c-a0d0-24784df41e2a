package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.domain.LocalStorage;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.LicenceVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.MasterPersonInfoDto;
import com.bassims.modules.atour.service.dto.ProjectInfoDto;
import com.bassims.modules.atour.service.dto.SupplierInfoDto;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.repository.LocalStorageRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnterMasterServiceImpl implements EnterMasterService {

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;

    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private MasterPersonInfoRepository masterPersonInfoRepository;

    @Autowired
    private MasterLicenseInfoRepository masterLicenseInfoRepository;

    @Autowired
    private MasterLicenseInfoService masterLicenseInfoService;

    @Autowired
    private StoreMasterInfoRepository storeMasterInfoRepository;

    @Autowired
    private MasterContractInfoRepository masterContractInfoRepository;

    @Autowired
    private StoreMasterInfoService storeMasterInfoService;

    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private MasterEnergyConsumeRepository masterEnergyConsumeRepository;

    @Autowired
    private MasterEnergyConsumeService masterEnergyConsumeService;

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Autowired
    private DictDetailRepository dictDetailRepository;

    @Autowired
    private OrderNodeInfoRepository orderNodeInfoRepository;

    @Autowired
    private MasterStoreOrderDataRepository masterStoreOrderDataRepository;

    @Autowired
    private MasterStoreOrderDataService masterStoreOrderDataService;

    @Autowired
    private ProjectGroupRepository projectGroupRepository;

    @Autowired
    private ProjectNodeInfoService projectNodeInfoService ;

    @Autowired
    private LocalStorageRepository localStorageRepository;

    @Autowired
    private MasterDrawingInfoRepository masterDrawingInfoRepository;

    @Autowired
    private MasterDeviceInfoService masterDeviceInfoService;

    @Autowired
    private ProjectGroupService projectGroupService;

    @Autowired
    private OrderInfoRepository orderInfoRepository;

    @Autowired
    private OrderDetailRepository orderDetailRepository;


    @Override
    public Boolean enterMaster() {
        LambdaQueryWrapper<StoreMasterInfo> storeMasterInfoLambdaQueryWrapper = new LambdaQueryWrapper<>(StoreMasterInfo.class)
                .eq(StoreMasterInfo::getIsDelete,false);
        List<StoreMasterInfo> storeMasterList = storeMasterInfoService.list(storeMasterInfoLambdaQueryWrapper);
        for (StoreMasterInfo storeMasterInfo : storeMasterList) {
            Long storeId = storeMasterInfo.getStoreMasterId();
            LambdaQueryWrapper<ProjectInfo> projectInfoQueryWrapper = new LambdaQueryWrapper<>();
            projectInfoQueryWrapper.eq(ProjectInfo::getStoreId, storeId);
            List<ProjectInfo> projectInfoList = projectInfoService.list(projectInfoQueryWrapper);
            for (ProjectInfo projectInfo : projectInfoList) {
                Long projectId = projectInfo.getProjectId();
                MasterContractInfo masterContractInfo = new MasterContractInfo();
                masterContractInfo.setStoreId(storeId);
                //获取立项时间
                Date establishTime = projectInfo.getEstablishTime();
                if (establishTime != null) {
                    //获取干系人
                    LambdaQueryWrapper<ProjectStakeholders> prostaQueryWrapper = new LambdaQueryWrapper<>();
                    prostaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectId);
                    List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(prostaQueryWrapper);

                    LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
                    projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId);
                    List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);

                    for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
                        if (!Objects.isNull(projectNodeInfo)) {
                            //人员信息
                            if (("con-0010110").equals(projectNodeInfo.getNodeCode())) {
                                if (CollectionUtils.isNotEmpty(projectStakeholders)) {
                                    projectStakeholders.stream().forEach(projectStakeholders1 -> {
                                        UserDto userDto = userService.findById(projectStakeholders1.getUserId());
                                        MasterPersonInfoDto masterPersonInfoDto = new MasterPersonInfoDto();
                                        MasterPersonInfo masterPersonInfo = new MasterPersonInfo();
                                        masterPersonInfoDto.setRoleName(projectStakeholders1.getRoleName());
                                        masterPersonInfoDto.setUserName(userDto.getNickName());
                                        masterPersonInfoDto.setPhone(userDto.getPhone());
                                        masterPersonInfoDto.setShakeholderStatus(projectStakeholders1.getShakeholderStatus());
                                        masterPersonInfoDto.setJoinTime(projectStakeholders1.getJoinTime());
                                        masterPersonInfoDto.setLeaveTime(projectStakeholders1.getLeaveTime());
                                        masterPersonInfoDto.setReason(projectStakeholders1.getReason());
                                        BeanUtils.copyProperties(masterPersonInfoDto, masterPersonInfo);
                                        masterPersonInfo.setStoreId(storeId);
                                        masterPersonInfoRepository.insert(masterPersonInfo);
                                    });
                                }
                            }
                            //合同信息
                            else if ("con-0040308".equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050108").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060108").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setContractName(projectNodeInfo.getRemark());
                            } else if (("con-0040309").equals(projectNodeInfo.getNodeCode())) {
                                String remark = projectNodeInfo.getRemark();
                                if (!Objects.isNull(remark)) {
                                    SupplierInfoDto serviceById = supplierInfoService.findById(Long.valueOf(remark));
                                    masterContractInfo.setCompanyName(serviceById.getSupNameCn());
                                }
                            } else if (("con-0040310").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050110").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060110").equals(projectNodeInfo.getNodeCode())) {
                                if (projectNodeInfo.getRemark() != null) {
                                    masterContractInfo.setContractNo(projectNodeInfo.getRemark());

                                }
                            } else if (("con-0040311").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050111").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060111").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setContractOwner(projectNodeInfo.getRemark());
                            } else if (("con-0040312").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050112").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060112").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setContractOwnerName(projectNodeInfo.getRemark());
                            } else if (("con-0010104").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0010204").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setProjectName(projectNodeInfo.getRemark());
                            }//合同类型
                            else if (("con-0040314").equals(projectNodeInfo.getNodeCode())) {
                                DictDetail label = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(projectNodeInfo.getRemark())).orElseGet(DictDetail::new);
                                masterContractInfo.setContractType(label.getLabel());
                            }//合同开始日期
                            else if (("con-0040315").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050115").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060115").equals(projectNodeInfo.getNodeCode())) {
                                if (projectNodeInfo.getRemark() != null) {
                                    DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                    try {
                                        masterContractInfo.setContractStartTime(new Timestamp(format1.parse(projectNodeInfo.getRemark()).getTime()));
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }//合同结束日期
                            else if (("con-0040316").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050116").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060116").equals(projectNodeInfo.getNodeCode())) {
                                if (projectNodeInfo.getRemark() != null) {
                                    DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                    try {
                                        masterContractInfo.setContractEndTime(new Timestamp(format1.parse(projectNodeInfo.getRemark()).getTime()));
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                    }
                                }
                            } else if (("con-0040324").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050124").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060124").equals(projectNodeInfo.getNodeCode())) {
                                if (projectNodeInfo.getRemark() != null) {
                                    masterContractInfo.setTotalPay(new BigDecimal(projectNodeInfo.getRemark()));

                                }
                            } else if (("con-0040325").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050125").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060125").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setInvoiceType(projectNodeInfo.getRemark());
                            } else if (("con-0040326").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050126").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060126").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setInvoiceTaxRate(projectNodeInfo.getRemark());
                            } else if (("con-0040328").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050128").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060128").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setContractDesc(projectNodeInfo.getRemark());
                            } else if (("con-0040329").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0050129").equals(projectNodeInfo.getNodeCode()) ||
                                    ("con-0060129").equals(projectNodeInfo.getNodeCode())) {
                                masterContractInfo.setContractReviewStatus(projectNodeInfo.getRemark());
                            }//证照信息 图纸审核意见书 前置证照
                            else if (projectNodeInfo.getNodeCode().equals("con-*********")) {
                                String remark = projectNodeInfo.getRemark();
                                String nodeName = projectNodeInfo.getNodeName();
                                List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                                MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                                if (CollectionUtils.isNotEmpty(licenceVos)) {
                                    masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.PRE_LICENSE.getKey());
                                    masterLicenseInfo.setLicenseName(nodeName);
                                    if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setActualStart(new Timestamp(format1.parse((licenceVos.get(2).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setActualEnd(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    if (licenceVos.get(4).getValue() != null && licenceVos.get(4).getValue().length() > 0) {
                                        masterLicenseInfo.setUploader(licenceVos.get(4).getValue());
                                    }
                                    if (licenceVos.get(5).getValue() != null && licenceVos.get(5).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(5).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    masterLicenseInfoRepository.insert(masterLicenseInfo);
                                }
                            }//证件信息 甲方土建验收合格证 后置证照
                            else if (projectNodeInfo.getNodeCode().equals("con-002020501")) {
                                String remark = projectNodeInfo.getRemark();
                                String nodeName = projectNodeInfo.getNodeName();
                                List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                                MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                                masterLicenseInfo.setStoreId(storeId);
                                if (CollectionUtils.isNotEmpty(licenceVos)) {
                                    masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getKey());
                                    masterLicenseInfo.setLicenseName(nodeName);
                                    if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                        masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                                    }
                                    if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    masterLicenseInfoRepository.insert(masterLicenseInfo);
                                }
                            }//证件信息 住建竣工验收合格证 后置证照
                            else if (projectNodeInfo.getNodeCode().equals("con-002020502")) {
                                String remark = projectNodeInfo.getRemark();
                                String nodeName = projectNodeInfo.getNodeName();
                                List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                                MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                                masterLicenseInfo.setStoreId(storeId);
                                if (CollectionUtils.isNotEmpty(licenceVos)) {
                                    masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getKey());
                                    masterLicenseInfo.setLicenseName(nodeName);
                                    if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                        masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                                    }
                                    if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    masterLicenseInfoRepository.insert(masterLicenseInfo);
                                }

                            }//证件信息 消防开业检查合格证 后置证照
                            else if (projectNodeInfo.getNodeCode().equals("con-002020503")) {
                                String remark = projectNodeInfo.getRemark();
                                String nodeName = projectNodeInfo.getNodeName();
                                List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                                MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                                masterLicenseInfo.setStoreId(storeId);
                                if (CollectionUtils.isNotEmpty(licenceVos)) {
                                    masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getKey());
                                    masterLicenseInfo.setLicenseName(nodeName);
                                    if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                        masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                                    }
                                    if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    masterLicenseInfoRepository.insert(masterLicenseInfo);
                                }

                            }//证件信息 甲方承诺函 后置证照
                            else if (projectNodeInfo.getNodeCode().equals("con-002020504")) {
                                String remark = projectNodeInfo.getRemark();
                                String nodeName = projectNodeInfo.getNodeName();
                                List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                                MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                                masterLicenseInfo.setStoreId(storeId);
                                if (CollectionUtils.isNotEmpty(licenceVos)) {
                                    masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getKey());
                                    masterLicenseInfo.setLicenseName(nodeName);
                                    if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                        masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                                    }
                                    if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                        DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                        try {
                                            masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    masterLicenseInfoRepository.insert(masterLicenseInfo);
                                }
                            }


                        }

                    }
//                StoreMasterInfo storeMasterInfo1 = storeMasterInfoRepository.selectById(storeMasterInfo.getStoreMasterId());
//                MasterContractInfo masterContractInfo1 = masterContractInfoRepository.selectById(masterContractInfo.getContractId());
                    MasterContractInfo masterContractInfo1 = masterContractInfoRepository.selectById(masterContractInfo.getContractId());
                    if (Objects.isNull(masterContractInfo1)) {
                        masterContractInfoRepository.insert(masterContractInfo);
                    } else {
                        masterContractInfoRepository.updateById(masterContractInfo);

                    }

                    if (!Objects.isNull(masterContractInfo.getStoreId())) {
                        masterContractInfoRepository.updateMasterContractInfo(masterContractInfo);
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsume() {
        //查询有关门店
        LambdaQueryWrapper<StoreMasterInfo> storeMasterInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeMasterInfoLambdaQueryWrapper.eq(StoreMasterInfo::getIsDelete, false);
        List<StoreMasterInfo> storeMasterInfoList = storeMasterInfoService.list(storeMasterInfoLambdaQueryWrapper);
        for (StoreMasterInfo storeMasterInfo : storeMasterInfoList) {
            Long storeId = storeMasterInfo.getStoreMasterId();
            LambdaUpdateWrapper<MasterEnergyConsume> masterEnergyConsumeLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterEnergyConsume.class)
                    .eq(MasterEnergyConsume::getStoreId, storeId)
                    .eq(MasterEnergyConsume::getProType,"灯具")
                    .set(MasterEnergyConsume::getIsDelete, true);
            masterEnergyConsumeService.update(masterEnergyConsumeLambdaUpdateWrapper);
            //查询订单
            LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderInfoLambdaQueryWrapper.eq(OrderInfo::getStoreId,storeId)
                                        .eq(OrderInfo::getIsDelete,false);
            List<OrderInfo> orderInfoList = orderInfoService.list(orderInfoLambdaQueryWrapper);
            for (OrderInfo orderInfo : orderInfoList){
                Long orderId = orderInfo.getOrderId();
                //查询订单详情
                LambdaQueryWrapper<OrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orderDetailLambdaQueryWrapper.eq(OrderDetail::getOrderId, orderId);
                List<OrderDetail> orderDetailList = orderDetailService.list(orderDetailLambdaQueryWrapper);
                if (!ObjectUtils.isNull(orderDetailList) && orderDetailList.size() > 0) {
                    for (OrderDetail orderDetail : orderDetailList) {
                        MasterEnergyConsume masterEnergyConsume = new MasterEnergyConsume();
                        String financeCode = orderDetail.getFinanceCode();
                        //灯具
                        if (("G010700400018").equals(financeCode)) {
                            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                            masterEnergyConsume.setEnergyConsumeId(snowflake.nextId());
                            masterEnergyConsume.setBrand(orderDetail.getBrand());
                            masterEnergyConsume.setModel(orderDetail.getModel());
                            String storeNo = storeMasterInfo.getStoreNo();
                            masterEnergyConsume.setStoreNo(storeNo);
                            masterEnergyConsume.setProName(orderDetail.getProductName());
                            String secondClass = orderDetail.getSecondClass();
                            masterEnergyConsume.setProType(secondClass);
                            masterEnergyConsume.setSupplierName(orderDetail.getSupNameCn());
                            //数量
                            BigDecimal subtotal = orderDetail.getSubtotal();
                            if (ObjectUtils.isNull(subtotal)) {
                                BigDecimal subtotalValue = (subtotal == null) ? BigDecimal.ZERO : subtotal;
                                masterEnergyConsume.setQuantity(subtotalValue);
                                //规格
                                String spec = orderDetail.getSpec();
                                BigDecimal specBigDecimal = new BigDecimal(spec);
                                if (ObjectUtils.isNull(specBigDecimal)) {
                                    masterEnergyConsume.setProSpecifications("0");
                                    masterEnergyConsume.setTotalPower("0");
                                } else {
                                    masterEnergyConsume.setProSpecifications(spec);
                                    String totalPower = specBigDecimal.multiply(subtotalValue).toString();
                                    masterEnergyConsume.setTotalPower(totalPower);
                                }
                            } else {
                                masterEnergyConsume.setQuantity(subtotal);
                                String spec = orderDetail.getSpec();
                                BigDecimal specBigDecimal = new BigDecimal(spec);
                                if (ObjectUtils.isNull(specBigDecimal)) {
                                    masterEnergyConsume.setProSpecifications("0");
                                    masterEnergyConsume.setTotalPower("0");
                                } else {
                                    masterEnergyConsume.setProSpecifications(spec);
                                    String totalPower = specBigDecimal.multiply(subtotal).toString();
                                    masterEnergyConsume.setTotalPower(totalPower);
                                }
                            }
                            /*BigDecimal defaultValue = BigDecimal.ZERO;
                            BigDecimal value = (subtotal == null) ? defaultValue : subtotal;*/
                            //总功率
                            masterEnergyConsume.setCreateTime(orderDetail.getCreateTime());
                            masterEnergyConsume.setCreateBy(orderDetail.getCreateBy());
                            masterEnergyConsume.setUpdateTime(orderDetail.getUpdateTime());
                            masterEnergyConsume.setUpdateBy(orderDetail.getUpdateBy());
                            masterEnergyConsume.setIsDelete(false);
                            masterEnergyConsume.setStoreId(storeId);
                            masterEnergyConsumeRepository.insert(masterEnergyConsume);
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsume(ProjectGroup projectGroup) {
        Long orderId = projectGroup.getOrderId();
        OrderInfo orderInfo = Optional.ofNullable(orderInfoRepository.selectById(orderId)).orElseGet(OrderInfo::new);
        Long storeId = orderInfo.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        LambdaQueryWrapper orderDetailQuery = Wrappers.lambdaQuery(OrderDetail.class)
                .eq(OrderDetail::getOrderId,orderId);
        List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQuery);
        LambdaUpdateWrapper<MasterEnergyConsume> masterEnergyConsumeLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterEnergyConsume.class)
                .eq(MasterEnergyConsume::getStoreId, storeId)
                .eq(MasterEnergyConsume::getProType,"灯具")
                .set(MasterEnergyConsume::getIsDelete, true);
        masterEnergyConsumeService.update(masterEnergyConsumeLambdaUpdateWrapper);
        if (!ObjectUtils.isNull(orderDetailList) && orderDetailList.size() > 0) {
            for (OrderDetail orderDetail : orderDetailList) {
                MasterEnergyConsume masterEnergyConsume = new MasterEnergyConsume();
                String financeCode = orderDetail.getFinanceCode();
                //灯具
                if (("G010700400018").equals(financeCode)) {
                    Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                    masterEnergyConsume.setEnergyConsumeId(snowflake.nextId());
                    masterEnergyConsume.setBrand(orderDetail.getBrand());
                    masterEnergyConsume.setModel(orderDetail.getModel());
                    String storeNo = storeMasterInfo.getStoreNo();
                    masterEnergyConsume.setStoreNo(storeNo);
                    masterEnergyConsume.setProName(orderDetail.getProductName());
                    String secondClass = orderDetail.getSecondClass();
                    masterEnergyConsume.setProType(secondClass);
                    masterEnergyConsume.setSupplierName(orderDetail.getSupNameCn());
                    //数量
                    BigDecimal subtotal = orderDetail.getSubtotal();
                    if (ObjectUtils.isNull(subtotal)) {
                        BigDecimal subtotalValue = (subtotal == null) ? BigDecimal.ZERO : subtotal;
                        masterEnergyConsume.setQuantity(subtotalValue);
                        //规格
                        String spec = orderDetail.getSpec();
                        BigDecimal specBigDecimal = new BigDecimal(spec);
                        if (ObjectUtils.isNull(specBigDecimal)) {
                            masterEnergyConsume.setProSpecifications("0");
                            masterEnergyConsume.setTotalPower("0");
                        } else {
                            masterEnergyConsume.setProSpecifications(spec);
                            String totalPower = specBigDecimal.multiply(subtotalValue).toString();
                            masterEnergyConsume.setTotalPower(totalPower);
                        }
                    } else {
                        masterEnergyConsume.setQuantity(subtotal);
                        String spec = orderDetail.getSpec();
                        BigDecimal specBigDecimal = new BigDecimal(spec);
                        if (ObjectUtils.isNull(specBigDecimal)) {
                            masterEnergyConsume.setProSpecifications("0");
                            masterEnergyConsume.setTotalPower("0");
                        } else {
                            masterEnergyConsume.setProSpecifications(spec);
                            String totalPower = specBigDecimal.multiply(subtotal).toString();
                            masterEnergyConsume.setTotalPower(totalPower);
                        }
                    }
                            /*BigDecimal defaultValue = BigDecimal.ZERO;
                            BigDecimal value = (subtotal == null) ? defaultValue : subtotal;*/
                    //总功率
                    masterEnergyConsume.setCreateTime(orderDetail.getCreateTime());
                    masterEnergyConsume.setCreateBy(orderDetail.getCreateBy());
                    masterEnergyConsume.setUpdateTime(orderDetail.getUpdateTime());
                    masterEnergyConsume.setUpdateBy(orderDetail.getUpdateBy());
                    masterEnergyConsume.setIsDelete(false);
                    masterEnergyConsume.setStoreId(storeId);
                    masterEnergyConsumeRepository.insert(masterEnergyConsume);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsumeOther(ProjectGroup projectGroup){
        Long projectId = projectGroup.getProjectId();
        Long templateId = projectGroup.getTemplateId();
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        Long storeId = projectInfoDto.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        if ("new".equals(projectInfoDto.getProjectType())) {
            LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
            projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getParentId, templateId);
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
            if (ObjectUtil.isNotEmpty(projectNodeInfos) && projectNodeInfos.size() > 0) {
                Map<String, String> nodeMap = new HashMap<>();
                nodeMap = projectNodeInfos.stream().filter(n -> ObjectUtil.isNotEmpty(n.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark, (a, b) -> b));
                String brand = "";
                String model = "";
                String proName = "";
                String proType = "";
                String supplierName = "";
                String proSpecifications = "";
                String totalPower = "";
                BigDecimal power = BigDecimal.ZERO;
                BigDecimal quantity = BigDecimal.ZERO;
                if ("con-00111".equals(projectGroup.getNodeCode())) {
                    if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                        proType = "其他";
                        String homePower = nodeMap.get("con-0011136");
                        if (ObjectUtil.isNotEmpty(homePower)) {
                            BigDecimal homePowerDecimal = new BigDecimal(homePower);
                            power.add(homePowerDecimal);
                        }
                        String officePower = nodeMap.get("con-0011137");
                        if (ObjectUtil.isNotEmpty(officePower)) {
                            BigDecimal officePowerDecimal = new BigDecimal(officePower);
                            power.add(officePowerDecimal);
                        }
                        if (ObjectUtil.isNotEmpty(power) && power.equals(0)) {
                            proSpecifications = power.toString();
                            totalPower = power.toString();
                        } else {
                            proSpecifications = null;
                            totalPower = null;
                        }
                    }
                }
                LambdaUpdateWrapper<MasterEnergyConsume> masterEnergyConsumeLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterEnergyConsume.class)
                        .eq(MasterEnergyConsume::getStoreId, storeId)
                        .eq(MasterEnergyConsume::getProType,"其他")
                        .set(MasterEnergyConsume::getIsDelete, true);
                masterEnergyConsumeService.update(masterEnergyConsumeLambdaUpdateWrapper);
                MasterEnergyConsume masterEnergyConsume = new MasterEnergyConsume();
                if (ObjectUtil.isNotEmpty(proSpecifications)&&ObjectUtil.isNotEmpty(totalPower)) {
                    Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                    masterEnergyConsume.setEnergyConsumeId(snowflake.nextId());
                    masterEnergyConsume.setBrand(brand);
                    masterEnergyConsume.setModel(model);
                    masterEnergyConsume.setStoreId(storeId);
                    masterEnergyConsume.setStoreNo(storeMasterInfo.getStoreNo());
                    masterEnergyConsume.setProName(proName);
                    masterEnergyConsume.setProType(proType);
                    masterEnergyConsume.setSupplierName(supplierName);
                    masterEnergyConsume.setProSpecifications(proSpecifications);
                    masterEnergyConsume.setTotalPower(totalPower);
                    masterEnergyConsume.setIsDelete(false);
                    masterEnergyConsumeService.save(masterEnergyConsume);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsumeAirCondition() {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getIsDelete,false);
        List<ProjectGroup> projectGroupList = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(projectGroupList) && projectGroupList.size() > 0) {
            for (ProjectGroup projectGroup : projectGroupList){
                if ("con-00116".equals(projectGroup.getNodeCode())) {
                    enterMasterEnergyConsumeAirCondition(projectGroup);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsumeAirCondition(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        Long templateId = projectGroup.getTemplateId();
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        Long storeId = projectInfoDto.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        if ("new".equals(projectInfoDto.getProjectType())) {
            LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
            projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getParentId, templateId);
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
            if (ObjectUtil.isNotEmpty(projectNodeInfos) && projectNodeInfos.size() > 0) {
                Map<String, String> nodeMap = new HashMap<>();
                nodeMap = projectNodeInfos.stream().filter(n -> ObjectUtil.isNotEmpty(n.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark, (a, b) -> b));
                String brand = "";
                String model = "";
                String proName = "";
                String proType = "";
                String supplierName = "";
                String proSpecifications = "";
                String totalPower = "";
                BigDecimal quantity = BigDecimal.ZERO;
                if ("con-00116".equals(projectGroup.getNodeCode())) {
                    if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                        brand = nodeMap.get("con-0011603");
                        model = nodeMap.get("con-0011602");
                        proType = "空调";
                        supplierName = nodeMap.get("con-0011605");
                        proSpecifications = nodeMap.get("con-0011610");
                        totalPower = nodeMap.get("con-0011610");
                    }
                }
                LambdaUpdateWrapper<MasterEnergyConsume> masterEnergyConsumeLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterEnergyConsume.class)
                        .eq(MasterEnergyConsume::getStoreId, storeId)
                        .eq(MasterEnergyConsume::getProType,"空调")
                        .set(MasterEnergyConsume::getIsDelete, true);
                masterEnergyConsumeService.update(masterEnergyConsumeLambdaUpdateWrapper);
                MasterEnergyConsume masterEnergyConsume = new MasterEnergyConsume();
                if (ObjectUtil.isNotEmpty(proSpecifications)&&ObjectUtil.isNotEmpty(totalPower)) {
                    Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                    masterEnergyConsume.setEnergyConsumeId(snowflake.nextId());
                    masterEnergyConsume.setBrand(brand);
                    masterEnergyConsume.setModel(model);
                    masterEnergyConsume.setStoreId(storeId);
                    masterEnergyConsume.setStoreNo(storeMasterInfo.getStoreNo());
                    masterEnergyConsume.setProName(proName);
                    masterEnergyConsume.setProType(proType);
                    masterEnergyConsume.setSupplierName(supplierName);
                    masterEnergyConsume.setProSpecifications(proSpecifications);
                    masterEnergyConsume.setTotalPower(totalPower);
                    masterEnergyConsume.setIsDelete(false);
                    masterEnergyConsumeService.save(masterEnergyConsume);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterEnergyConsumeOther() {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getIsDelete,false);
        List<ProjectGroup> projectGroupList = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(projectGroupList) && projectGroupList.size() > 0) {
            for (ProjectGroup projectGroup : projectGroupList){
                if ("con-00111".equals(projectGroup.getNodeCode())) {
                    enterMasterEnergyConsumeOther(projectGroup);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterDeviceInfo(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        Long templateId = projectGroup.getTemplateId();
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        Long storeId = projectInfoDto.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
        projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getParentId, templateId);
        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
        if (ObjectUtil.isNotEmpty(projectNodeInfos) && projectNodeInfos.size() > 0) {
            Map<String, String> nodeMap = new HashMap<>();
            nodeMap = projectNodeInfos.stream().filter(n -> ObjectUtil.isNotEmpty(n.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark, (a, b) -> b));
            String deviceType = "";
            String deviceName = "";
            String secondConstruct = "";
            String deviceBrand = "";
            String deviceMaintenance = "";
            String deviceElectricity = "";
            String completeAcceptTime = "";
            String deviceWarranty = "";
            if ("con-00116".equals(projectGroup.getNodeCode())) {
                if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                    deviceType = "空调";
                    secondConstruct = nodeMap.get("con-0011604");
                    deviceBrand = nodeMap.get("con-0011603");
                    deviceMaintenance = nodeMap.get("con-0011608");
                    deviceElectricity = nodeMap.get("con-0011609");
                    if (ObjectUtil.isNotEmpty(projectGroup.getActualEndDate())) {
                        completeAcceptTime = projectGroup.getActualEndDate().toString();
                    }
                    deviceWarranty = nodeMap.get("con-0011607");
                    LambdaUpdateWrapper<MasterDeviceInfo> masterDeviceInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterDeviceInfo.class)
                            .eq(MasterDeviceInfo::getStoreId, storeId)
                            .eq(MasterDeviceInfo::getDeviceType, "空调")
                            .set(MasterDeviceInfo::getIsDelete, true);
                    masterDeviceInfoService.update(masterDeviceInfoLambdaUpdateWrapper);
                }
            } else if ("con-00117".equals(projectGroup.getNodeCode())) {
                if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                    deviceType = "消防";
                    secondConstruct = nodeMap.get("con-0011702");
                    deviceMaintenance = nodeMap.get("con-0011705");
                    if (ObjectUtil.isNotEmpty(projectGroup.getActualEndDate())) {
                        completeAcceptTime = projectGroup.getActualEndDate().toString();
                    }
                    deviceWarranty = nodeMap.get("con-0011706");
                    LambdaUpdateWrapper<MasterDeviceInfo> masterDeviceInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterDeviceInfo.class)
                            .eq(MasterDeviceInfo::getStoreId, storeId)
                            .eq(MasterDeviceInfo::getDeviceType, "消防")
                            .set(MasterDeviceInfo::getIsDelete, true);
                    masterDeviceInfoService.update(masterDeviceInfoLambdaUpdateWrapper);
                }
            } else if ("con-00111".equals(projectGroup.getNodeCode())) {
                if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                    deviceType = "电梯";
                    secondConstruct = nodeMap.get("con-0011140");
                    deviceBrand = nodeMap.get("con-0011139");
                    deviceMaintenance = nodeMap.get("con-0011144");
                    deviceElectricity = nodeMap.get("con-0011145");
                    if (ObjectUtil.isNotEmpty(projectGroup.getActualEndDate())) {
                        completeAcceptTime = projectGroup.getActualEndDate().toString();
                    }
                    deviceWarranty = nodeMap.get("con-0011143");
                    LambdaUpdateWrapper<MasterDeviceInfo> masterDeviceInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(MasterDeviceInfo.class)
                            .eq(MasterDeviceInfo::getStoreId, storeId)
                            .eq(MasterDeviceInfo::getDeviceType, "电梯")
                            .set(MasterDeviceInfo::getIsDelete, true);
                    masterDeviceInfoService.update(masterDeviceInfoLambdaUpdateWrapper);
                }
            }
            MasterDeviceInfo masterDeviceInfo = new MasterDeviceInfo();
            if (ObjectUtil.isNotEmpty(secondConstruct)||ObjectUtil.isNotEmpty(deviceBrand)||ObjectUtil.isNotEmpty(deviceMaintenance)||ObjectUtil.isNotEmpty(deviceWarranty)) {
                Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                masterDeviceInfo.setDeviceId(snowflake.nextId());
                masterDeviceInfo.setDeviceType(deviceType);
                masterDeviceInfo.setStoreId(storeId);
                masterDeviceInfo.setStoreNo(storeMasterInfo.getStoreNo());
                masterDeviceInfo.setSecondConstruct(secondConstruct);
                masterDeviceInfo.setDeviceBrand(deviceBrand);
                masterDeviceInfo.setDeviceMaintenance(deviceMaintenance);
                masterDeviceInfo.setDeviceElectricity(deviceElectricity);
                masterDeviceInfo.setCompleteAcceptTime(completeAcceptTime);
                masterDeviceInfo.setDeviceWarranty(deviceWarranty);
                masterDeviceInfo.setIsDelete(false);
                masterDeviceInfoService.save(masterDeviceInfo);
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterDeviceInfo() {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getIsDelete,false);
        List<ProjectGroup> projectGroupList = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(projectGroupList) && projectGroupList.size() > 0) {
            for (ProjectGroup projectGroup : projectGroupList){
                if ("con-00116".equals(projectGroup.getNodeCode())||"con-00117".equals(projectGroup.getNodeCode())||"con-00111".equals(projectGroup.getNodeCode())) {
                    enterMasterDeviceInfo(projectGroup);
                }
            }
        }
        return true;
    }


    @Override
    public Boolean enterMasterOrderData() {
        LambdaQueryWrapper<StoreMasterInfo> storeMasterInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeMasterInfoLambdaQueryWrapper.eq(StoreMasterInfo::getIsDelete, false);
        List<StoreMasterInfo> storeMasterInfoList = storeMasterInfoService.list(storeMasterInfoLambdaQueryWrapper);
        for (StoreMasterInfo storeMasterInfo : storeMasterInfoList){
            Long storeMasterId = storeMasterInfo.getStoreMasterId();
            LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderInfoLambdaQueryWrapper.eq(OrderInfo::getStoreId, storeMasterId);
            List<OrderInfo> orderInfoList = orderInfoService.list(orderInfoLambdaQueryWrapper);
            if (!ObjectUtils.isNull(orderInfoList) && orderInfoList.size() > 0){
                for (OrderInfo orderInfo : orderInfoList) {
                    String orderStatus = orderInfo.getOrderStatus();
                    if (!orderStatus.equals(KidsSystemEnum.OrderStatus.ORDER_CANCEL.getValue())) {
                        Long orderId = orderInfo.getOrderId();
                        LambdaQueryWrapper<MasterStoreOrderData> storeOrderDataLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        storeOrderDataLambdaQueryWrapper.eq(MasterStoreOrderData::getOrderId, orderId)
                                .eq(MasterStoreOrderData::getStoreId, storeMasterId)
                                .eq(MasterStoreOrderData::getIsDelete, false);
                        MasterStoreOrderData masterStoreOrderData = masterStoreOrderDataRepository.selectOne(storeOrderDataLambdaQueryWrapper);
                        LambdaQueryWrapper<ProjectGroup> projectGroupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                                .eq(ProjectGroup::getOrderId, orderId)
                                .eq(ProjectGroup::getNodeCode, "con-00121");
                        ProjectGroup projectGroup = projectGroupRepository.selectOne(projectGroupQuery);
                        if (Objects.isNull(masterStoreOrderData)) {
                            if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                                MasterStoreOrderData storeOrderData = new MasterStoreOrderData();
                                Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                                storeOrderData.setStoreOrderId(snowflake.nextId());
                                storeOrderData.setOrderNo(ObjectUtil.isNotEmpty(orderInfo.getOrderNo()) ? orderInfo.getOrderNo() : null);
                                storeOrderData.setOrderId(orderId);
                                storeOrderData.setStoreId(orderInfo.getStoreId());
                                storeOrderData.setStoreNo(storeMasterInfo.getStoreNo());
                                storeOrderData.setOrderType(orderInfo.getOrderType());
                                storeOrderData.setExpenseType(orderInfo.getCostType());
                                storeOrderData.setProject(orderInfo.getProjectName());
                                storeOrderData.setOrderAmount(orderInfo.getOrderAmount());
                                storeOrderData.setOrderStatus(orderInfo.getOrderStatus());
                                storeOrderData.setOrderTime(orderInfo.getAddTime());
                                storeOrderData.setArrivalTime(orderInfo.getArrivalTime());
                                storeOrderData.setSupplier(orderInfo.getSupNameCn());
                                storeOrderData.setConsignee(orderInfo.getReceiver());
                                storeOrderData.setIsDelete(orderInfo.getIsDelete());
                                LambdaQueryWrapper<OrderNodeInfo> orderNodeInfoDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                                orderNodeInfoDtoLambdaQueryWrapper.eq(OrderNodeInfo::getOrderId, orderId);
                                List<OrderNodeInfo> orderNodeInfos = orderNodeInfoRepository.selectList(orderNodeInfoDtoLambdaQueryWrapper);
                                for (OrderNodeInfo orderNodeInfo : orderNodeInfos) {
                                    if (!Objects.isNull(orderNodeInfo)) {
                                        if ("con-0090951".equals(orderNodeInfo.getNodeCode())) {
                                            if (orderNodeInfo.getRemark() != null) {
                                                storeOrderData.setOrderSettlementAmount(new BigDecimal(orderNodeInfo.getRemark()));
                                            }
                                        }
                                    }
                                }
                                masterStoreOrderDataRepository.insert(storeOrderData);
                            }
                        } else {
                            if (projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                                masterStoreOrderData.setOrderNo(orderInfo.getOrderNo());
                                masterStoreOrderData.setOrderId(orderId);
                                masterStoreOrderData.setStoreId(orderInfo.getStoreId());
                                masterStoreOrderData.setStoreNo(storeMasterInfo.getStoreNo());
                                masterStoreOrderData.setOrderType(orderInfo.getOrderType());
                                masterStoreOrderData.setExpenseType(orderInfo.getCostType());
                                masterStoreOrderData.setProject(orderInfo.getProjectName());
                                masterStoreOrderData.setOrderAmount(orderInfo.getOrderAmount());
                                masterStoreOrderData.setOrderStatus(orderInfo.getOrderStatus());
                                masterStoreOrderData.setOrderTime(orderInfo.getAddTime());
                                masterStoreOrderData.setArrivalTime(orderInfo.getArrivalTime());
                                masterStoreOrderData.setSupplier(orderInfo.getSupNameCn());
                                masterStoreOrderData.setConsignee(orderInfo.getReceiver());
                                masterStoreOrderData.setIsDelete(orderInfo.getIsDelete());
                                LambdaQueryWrapper<OrderNodeInfo> orderNodeInfoDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                                orderNodeInfoDtoLambdaQueryWrapper.eq(OrderNodeInfo::getOrderId, orderId);
                                List<OrderNodeInfo> orderNodeInfos = orderNodeInfoRepository.selectList(orderNodeInfoDtoLambdaQueryWrapper);
                                for (OrderNodeInfo orderNodeInfo : orderNodeInfos) {
                                    if (!Objects.isNull(orderNodeInfo)) {
                                        if ("con-0090951".equals(orderNodeInfo.getNodeCode())) {
                                            if (orderNodeInfo.getRemark() != null) {
                                                masterStoreOrderData.setOrderSettlementAmount(new BigDecimal(orderNodeInfo.getRemark()));
                                            }
                                        }
                                    }
                                }
                                masterStoreOrderDataRepository.updateById(masterStoreOrderData);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterOrderData(ProjectGroup projectGroup) {
        Long orderId = projectGroup.getOrderId();
        OrderInfo orderInfo = Optional.ofNullable(orderInfoRepository.selectById(orderId)).orElseGet(OrderInfo::new);
        String orderStatus = orderInfo.getOrderStatus();
        if (!orderStatus.equals(KidsSystemEnum.OrderStatus.ORDER_CANCEL.getValue())) {
            Long storeId = orderInfo.getStoreId();
            StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
            String storeNo = storeMasterInfo.getStoreNo();
            LambdaQueryWrapper<OrderNodeInfo> orderNodeInfoDtoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderNodeInfo.class)
                    .eq(OrderNodeInfo::getOrderId, orderId);
            List<OrderNodeInfo> orderNodeList = orderNodeInfoRepository.selectList(orderNodeInfoDtoLambdaQueryWrapper);
            Map<String, String> orderNodeMap = new HashMap<>();
            orderNodeMap = orderNodeList.stream().filter(n -> ObjectUtil.isNotEmpty(n.getRemark())).collect(Collectors.toMap(OrderNodeInfo::getNodeCode, OrderNodeInfo::getRemark, (a, b) -> b));
            String orderSettlementAmount = orderNodeMap.get("con-0090951");
            LambdaQueryWrapper<MasterStoreOrderData> storeOrderDataLambdaQueryWrapper = Wrappers.lambdaQuery(MasterStoreOrderData.class)
                    .eq(MasterStoreOrderData::getOrderId, orderId)
                    .eq(MasterStoreOrderData::getStoreId, storeId)
                    .eq(MasterStoreOrderData::getSupplier, orderInfo.getSupNameCn())
                    .eq(MasterStoreOrderData::getIsDelete, false);
            List<MasterStoreOrderData> storeOrderDataList = masterStoreOrderDataRepository.selectList(storeOrderDataLambdaQueryWrapper);
            String nodeCode = projectGroup.getNodeCode();
            if ("con-00121".equals(nodeCode) && projectGroup.getNodeStatus().equals(KidsSystemEnum.TaskStatusEnum.FINISH.getValue())) {
                MasterStoreOrderData masterStoreOrderData = new MasterStoreOrderData();
                if (ObjectUtil.isNotEmpty(storeOrderDataList) && storeOrderDataList.size() > 0) {
                    masterStoreOrderData = storeOrderDataList.get(0);
                } else {
                    Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                    masterStoreOrderData.setStoreOrderId(snowflake.nextId());
                    masterStoreOrderData.setOrderNo(ObjectUtil.isNotEmpty(orderInfo.getOrderNo()) ? orderInfo.getOrderNo() : null);
                    masterStoreOrderData.setOrderId(orderInfo.getOrderId());
                    masterStoreOrderData.setStoreId(storeId);
                    masterStoreOrderData.setStoreNo(storeNo);
                    masterStoreOrderData.setOrderType(orderInfo.getOrderType());
                    masterStoreOrderData.setExpenseType(orderInfo.getCostType());
                    masterStoreOrderData.setProject(orderInfo.getProjectName());
                    masterStoreOrderData.setOrderAmount(orderInfo.getOrderAmount());
                    masterStoreOrderData.setOrderStatus(orderInfo.getOrderStatus());
                    masterStoreOrderData.setOrderTime(orderInfo.getAddTime());
                    masterStoreOrderData.setArrivalTime(orderInfo.getArrivalTime());
                    masterStoreOrderData.setSupplier(orderInfo.getSupNameCn());
                    masterStoreOrderData.setConsignee(orderInfo.getReceiver());
                    masterStoreOrderData.setIsDelete(orderInfo.getIsDelete());
                    masterStoreOrderData.setOrderSettlementAmount(new BigDecimal(orderSettlementAmount));

                }
                masterStoreOrderDataService.saveOrUpdate(masterStoreOrderData);
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterDrawingInfo() {
        LambdaQueryWrapper<StoreMasterInfo> storeMasterInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storeMasterInfoLambdaQueryWrapper.eq(StoreMasterInfo::getIsDelete, false);
        List<StoreMasterInfo> storeMasterInfoList = storeMasterInfoService.list(storeMasterInfoLambdaQueryWrapper);
        for (StoreMasterInfo storeMasterInfo : storeMasterInfoList){
            Long storeMasterId = storeMasterInfo.getStoreMasterId();
            //查询门店下的项目
            LambdaQueryWrapper<ProjectInfo> projectInfoQueryWrapper = new LambdaQueryWrapper<>();
            projectInfoQueryWrapper.eq(ProjectInfo::getStoreId, storeMasterId);
            List<ProjectInfo> projectInfoList = projectInfoService.list(projectInfoQueryWrapper);
            for (ProjectInfo projectInfo : projectInfoList) {
                Long projectId = projectInfo.getProjectId();
                LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
                projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId);
                List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
                for (ProjectNodeInfo projectNodeInfo:projectNodeInfos){
                    List<String> drawingNodeCode = new ArrayList<>();
                    drawingNodeCode.add("con-0070208");
                    drawingNodeCode.add("con-0070209");
                    drawingNodeCode.add("con-0070302");
                    drawingNodeCode.add("con-0070303");
                    drawingNodeCode.add("con-0070304");
                    drawingNodeCode.add("con-0070402");
                    drawingNodeCode.add("con-0070404");
                    drawingNodeCode.add("con-0070407");
                    drawingNodeCode.add("con-0070408");
                    drawingNodeCode.add("con-0070409");
                    drawingNodeCode.add("con-0070410");
                    drawingNodeCode.add("con-0070414");
                    drawingNodeCode.add("con-0070412");
                    drawingNodeCode.add("con-0070413");
                    drawingNodeCode.add("con-0080104");
                    drawingNodeCode.add("con-0080105");
                    drawingNodeCode.add("con-0080305");
                    drawingNodeCode.add("con-0080306");
                    drawingNodeCode.add("con-0080307");
                    if (drawingNodeCode.contains(projectNodeInfo.getNodeCode())){
                        Long nodeId = projectNodeInfo.getNodeId();
                        List<LocalStorage> localStorages = localStorageRepository.findByNodeId(nodeId.toString());
                        for (LocalStorage localStorage : localStorages){
                            String path = localStorage.getPath();
                            LambdaQueryWrapper<MasterDrawingInfo> drawingInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            drawingInfoLambdaQueryWrapper.eq(MasterDrawingInfo::getPath,path)
                                    .eq(MasterDrawingInfo::getIsDelete,false);
                            MasterDrawingInfo masterDrawingInfo = masterDrawingInfoRepository.selectOne(drawingInfoLambdaQueryWrapper);
                            if (Objects.isNull(masterDrawingInfo)){
                                MasterDrawingInfo masterDrawing = new MasterDrawingInfo();
                                masterDrawing.setStoreId(storeMasterId);
                                masterDrawing.setStoreNo(storeMasterInfo.getStoreNo());
                                if (!Objects.isNull(projectNodeInfo)) {
                                    if ("con-0070208".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("zred_line_CAD");
                                    } else if ("con-0070209".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("signed_version_upload");
                                    } else if ("con-0070302".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("pred_line_CAD");
                                    } else if ("con-0070303".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("plane_PDF");
                                    } else if ("con-0070304".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("signed_version_plane");
                                    } else if ("con-0070402".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("original_building_drawing");
                                    } else if ("con-0070404".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("fire_approve_CAD");
                                    } else if ("con-0070407".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("capital_drawing_CAD");
                                    } else if ("con-0070408".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("plane_construction_CAD");
                                    } else if ("con-0070409".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("facade_construction_CAD");
                                    } else if ("con-0070410".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("weak_current_CAD");
                                    } else if ("con-0070414".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("doorhead_construction_CAD");
                                    } else if ("con-0070412".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("doorhead_effect");
                                    } else if ("con-0070413".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("doorhead_effect_signed");
                                    } else if ("con-0080104".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("joint_brand_effect");
                                    } else if ("con-0080105".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("joint_brand_construction");
                                    } else if ("con-0080305".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("business_red_line");
                                    } else if ("con-0080306".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("business_effect");
                                    } else if ("con-0080307".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawing.setDrawingName("business_construction");
                                    }
                                }
                                masterDrawing.setDocumentName(localStorage.getName());
                                masterDrawing.setDocumentRealName(localStorage.getRealName());
                                masterDrawing.setSuffix(localStorage.getSuffix());
                                masterDrawing.setPath(path);
                                masterDrawing.setType(localStorage.getType());
                                masterDrawing.setSize(localStorage.getSize());
                                masterDrawing.setIsDelete(projectInfo.getIsDelete());
                                masterDrawing.setCreateUser(localStorage.getCreateBy());
                                masterDrawing.setCreateTime(localStorage.getCreateTime());
                                masterDrawing.setUpdateUser(localStorage.getUpdateBy());
                                masterDrawing.setUpdateTime(localStorage.getUpdateTime());
                                masterDrawingInfoRepository.insert(masterDrawing);
                            } else {
                                masterDrawingInfo.setStoreId(storeMasterId);
                                masterDrawingInfo.setStoreNo(storeMasterInfo.getStoreNo());
                                if (!Objects.isNull(projectNodeInfo)) {
                                    if ("con-0070208".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("zred_line_CAD");
                                    } else if ("con-0070209".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("signed_version_upload");
                                    } else if ("con-0070302".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("pred_line_CAD");
                                    } else if ("con-0070303".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("plane_PDF");
                                    } else if ("con-0070304".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("signed_version_plane");
                                    } else if ("con-0070402".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("original_building_drawing");
                                    } else if ("con-0070404".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("fire_approve_CAD");
                                    } else if ("con-0070407".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("capital_drawing_CAD");
                                    } else if ("con-0070408".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("plane_construction_CAD");
                                    } else if ("con-0070409".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("facade_construction_CAD");
                                    } else if ("con-0070410".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("weak_current_CAD");
                                    } else if ("con-0070414".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("doorhead_construction_CAD");
                                    } else if ("con-0070412".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("doorhead_effect");
                                    } else if ("con-0070413".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("doorhead_effect_signed");
                                    } else if ("con-0080104".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("joint_brand_effect");
                                    } else if ("con-0080105".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("joint_brand_construction");
                                    } else if ("con-0080305".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("business_red_line");
                                    } else if ("con-0080306".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("business_effect");
                                    } else if ("con-0080307".equals(projectNodeInfo.getNodeCode())){
                                        masterDrawingInfo.setDrawingName("business_construction");
                                    }
                                }
                                masterDrawingInfo.setDocumentName(localStorage.getName());
                                masterDrawingInfo.setDocumentRealName(localStorage.getRealName());
                                masterDrawingInfo.setSuffix(localStorage.getSuffix());
                                masterDrawingInfo.setPath(path);
                                masterDrawingInfo.setType(localStorage.getType());
                                masterDrawingInfo.setSize(localStorage.getSize());
                                masterDrawingInfo.setIsDelete(projectInfo.getIsDelete());
                                masterDrawingInfo.setCreateUser(localStorage.getCreateBy());
                                masterDrawingInfo.setCreateTime(localStorage.getCreateTime());
                                masterDrawingInfo.setUpdateUser(localStorage.getUpdateBy());
                                masterDrawingInfo.setUpdateTime(localStorage.getUpdateTime());

                                masterDrawingInfoRepository.updateById(masterDrawingInfo);
                            }
                        }
                    }
                    continue;
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterDrawingInfo(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        Long templateId = projectGroup.getTemplateId();
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        Long storeId = projectInfoDto.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        LambdaQueryWrapper nodeQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId,projectId)
                .eq(ProjectNodeInfo::getParentId,templateId);
        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoService.list(nodeQuery);
        for (ProjectNodeInfo projectNodeInfo:projectNodeInfos) {
            List<String> drawingNodeCode = new ArrayList<>();
            drawingNodeCode.add("con-0070208");
            drawingNodeCode.add("con-0070209");
            drawingNodeCode.add("con-0070302");
            drawingNodeCode.add("con-0070303");
            drawingNodeCode.add("con-0070304");
            drawingNodeCode.add("con-0070402");
            drawingNodeCode.add("con-0070404");
            drawingNodeCode.add("con-0070407");
            drawingNodeCode.add("con-0070408");
            drawingNodeCode.add("con-0070409");
            drawingNodeCode.add("con-0070410");
            drawingNodeCode.add("con-0070414");
            drawingNodeCode.add("con-0070412");
            drawingNodeCode.add("con-0070413");
            drawingNodeCode.add("con-0080104");
            drawingNodeCode.add("con-0080105");
            drawingNodeCode.add("con-0080305");
            drawingNodeCode.add("con-0080306");
            drawingNodeCode.add("con-0080307");
            if (drawingNodeCode.contains(projectNodeInfo.getNodeCode())) {
                Long nodeId = projectNodeInfo.getNodeId();
                List<LocalStorage> localStorages = localStorageRepository.findByNodeId(nodeId.toString());
                for (LocalStorage localStorage : localStorages) {
                    String path = localStorage.getPath();
                    LambdaQueryWrapper<MasterDrawingInfo> drawingInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    drawingInfoLambdaQueryWrapper.eq(MasterDrawingInfo::getPath, path)
                            .eq(MasterDrawingInfo::getIsDelete, false);
                    MasterDrawingInfo masterDrawingInfo = masterDrawingInfoRepository.selectOne(drawingInfoLambdaQueryWrapper);
                    if (Objects.isNull(masterDrawingInfo)) {
                        MasterDrawingInfo masterDrawing = new MasterDrawingInfo();
                        masterDrawing.setStoreId(storeId);
                        masterDrawing.setStoreNo(storeMasterInfo.getStoreNo());
                        if (!Objects.isNull(projectNodeInfo)) {
                            if ("con-0070208".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("zred_line_CAD");
                            } else if ("con-0070209".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("signed_version_upload");
                            } else if ("con-0070302".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("pred_line_CAD");
                            } else if ("con-0070303".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("plane_PDF");
                            } else if ("con-0070304".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("signed_version_plane");
                            } else if ("con-0070402".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("original_building_drawing");
                            } else if ("con-0070404".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("fire_approve_CAD");
                            } else if ("con-0070407".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("capital_drawing_CAD");
                            } else if ("con-0070408".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("plane_construction_CAD");
                            } else if ("con-0070409".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("facade_construction_CAD");
                            } else if ("con-0070410".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("weak_current_CAD");
                            } else if ("con-0070414".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("doorhead_construction_CAD");
                            } else if ("con-0070412".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("doorhead_effect");
                            } else if ("con-0070413".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("doorhead_effect_signed");
                            } else if ("con-0080104".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("joint_brand_effect");
                            } else if ("con-0080105".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("joint_brand_construction");
                            } else if ("con-0080305".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("business_red_line");
                            } else if ("con-0080306".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("business_effect");
                            } else if ("con-0080307".equals(projectNodeInfo.getNodeCode())) {
                                masterDrawing.setDrawingName("business_construction");
                            }
                        }
                        masterDrawing.setDocumentName(localStorage.getName());
                        masterDrawing.setDocumentRealName(localStorage.getRealName());
                        masterDrawing.setSuffix(localStorage.getSuffix());
                        masterDrawing.setPath(path);
                        masterDrawing.setType(localStorage.getType());
                        masterDrawing.setSize(localStorage.getSize());
                        masterDrawing.setIsDelete(false);
                        masterDrawing.setCreateUser(localStorage.getCreateBy());
                        masterDrawing.setCreateTime(localStorage.getCreateTime());
                        masterDrawing.setUpdateUser(localStorage.getUpdateBy());
                        masterDrawing.setUpdateTime(localStorage.getUpdateTime());
                        masterDrawingInfoRepository.insert(masterDrawing);
                    } else {
                        masterDrawingInfo.setStoreId(storeId);
                        masterDrawingInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        if (!Objects.isNull(projectNodeInfo)) {
                            if ("con-0070208".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("zred_line_CAD");
                            } else if ("con-0070209".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("signed_version_upload");
                            } else if ("con-0070302".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("pred_line_CAD");
                            } else if ("con-0070303".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("plane_PDF");
                            } else if ("con-0070304".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("signed_version_plane");
                            } else if ("con-0070402".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("original_building_drawing");
                            } else if ("con-0070404".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("fire_approve_CAD");
                            } else if ("con-0070407".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("capital_drawing_CAD");
                            } else if ("con-0070408".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("plane_construction_CAD");
                            } else if ("con-0070409".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("facade_construction_CAD");
                            } else if ("con-0070410".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("weak_current_CAD");
                            } else if ("con-0070414".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("doorhead_construction_CAD");
                            } else if ("con-0070412".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("doorhead_effect");
                            } else if ("con-0070413".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("doorhead_effect_signed");
                            } else if ("con-0080104".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("joint_brand_effect");
                            } else if ("con-0080105".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("joint_brand_construction");
                            } else if ("con-0080305".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("business_red_line");
                            } else if ("con-0080306".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("business_effect");
                            } else if ("con-0080307".equals(projectNodeInfo.getNodeCode())){
                                masterDrawingInfo.setDrawingName("business_construction");
                            }
                        }
                        masterDrawingInfo.setDocumentName(localStorage.getName());
                        masterDrawingInfo.setDocumentRealName(localStorage.getRealName());
                        masterDrawingInfo.setSuffix(localStorage.getSuffix());
                        masterDrawingInfo.setPath(path);
                        masterDrawingInfo.setType(localStorage.getType());
                        masterDrawingInfo.setSize(localStorage.getSize());
                        masterDrawingInfo.setIsDelete(false);
                        masterDrawingInfo.setCreateUser(localStorage.getCreateBy());
                        masterDrawingInfo.setCreateTime(localStorage.getCreateTime());
                        masterDrawingInfo.setUpdateUser(localStorage.getUpdateBy());
                        masterDrawingInfo.setUpdateTime(localStorage.getUpdateTime());

                        masterDrawingInfoRepository.updateById(masterDrawingInfo);
                    }
                }
            }
            continue;
        }
        return true;
    }

    @Override
    public Boolean enterMasterLicenseInfo() {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getIsDelete,false);
        List<ProjectGroup> projectGroupList = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(projectGroupList) && projectGroupList.size() > 0) {
            for (ProjectGroup projectGroup : projectGroupList){
                if ("con-00201".equals(projectGroup.getNodeCode()) || "con-00202".equals(projectGroup.getNodeCode())){
                    enterMasterLicenseInfo(projectGroup);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean enterMasterLicenseInfo(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        Long templateId = projectGroup.getTemplateId();
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        Long storeId = projectInfoDto.getStoreId();
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoRepository.selectById(storeId)).orElseGet(StoreMasterInfo::new);
        LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
        projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getParentId, templateId);
        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
        if (ObjectUtil.isNotEmpty(projectNodeInfos) && projectNodeInfos.size() > 0 && ObjectUtil.isNotEmpty(storeId)){
            for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
                //证照信息 图纸审核意见书 前置证照
                if (projectNodeInfo.getNodeCode().equals("con-*********")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"pre_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.PRE_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualStart(new Timestamp(format1.parse((licenceVos.get(2).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualEnd(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (licenceVos.get(4).getValue() != null && licenceVos.get(4).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(4).getValue());
                            }
                            if (licenceVos.get(5).getValue() != null && licenceVos.get(5).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(5).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);

                }//证照信息 施工许可证 前置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002010502")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"pre_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.PRE_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualStart(new Timestamp(format1.parse((licenceVos.get(2).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualEnd(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (licenceVos.get(4).getValue() != null && licenceVos.get(4).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(4).getValue());
                            }
                            if (licenceVos.get(5).getValue() != null && licenceVos.get(5).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(5).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证照信息 营业执照 前置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002010503")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"pre_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.PRE_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualStart(new Timestamp(format1.parse((licenceVos.get(2).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualEnd(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (licenceVos.get(4).getValue() != null && licenceVos.get(4).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(4).getValue());
                            }
                            if (licenceVos.get(5).getValue() != null && licenceVos.get(5).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(5).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证照信息 其他类证照 前置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002010504")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"pre_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.PRE_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualStart(new Timestamp(format1.parse((licenceVos.get(2).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setActualEnd(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (licenceVos.get(4).getValue() != null && licenceVos.get(4).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(4).getValue());
                            }
                            if (licenceVos.get(5).getValue() != null && licenceVos.get(5).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(5).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证件信息 甲方土建验收合格证 后置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002020501")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"post_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证件信息 住建竣工验收合格证 后置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002020502")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"post_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证件信息 消防开业检查合格证 后置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002020503")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"post_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }//证件信息 甲方承诺函 后置证照
                else if (projectNodeInfo.getNodeCode().equals("con-002020504")) {
                    String nodeName = projectNodeInfo.getNodeName();
                    MasterLicenseInfo masterLicenseInfo = new MasterLicenseInfo();
                    LambdaQueryWrapper<MasterLicenseInfo> masterLicenseInfoQuery = Wrappers.lambdaQuery(MasterLicenseInfo.class)
                            .eq(MasterLicenseInfo::getLicenseName,nodeName)
                            .eq(MasterLicenseInfo::getLicenseType,"post_license")
                            .eq(MasterLicenseInfo::getStoreId,storeId);
                    List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseInfoQuery);
                    if (ObjectUtil.isNotEmpty(masterLicenseInfos) && masterLicenseInfos.size() > 0){
                        masterLicenseInfo = masterLicenseInfos.get(0);
                    } else {
                        String remark = projectNodeInfo.getRemark();
                        List<LicenceVo> licenceVos = com.alibaba.fastjson.JSONObject.parseArray(remark, LicenceVo.class);
                        masterLicenseInfo.setStoreId(storeId);
                        masterLicenseInfo.setStoreNo(storeMasterInfo.getStoreNo());
                        masterLicenseInfo.setIsDelete(false);
                        if (CollectionUtils.isNotEmpty(licenceVos)) {
                            Long nodeId = projectNodeInfo.getNodeId();
                            masterLicenseInfo.setNodeId(nodeId.toString());
                            masterLicenseInfo.setLicenseType(JhSystemEnum.licenseTypeEnum.POST_LICENSE.getSpec());
                            masterLicenseInfo.setLicenseName(nodeName);
                            if (licenceVos.get(2).getValue() != null && licenceVos.get(2).getValue().length() > 0) {
                                masterLicenseInfo.setUploader(licenceVos.get(2).getValue());
                            }
                            if (licenceVos.get(3).getValue() != null && licenceVos.get(3).getValue().length() > 0) {
                                DateFormat format1 = new SimpleDateFormat("yyyyMMdd");
                                try {
                                    masterLicenseInfo.setUploadTime(new Timestamp(format1.parse((licenceVos.get(3).getValue())).getTime()));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    masterLicenseInfoService.saveOrUpdate(masterLicenseInfo);
                }
            }
        }
        return true;
    }

}
