/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.modules.atour.domain.TableConfiguringTertiaryNodes;
import com.bassims.modules.atour.domain.TemplateTable;
import com.bassims.modules.atour.service.TableConfiguringTertiaryNodesService;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesDto;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesVO;
import com.bassims.modules.atour.util.CodeUtil;
import com.bassims.modules.atour.util.JSONSerializer;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateTableRepository;
import com.bassims.modules.atour.service.TemplateTableService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateTableDto;
import com.bassims.modules.atour.service.dto.TemplateTableQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateTableMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-10-15
 **/
@Service
public class TemplateTableServiceImpl extends BaseServiceImpl<TemplateTableRepository, TemplateTable> implements TemplateTableService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableServiceImpl.class);

    @Autowired
    private TemplateTableRepository templateTableRepository;
    @Autowired
    private TemplateTableMapper templateTableMapper;
    @Autowired
    private TableConfiguringTertiaryNodesService tableConfiguringTertiaryNodesService;

    @Override
    public Map<String, Object> queryAll(TemplateTableQueryCriteria criteria, Pageable pageable) {
//        getPage(pageable);
//        PageInfo<TemplateTable> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateTable.class, criteria)));
//        Map<String, Object> map = new LinkedHashMap<>(2);
//        map.put("content", templateTableMapper.toDto(page.getList()));
//        map.put("totalElements", page.getTotal());
//        return map;

        String byStartSign = tableConfiguringTertiaryNodesService.getNodeByStartSign(criteria.getDynamicTableType());
        getPage(pageable);
        PageInfo<TemplateTable> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateTable.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(3);
        map.put("content", templateTableMapper.toDto(page.getList()));
        map.put("byStartSign", byStartSign);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateTableDto> queryAll(TemplateTableQueryCriteria criteria) {
        List<TemplateTableDto> dtos = new ArrayList<>();
        String byStartSign = tableConfiguringTertiaryNodesService.getNodeByStartSign(criteria.getDynamicTableType());
        if (ObjectUtil.isNotEmpty(byStartSign)) {
            dtos.add(new TemplateTableDto(byStartSign));
        }

        List list = templateTableMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateTable.class, criteria)));
        dtos.addAll(list);
        return dtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableDto findById(Long templateTableId) {
        TemplateTable templateTable = Optional.ofNullable(getById(templateTableId)).orElseGet(TemplateTable::new);
        ValidationUtil.isNull(templateTable.getTemplateTableId(), getEntityClass().getSimpleName(), "templateTableId", templateTableId);
        return templateTableMapper.toDto(templateTable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableDto create(List<TableConfiguringTertiaryNodesDto> dtos) {
        TemplateTable templateTable = new TemplateTable();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        templateTable.setTemplateTableId(snowflake.nextId());

        LambdaQueryWrapper<TemplateTable> lambdaQuery = Wrappers.lambdaQuery(TemplateTable.class);
        lambdaQuery.like(TemplateTable::getCreateTime, new DateTime().toString("yyyy-MM-dd"));
        long count = this.count(lambdaQuery);
        templateTable.setNodeCode(CodeUtil.idGenerate1("table_", count));

        List<TableConfiguringTertiaryNodesVO> vos = new ArrayList<>();
        for (TableConfiguringTertiaryNodesDto dto : dtos) {
            TableConfiguringTertiaryNodesVO vo = new TableConfiguringTertiaryNodesVO();
            BeanUtil.copyProperties(dto, vo, CopyOptions.create().setIgnoreNullValue(true));
            vos.add(vo);
            templateTable.setRelationCode(dto.getRelationCode());
            templateTable.setRelationName(dto.getRelationName());
            templateTable.setDynamicTableType(dto.getDynamicTableType());
        }
        templateTable.setRemark(JSONSerializer.serialize(vos));
        save(templateTable);
        return findById(templateTable.getTemplateTableId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateTable resources) {
        TemplateTable templateTable = Optional.ofNullable(getById(resources.getTemplateTableId())).orElseGet(TemplateTable::new);
        ValidationUtil.isNull(templateTable.getTemplateTableId(), "TemplateTable", "id", resources.getTemplateTableId());
        templateTable.copy(resources);
        updateById(templateTable);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateTableId : ids) {
            templateTableRepository.deleteById(templateTableId);
        }
    }

    @Override
    public void download(List<TemplateTableDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateTableDto templateTable : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目版本号", templateTable.getProjectVersion());
            map.put("表格节点编码", templateTable.getNodeCode());
            map.put("节点序号", templateTable.getNodeWbs());
            map.put("子节点排序", templateTable.getNodeIndex());
            map.put("节点等级", templateTable.getNodeLevel());
            map.put("节点类型", templateTable.getNodeType());
            map.put("节点状态", templateTable.getNodeStatus());
            map.put("表格节点备注（json）", templateTable.getRemark());
            map.put("isDelete", templateTable.getIsDelete());
            map.put("创建时间", templateTable.getCreateTime());
            map.put("createBy", templateTable.getCreateBy());
            map.put("修改时间", templateTable.getUpdateTime());
            map.put("修改人", templateTable.getUpdateBy());
            map.put("是否可用", templateTable.getIsEnabled());
            map.put("是否可以编辑", templateTable.getIsEdit());
            map.put("用户添加的版本", templateTable.getAddedVersion());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<TemplateTableDto> updateTable(String dynamicTableType) {
        //根据类型获取表头，在获取json数据
        String startSign = tableConfiguringTertiaryNodesService.getNodeByStartSign(dynamicTableType);
        if (ObjectUtils.isNotEmpty(startSign)) {
            System.out.println("表头:" + startSign);
            JSONArray startSignOld = JSON.parseArray(startSign);

            TemplateTableQueryCriteria criteria = new TemplateTableQueryCriteria();
            criteria.setDynamicTableType(dynamicTableType);
            List<TemplateTableDto> dtos = this.queryAll(criteria);
            for (TemplateTableDto templateTableDto : dtos) {
                JSONArray startSignNew = startSignOld;
                JSONArray remarkTableNew = new JSONArray();

                System.out.println("原先remark:" + templateTableDto.getRemark());
                JSONArray remarkTableOld = JSON.parseArray(templateTableDto.getRemark());
                for (Object object : remarkTableOld) {
                    JSONObject obj = (JSONObject) object;
                    for (Object startSignObj : startSignOld) {
                        JSONObject signObj = (JSONObject) startSignObj;
                        if (signObj.get("tertiaryKey").equals(obj.get("tertiaryKey"))) {
                            remarkTableNew.add(obj);
                            startSignNew.remove(signObj);
                        }
                    }
                }
                remarkTableNew.addAll(startSignNew);
                templateTableDto.setRemark(JSONSerializer.serialize(remarkTableNew));
                System.out.println("新的remark:" + templateTableDto.getRemark());
            }
            return dtos;
        }
        return null;
    }
}