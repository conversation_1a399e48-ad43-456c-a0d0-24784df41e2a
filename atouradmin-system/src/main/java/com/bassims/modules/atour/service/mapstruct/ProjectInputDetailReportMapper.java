package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectInputDetailReport;
import com.bassims.modules.atour.service.dto.ProjectInputDetailReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程投入明细报表
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectInputDetailReportMapper extends BaseMapper<ProjectInputDetailReportDto, ProjectInputDetailReport> {

}
