package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectLicenseReport;
import com.bassims.modules.atour.service.dto.ProjectLicenseReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程证照报表
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectLicenseReportMapper extends BaseMapper<ProjectLicenseReportDto, ProjectLicenseReport> {

}
