/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum.cateKey;
import com.bassims.domain.LocalStorage;
import com.bassims.domain.vo.EmailVo;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.ProjectStatusAndPhaseVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterSmsVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterTargetsVo;
import com.bassims.modules.atour.domain.vo.TemplateCompletionReceiptRelationVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.*;
import com.bassims.modules.atour.util.HlmClientUtil;
import com.bassims.modules.atour.util.HttpUtils;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.feishu.service.PortalService;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.AreaRepository;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.modules.system.service.mapstruct.UserMapper;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.service.EmailService;
import com.bassims.service.LocalStorageService;
import com.bassims.service.dto.LocalStorageDto;
import com.bassims.service.mapstruct.LocalStorageMapper;
import com.bassims.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.hssf.record.DVALRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-24
 **/
@Service
public class ProjectNodeInfoServiceImpl extends BaseServiceImpl<ProjectNodeInfoRepository, ProjectNodeInfo> implements ProjectNodeInfoService, Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ProjectNodeInfoServiceImpl.class);

    @Autowired
    private ProjectCateInfoService projectCateInfoService;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private ProjectNodeInfoMapper projectNodeInfoMapper;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private ProjectTemplateApproveRelationRepository projectTemplateApproveRelationRepository;
    @Autowired
    private JobUserRepository jobUserRepository;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private ProjectAppTemplateService projectAppTemplateService;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private OrderNodeInfoService orderNodeInfoService;
    @Autowired
    private SupplierInfoService supplierInfoService;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private ProjectToMasterService projectToMasterService;
    @Autowired
    private StoreMasterInfoService storeMasterInfoService;
    @Autowired
    private BudgetVersionService budgetVersionService;
    @Autowired
    private OutsourceVersionService outsourceVersionService;
    @Autowired
    private MeasureAreaService measureAreaService;
    @Autowired
    private OutsourceVersionDetailService outsourceVersionDetailService;
    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private KwThirdService kwThirdService;
    @Autowired
    private SupplierPmMapper supplierPmMapper;
    @Autowired
    private PaymentApplicationService paymentApplicationService;
    @Autowired
    private FinalPaymentApplicationService finalPaymentApplicationService;
    @Autowired
    private SupplierPmService supplierPmService;
    @Autowired
    private AccountInterfaceInfoService accountInterfaceInfoService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PortalService portalService;
    @Autowired
    private RepayService repayService;
    @Autowired
    private EnterMasterService enterMasterService;

    private static final String CONTRACT_NO_PREFIX = "contract_no::";
    @Autowired
    private OrderDetailRepository orderDetailRepository;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private ContractManageService contractManageService;
    @Autowired
    private FeeInfoService feeInfoService;
    @Autowired
    private ApprovedFormService approvedFormService;
    @Autowired
    private CompletionAcceptancePictureService completionAcceptancePictureService;
    @Autowired
    private ContractManageMapper contractManageMapper;
    @Autowired
    private FeeInfoMapper feeInfoMapper;
    @Autowired
    private ApprovedFormMapper approvedFormMapper;
    @Autowired
    private CompletionAcceptancePictureMapper completionAcceptancePictureMapper;
    @Autowired
    private LocalStorageService localStorageService;
    @Autowired
    private LocalStorageMapper localStorageMapper;
    @Autowired
    private LocalStorageRepository localStorageRepository;
    @Autowired
    private RepayMapper repayMapper;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private ProjectApproveDetailRepository projectApproveDetailRepository;
    @Autowired
    private ProjectJointTaskOnfigurationService projectJointTaskOnfigurationService;
    @Autowired
    private UnauthorizedConstructionService unauthorizedConstructionService;
    @Autowired
    private QualityControlService qualityControlService;
    @Autowired
    private ConstructionPhotographService constructionPhotographService;
    @Autowired
    private ProjectUnauthorizedConstructionService projectUnauthorizedConstructionService;
    @Autowired
    private TemplateQueueRepository templateQueueRepository;
    @Autowired
    private ProjectInsolubleMatterService projectInsolubleMatterService;
    @Autowired
    private ProjectDisclosureProblemService projectDisclosureProblemService;
    @Autowired
    private TemplateTableGroupRepository templateTableGroupRepository;
    @Autowired
    private SupplierPmRepository supplierPmRepository;

    @Autowired
    private ProjectInfoExpansionService projectInfoExpansionService;
    @Autowired
    private ProjectInfoExpansionRepository projectInfoExpansionRepository;
    @Autowired
    private ProjectInfoServiceImpl projectInfoServiceImpl;
    @Autowired
    private DictDetailRepository dictDetailRepository;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    @Autowired
    private ProjectTemplateRepository projectTemplateRepository;
    @Autowired
    private ProjectTableNodeInfoRepository projectTableNodeInfoRepository;
    @Autowired
    private EmailService emailService;

    @Autowired
    private HlmClientUtil hlmClientUtil;
    @Autowired
    private ProjectSpecialCaseDescriptionService projectSpecialCaseDescriptionService;
    @Autowired
    private ProjectSpecialNeedsFranchisorsService projectSpecialNeedsFranchisorsService;
    @Autowired
    private TemplateConditionRepository templateConditionRepository;
    @Autowired
    private ProjectNoticeService projectNoticeService;
    @Autowired
    private ProjectRoomService projectRoomService;
    @Autowired
    private ProjectRoomRepository projectRoomRepository;
    @Autowired
    private ProjectMaterialManagementService projectMaterialManagementService;
    @Autowired
    private MaterialManagementService materialManagementService;

    @Autowired
    private ProjectGroupExpandService projectGroupExpandService;
    @Autowired
    private ProjectSystemSelfInspectionRepository projectSystemSelfInspectionRepository;
    @Autowired
    private ProjectSystemSelfInspectionMapper projectSystemSelfInspectionMapper;
    @Autowired
    private QualityControlMapper qualityControlMapper;
    @Autowired
    private ProjectSystemSelfInspectionService projectSystemSelfInspectionService;
    @Autowired
    private ProjectConstructionLogRepository projectConstructionLogRepository;
    @Autowired
    private ProjectTableNodeInfoMapper projectTableNodeInfoMapper;
    @Autowired
    private ProjectMaterialManagementRepository projectMaterialManagementRepository;
    @Autowired
    private ProjectWeakCurrentService projectWeakCurrentService;
    @Autowired
    private TPlanEventInfoRepository planEventInfoRepository;
    @Autowired
    private TProjectPlaneventRelationRepository planeventRelationRepository;
    @Autowired
    private ProjectRoomRepository roomRepository;
    @Autowired
    private TProjectPlaneventRelationRepository tProjectPlaneventRelationRepository;
    @Autowired
    private TemplateCompletionReceiptRepository templateCompletionReceiptRepository;
    @Autowired
    private ProjectCompletionReceiptService projectCompletionReceiptService;
    @Autowired
    private ProjectCompletionReceiptRepository projectCompletionReceiptRepository;

    @Autowired
    private ProjectCompletionPhotoRepository projectCompletionPhotoRepository;
    @Autowired
    private ProjectCompletionPhotoService projectCompletionPhotoService;

    @Autowired
    private ProjectCompletionReceiptDescriptionService projectCompletionReceiptDescriptionService;
    @Autowired
    private ProjectVisaFilingRepository projectVisaFilingRepository;
    @Autowired
    private ProjectVisaFilingService projectVisaFilingService;
    @Autowired
    private ProjectCommunicationService projectCommunicationService;

    @Autowired
    private ProjectSpotCheckRepository projectSpotCheckRepository;

    @Autowired
    private TemplateGroupCompletionReceiptService templateGroupCompletionReceiptService;
    @Autowired
    private TemplateCompletionReceiptRelationRepository templateCompletionReceiptRelationRepository;


    @Autowired
    private ProjectCompletionReceiptSummaryService projectCompletionReceiptSummaryService;

    @Autowired
    private ProjectGroupExpandRepository projectGroupExpandRepository;

    @Override
    public Map<String, Object> queryAll(ProjectNodeInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectNodeInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectNodeInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectNodeInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectNodeInfoDto> queryAll(ProjectNodeInfoQueryCriteria criteria) {
        return projectNodeInfoMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectNodeInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoDto findById(Long nodeId) {
        ProjectNodeInfo projectNodeInfo = Optional.ofNullable(getById(nodeId)).orElseGet(ProjectNodeInfo::new);
        ValidationUtil.isNull(projectNodeInfo.getNodeId(), getEntityClass().getSimpleName(), "nodeId", nodeId);
        return projectNodeInfoMapper.toDto(projectNodeInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoDto create(ProjectNodeInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setNodeId(snowflake.nextId());
        save(resources);
        return findById(resources.getNodeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectNodeInfo resources) {
        ProjectNodeInfo projectNodeInfo = Optional.ofNullable(getById(resources.getNodeId())).orElseGet(ProjectNodeInfo::new);
        ValidationUtil.isNull(projectNodeInfo.getNodeId(), "ProjectNodeInfo", "id", resources.getNodeId());
        projectNodeInfo.copy(resources);
        updateById(projectNodeInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long nodeId : ids) {
            projectNodeInfoRepository.deleteById(nodeId);
        }
    }

    @Override
    public void download(List<ProjectNodeInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectNodeInfoDto projectNodeInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectNodeInfo.getProjectId());
            map.put("队列id", projectNodeInfo.getTemplateQueueId());
            map.put("模板主键", projectNodeInfo.getTemplateId());
            map.put("父节点", projectNodeInfo.getParentId());
            map.put("项目版本号", projectNodeInfo.getProjectVersion());
            map.put("节点编码", projectNodeInfo.getNodeCode());
            map.put("节点名称", projectNodeInfo.getNodeName());
            map.put("计划开始时间", projectNodeInfo.getPlanStartDate());
            map.put("计划结束时间", projectNodeInfo.getPlanEndDate());
            map.put("预估开始日期", projectNodeInfo.getPredictStartDate());
            map.put("预估结束日期", projectNodeInfo.getPredictEndDate());
            map.put("计划需要完成天数", projectNodeInfo.getPlanDay());
            map.put("实际完成时间", projectNodeInfo.getActualEndDate());
            map.put("提醒天数", projectNodeInfo.getNoticeDay());
            map.put("延期天数", projectNodeInfo.getDelayDay());
            map.put("节点序号", projectNodeInfo.getNodeWbs());
            map.put("前置任务配置", projectNodeInfo.getFrontWbsConfig());
            map.put("是否是关键节点", projectNodeInfo.getIsKey());
            map.put("关键节点前置任务", projectNodeInfo.getKeyFrontWbs());
            map.put("子节点排序", projectNodeInfo.getNodeIndex());
            map.put("节点等级", projectNodeInfo.getNodeLevel());
            map.put("节点类型", projectNodeInfo.getNodeType());
            map.put("节点状态", projectNodeInfo.getNodeStatus());
            map.put("已完成按钮", projectNodeInfo.getNodeIsfin());

            map.put("节点备注", projectNodeInfo.getRemark());
            map.put("小程序标志", projectNodeInfo.getIcon());
            map.put(" isDelete", projectNodeInfo.getIsDelete());
            map.put("创建时间", projectNodeInfo.getCreateTime());
            map.put("创建人", projectNodeInfo.getCreateBy());
            map.put("修改时间", projectNodeInfo.getUpdateTime());
            map.put("修改人", projectNodeInfo.getUpdateBy());
            map.put("是否可用", projectNodeInfo.getIsEnabled());
            map.put("总工期", projectNodeInfo.getTotalDay());
            map.put("开始标志（甘特图）", projectNodeInfo.getStartSign());
            map.put("结束标志（甘特图）", projectNodeInfo.getEndSign());

            map.put("干系人角色名称", projectNodeInfo.getJobCode());
            map.put("下拉列表角色名称", projectNodeInfo.getDownCode());
            map.put("是否手机端", projectNodeInfo.getIsMobile());
            map.put("关联nodecode", projectNodeInfo.getRelationCode());
            map.put("关联的类型", projectNodeInfo.getRelationType());
            map.put("使用场景", projectNodeInfo.getUseCase());
            map.put("角色code", projectNodeInfo.getRoleCode());
            map.put("是否可以编辑", projectNodeInfo.getIsEdit());
            map.put("占位", projectNodeInfo.getSeat());
            map.put("公式", projectNodeInfo.getFormula());
            map.put("影响的code", projectNodeInfo.getFormulaCode());

            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 根据二三级模版的projectId查询对应的项目主档的ID
     * 二三级模版的projectId 目前存了 项目主档、质量管理、项目施工日志  等主键
     * 他们都和项目主档进行关联，模版数据存在项目主档的分表中，所以在这个接口返回项目主档的ID,去查询对应的分表
     */
    @Override
    public Long getSubmeterProjectId(Long projectId) {
        QualityControl qualityControl = qualityControlRepository.selectById(projectId);
        if (ObjectUtil.isNotEmpty(qualityControl)) {
            //质量管理的模版或者竣工常规系统模板
            return qualityControl.getProjectId();
        }
        final ProjectConstructionLog projectConstructionLog = projectConstructionLogRepository.selectById(projectId);
        if (ObjectUtil.isNotEmpty(projectConstructionLog)) {
            //施工日志的模版
            return projectConstructionLog.getProjectId();
        }

        ProjectVisaFiling projectVisaFiling = projectVisaFilingRepository.selectById(projectId);
        if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
            //签证报备的模版
            return projectVisaFiling.getProjectId();
        }

        ProjectSpotCheck projectSpotCheck = projectSpotCheckRepository.selectById(projectId);
        if (ObjectUtil.isNotEmpty(projectSpotCheck)) {
            //工程抽查的模版
            return projectSpotCheck.getProjectId();
        }
        //竣工系统模板
        final ProjectSystemSelfInspection projectSystemSelfInspection = projectSystemSelfInspectionRepository.selectById(projectId);
        //设计样板间验收模板
        final ProjectTableNodeInfo projectTableNodeInfo = projectTableNodeInfoRepository.selectById(projectId);
        return Optional.ofNullable(projectSystemSelfInspection).map(ProjectSystemSelfInspection::getProjectId).filter(ObjectUtil::isNotEmpty)
                .orElseGet(() -> Optional.ofNullable(projectTableNodeInfo).map(ProjectTableNodeInfo::getProjectId).filter(ObjectUtil::isNotEmpty).orElse(projectId));
    }

    @Override
    public List<Tree<String>> projectTree2(Long projectId, String useCase, Boolean isMobile, int deep, Long templateId, String nodeCode, Long nodeId, String roundMarking) {
        Long submeterProjectId = getSubmeterProjectId(projectId);
        ProjectInfo info = projectInfoRepository.selectById(submeterProjectId);
        if (info == null) {
            return projectTree3(projectId, useCase, isMobile, deep, templateId, nodeCode, nodeId, roundMarking);
        } else {
            return projectTree1(projectId, useCase, isMobile, deep, templateId, nodeCode, nodeId, roundMarking);
        }
    }

    public List<Tree<String>> projectTree3(Long projectId, String useCase, Boolean isMobile, int deep, Long templateId, String nodeCode, Long nodeId, String roundMarking) {

        //获取当前用户id，并判断用户是否存在
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        Long submeterProjectId = getSubmeterProjectId(projectId);
        //动态获取 t_project_node_info 表名
        util.initialize(projectId);

        //查询当前用户,并获取对应干系人的roleId
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(currentUserId);
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);

        //查询当前项目的0级数据
        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(null != projectId, ProjectGroup::getProjectId, projectId)
                .eq(null != roundMarking, ProjectGroup::getRoundMarking, roundMarking)
                .isNull(ProjectGroup::getParentId)
                .isNull(ProjectGroup::getOrderId)
                .like(ProjectGroup::getNodeCode, nodeCode.substring(0, 3))
                .last("limit 1");
        ProjectGroupDto parent = projectGroupMapper.toDto(projectGroupRepository.selectOne(project));

        //设置条件 query ，查询 t_project_group 表
        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId)
                .eq(null != roundMarking, ProjectNodeInfo::getRoundMarking, roundMarking)
                .eq(ProjectNodeInfo::getIsDelete, false);
        if (StringUtils.isNotEmpty(useCase)) {
            //判断 useCase 是否为空，修改条件 query
            query.eq(ProjectNodeInfo::getUseCase, useCase);
        }
        if (null != isMobile) {
            query.eq(ProjectNodeInfo::getIsMobile, isMobile);
        }

        //查询 t_project_group 表和 t_job_node_info 表的联查，存放到 t_project_node_info 表里
        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo1(projectId, useCase, isMobile, roleIds, templateId, parent.getRoundMarking());
        //判断 t_project_group 表是否存在 dtos
        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }

        //过滤出 t_project_node_info 表的二级节点
        List<ProjectNodeInfoDto> collect = dtos.stream().filter(e -> e.getNodeLevel() != null && e.getNodeLevel() == 2).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //根据当前用户，获取对应干系人的roleCode,有多个roleCode
            List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, submeterProjectId);
            //todo:其他地方需要引用
//        读写判断
//        获取的roleCode与当前节点对应的roleCode做比较
            for (ProjectNodeInfoDto dto : collect) {

                //判断当前二级任务的紧前任务是否完成
                this.showFrontWbsConfig(dto);

                //获取的roleCode与当前节点对应的roleCode做比较
                if (roleCodes.isEmpty()) {
                    dto.setIsWrite(Boolean.FALSE);
                } else {
                    //当前用户是当前项目的干系人的话，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                    OK:
                    for (String roleCode : roleCodes) {
                        //当前roleCode不为空且等于
                        if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && Arrays.stream(dto.getRoleCode().split(",")).anyMatch(s -> s.contains(roleCode))
//                            if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && dto.getRoleCode().equals(roleCode)
//                                    && parent.getIsWrite() != null && parent.getIsWrite()
                                && dto.getIsOpen() != null && dto.getIsOpen()
                        ) {
                            dto.setIsWrite(Boolean.TRUE);
                            dto.setIsHidden(Boolean.TRUE);

                            break OK;
                        } else {
                            Integer userAndRoleCode = roleRepository.getCountByUserAndRoleCode(currentUserId, JhSystemEnum.JobEnum.CSXMJL.getKey());
                            if (userAndRoleCode > 0) {
                                //是厂商项目经理的话，查询是否是该任务的审批人，不是的话，不可见该任务
                                Integer byUserAndNodeId = projectApproveRepository.getCountByUserAndNodeId(currentUserId, dto.getProjectGroupId(), projectId);
                                if (byUserAndNodeId == 0) {
                                    //当前用户是厂商项目经理，不显示非负责人、非审批人的任务
                                    dto.setIsHidden(Boolean.FALSE);
                                }
                            }
                        }
                    }
                }
//                  currentUserId == 1 || currentUserId == 488
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
                    //超级管理员，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                }
//                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey()) && !dto.getIsWrite()) {
//                    dto.setIsWrite(Boolean.TRUE);
//                    dto.setIsHidden(Boolean.TRUE);
//                }

            }


            //查询出 t_project_node_info 表 的三级节点     设置条件，t_project_node_info 表 的三级
            LambdaQueryWrapper<ProjectNodeInfo> thirdQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(null != roundMarking, ProjectNodeInfo::getRoundMarking, roundMarking)
                    .in(ProjectNodeInfo::getParentId, collect.stream().map(ProjectNodeInfoDto::getTemplateId)
                            .collect(Collectors.toList()));
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(thirdQuery);

            //根据  parent_id ，对 projectNodeInfos 三级分组
            Map<Long, List<ProjectNodeInfo>> collectThree = projectNodeInfos.stream().collect(Collectors.groupingBy(ProjectNodeInfo::getParentId));

            //遍历，dtos增加三级节点
            for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
                //分组后，根据 TemplateId 获取三级
                List<ProjectNodeInfo> nodeInfos = collectThree.get(projectNodeInfo.getParentId());
                //实体集合转dto集合
                List<ProjectNodeInfoDto> projectNodeInfoDtos = projectNodeInfoMapper.toDto(nodeInfos);
                //三级节点增加到dtos
                if (ObjectUtil.isNotEmpty(projectNodeInfoDtos) && projectNodeInfoDtos.size() > 0) {
                    dtos.addAll(projectNodeInfoDtos);
                }
            }
        }

        //对于用户新增的块，进行顺序调整
//        dtos = againNodeIndex(dtos, projectId, nodeCode, Long.valueOf(parent.getTemplateId()));

        //查询 t_project_stakeholders 表，获取所有干系人
        List<ProjectTemplateApproveRelation> appRelations = projectTemplateApproveRelationService.list();

        //根据id对所有干系人分组
        Map<String, List<ProjectTemplateApproveRelation>> collect4 = appRelations.stream().collect(Collectors.groupingBy(e -> e.getTemplateId() + "-" + e.getTemplateGroupId()));

        //查询 t_project_stakeholders 表，获取有条件atdLambdaQueryWrapper的干系人    设置条件atdLambdaQueryWrapper，入参projectId和getOrderId为空
        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class).
                eq(ProjectStakeholders::getProjectId, submeterProjectId).isNotNull(ProjectStakeholders::getRoleCode)
                .isNull(ProjectStakeholders::getOrderId).isNotNull(ProjectStakeholders::getUserId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);

        //获取原项目主档的数据，用于带出
        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        LambdaQueryWrapper<ProjectNodeInfo> query1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, submeterProjectId)
                .eq(ProjectNodeInfo::getIsDelete, false);
        List<ProjectNodeInfoDto> dtosOld = projectNodeInfoMapper.toDto(list(query1));
        for (ProjectNodeInfoDto dto : dtosOld) {
            projectNodeInfoMap.put(dto.getNodeCode(), dto);
        }
        //获取原项目主档下已完成的二级任务
        final LambdaQueryWrapper<ProjectGroup> wrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, submeterProjectId)
                .eq(ProjectGroup::getNodeLevel, 2)
                .eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
        final List<ProjectGroup> projectGroups = projectGroupRepository.selectList(wrapper);
        final List<String> collectFin = projectGroups.stream().map(ProjectGroup::getNodeCode).collect(Collectors.toList());

        //设置条件lambdaQueryWrapper ，查询干系人数据
        LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        lambdaQueryWrapper.eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                .eq(ProjectStakeholders::getProjectId,submeterProjectId)
                .isNull(ProjectStakeholders::getOrderId);
        List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);

        //对获取的干系人去重
        List<Long> collect2 = stakeholderslist.stream().map(ProjectStakeholders::getUserId).distinct().collect(Collectors.toList());
        List<UserDto> byIds = userMapper.toDto(userService.findByIds(collect2));

        //转换成map
        Map<Long, UserDto> collect3 = byIds.stream().collect(Collectors.toMap(UserDto::getId, e -> e));

        //转换成values为ProjectStakeholders的map
        Map<String, List<ProjectStakeholders>> collect1 = stakeholderslist.stream().collect(Collectors.groupingBy(ProjectStakeholders::getRoleCode));

        //获取角色编码对应的角色ID
        Map<String, Role> roleIdMap = roleRepository.findAll().stream().collect(Collectors.toMap(Role::getRoleCode, r -> r));

        //用于配置
        for (ProjectNodeInfoDto dto : dtos) {
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                }
                if (isMobile != null && isMobile) {
                    String nodeCodeDto = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCodeDto);
                    dto.setSimpleName(simple);
                }

            } else if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
                //改为只从干系人中取值 2022 0608
                if (dto.getRoleCode() != null) {
                    List<String> list = Lists.newArrayList();
                    Arrays.stream(dto.getRoleCode().split(",")).forEach(s -> {
                        list.add(s);
                    });
                    String userName = "";
                    for (String roleCode : list) {
                        Role roleIdByCode = roleIdMap.get(roleCode);
                        if (ObjectUtil.isNotEmpty(roleIdByCode)) {
                            if (ObjectUtil.isEmpty(dto.getRoleName())) {
                                dto.setRoleName(roleIdByCode.getName());
                            } else {
                                dto.setRoleName(dto.getRoleName() + "," + roleIdByCode.getName());
                            }
                        }
                        //获取roleCode对应的干系人
                        List<ProjectStakeholders> projectStakeholders = collect1.get(roleCode);
                        if (CollUtil.isNotEmpty(projectStakeholders)) {
                            for (ProjectStakeholders one : projectStakeholders) {
                                if (one != null && null != one.getUserId()) {
                                    UserDto userDto = collect3.get(one.getUserId());
                                    if (null != userDto) {
                                        if (ObjectUtil.isNotEmpty(userDto.getEnabled()) && !userDto.getEnabled()) {
                                            continue;
                                        }
                                        if (userName.equals("")) {
                                            userName = userDto.getNickName();
                                        } else {
                                            userName = userName + "," + userDto.getNickName();
                                        }

                                    }
                                }
                            }
                        }
                    }
                    //userName 去重
                    if (StrUtil.isNotBlank(userName)) {
                        Set<String> set = new HashSet<>(StrUtil.split(userName, ","));
                        dto.setRemark(StrUtil.join(",", set));
                    }
                }

                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                }
                List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = collect4.get(dto.getTemplateId() + "-" + dto.getTemplateGroupId());
                //若存在审批节点，则给值
                if (CollUtil.isNotEmpty(projectTemplateApproveRelations)) {
                    dto.setApproveMatrixId(projectTemplateApproveRelations.get(0).getApproveMatrixId());
                }
            }
            Map<String, Long> userMap = approveTemplateDetails.stream().collect(Collectors.toMap(ProjectStakeholders::getRoleCode, ProjectStakeholders::getUserId, (v1, v2) -> v1));
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType())) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            //relationCode的判断
            updateRelationCode(projectNodeInfoMap, dto, collectFin);
        }

        //转换treeNode
        final List<ProjectNodeInfoDto> infoDtos = dtos.stream().filter(d -> ObjectUtil.isNotEmpty(d.getIsDelete()) && d.getIsDelete().equals(Boolean.FALSE)).collect(Collectors.toList());
        List<TreeNode<String>> treeNodes = infoDtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());

        //设置treeNode，配置deep
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);

        //使用Hutools TreeUtil转换树结构
        List<Tree<String>> result = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
        return result;
    }

        @Override
    public List<Tree<String>> projectTree1(Long projectId, String useCase, Boolean isMobile, int deep, Long templateId, String nodeCode, Long nodeId, String roundMarking) {

        //获取当前用户id，并判断用户是否存在
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        Long submeterProjectId = getSubmeterProjectId(projectId);
        //动态获取 t_project_node_info 表名
        util.initialize(submeterProjectId);

        //查询当前用户,并获取对应干系人的roleId
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(currentUserId);
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);

        //查询当前项目的0级数据
        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(null != projectId, ProjectGroup::getProjectId, projectId)
                .eq(null != roundMarking, ProjectGroup::getRoundMarking, roundMarking)
                .isNull(ProjectGroup::getParentId)
                .isNull(ProjectGroup::getOrderId)
                .like(ProjectGroup::getNodeCode, nodeCode.substring(0, 3))
                .last("limit 1");
        ProjectGroupDto parent = projectGroupMapper.toDto(projectGroupRepository.selectOne(project));

        //设置条件 query ，查询 t_project_group 表
        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId)
                .eq(null != roundMarking, ProjectNodeInfo::getRoundMarking, roundMarking)
                .eq(ProjectNodeInfo::getIsDelete, false);
        if (StringUtils.isNotEmpty(useCase)) {
            //判断 useCase 是否为空，修改条件 query
            query.eq(ProjectNodeInfo::getUseCase, useCase);
        }
        if (null != isMobile) {
            query.eq(ProjectNodeInfo::getIsMobile, isMobile);
        }

        //查询 t_project_group 表和 t_job_node_info 表的联查，存放到 t_project_node_info 表里
        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo1(projectId, useCase, isMobile, roleIds, templateId, parent.getRoundMarking());
        //判断 t_project_group 表是否存在 dtos
        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }

        //过滤出 t_project_node_info 表的二级节点
        List<ProjectNodeInfoDto> collect = dtos.stream().filter(e -> e.getNodeLevel() != null && e.getNodeLevel() == 2).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
        //根据当前用户，获取对应干系人的roleCode,有多个roleCode
            List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, submeterProjectId);
            //todo:其他地方需要引用
//        读写判断
//        获取的roleCode与当前节点对应的roleCode做比较
            for (ProjectNodeInfoDto dto : collect) {

                //判断当前二级任务的紧前任务是否完成
                this.showFrontWbsConfig(dto);

                //获取的roleCode与当前节点对应的roleCode做比较
                if (roleCodes.isEmpty()) {
                    dto.setIsWrite(Boolean.FALSE);
                } else {
                    //当前用户是当前项目的干系人的话，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                    OK:
                    for (String roleCode : roleCodes) {
                        //当前roleCode不为空且等于
                        if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && Arrays.stream(dto.getRoleCode().split(",")).anyMatch(s -> s.contains(roleCode))
//                            if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && dto.getRoleCode().equals(roleCode)
//                                    && parent.getIsWrite() != null && parent.getIsWrite()
                                && dto.getIsOpen() != null && dto.getIsOpen()
                        ) {
                            dto.setIsWrite(Boolean.TRUE);
                            dto.setIsHidden(Boolean.TRUE);

                            break OK;
                        } else {
                            Integer userAndRoleCode = roleRepository.getCountByUserAndRoleCode(currentUserId, JhSystemEnum.JobEnum.CSXMJL.getKey());
                            if (userAndRoleCode > 0) {
                                //是厂商项目经理的话，查询是否是该任务的审批人，不是的话，不可见该任务
                                Integer byUserAndNodeId = projectApproveRepository.getCountByUserAndNodeId(currentUserId, dto.getProjectGroupId(), projectId);
                                if (byUserAndNodeId == 0) {
                                    //当前用户是厂商项目经理，不显示非负责人、非审批人的任务
                                    dto.setIsHidden(Boolean.FALSE);
                                }
                            }
                        }
                    }
                }
//                  currentUserId == 1 || currentUserId == 488
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
                    //超级管理员，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                }
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey()) && !dto.getIsWrite()) {
                    dto.setIsWrite(Boolean.TRUE);
                    dto.setIsHidden(Boolean.TRUE);
                }

            }


            //查询出 t_project_node_info 表 的三级节点     设置条件，t_project_node_info 表 的三级
            LambdaQueryWrapper<ProjectNodeInfo> thirdQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(null != roundMarking, ProjectNodeInfo::getRoundMarking, roundMarking)
                    .in(ProjectNodeInfo::getParentId, collect.stream().map(ProjectNodeInfoDto::getTemplateId)
                            .collect(Collectors.toList()));
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(thirdQuery);

            //根据  parent_id ，对 projectNodeInfos 三级分组
            Map<Long, List<ProjectNodeInfo>> collectThree = projectNodeInfos.stream().collect(Collectors.groupingBy(ProjectNodeInfo::getParentId));

            //遍历，dtos增加三级节点
            for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
                //分组后，根据 TemplateId 获取三级
                List<ProjectNodeInfo> nodeInfos = collectThree.get(projectNodeInfo.getParentId());
                //实体集合转dto集合
                List<ProjectNodeInfoDto> projectNodeInfoDtos = projectNodeInfoMapper.toDto(nodeInfos);
                //三级节点增加到dtos
                if (ObjectUtil.isNotEmpty(projectNodeInfoDtos) && projectNodeInfoDtos.size() > 0) {
                    dtos.addAll(projectNodeInfoDtos);
                }
            }
        }

        //对于用户新增的块，进行顺序调整
//        dtos = againNodeIndex(dtos, projectId, nodeCode, Long.valueOf(parent.getTemplateId()));

        //查询 t_project_stakeholders 表，获取所有干系人
        List<ProjectTemplateApproveRelation> appRelations = projectTemplateApproveRelationService.list();

        //根据id对所有干系人分组
        Map<String, List<ProjectTemplateApproveRelation>> collect4 = appRelations.stream().collect(Collectors.groupingBy(e -> e.getTemplateId() + "-" + e.getTemplateGroupId()));

        //查询 t_project_stakeholders 表，获取有条件atdLambdaQueryWrapper的干系人    设置条件atdLambdaQueryWrapper，入参projectId和getOrderId为空
        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class).
                eq(ProjectStakeholders::getProjectId, submeterProjectId).isNotNull(ProjectStakeholders::getRoleCode)
                .isNull(ProjectStakeholders::getOrderId).isNotNull(ProjectStakeholders::getUserId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);

        //获取原项目主档的数据，用于带出
        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        LambdaQueryWrapper<ProjectNodeInfo> query1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, submeterProjectId)
                .eq(ProjectNodeInfo::getIsDelete, false);
        List<ProjectNodeInfoDto> dtosOld = projectNodeInfoMapper.toDto(list(query1));
        for (ProjectNodeInfoDto dto : dtosOld) {
            projectNodeInfoMap.put(dto.getNodeCode(), dto);
        }
        //获取原项目主档下已完成的二级任务
        final LambdaQueryWrapper<ProjectGroup> wrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, submeterProjectId)
                .eq(ProjectGroup::getNodeLevel, 2)
                .eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
        final List<ProjectGroup> projectGroups = projectGroupRepository.selectList(wrapper);
        final List<String> collectFin = projectGroups.stream().map(ProjectGroup::getNodeCode).collect(Collectors.toList());

        //设置条件lambdaQueryWrapper ，查询干系人数据
        LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        lambdaQueryWrapper.eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                .eq(ProjectStakeholders::getProjectId,submeterProjectId)
                .isNull(ProjectStakeholders::getOrderId);
        List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);

        //对获取的干系人去重
        List<Long> collect2 = stakeholderslist.stream().map(ProjectStakeholders::getUserId).distinct().collect(Collectors.toList());
        List<UserDto> byIds = userMapper.toDto(userService.findByIds(collect2));

        //转换成map
        Map<Long, UserDto> collect3 = byIds.stream().collect(Collectors.toMap(UserDto::getId, e -> e));

        //转换成values为ProjectStakeholders的map
        Map<String, List<ProjectStakeholders>> collect1 = stakeholderslist.stream().collect(Collectors.groupingBy(ProjectStakeholders::getRoleCode));

        //获取角色编码对应的角色ID
        Map<String, Role> roleIdMap = roleRepository.findAll().stream().collect(Collectors.toMap(Role::getRoleCode, r -> r));

        //用于配置
        for (ProjectNodeInfoDto dto : dtos) {
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                }
                if (isMobile != null && isMobile) {
                    String nodeCodeDto = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCodeDto);
                    dto.setSimpleName(simple);
                }

            } else if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
                //改为只从干系人中取值 2022 0608
                if (dto.getRoleCode() != null) {
                    List<String> list = Lists.newArrayList();
                    Arrays.stream(dto.getRoleCode().split(",")).forEach(s -> {
                        list.add(s);
                    });
                    String userName = "";
                    for (String roleCode : list) {
                        Role roleIdByCode = roleIdMap.get(roleCode);
                        if (ObjectUtil.isNotEmpty(roleIdByCode)) {
                            if (ObjectUtil.isEmpty(dto.getRoleName())) {
                                dto.setRoleName(roleIdByCode.getName());
                            } else {
                                dto.setRoleName(dto.getRoleName() + "," + roleIdByCode.getName());
                            }
                        }
                        //获取roleCode对应的干系人
                        List<ProjectStakeholders> projectStakeholders = collect1.get(roleCode);
                        if (CollUtil.isNotEmpty(projectStakeholders)) {
                            for (ProjectStakeholders one : projectStakeholders) {
                                if (one != null && null != one.getUserId()) {
                                    UserDto userDto = collect3.get(one.getUserId());
                                    if (null != userDto) {
                                        if (ObjectUtil.isNotEmpty(userDto.getEnabled()) && !userDto.getEnabled()) {
                                            continue;
                                        }
                                        if (userName.equals("")) {
                                            userName = userDto.getNickName();
                                        } else {
                                            userName = userName + "," + userDto.getNickName();
                                        }

                                    }
                                }
                            }
                        }
                    }
                    //userName 去重
                    if (StrUtil.isNotBlank(userName)) {
                        Set<String> set = new HashSet<>(StrUtil.split(userName, ","));
                        dto.setRemark(StrUtil.join(",", set));
                    }
                }

                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                }
                List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = collect4.get(dto.getTemplateId() + "-" + dto.getTemplateGroupId());
                //若存在审批节点，则给值
                if (CollUtil.isNotEmpty(projectTemplateApproveRelations)) {
                    dto.setApproveMatrixId(projectTemplateApproveRelations.get(0).getApproveMatrixId());
                }
            }
            Map<String, Long> userMap = approveTemplateDetails.stream().collect(Collectors.toMap(ProjectStakeholders::getRoleCode, ProjectStakeholders::getUserId, (v1, v2) -> v1));
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType())) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            //relationCode的判断
            updateRelationCode(projectNodeInfoMap, dto, collectFin);
        }

        //转换treeNode
        final List<ProjectNodeInfoDto> infoDtos = dtos.stream().filter(d -> ObjectUtil.isNotEmpty(d.getIsDelete()) && d.getIsDelete().equals(Boolean.FALSE)).collect(Collectors.toList());
        List<TreeNode<String>> treeNodes = infoDtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());

        //设置treeNode，配置deep
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);

        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
    }

    private String showProjectStakeholdersByRoleCode(String roleCode, Long projectId) {
        //获取roleCode对应的干系人
        String userName = "";
        //     设置条件lambdaQueryWrapper
        LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.
                lambdaQuery(ProjectStakeholders.class);
        lambdaQueryWrapper.eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.
                STA_STATUS0.getKey())
                .eq(ProjectStakeholders::getProjectId, getSubmeterProjectId(projectId))
                .isNull(ProjectStakeholders::getOrderId);

//     根据条件lambdaQueryWrapper，查询干系人数据
        List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.
                list(lambdaQueryWrapper);

//     转换成values为ProjectStakeholders的map
        Map<String, List<ProjectStakeholders>> collect1 = stakeholderslist.stream().
                collect(Collectors.groupingBy(ProjectStakeholders::getRoleCode));

//     对获取的干系人去重
        List<Long> collect2 = stakeholderslist.stream().map(ProjectStakeholders::getUserId).
                distinct().collect(Collectors.toList());

        List<UserDto> byIds = userMapper.toDto(userService.findByIds(collect2));
//     转换成map
        Map<Long, UserDto> collect3 = byIds.stream().collect(Collectors.toMap(UserDto::getId, e -> e));


        List<ProjectStakeholders> projectStakeholders = collect1.get(roleCode);
        if (CollUtil.isNotEmpty(projectStakeholders)) {
            for (ProjectStakeholders one : projectStakeholders) {
                if (one != null && null != one.getUserId()) {
                    UserDto userDto = collect3.get(one.getUserId());
                    if (null != userDto) {
                        if (ObjectUtil.isNotEmpty(userDto.getEnabled()) && !userDto.getEnabled()) {
                            continue;
                        }
                        if (userName.equals("")) {
                            userName = userDto.getNickName();
                        } else {
                            userName = userName + "," + userDto.getNickName();
                        }

                    }
                }
            }
        }
        return userName;
    }

    private void showFrontWbsConfig(ProjectNodeInfoDto dto) {
        String frontWbsConfig = dto.getFrontWbsConfig();
        if (ObjectUtils.isNotEmpty(frontWbsConfig)) {
            Boolean statusFlag = Boolean.TRUE;
            JSONArray jsonArray = new JSONArray(frontWbsConfig);
            String unFinish = "";
            String urgentTaskShowMsg = "";

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                String type = object.getStr("type");
                String wbs = object.getStr("wbs");
                if ("FS".equals(type)) {
                    //查找当前节点的紧前是否已完成
                    LambdaQueryWrapper<ProjectGroup> lastLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                            .eq(ProjectGroup::getProjectId, dto.getProjectId())
                            .eq(ObjectUtil.isNotEmpty(dto.getRoundMarking()), ProjectGroup::getRoundMarking, dto.getRoundMarking())
                            .eq(ProjectGroup::getNodeCode, wbs)
                            .last("LIMIT 1");
                    ProjectGroup one = projectGroupService.getOne(lastLambdaQueryWrapper);
                    if (ObjectUtil.isNotEmpty(one)) {
                        if (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(one.getNodeStatus())) {
                            //当前紧前任务没有完成
                            statusFlag = Boolean.FALSE;
                            unFinish = unFinish + (ObjectUtil.isNotEmpty(one) ? one.getNodeName() + "," : "当前上一个节点数据没查询到");
                        }
                        String userNames = this.showProjectStakeholdersByRoleCode(one.getRoleCode(), one.getProjectId());
                        urgentTaskShowMsg += "前置任务：" + one.getNodeName() + "【" + JhSystemEnum.NodeStatusEnum.getNodeStatus(one.getNodeStatus()) + "】" + "、责任人：" + userNames + ";";
//                        if (one.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey())) {
//                            //当前项目是深化方案，查询关联的设计图纸状态
//                            List<String> list = projectGroupExpandRepository.getDrawingStatus(dto.getProjectId(),dto.getNodeCode(),one.getNodeCode());
//                            String drawingStatus= "";
//                            for (int i1 = 0; i1 < list.size(); i1++) {
//                                drawingStatus=ObjectUtil.isNotEmpty(drawingStatus)?drawingStatus+","+list.get(i1):list.get(i1);
//                            }
//                            urgentTaskShowMsg += "前置任务：" + one.getNodeName() + "【" + JhSystemEnum.NodeStatusEnum.getNodeStatus(one.getNodeStatus()) + "】" + "、责任人：" + userNames + "，关联图纸【"+drawingStatus+"】";
//                        }else{
//                            urgentTaskShowMsg += "前置任务：" + one.getNodeName() + "【" + JhSystemEnum.NodeStatusEnum.getNodeStatus(one.getNodeStatus()) + "】" + "、责任人：" + userNames + ";";
//                        }
                    }
                }
            }
            dto.setUrgentTaskShowMsg(urgentTaskShowMsg);
            if (!statusFlag) {
                unFinish = unFinish.substring(0, unFinish.length() - 1);
                dto.setABooleanUrgentTask(Boolean.TRUE);
                dto.setUrgentTaskMsg("此任务有紧前任务【" + unFinish + "】尚未完成，请先处理前置任务");
            }
        }
    }

    /**
     * 用于修改relationCode的使用范围
     */
    private List<ProjectNodeInfoDto> getProjectNodeInfoDtos(Long projectId, String nodeCode) {
        LambdaQueryWrapper<ProjectNodeInfo> like = Wrappers.lambdaQuery(ProjectNodeInfo.class);
        if (nodeCode == null) {
            like.eq(ProjectNodeInfo::getProjectId, projectId);
        } else {
            like.eq(ProjectNodeInfo::getProjectId, projectId)
                    .like(ProjectNodeInfo::getNodeCode, nodeCode.substring(0, 3));
        }
        List<ProjectNodeInfoDto> projectNodeInfos = projectNodeInfoMapper.toDto(projectNodeInfoRepository.selectList(like));
        return projectNodeInfos;
    }

    /**
     * 获取当前0级模板对应的所有dtos,用于relationCode
     */
    private void updateRelationCode(Map<String, ProjectNodeInfoDto> projectNodeInfoMap, ProjectNodeInfoDto dto, List<String> collectFin) {
        if (StringUtils.isNotEmpty(dto.getRelationCode())) {
//            ProjectNodeInfoDto temp = projectNodeInfoMap.get(dto.getRelationCode());
//            if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(dto.getNodeType()) && (null != temp)) {
//                //如果是附件类型的关联
//                dto.setStartSign(temp.getNodeId());
//            } else if (null != temp) {
//                dto.setRemark(temp.getRemark());
//            }

            String[] split = dto.getRelationCode().split(",");
            String startSign = null;
            String remark = null;
            for (String relationCode : split) {
                ProjectNodeInfoDto temp = projectNodeInfoMap.get(relationCode);
                if ((JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(dto.getNodeType()) ||
                        JhSystemEnum.NodeType.FILE_SHOW.getValue().equals(dto.getNodeType())
                ) && (null != temp)
                        && ObjectUtil.isNotEmpty(collectFin) && collectFin.contains(relationCode.substring(0, 9))
                ) {

                    if (ObjectUtils.isNotEmpty(startSign)) {
                        startSign = startSign + "," + temp.getNodeId();
                    } else {
                        startSign = temp.getNodeId();
                    }

                } else if (null != temp) {
                    String[] nodeCodes = {"eng-00135029", "eng-00135024", "eng-00129030", "eng-00129031", "eng-00129032", "eng-00129033", "eng-00129034", "eng-00129035", "eng-00129036", "eng-00129037", "eng-00129038", "eng-00129039", "eng-00129040", "eng-00129041", "eng-00129042", "eng-00129043", "eng-00129044"};

                    if (ObjectUtil.isNotEmpty(dto.getRemark()) && (Arrays.stream(nodeCodes).anyMatch(str -> str.equals(dto.getNodeCode())))) {
                        remark = dto.getRemark();
                    } else {
                        if (ObjectUtils.isNotEmpty(remark)) {
                            remark = remark + "," + temp.getRemark();
                        } else {
                            remark = temp.getRemark();
                        }
                    }

                }
            }
            if (ObjectUtils.isNotEmpty(startSign)) {
                dto.setStartSign(startSign);
            }
            if (ObjectUtils.isNotEmpty(remark)) {
                dto.setRemark(remark);
            }
        }
    }

    @Override
    public List<Tree<String>> projectTree(Long projectId, String useCase, Boolean isMobile, int deep) {
        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(null != projectId, ProjectGroup::getProjectId, projectId)
                .isNull(ProjectGroup::getParentId).isNull(ProjectGroup::getOrderId);
        ProjectGroupDto parent = projectGroupMapper.toDto(projectGroupRepository.selectOne(project));
        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId);

        if (StringUtils.isNotEmpty(useCase)) {
            query.eq(ProjectNodeInfo::getUseCase, useCase);
        }

        if (null != isMobile) {
            query.eq(ProjectNodeInfo::getIsMobile, isMobile);
        }

        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);

        // List<ProjectNodeInfoDto> dtos = projectNodeInfoMapper.toDto(list(query));
        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo(projectId, useCase, isMobile, roleIds, null);
        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }

        for (ProjectNodeInfoDto dto : dtos) {
            if (dto.getIsWrite() != null && dto.getIsWrite() && dto.getIsOpen() != null && dto.getIsOpen()) {
                dto.setIsWrite(Boolean.TRUE);
            } else {
                dto.setIsWrite(Boolean.FALSE);
            }
            //临时将groupid 给nodeId
            dto.setNodeId(dto.getProjectGroupId());
        }

        List<ProjectNodeInfoDto> collect = dtos.stream().filter(e -> e.getNodeLevel() != null && e.getNodeLevel() == 2)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            LambdaQueryWrapper<ProjectNodeInfo> twoQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .in(ProjectNodeInfo::getTemplateQueueId, collect.stream()
                            .map(ProjectNodeInfoDto::getTemplateQueueId).collect(Collectors.toList()));
            List<ProjectNodeInfo> projectNodeInfoList = projectNodeInfoRepository.selectList(twoQuery);

            LambdaQueryWrapper<ProjectNodeInfo> thirdQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .in(ProjectNodeInfo::getParentId, collect.stream().map(ProjectNodeInfoDto::getTemplateId)
                            .collect(Collectors.toList()));
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(thirdQuery);

            Map<Long, List<ProjectNodeInfo>> collect1 = projectNodeInfos.stream().collect(Collectors
                    .groupingBy(ProjectNodeInfo::getParentId));
            //todo 2023.09.06暂时删除
            for (ProjectNodeInfo projectNodeInfo : projectNodeInfoList) {
                List<ProjectNodeInfo> projectNodeInfoList1 = collect1.get(projectNodeInfo.getTemplateId());
                List<ProjectNodeInfoDto> projectNodeInfoDtos = projectNodeInfoMapper.toDto(projectNodeInfoList1);
                // projectNodeInfoDtos = againNodeIndex(projectNodeInfoDtos, projectId, projectNodeInfo.getNodeCode(),projectNodeInfo.getTemplateId());
                dtos.addAll(projectNodeInfoDtos);
            }
        }

//        List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(Long.parseLong(dto.getTemplateId()), Long.parseLong(dto.getTemplateGroupId()));
        List<ProjectTemplateApproveRelation> appRelations = projectTemplateApproveRelationService.list();
        Map<String, List<ProjectTemplateApproveRelation>> collect4 = appRelations.stream()
                .collect(Collectors.groupingBy(e -> e.getTemplateId() + "-" + e.getTemplateGroupId()));

        /*for (int i = 0; i < dtos.size(); i++) {
            if (null != dtos.get(i).getNodeLevel() && dtos.get(i).getNodeLevel() == 2) {
                //查找二三级数据
                String templateQueueId = dtos.get(i).getTemplateQueueId();
                logger.info(templateQueueId + "----" + dtos.get(i).getNodeCode());
                LambdaQueryWrapper<ProjectNodeInfo> twoQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getTemplateQueueId, templateQueueId);
                ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(twoQuery);
                LambdaQueryWrapper<ProjectNodeInfo> thirdQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getParentId, projectNodeInfo.getTemplateId());
                List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(thirdQuery);
                List<ProjectNodeInfoDto> projectNodeInfoDtos = projectNodeInfoMapper.toDto(projectNodeInfos);
                projectNodeInfoDtos = againNodeIndex(projectNodeInfoDtos, projectId, projectNodeInfo.getNodeCode());
                dtos.addAll(projectNodeInfoDtos);
            } else {
                continue;
            }
        }*/
        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        atdLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectId)
                .isNull(ProjectStakeholders::getOrderId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);
        Map<String, List<ContractManageDto>> node0014043 = new HashMap<>();
        Map<String, List<FeeInfoDto>> feeQuerynode = new HashMap<>();
        Map<String, List<FeeInfoDto>> otherfeeQuerynode = new HashMap<>();
        Map<String, List<ApprovedFormDto>> approvedFormsnode = new HashMap();
        Map<String, List<CompletionAcceptancePictureDto>> picturenode = new HashMap();
        Map<String, RepayDto> acceptnode = new HashMap();
        Map<String, List<OrderInfo>> orderMap = new HashMap();
        Map<String, List<OrderDetailDto>> orderDetailMap = new HashMap();
        //用于存信息
        for (ProjectNodeInfoDto dto : dtos) {
//            if (ObjectUtil.isNotEmpty(dto.getNodeCode()) && "con-0014043".equals(dto.getNodeCode())) {
//                LambdaQueryWrapper contractQuery = Wrappers.lambdaQuery(ContractManage.class)
//                        .eq(ContractManage::getProjectId, dto.getProjectId())
//                        .eq(ContractManage::getIsDelete, Boolean.FALSE);
//                List<ContractManage> contractList = contractManageService.list(contractQuery);
//                dto.setContractManages(contractManageMapper.toDto(contractList));
//            }
//
//            if (ObjectUtil.isNotEmpty(dto.getNodeCode()) && "con-0014373".equals(dto.getNodeCode())) {
//                LambdaQueryWrapper feeQuery = Wrappers.lambdaQuery(FeeInfo.class)
//                        .eq(FeeInfo::getProjectId, dto.getProjectId())
//                        .notLike(FeeInfo::getUnitName, "其他")
//                        .eq(FeeInfo::getIsDelete, Boolean.FALSE);
//                List<FeeInfo> feeList = feeInfoService.list(feeQuery);
//                dto.setFeeInfos(feeInfoMapper.toDto(feeList));
//                LambdaQueryWrapper otherFeeQuery = Wrappers.lambdaQuery(FeeInfo.class)
//                        .eq(FeeInfo::getProjectId, dto.getProjectId())
//                        .likeRight(FeeInfo::getUnitName, "其他")
//                        .eq(FeeInfo::getIsDelete, Boolean.FALSE);
//                List<FeeInfo> otherFeeList = feeInfoService.list(otherFeeQuery);
//                dto.setOtherFeeInfos(feeInfoMapper.toDto(otherFeeList));
//            }
//
//            if (ObjectUtil.isNotEmpty(dto.getNodeCode()) && "con-0014405".equals(dto.getNodeCode())) {
//                LambdaQueryWrapper approvedQuery = Wrappers.lambdaQuery(ApprovedForm.class)
//                        .eq(ApprovedForm::getProjectId, dto.getProjectId())
//                        .eq(ApprovedForm::getIsDelete, Boolean.FALSE);
//                List<ApprovedForm> approvedList = approvedFormService.list(approvedQuery);
//                dto.setApprovedForms(approvedFormMapper.toDto(approvedList));
//            }
//
//            if (ObjectUtil.isNotEmpty(dto.getNodeCode()) && "con-0011153".equals(dto.getNodeCode())) {
//                LambdaQueryWrapper completionAcceptancePictureQuery = Wrappers.lambdaQuery(CompletionAcceptancePicture.class)
//                        .eq(CompletionAcceptancePicture::getProjectId, projectId)
//                        .eq(CompletionAcceptancePicture::getIsDelete, Boolean.FALSE);
//                List<CompletionAcceptancePicture> pictureList = completionAcceptancePictureService
//                        .list(completionAcceptancePictureQuery);
//                dto.setCompletionAcceptancePictures(completionAcceptancePictureMapper.toDto(pictureList));
//            }
//
//            //首付对账单付款明细
//            if ("con-0040421".equals(dto.getNodeCode()) || "con-0013721".equals(dto.getNodeCode()) || "con-0013821"
//                    .equals(dto.getNodeCode())) {
//                String queryCode = dto.getNodeCode().substring(0, 9);
//                RepayDto repayDto = repayService.findByProjectIdAndCode(Long.valueOf(dto.getProjectId()), queryCode);
//                if (ObjectUtil.isEmpty(repayDto)) {
//                    repayDto = new RepayDto();
//                    List<RepayItemDto> repayItemDtos = new LinkedList<>();
//                    repayDto.setRepayItemDtoList(repayItemDtos);
//                }
//                dto.setRepay(repayDto);
//            }
//
//            //闭店-资产处置物料列表展示
//            if (null != dto.getNodeCode() && "con-0014802".equals(dto.getNodeCode())) {
//                LambdaQueryWrapper<ProjectInfo> projectInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
//                        .eq(ProjectInfo::getProjectId, projectId);
//                ProjectInfo projectInfo = Optional.ofNullable(projectInfoRepository
//                        .selectOne(projectInfoLambdaQueryWrapper)).orElseGet(ProjectInfo::new);
//                Long storeId = projectInfo.getStoreId();
//                LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderInfo.class)
//                        .eq(OrderInfo::getStoreId, storeId);
//                List<OrderInfo> orderInfos = orderInfoService.list(orderInfoLambdaQueryWrapper);
//                if (ObjectUtil.isEmpty(orderInfos)) {
//                    List<OrderDetailDto> orderDetails = new ArrayList<>();
//                    dto.setOrderDetails(orderDetails);
//                } else {
//                    for (OrderInfo orderInfo : orderInfos) {
//                        Long orderId = orderInfo.getOrderId();
//                        LambdaQueryWrapper<OrderDetail> orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
//                                .eq(OrderDetail::getOrderId, orderId);
//                        List<OrderDetail> orderDetails = orderDetailRepository.selectList(orderDetailQueryWrapper);
//                        if (ObjectUtil.isNotEmpty(orderDetails) && orderDetails.size() > 0) {
//                            for (OrderDetail orderDetail : orderDetails) {
//                                orderDetail.setServiceNum(null);
//                                orderDetail.setToyNum(null);
//                                orderDetail.setDailyNum(null);
//                                orderDetail.setFastNum(null);
//                                orderDetail.setSpinNum(null);
//                            }
//                            dto.setOrderDetails(orderDetailMapper.toDto(orderDetails));
//                        }
//                    }
//                }
//            }

            projectNodeInfoMap.put(dto.getNodeCode(), dto);

        }

        //用于配置
        for (ProjectNodeInfoDto dto : dtos) {
//            //竣工验收装修厂商名称塞值
//            if (ObjectUtil.isNotEmpty(projectNodeInfoMap.get("con-0010302"))) {
//                String remark = projectNodeInfoMap.get("con-0010302").getRemark();
//                SupplierInfo sup = Optional.ofNullable(supplierInfoRepository.selectById(remark))
//                        .orElseGet(SupplierInfo::new);
//                ProjectNodeInfoDto projectNodeInfoDto = projectNodeInfoMap.get("con-0011111");
//                projectNodeInfoDto.setRemark(sup.getSupNameCn());
//            } else if (ObjectUtil.isNotEmpty(projectNodeInfoMap.get("con-0040309"))) {
//                String remark1 = projectNodeInfoMap.get("con-0040309").getRemark();
//                SupplierInfo sup = Optional.ofNullable(supplierInfoRepository.selectById(remark1))
//                        .orElseGet(SupplierInfo::new);
//                ProjectNodeInfoDto projectNodeInfoDto = projectNodeInfoMap.get("con-0011111");
//                projectNodeInfoDto.setRemark(sup.getSupNameCn());
//            }
//
//            if ("con-0011112".equals(dto.getNodeCode())) {
//                dto.setRemark(orderInfoService.getSupByProjectId(Long.valueOf(dto.getProjectId()), "hj"));
//            } else if ("con-0011113".equals(dto.getNodeCode())) {
//                dto.setRemark(orderInfoService.getSupByProjectId(Long.valueOf(dto.getProjectId()), "dj"));
//            }


            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                /*  String startDate=dto.getPredictStartDate().toString();
                  String endDate=dto.getPredictEndDate().toString();
                  String status=calPhaseStatus(startDate,endDate);
                  dto.setPhaseStatus(status);*/
                }
                if (isMobile != null && isMobile) {
                    String nodeCode = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCode);
                    dto.setSimpleName(simple);
                }

            }

            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
                //查找二三级数据
             /*   String templateQueueId = dto.getTemplateQueueId();
                LambdaQueryWrapper<ProjectNodeInfo> thirdQuery=Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId,projectId).eq(ProjectNodeInfo::getTemplateQueueId,templateQueueId);
                List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(thirdQuery);
                dtos.addAll(projectNodeInfoMapper.toDto(projectNodeInfos));*/
//                if(StringUtils.isNotEmpty(dto.getRoleCode())){
//                    String userName = getUserByRole(projectId,dto.getRoleCode());
//                    if(StringUtils.isNotEmpty(userName)){
//                        dto.setRemark(userName);
//                    }
//                }
               /* String remark = dto.getRemark();
                if (StringUtils.isNotEmpty(remark)) {
                    List<String> userIds = Arrays.asList(remark.split(","));
                    List<Long> collect = userIds.stream().map(Long::parseLong).collect(Collectors.toList());
                    UserQueryCriteria userQueryCriteria = new UserQueryCriteria();
                    userQueryCriteria.setUserId(collect);
                    List<UserDto> userDtos = userService.queryAll(userQueryCriteria);
                    if (CollectionUtils.isNotEmpty(userDtos)) {
                        dto.setRemark(userDtos.stream().map(UserDto::getUsername).collect(Collectors.joining(",")));
                    }
                }*/
                //改为只从干系人中取值 2022 0608
                if (dto.getRoleCode() != null) {
                    List<String> list = Lists.newArrayList();
                    Arrays.stream(dto.getRoleCode().split(",")).forEach(s -> {
                        list.add(s);
                    });
                    String userName = "";

                    LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers
                            .lambdaQuery(ProjectStakeholders.class);
                    lambdaQueryWrapper.in(ProjectStakeholders::getRoleCode, list)
                            .eq(ProjectStakeholders::getShakeholderStatus,
                                    JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                            .eq(ProjectStakeholders::getProjectId, projectId)
                            .isNull(ProjectStakeholders::getOrderId);
                    List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);
                    List<Long> collect2 = stakeholderslist.stream().map(ProjectStakeholders::getUserId).distinct()
                            .collect(Collectors.toList());
                    List<UserDto> byIds = userMapper.toDto(userService.findByIds(collect2));

                    Map<Long, UserDto> collect3 = byIds.stream().collect(Collectors.toMap(UserDto::getId, e -> e));


                    Map<String, List<ProjectStakeholders>> collect1 = stakeholderslist.stream().collect(Collectors
                            .groupingBy(ProjectStakeholders::getRoleCode));
                    for (String roleCode : list) {
                        Role roleIdByCode = roleRepository.findRoleIdByCode(roleCode);
                        if (ObjectUtil.isNotEmpty(roleIdByCode)) {
                            if (ObjectUtil.isEmpty(dto.getRoleName())) {
                                dto.setRoleName(roleIdByCode.getName());
                            } else {
                                dto.setRoleName(dto.getRoleName() + "," + roleIdByCode.getName());
                            }
                        }
                        List<ProjectStakeholders> projectStakeholders = collect1.get(roleCode);
                        if (CollUtil.isNotEmpty(projectStakeholders)) {
                            for (ProjectStakeholders one : projectStakeholders) {
                                if (one != null && null != one.getUserId()) {
                                    UserDto userDto = collect3.get(one.getUserId());
                                    if (null != userDto) {
                                        if (ObjectUtil.isNotEmpty(userDto.getEnabled()) && !userDto.getEnabled()) {
                                            continue;
                                        }
                                        if (userName.equals("")) {
                                            userName = userDto.getNickName();
                                        } else {
                                            userName = userName + "," + userDto.getNickName();
                                        }

                                    }
                                }
                            }
                        }
                        dto.setRemark(userName);
                    }
                }

                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");
                }
                List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = collect4.get(dto.getTemplateId() + "-" + dto.getTemplateGroupId());
                //若存在审批节点，则给值
                if (CollUtil.isNotEmpty(projectTemplateApproveRelations)) {
                    dto.setApproveMatrixId(projectTemplateApproveRelations.get(0).getApproveMatrixId());
                }

            }
            Map<String, Long> userMap = approveTemplateDetails.stream().collect(Collectors.toMap(ProjectStakeholders::getRoleCode, ProjectStakeholders::getUserId, (v1, v2) -> v1));
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType())) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            updateRelationCode(projectNodeInfoMap, dto, null);
        }
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);
        //使用Hutools TreeUtil转换树结构
//        List<List<Tree<String>>> builds = new ArrayList<>();
//        for (ProjectGroupDto parent :
//                parents) {
        List<Tree<String>> build = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
//            builds.add(build);
//        }
        return build;
    }

    /**
     * 看板数据
     *
     * @param projectId
     * @param useCase
     * @param isMobile
     * @param deep
     * @return
     */
    private static Map<String, Map<Long, List<List<Tree<String>>>>> TreeForBoardMap = new HashMap<>();

    //    private static Object TreeForBoardLock = new Object();
    @Override
    public List<List<Tree<String>>> projectTreeForBoard(Long projectId, String useCase, Boolean isMobile, int deep) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        //项目九宫格只查询和项目主档的projectID相关的任务，所以这边处理一下projectID
        projectId = getSubmeterProjectId(projectId);
        ProjectInfo byId = projectInfoService.getById(projectId);
        if (byId.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue()) && TreeForBoardMap.get(projectId + ":" + useCase + ":" + isMobile + ":" + deep + ":" + currentUserId) != null) {
            Map<Long, List<List<Tree<String>>>> timeMap = TreeForBoardMap.get(projectId + ":" + useCase + ":" + isMobile + ":" + deep + ":" + currentUserId);
            for (Long time : timeMap.keySet()) {
                if (new Date().getTime() - time > 90 * 1000L) {
                    timeMap.remove(time);
                } else {
                    return timeMap.get(time);
                }
            }
        }

        String templateCode = null;
        if (ObjectUtil.isNotEmpty(byId) && ObjectUtil.isNotEmpty(byId.getProjectType()) && KidsSystemEnum.ProjectTypeEnum.NEW.getValue().equals(byId.getProjectType())) {
            templateCode = JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "," + JhSystemEnum.TaskPhaseEnum.DESIGN.getKey() + "," + JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey();
        }
        List<ProjectGroup> projectGroups = projectGroupRepository.selectListByProjectId(projectId, templateCode);
        if (ObjectUtil.isEmpty(projectGroups)) {
            throw new BadRequestException("当前暂无营建模版数据，请先确定是否进行营建对接启动！");
        }
        List<ProjectGroupDto> parents = projectGroupMapper.toDto(projectGroups);
        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId);
        util.initialize(projectId);
        if (StringUtils.isNotEmpty(useCase)) {
            query.eq(ProjectNodeInfo::getUseCase, useCase);
        }
        if (null != isMobile) {
            query.eq(ProjectNodeInfo::getIsMobile, isMobile);
        }
        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        atdLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);
        Map<String, Long> userMap = new HashMap<>();
        Map<String, List<ProjectStakeholders>> userCodeListMap = new HashMap<>();

        for (ProjectStakeholders sta : approveTemplateDetails) {
            userMap.put(sta.getRoleCode(), sta.getUserId());
            List<ProjectStakeholders> stakeholderslist;
            if (userCodeListMap.get(sta.getRoleCode()) != null) {
                stakeholderslist = userCodeListMap.get(sta.getRoleCode());
            } else {
                stakeholderslist = new ArrayList<>();
                userCodeListMap.put(sta.getRoleCode(), stakeholderslist);
            }
            stakeholderslist.add(sta);
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(currentUserId);
//        根据当前用户，获取对应干系人的roleCode,有多个roleCode

        List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, projectId);
        // List<ProjectNodeInfoDto> dtos = projectNodeInfoMapper.toDto(list(query));
        //todo add
        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo(projectId, useCase, isMobile, roleIds, null);

        if (null == parents || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }
        List<Long> templeteList = new ArrayList<>();
        for (ProjectNodeInfoDto dto : dtos) {
            templeteList.add(Long.parseLong(dto.getTemplateId()));
        }
        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
        ptarLambdaQueryWrapper.in(ProjectTemplateApproveRelation::getTemplateId, templeteList)
                .eq(ProjectTemplateApproveRelation::getIsDelete, 0)
                .isNotNull(ProjectTemplateApproveRelation::getTemplateGroupId);
        List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = projectTemplateApproveRelationRepository.selectList(ptarLambdaQueryWrapper);
        Map<String, Map<String, ProjectTemplateApproveRelation>> relationMap = new HashMap<>();
        for (ProjectTemplateApproveRelation ob : projectTemplateApproveRelations) {
            Map<String, ProjectTemplateApproveRelation> relationMap1 = null;
            if (relationMap.get(ob.getTemplateId() + "") != null) {
                relationMap1 = relationMap.get(ob.getTemplateId() + "");
            } else {
                relationMap1 = new HashMap<>();
                relationMap.put(ob.getTemplateId() + "", relationMap1);
            }
            relationMap1.put(ob.getTemplateGroupId() + "", ob);
        }
        //List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(Long.parseLong(dto.getTemplateId()), Long.parseLong(dto.getTemplateGroupId()));

        for (ProjectNodeInfoDto dto : dtos) {
            projectNodeInfoMap.put(dto.getNodeCode(), dto);
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
//                //判断当前二级任务的紧前任务是否完成  当前接口为返回九宫格数据，暂时不需要作此判断
//                this.showFrontWbsConfig(dto);

                //获取的roleCode与当前节点对应的roleCode做比较
                if (roleCodes.isEmpty()) {
                    dto.setIsWrite(Boolean.FALSE);
                } else {
                    //当前用户是当前项目的干系人的话，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                    OK:
                    for (String roleCode : roleCodes) {
                        //当前roleCode不为空且等于
                        if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && Arrays.stream(dto.getRoleCode().split(",")).anyMatch(s -> s.contains(roleCode))
                                && dto.getIsOpen() != null && dto.getIsOpen()
                        ) {
                            dto.setIsWrite(Boolean.TRUE);
                            dto.setIsHidden(Boolean.TRUE);
                            break OK;
                        } else {
                            Integer userAndRoleCode = roleRepository.getCountByUserAndRoleCode(currentUserId, JhSystemEnum.JobEnum.CSXMJL.getKey());
                            if (userAndRoleCode > 0) {
                                //是厂商项目经理的话，查询是否是该任务的审批人，不是的话，不可见该任务
                                Integer byUserAndNodeId = projectApproveRepository.getCountByUserAndNodeId(currentUserId, dto.getProjectGroupId(), projectId);
                                if (byUserAndNodeId == 0) {
                                    //当前用户是厂商项目经理，不显示非负责人、非审批人的任务
                                    dto.setIsHidden(Boolean.FALSE);
                                }
                            }
                        }
                    }
                }
                //临时将groupid 给nodeId
                dto.setNodeId(dto.getProjectGroupId());
//                if (currentUserId == 1 || currentUserId == 488) {
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
                    //超级管理员，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                }
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey()) && !dto.getIsWrite()) {
                    dto.setIsWrite(Boolean.TRUE);
                    dto.setIsHidden(Boolean.TRUE);
                }
                //改为只从干系人中取值 2022 0608
                if (dto.getRoleCode() != null) {
                    List<String> list = Lists.newArrayList();
                    Arrays.stream(dto.getRoleCode().split(",")).forEach(s -> {
                        list.add(s);
                    });
                    String userName = "";
                    for (String roleCode : list) {
                        Role roleIdByCode = roleRepository.findRoleIdByCode(roleCode);
                        if (ObjectUtil.isNotEmpty(roleIdByCode)) {
                            if (ObjectUtil.isEmpty(dto.getRoleName())) {
                                dto.setRoleName(roleIdByCode.getName());
                            } else {
                                dto.setRoleName(dto.getRoleName() + "," + roleIdByCode.getName());
                            }
                        }
                        if (userCodeListMap.get(roleCode) != null && userCodeListMap.get(roleCode).size() > 0) {
                            for (ProjectStakeholders one : userCodeListMap.get(roleCode)) {
                                if (one != null && null != one.getUserId()) {
                                    UserDto userDto = userService.findById(one.getUserId());
                                    if (null != userDto) {
                                        if (userName == "") {
                                            userName = userDto.getNickName();
                                        } else {
                                            userName = userName + "," + userDto.getNickName();
                                        }
                                    }
                                }
                            }
                        }
                        dto.setRemark(userName);
                    }
                }
                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getSpec());
                } else if (JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getSpec());
                } else if (JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getSpec());
                } else {
                    dto.setPhaseStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getSpec());
                }

                //若存在审批节点，则给值
                if (relationMap.get(dto.getTemplateId()) != null && relationMap.get(dto.getTemplateId()).get(dto.getTemplateGroupId()) != null) {
                    dto.setApproveMatrixId(relationMap.get(dto.getTemplateId()).get(dto.getTemplateGroupId()).getApproveMatrixId());
                }
            } else if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (isMobile != null && isMobile) {
                    String nodeCode = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCode);
                    dto.setSimpleName(simple);
                }
            }
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType())) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            updateRelationCode(projectNodeInfoMap, dto, null);
        }
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);
        //使用Hutools TreeUtil转换树结构
        List<List<Tree<String>>> builds = new ArrayList<>();
        for (ProjectGroupDto parent :
                parents) {
            List<Tree<String>> build = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getId());
                        tree.setParentId(treeNode.getParentId());
                        tree.setWeight(treeNode.getWeight());
                        tree.setName(treeNode.getName());
                        // 扩展属性 ...
                        Map<String, Object> extra = treeNode.getExtra();
                        Set<Map.Entry<String, Object>> entries = extra.entrySet();
                        for (Map.Entry<String, Object> entry : entries) {
                            tree.putExtra(entry.getKey(), entry.getValue());
                        }
                    });
            builds.add(build);
        }
        if (byId.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue()) && ObjectUtil.isNotEmpty(builds) && builds.size() > 0) {
            Map<Long, List<List<Tree<String>>>> timeMap = new LinkedHashMap<>();
            timeMap.put(new Date().getTime(), builds);
            TreeForBoardMap.put(projectId + ":" + useCase + ":" + isMobile + ":" + deep + ":" + currentUserId, timeMap);
        }
        return builds;
//        }
    }

    @Override
    public List<List<Tree<String>>> projectTreeForBoardNew(Long projectId, String useCase, Boolean isMobile, int deep) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            //currentUserId =1l;
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        projectId = getSubmeterProjectId(projectId);
        ProjectInfo byId = projectInfoService.getById(projectId);

        String templateCode = null;
        if (ObjectUtil.isNotEmpty(byId) && ObjectUtil.isNotEmpty(byId.getProjectType()) && KidsSystemEnum.ProjectTypeEnum.NEW.getValue().equals(byId.getProjectType())) {
            templateCode = JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "," + JhSystemEnum.TaskPhaseEnum.DESIGN.getKey() + "," + JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey();
        }
        List<ProjectGroup> projectGroups = projectGroupRepository.selectListByProjectId(projectId, templateCode);
        if (ObjectUtil.isEmpty(projectGroups)) {
            throw new BadRequestException("当前暂无营建模版数据，请先确定是否进行营建对接启动！");
        }
        List<ProjectGroupDto> parents = projectGroupMapper.toDto(projectGroups);
        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId);
        util.initialize(projectId);
        if (StringUtils.isNotEmpty(useCase)) {
            query.eq(ProjectNodeInfo::getUseCase, useCase);
        }
        if (null != isMobile) {
            query.eq(ProjectNodeInfo::getIsMobile, isMobile);
        }

        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(currentUserId);
//        根据当前用户，获取对应干系人的roleCode,有多个roleCode

        List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, projectId);
        //todo add
        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo(projectId, useCase, isMobile, roleIds, null);

        if (null == parents || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }
        Map<String, String> collect = parents.stream().collect(Collectors.toMap(ProjectGroupDto::getTemplateId, ProjectGroupDto::getNodeName, (v1, v2) -> v1));

        for (ProjectNodeInfoDto dto : dtos) {
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
//                //判断当前二级任务的紧前任务是否完成  当前接口为返回九宫格数据，暂时不需要作此判断
//                this.showFrontWbsConfig(dto);

                //获取的roleCode与当前节点对应的roleCode做比较
                if (roleCodes.isEmpty()) {
                    dto.setIsWrite(Boolean.FALSE);
                } else {
                    //当前用户是当前项目的干系人的话，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                    OK:
                    for (String roleCode : roleCodes) {
                        //当前roleCode不为空且等于
                        if (ObjectUtils.isNotEmpty(dto.getRoleCode()) && Arrays.stream(dto.getRoleCode().split(",")).anyMatch(s -> s.contains(roleCode))
                                && dto.getIsOpen() != null && dto.getIsOpen()
                        ) {
                            dto.setIsWrite(Boolean.TRUE);
                            dto.setIsHidden(Boolean.TRUE);
                            break OK;
                        } else {
                            Integer userAndRoleCode = roleRepository.getCountByUserAndRoleCode(currentUserId, JhSystemEnum.JobEnum.CSXMJL.getKey());
                            if (userAndRoleCode > 0) {
                                //是厂商项目经理的话，查询是否是该任务的审批人，不是的话，不可见该任务
                                Integer byUserAndNodeId = projectApproveRepository.getCountByUserAndNodeId(currentUserId, dto.getProjectGroupId(), projectId);
                                if (byUserAndNodeId == 0) {
                                    //当前用户是厂商项目经理，不显示非负责人、非审批人的任务
                                    dto.setIsHidden(Boolean.FALSE);
                                }
                            }
                        }
                    }
                }
                //临时将groupid 给nodeId
                dto.setNodeId(dto.getProjectGroupId());
//                if (currentUserId == 1 || currentUserId == 488) {
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
                    //超级管理员，那么都能点击任务进行查看
                    dto.setIsHidden(Boolean.TRUE);
                }
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey()) && !dto.getIsWrite()) {
                    dto.setIsWrite(Boolean.TRUE);
                    dto.setIsHidden(Boolean.TRUE);
                }

                String nodeStatus = JhSystemEnum.NodeStatusEnum.getNodeStatus(dto.getNodeStatus());
                dto.setPhaseStatus(nodeStatus);
            } else if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (ObjectUtil.isNotEmpty(collect)) {
                    dto.setPhaseName(collect.get(dto.getParentId()));
                    if (ObjectUtil.isNotEmpty(dto.getRoundMarking())) {
                        dto.setPhaseName(dto.getPhaseName() + "(轮次" + dto.getRoundMarking() + ")");
                    }
                }

                if (isMobile != null && isMobile) {
                    String nodeCode = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCode);
                    dto.setSimpleName(simple);
                }
            }
            dto.setParentId(dto.getParentId() + dto.getRoundMarking());
        }
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId() + dto.getRoundMarking())
                    .setName(dto.getNodeName())
                    .setParentId(ObjectUtil.isNotEmpty(dto.getParentId()) ? dto.getParentId() + dto.getRoundMarking() : null)
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);
        //使用Hutools TreeUtil转换树结构
        List<List<Tree<String>>> builds = new ArrayList<>();
        for (ProjectGroupDto parent : parents) {
            parent.setParentId(ObjectUtil.isNotEmpty(parent.getParentId()) ? parent.getParentId() + parent.getRoundMarking() : null);
//            parent.setPhaseName();
            List<Tree<String>> build = TreeUtil.build(treeNodes, parent.getTemplateId() + parent.getRoundMarking(), treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getId());
                        tree.setParentId(treeNode.getParentId());
                        tree.setWeight(treeNode.getWeight());
                        tree.setName(treeNode.getName());
                        // 扩展属性 ...
                        Map<String, Object> extra = treeNode.getExtra();
                        Set<Map.Entry<String, Object>> entries = extra.entrySet();
                        for (Map.Entry<String, Object> entry : entries) {
                            tree.putExtra(entry.getKey(), entry.getValue());
                        }
                    });
            builds.add(build);
        }
        if (byId.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue()) && builds != null && builds.size() > 0) {
            Map<Long, List<List<Tree<String>>>> timeMap = new LinkedHashMap<>();
            timeMap.put(new Date().getTime(), builds);
            TreeForBoardMap.put(projectId + ":" + useCase + ":" + isMobile + ":" + deep + ":" + currentUserId, timeMap);
        }
        return builds;
//        }
    }

    @Override
    public ProjectNodeForProgress projectTreeProgress(Long projectId, String useCase, Boolean isMobile) {
        if (projectId == null) {
            throw new BadRequestException("项目id为空，请输入项目Id后请求！");
        }
        //查询项目创建时间
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);
        //查询一级节点数据

        LambdaQueryWrapper<ProjectGroup> query = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != projectId, ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeLevel, 1).isNull(ProjectGroup::getOrderId);

        if (StringUtils.isNotEmpty(useCase)) {
            query.eq(ProjectGroup::getUseCase, useCase);
        }
        if (null != isMobile) {
            query.eq(ProjectGroup::getIsMobile, isMobile);
        }
        List<ProjectGroupDto> dtos = projectGroupMapper.toDto(projectGroupRepository.selectList(query));
        for (ProjectGroupDto dto : dtos) {
            LambdaQueryWrapper<ProjectGroup> querychild = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != projectId, ProjectGroup::getParentId, dto.getTemplateId())
                    .eq(ProjectGroup::getProjectId, projectId).eq(ProjectGroup::getNodeLevel, 2);
            List<ProjectGroup> children = projectGroupRepository.selectList(querychild);
            int allnum = children.size();
            int finishnum = 0;
            Boolean isDelay = false;
            String percent = "0%";
            for (ProjectGroup nodechild : children) {
                if (nodechild.getNodeStatus() != null && JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(nodechild.getNodeStatus())) {
                    finishnum = finishnum + 1;
                }
              /*  if(nodechild.getDelayDay()!=null&&nodechild.getDelayDay()>0){
                    isDelay=true;
                }*/
            }
            if (allnum > 0 && finishnum > 0) {
                percent = taskProgress(finishnum, allnum);
            }
            //塞当前节点状态在里面
            if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                dto.setPhaseStatus("已完成");
            } else {
               /* String startDate=dto.getPredictStartDate().toString();
                String endDate=dto.getPredictEndDate().toString();
                String status=calPhaseStatus(startDate,endDate);
                dto.setPhaseStatus(status);*/
            }
            dto.setIsDelay(isDelay);
            dto.setProgress(percent);
            // 临时将groupid 给nodeId
            dto.setNodeId(dto.getProjectGroupId());

        }
        ProjectNodeForProgress projectNodeForProgress = new ProjectNodeForProgress();
        if (projectInfoDto != null) {
            projectNodeForProgress.setProjectId(projectInfoDto.getProjectId().toString());
            projectNodeForProgress.setProjectCreateDate(projectInfoDto.getProjectCreateDate());
            projectNodeForProgress.setTaskPhase(projectInfoDto.getTaskPhase());
        }
        if (dtos.size() > 0) {
            projectNodeForProgress.setProjectNodeInfoList(dtos);
        }
        //封装数据
        return projectNodeForProgress;
    }

    /**
     * 将用户新添加的块，后面部分的顺序进行调整
     *
     * @param sortVersion
     * @param nodeCode
     * @param index
     * @return
     */
    private List<ProjectNodeInfoDto> addIndex(List<ProjectNodeInfoDto> sortVersion, String nodeCode,
                                              double index, Long parentIdSed) {
//        List<ProjectNodeInfoDto> sortVersion = secondInfos.stream().filter(p -> ObjectUtils.isEmpty(p.getAddedVersion())).sorted((p1, p2) -> {
//            return p2.getNodeIndex() - p1.getNodeIndex();
//        }).collect(Collectors.toList());
        double nodeIndex = 0;
        for (ProjectNodeInfoDto dto : sortVersion) {
            if (String.valueOf(parentIdSed).equals(dto.getParentId())) {
                if (dto.getNodeCode().equals(nodeCode)) {
                    nodeIndex = dto.getNodeIndex();
                }
                if (ObjectUtils.isNotEmpty(nodeIndex) && dto.getNodeIndex() > nodeIndex) {
                    dto.setNodeIndex(++index);
                }
            }
        }
        return sortVersion;
    }

    /**
     * 调整2级节点下的页面顺序
     *
     * @param dtos
     * @param projectId
     * @param nodeCode
     * @return
     */
    private List<ProjectNodeInfoDto> againNodeIndex(List<ProjectNodeInfoDto> dtos, Long projectId, String
            nodeCode, Long templateIdSed) {
        List<ProjectNodeInfoDto> resultDtos = dtos.stream().filter(p -> !String.valueOf(templateIdSed).equals(p.getParentId())).collect(Collectors.toList());
        List<ProjectNodeInfoDto> secondInfos = dtos.stream().filter(p -> String.valueOf(templateIdSed).equals(p.getParentId())).collect(Collectors.toList());
        List<ProjectNodeInfoDto> addedVersions = secondInfos.stream().filter(p -> ObjectUtils.isNotEmpty(p.getAddedVersion())).collect(Collectors.toList());
//        List<ProjectNodeInfoDto> originVersions = secondInfos.stream().filter(p -> ObjectUtils.isEmpty(p.getAddedVersion())).sorted((p1, p2) -> {
//            return p1.getNodeIndex() - p2.getNodeIndex();
//        }).collect(Collectors.toList());
        List<ProjectNodeInfoDto> originVersions = secondInfos.stream().sorted(Comparator.comparing(ProjectNodeInfoDto::getNodeIndex)).collect(Collectors.toList());

        List<ProjectNodeInfoDto> sortVersion = originVersions;
        Boolean isSort = false;
        //给单个2级节点下的字段进行重新排序，防止中间断排序，造成排序混乱
        double startIndex = 0;
        Map<String, Double> indexMap = new HashMap<>();
        for (int i = 0; i < sortVersion.size(); i++) {
            ProjectNodeInfoDto origin = sortVersion.get(i);
            if (i == 0) {
                startIndex = origin.getNodeIndex();
            } else {
                origin.setNodeIndex(++startIndex);
            }
            indexMap.put(origin.getTemplateQueueId(), origin.getNodeIndex());
        }
        addedVersions.forEach(a -> a.setNodeIndex(indexMap.get(a.getTemplateQueueId())));

        if (ObjectUtils.isNotEmpty(addedVersions)) {
//            List<ProjectNodeInfoDto> originSenInfos = new LinkedList<>();
            List<Map<String, Object>> numList = projectNodeInfoRepository.getAddedVersionNum(projectId, templateIdSed);
            if (ObjectUtils.isNotEmpty(numList) && numList.size() > 0) {
                for (Map<String, Object> numVersion : numList) {
                    String lastCode = String.valueOf(numVersion.get("last_code"));
                    Integer addedVersion = Integer.valueOf(String.valueOf(numVersion.get("added_version")));
                    Integer num = Integer.valueOf(String.valueOf(numVersion.get("num")));
                    double max = 0;
                    Map<String, ProjectNodeInfoDto> map = new HashMap<>();
//                    dtos.forEach(d -> map.put(d.getNodeCode(), d));
                    if (ObjectUtils.isNotEmpty(lastCode)) {
                        isSort = true;
                        for (ProjectNodeInfoDto info : addedVersions) {
                            Integer version = info.getAddedVersion();
                            String code = info.getLastCode();
                            if (lastCode.equals(code) && (addedVersion.intValue() == version.intValue())) {
                                double index = info.getNodeIndex() + num * version;
                                if (max < index) {
                                    max = index;
                                }
                                info.setNodeIndex(index);
                            }
                        }
                        sortVersion = addIndex(sortVersion, lastCode, max, templateIdSed);
                    }
                }
//                addedVersions.forEach(p -> resultDtos.add(p));
                resultDtos.addAll(addedVersions);
//                if (CollectionUtils.isNotEmpty(sortVersion)){
//                    sortVersion.forEach(s -> resultDtos.add(s));
//                }
            }
        }
        if (isSort) {
            //如果有用户添加节点版本，存放排序后的节点集合
            resultDtos.addAll(sortVersion);
        } else {
            //如果没有用户添加节点版本，既原数据，存放原来的节点集合
            resultDtos.addAll(originVersions);
        }

        return resultDtos;
    }

    @Override
    public ProjectNodeInfoDto projectTreeByNodeCode(Long projectId, String nodeCode, String useCase, Boolean isMobile, String roundMarking) {
        //查询当前人的角色
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //分表查询
        Long submeterProjectId = getSubmeterProjectId(projectId);
        util.setProjectTableName(submeterProjectId);

        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(currentUserId);
        //根据角色出二级页面权限
        // LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectId, ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, nodeCode);
        ProjectNodeInfoDto parent = projectNodeInfoRepository.getLevelTwoNodeInfo(projectId, nodeCode, roleIds, roundMarking);
        parent.setNodeId(parent.getProjectGroupId());

        //判断当前二级任务的紧前任务是否完成
        this.showFrontWbsConfig(parent);

        //读写判断
        //根据当前用户，获取对应干系人的roleCode,有多个roleCode
        List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, submeterProjectId);

        //获取的roleCode与当前节点对应的roleCode做比较
        if (roleCodes.isEmpty()) {
            parent.setIsWrite(Boolean.FALSE);
        } else {
            OK:
            //当前用户是当前项目的干系人的话，那么都能点击任务进行查看
            parent.setIsHidden(Boolean.TRUE);
            for (String roleCode : roleCodes) {
                //当前roleCode不为空且等于
                if (ObjectUtils.isNotEmpty(parent.getRoleCode()) && Arrays.stream(parent.getRoleCode().split(",")).anyMatch(s -> s.contains(roleCode))
//                        && parent.getIsWrite() != null && parent.getIsWrite()
                        && parent.getIsOpen() != null && parent.getIsOpen()
                ) {
                    parent.setIsWrite(Boolean.TRUE);
                    parent.setIsHidden(Boolean.TRUE);
                } else {
                    Integer userAndRoleCode = roleRepository.getCountByUserAndRoleCode(currentUserId, JhSystemEnum.JobEnum.CSXMJL.getKey());
                    if (userAndRoleCode > 0) {
                        //是厂商项目经理的话，查询是否是该任务的审批人，不是的话，不可见该任务
                        Integer byUserAndNodeId = projectApproveRepository.getCountByUserAndNodeId(currentUserId, parent.getProjectGroupId(), projectId);
                        if (byUserAndNodeId == 0) {
                            //当前用户是厂商项目经理，不显示非负责人、非审批人的任务
                            parent.setIsHidden(Boolean.FALSE);
                        }
                    }
                }
            }
        }

        if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
            //超级管理员，那么都能点击任务进行查看
            parent.setIsHidden(Boolean.TRUE);
        }
        if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey()) && !parent.getIsWrite()) {
            parent.setIsWrite(Boolean.TRUE);
            parent.setIsHidden(Boolean.TRUE);
        }

        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, projectId)
                .eq(ObjectUtil.isNotEmpty(parent.getRoundMarking()), ProjectNodeInfo::getRoundMarking, parent.getRoundMarking())
                .eq(ProjectNodeInfo::getIsDelete, false);

        List<ProjectNodeInfoDto> dtos = projectNodeInfoMapper.toDto(list(query));

        if (CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId,nodeCode 错误，无法获取数据！");
        }


        //对于用户新增的块，进行顺序调整
//        dtos = againNodeIndex(dtos, projectId, nodeCode, Long.valueOf(parent.getTemplateId()));

        //获取原项目主档的干系人    根据条件lambdaQueryWrapper，查询干系人数据           设置条件lambdaQueryWrapper
        LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        lambdaQueryWrapper.eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                .eq(ProjectStakeholders::getProjectId, submeterProjectId)
                .isNull(ProjectStakeholders::getOrderId);
        List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);
        Map<String, Long> userMap = new HashMap<>();
        for (ProjectStakeholders sta : stakeholderslist) {
            userMap.put(sta.getRoleCode(), sta.getUserId());
        }

        //获取原项目主档的数据，进行带出
        LambdaQueryWrapper<ProjectNodeInfo> query1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(null != projectId, ProjectNodeInfo::getProjectId, submeterProjectId)
                .eq(ProjectNodeInfo::getIsDelete, false);
        List<ProjectNodeInfoDto> dtosOld = projectNodeInfoMapper.toDto(list(query1));
        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        for (ProjectNodeInfoDto dto : dtosOld) {
            projectNodeInfoMap.put(dto.getNodeCode(), dto);
        }

        //获取原项目主档的已完成二级任务,用于数据带出
        final LambdaQueryWrapper<ProjectGroup> wrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, submeterProjectId)
                .eq(ProjectGroup::getNodeLevel, 2)
                .eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
        final List<ProjectGroup> projectGroups = projectGroupRepository.selectList(wrapper);
        final List<String> collectFin = projectGroups.stream().map(ProjectGroup::getNodeCode).collect(Collectors.toList());


        for (ProjectNodeInfoDto dto : dtos) {
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType()) && (!JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(dto.getJobCode()))) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            updateRelationCode(projectNodeInfoMap, dto, collectFin);
        }
        //任务负责人转值
        if (null != parent.getNodeLevel() && parent.getNodeLevel() == 2) {
            List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(Long.parseLong(parent.getTemplateId()), Long.parseLong(parent.getTemplateGroupId()));
            //若存在审批节点，则给值
            if (ObjectUtils.isNotNull(appRelations) && appRelations.size() > 0) {
                parent.setApproveMatrixId(appRelations.get(0).getApproveMatrixId());
            }
            //改为只从干系人中取值 2022 0608
            if (parent.getRoleCode() != null) {
                List<String> list = Lists.newArrayList();
                Arrays.stream(parent.getRoleCode().split(",")).forEach(s -> {
                    list.add(s);
                });
                String userName = "";
                for (String roleCode : list) {
                    Role roleIdByCode = roleRepository.findRoleIdByCode(roleCode);
                    if (ObjectUtil.isNotEmpty(roleIdByCode)) {
                        if (ObjectUtil.isEmpty(parent.getRoleName())) {
                            parent.setRoleName(roleIdByCode.getName());
                        } else {
                            parent.setRoleName(parent.getRoleName() + "," + roleIdByCode.getName());
                        }
                    }
                    if (stakeholderslist.size() > 0) {
                        final List<Long> collect = stakeholderslist.stream().map(ProjectStakeholders::getUserId).collect(Collectors.toList());
                        final List<User> byUserIds = userRepository.findByUserIds(collect);
                        for (ProjectStakeholders one : stakeholderslist) {
                            if (one != null && null != one.getUserId()) {
                                if (!roleCode.equals(one.getRoleCode())) {
                                    continue;
                                }
                                final List<User> userList = byUserIds.stream().filter(b -> one.getUserId().equals(b.getId())).limit(1).collect(Collectors.toList());
                                final User user = Optional.ofNullable(userList).flatMap(u -> Optional.ofNullable(u.get(0))).orElse(null);
                                UserDto userDto = userMapper.toDto(user);
                                if (null != userDto) {
                                    if (ObjectUtil.isNotEmpty(userDto.getEnabled()) && !userDto.getEnabled()) {
                                        continue;
                                    }

                                    if (userName == "") {
                                        userName = userDto.getNickName();
                                        if (userName == "") {
                                            userName = "--";
                                        }
                                    } else {
                                        userName = userName + "," + userDto.getNickName();
                                    }

                                }
                            }
                        }
                    }
                    //userName 去重
                    if (StrUtil.isNotBlank(userName)) {
                        Set<String> set = new HashSet<>(StrUtil.split(userName, ","));
                        parent.setRemark(StrUtil.join(",", set));
                    }
                }
            }

        }

        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(4);
        //使用Hutools TreeUtil转换树结构
        List<Tree<String>> allTree = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
        parent.setChildren(allTree);
        return parent;

    }

    //将username设置为username-nickName
    @Override
    public String changeUserName(UserDto userDto, String username) {
        if (StringUtils.isNotEmpty(username) && !username.equals(userDto.getNickName().trim())) {
            return username + "-" + userDto.getNickName();
        }
        return username;
    }

    /**
     * 根据不同合同类型，获取合同申请单号
     *
     * @param contractType
     * @return
     */
    private String getContractApplyNo(String topStr, String contractType) {
        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String key = CONTRACT_NO_PREFIX + contractType + current;
        Integer value = (Integer) redisUtils.get(key);
        String num = "";
        String prefix = topStr + contractType;
        if (null == value) {
            redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
            num = "001";
        } else {
            Long increment = redisUtils.increment(key);
            StringBuilder s = new StringBuilder(increment.toString());
            for (int i = s.length(); i < 3; i++) {
                s.insert(0, "0");
            }
            num = s.toString();
        }


        String code = prefix + current + num;
        return code;
    }


    public List<ProjectNodeInfo> updateDataNew(List<ProjectNodeInfo> list, Boolean isCommit) {
        //判断是否存在数据
        Boolean isUpdateNodeStatus = true;
        if (CollectionUtils.isNotEmpty(list)) {
            //用于添加 用户
            Map<String, List<ProjectNodeInfo>> pieceMap = list.stream().collect(Collectors.groupingBy(p -> StringUtils.isEmpty(p.getPieceIndex()) ? "" : p.getPieceIndex()));
            if (MapUtils.isNotEmpty(pieceMap)) {
                Set<String> pieceIndexSet = pieceMap.keySet();
                if (CollectionUtils.isNotEmpty(pieceIndexSet)) {
                    for (String pieceIndex : pieceIndexSet) {
                        if (StringUtils.isEmpty(pieceIndex)) {
                            continue;
                        }
                        this.createUserAddData(projectNodeInfoMapper.toDto(pieceMap.get(pieceIndex)));
                    }
                }
            }

            //保存
            Long projectId = getSubmeterProjectId(list.get(0).getProjectId());
            //供应商和人员数据同步
            if (list.get(0).getNodeCode().startsWith("sup")||list.get(0).getNodeCode().startsWith("spm")) {
                updateSupplier(list);
            }
            util.initialize(getSubmeterProjectId(list.get(0).getProjectId()));

            //查询签证报备列表
            LambdaQueryWrapper<ProjectVisaFiling> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(ProjectVisaFiling::getId, list.get(0).getProjectId());
            ProjectVisaFiling projectVisaFiling = projectVisaFilingRepository.selectOne(queryWrapper1);

            //判断进度描述（是否存在擅自施工），是否等于 yes
            Boolean unautor = false;
            for (ProjectNodeInfo projectNodeInfo : list) {
                //签证报备模版修改数据的时候，同步到签证报备列表中
                if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
                    this.upVisaFiling(projectNodeInfo, projectVisaFiling);
                }

                if (JhSystemEnum.unauthorizedConstruction.getNodeCode(projectNodeInfo.getNodeCode()) && (ObjectUtils.isNotEmpty(projectNodeInfo.getRemark()) && projectNodeInfo.getRemark().equals("yes"))) {
                    unautor = true;
                }
                if (JhSystemEnum.unauthorizedConstructionList.getNodeCode(projectNodeInfo.getNodeCode()) && unautor) {
                    if (ObjectUtil.isEmpty(projectNodeInfo.getUnauthorizedConstructions())) {
                        UnauthorizedConstructionQueryCriteria criteria = new UnauthorizedConstructionQueryCriteria();
                        criteria.setProjectId(list.get(0).getProjectId());
                        criteria.setNodeCode(projectNodeInfo.getNodeCode());
                        List<UnauthorizedConstructionDto> constructionDtos = unauthorizedConstructionService.queryByProjectId(criteria);
                        if (ObjectUtils.isEmpty(constructionDtos)) {
                            throw new BadRequestException("保存失败，进度描述已选择存在擅自施工，请确定是否已添加擅自施工列表");
                        }
                    }
                }
//                //【客房平面及效果图】选择的两个房号存入remake;des-00107057;des-00107060
//                if (AtourSystemEnum.DesignDrawing.DES00107057.getKey().equals(projectNodeInfo.getNodeCode())
//                        || AtourSystemEnum.DesignDrawing.DES00107060.getKey().equals(projectNodeInfo.getNodeCode())) {
//                    saveRemarkByRoom(projectNodeInfo);
//                }

                //【客房样板间施工图】选择的两个房号存入remake;des-00111033;des-00111036
                if (AtourSystemEnum.DesignDrawingTow.DES00111033.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.DesignDrawingTow.DES00111036.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.DesignDrawingTow.DES00111040.getKey().equals(projectNodeInfo.getNodeCode())) {
                    saveRemarkByRoom(projectNodeInfo);
                }
                //【竣工验收申请】【弱电验收申请】选择的房号存入remake;eng-00129025;eng-00137008
                if (AtourSystemEnum.CompletionWeakRoom.ENG00129025.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00137008.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00139009.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00141011.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00133051.getKey().equals(projectNodeInfo.getNodeCode())) {
                    saveRemarkByRoom(projectNodeInfo);
                }
                //筹建启动会-擅自施工
                if (projectNodeInfo.getUnauthorizedConstructions() != null && !projectNodeInfo.getUnauthorizedConstructions().isEmpty()) {
                    //先删除
                    unauthorizedConstructionService.deleteByIds(projectNodeInfo.getUnauthorizedConstructions().stream().map(UnauthorizedConstruction::getUnauthorizedId).distinct().collect(Collectors.toList()));
                    //在新增
                    unauthorizedConstructionService.saveOrUpdateBatch(projectNodeInfo.getUnauthorizedConstructions());
                }
                //项目进度
                if (projectNodeInfo.getNodeInfoDtos() != null && !projectNodeInfo.getNodeInfoDtos().isEmpty()) {
                    projectGroupService.saveOrUpdateBatch(projectNodeInfo.getNodeInfoDtos());
                }
                //质量管理
                if (projectNodeInfo.getQualityControls() != null && !projectNodeInfo.getQualityControls().isEmpty()) {
                    //先删除
                    qualityControlService.deleteByIds(projectNodeInfo.getQualityControls().stream().map(QualityControl::getQualityControlId).distinct().collect(Collectors.toList()));
                    qualityControlService.saveOrUpdateBatch(projectNodeInfo.getQualityControls());
                    if (projectNodeInfo.getQualityControls() != null && projectNodeInfo.getQualityControls().size() > 0) {
                        //如果检查内容字典表没有，则要插入字典表
                        List<DictDetail> descList = dictDetailRepository.findDictDetailByDictName("check_detail");
                        for (QualityControl control : projectNodeInfo.getQualityControls()) {
                            String desc = control.getQualityDescribe();//检查内容详情
                            String quaityName = control.getQualityName();//内容 completion_content
                            String importline = control.getIssueImportance();//重要程度 importance_of_the_problem
                            List<DictDetail> querylist = dictDetailRepository.getDictByNameAndParentInfo(desc, quaityName, "completion_content");
                            //如果没查出来，则需要在字典表中新增一条
                            if (querylist == null || querylist.size() < 1) {
                                DictDetail newOne = new DictDetail();
                                newOne.setDictSort(5);
                                newOne.setDict(descList.get(0).getDict());
                                newOne.setLabel(desc);
                                newOne.setValue("detail" + (dictDetailRepository.findMymaxvalue() + 1));
                                newOne.setParentId(dictDetailRepository.getIdByParentInfo(quaityName, "completion_content"));
                                newOne.setNextId(dictDetailRepository.getIdByParentInfo(importline, "importance_of_the_problem"));
                                newOne.setCreateBy("admin");
                                newOne.setCreateTime(new Timestamp(new Date().getTime()));
                                dictDetailRepository.save(newOne);
                            }
                        }
                    }
                }
                //竣工常规项目自检表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getRoutineSelfInspection())) {
                    //先删除
                    qualityControlService.deleteByIds(projectNodeInfo.getRoutineSelfInspection().stream().map(QualityControl::getQualityControlId).distinct().collect(Collectors.toList()));
                    qualityControlService.saveOrUpdateBatch(projectNodeInfo.getRoutineSelfInspection());
                }
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectSystemSelfInspection())) {
                    //先删除
                    projectSystemSelfInspectionService.deleteByIds(projectNodeInfo.getProjectSystemSelfInspection().stream().map(ProjectSystemSelfInspection::getSystemSelfInspectionId).distinct().collect(Collectors.toList()));
                    projectSystemSelfInspectionService.saveOrUpdateBatch(projectNodeInfo.getProjectSystemSelfInspection());
                }
                //施工照片
                if (projectNodeInfo.getConstructionPhotographs() != null && !projectNodeInfo.getConstructionPhotographs().isEmpty()) {
                    //没有删除，不需要删除
//                    constructionPhotographService.deleteByIds(projectNodeInfo.getConstructionPhotographs().stream().map(ConstructionPhotograph::getConstructionId).distinct().collect(Collectors.toList()));
                    constructionPhotographService.saveOrUpdateBatch(projectNodeInfo.getConstructionPhotographs());
                }
                //设计交底问题汇总
                if (projectNodeInfo.getProjectDisclosureProblems() != null && !projectNodeInfo.getProjectDisclosureProblems().isEmpty()) {
                    //先删除
                    projectDisclosureProblemService.deleteByIds(projectNodeInfo.getProjectDisclosureProblems().stream().map(ProjectDisclosureProblem::getDisclosureProblemId).distinct().collect(Collectors.toList()));
                    projectDisclosureProblemService.saveOrUpdateBatch(projectNodeInfo.getProjectDisclosureProblems());
                }
                //现场特殊情况暂无法解决事项
                if (projectNodeInfo.getProjectInsolubleMatter() != null && !projectNodeInfo.getProjectInsolubleMatter().isEmpty()) {
                    //先删除
                    projectInsolubleMatterService.deleteByIds(projectNodeInfo.getProjectInsolubleMatter().stream().map(ProjectInsolubleMatter::getInsolubleMatterId).distinct().collect(Collectors.toList()));
                    projectInsolubleMatterService.saveOrUpdateBatch(projectNodeInfo.getProjectInsolubleMatter());
                }
                //擅自施工与图纸不符部分
                if (projectNodeInfo.getProjectUnauthorizedConstructions() != null && !projectNodeInfo.getProjectUnauthorizedConstructions().isEmpty()) {
                    //先删除
                    projectUnauthorizedConstructionService.deleteByIds(projectNodeInfo.getProjectUnauthorizedConstructions().stream().map(ProjectUnauthorizedConstruction::getUnauthorizedConstructionId).distinct().collect(Collectors.toList()));
                    projectUnauthorizedConstructionService.saveOrUpdateBatch(projectNodeInfo.getProjectUnauthorizedConstructions());
                }
                //项目特殊情况说明表
                if (projectNodeInfo.getProjectSpecialCaseDescription() != null && !projectNodeInfo.getProjectSpecialCaseDescription().isEmpty()) {
                    //先删除
                    projectSpecialCaseDescriptionService.deleteByIds(projectNodeInfo.getProjectSpecialCaseDescription().stream().map(ProjectSpecialCaseDescription::getId).distinct().collect(Collectors.toList()));
                    projectSpecialCaseDescriptionService.saveOrUpdateBatch(projectNodeInfo.getProjectSpecialCaseDescription());
                }
                //项目的特许商特殊需求表
                if (projectNodeInfo.getProjectSpecialNeedsFranchisors() != null && !projectNodeInfo.getProjectSpecialNeedsFranchisors().isEmpty()) {
                    //先删除
                    projectSpecialNeedsFranchisorsService.deleteByIds(projectNodeInfo.getProjectSpecialNeedsFranchisors().stream().map(ProjectSpecialNeedsFranchisors::getFranchisorsId).distinct().collect(Collectors.toList()));
                    projectSpecialNeedsFranchisorsService.saveOrUpdateBatch(projectNodeInfo.getProjectSpecialNeedsFranchisors());
                }
                //【开工申请】的样板间确定
                if (projectNodeInfo.getProjectRoomSure() != null && !projectNodeInfo.getProjectRoomSure().isEmpty()) {
                    List<ProjectRoom> projectRoomSure = projectNodeInfo.getProjectRoomSure();
                    final LambdaQueryWrapper<ProjectRoom> wrapper = Wrappers.lambdaQuery(ProjectRoom.class)
                            .eq(ProjectRoom::getProjectId, projectNodeInfo.getProjectId());
                    final List<ProjectRoom> rooms = projectRoomRepository.selectList(wrapper);
                    rooms.stream().forEach(room -> projectRoomSure.stream().forEach(projectRoom -> {
                        if (ObjectUtil.isNotEmpty(projectRoom.getRoomNum()) && room.getRoomNum().equals(projectRoom.getRoomNum())) {
                            room.setIsModelRoom("1");
                            room.setIsUsed("1");
                            room.setIsMandatory(projectRoom.getIsMandatory());
                            projectRoomService.update(room);
                        }
                    }));
                }
                //物资管理材料表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectMaterialManagement())) {
                    //先删除
                    projectMaterialManagementService.deleteByIds(projectNodeInfo.getProjectMaterialManagement().stream().map(ProjectMaterialManagement::getProjectManagementId).distinct().collect(Collectors.toList()));
                    projectMaterialManagementService.saveOrUpdateBatch(projectNodeInfo.getProjectMaterialManagement());
                }
                //弱电设施设备信息表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectWeakCurrent())) {
                    //先删除
                    projectWeakCurrentService.deleteAll(projectNodeInfo.getProjectWeakCurrent().stream().map(ProjectWeakCurrent::getProjectWeakCurrentId).distinct().toArray(Long[]::new));
                    projectWeakCurrentService.saveOrUpdateBatch(projectNodeInfo.getProjectWeakCurrent());
                }
                //证照管理表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectCateInfoList())) {
                    //先删除
                    projectCateInfoService.deleteByIds(projectNodeInfo.getProjectCateInfoList().stream().map(ProjectCateInfo::getCateId).distinct().collect(Collectors.toList()));
                    //将每个cate_type set为 new，新开店
                    projectNodeInfo.getProjectCateInfoList().stream().forEach(m -> {
                        //如果是证照新增的 为空就是可以删除
                        if (ObjectUtil.isEmpty(m.getNotDelete())) {
                            m.setNotDelete("0");
                        }
                        //1.17改动-只保留正式开工的证照
                        m.setThreeNodeCode(cateKey.CATE_THREE_1.getKey());
                        m.setTwoNodeCode(cateKey.CATE_TWO_1.getKey());
                        m.setCateType(cateKey.CATE_NEW.getKey());
                        m.setIsDelete(0);
                    });
                    projectCateInfoService.saveOrUpdateBatch(projectNodeInfo.getProjectCateInfoList());
                }
                //竣工验收验收单保存数据
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectCompletionReceiptListDto())) {
                    projectCompletionReceiptService.updateCompletionReceipt(projectNodeInfo.getProjectCompletionReceiptListDto());
                }


                //判断节点等级，若等级为1,2 则不可以修改 跳过
                if (null != projectNodeInfo.getNodeLevel() && projectNodeInfo.getNodeLevel() > 2) {
                    if (JhSystemEnum.NodeType.TEXTAREA.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_SELECT.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_DATE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.INPUT_SHOW_BLOCK.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.RADIO_BUTTON.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.SINGLE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.DIGITAL_SHOW_BLOCK.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.TABLE_VALUE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_SEARCH.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.MULTIPLE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_MULTI_INTER.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.LIST.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_SELECT_FILE.getValue().equals(projectNodeInfo.getNodeType())
                    ) {
                        if ("eng-00103060".equals(projectNodeInfo.getNodeCode()) && StringUtils.isNotEmpty(projectNodeInfo.getRemark())) {
                            updatePlanRelationTime(list.get(0).getProjectId(), projectNodeInfo.getRemark(), "2");
                        }
                        if ("sup-00101031".equals(projectNodeInfo.getNodeCode())) {
                            List<SupplierPm> supplierPmList = projectNodeInfo.getSupplierPmList();
                            if (supplierPmList != null && !supplierPmList.isEmpty()){
                                supplierPmList.forEach(p->{
                                    SupplierInfo info = supplierInfoRepository.selectById(projectNodeInfo.getProjectId());
                                    if (info != null) {
                                        String availableAmountStart = p.getAvailableAmountStart();
                                        String availableAmountEnd = p.getAvailableAmountEnd();
                                        StringBuilder builder = new StringBuilder();
                                        if (availableAmountStart != null) {
                                            builder.append(availableAmountStart);
                                        }
                                        builder.append(" , ");
                                        if (availableAmountEnd != null) {
                                            builder.append(availableAmountEnd);
                                        }
                                        p.setAvailableAmount(builder.toString());
                                        p.setFisyear(info.getReferrer());
                                        if (p.getSupplierId() == null) {
                                            p.setSupplierId(info.getId());
                                        }
//                                    if (StringUtils.isNotEmpty(p.getProjectScope())) {
//                                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
//                                                .in(LocalStorage::getId, p.getProjectScope().split(","))
//                                                .set(LocalStorage::getNodeId, projectNodeInfo.getNodeId()));
//                                    }
//                                    if (StringUtils.isNotEmpty(p.getPlanAmount())) {
//                                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
//                                                .in(LocalStorage::getId, p.getPlanAmount().split(","))
//                                                .set(LocalStorage::getNodeId, projectNodeInfo.getNodeId()));
//                                    }
                                        if (p.getSupplierPersonnelRoles() != null && p.getSupplierPersonnelRoles().length > 0) {
                                            StringBuilder sb = new StringBuilder();
                                            for (String role : p.getSupplierPersonnelRoles()) {
                                                sb.append(role).append(",");
                                            }
                                            p.setSupplierPersonnelRole(sb.substring(0, sb.length()-1));
                                        }
                                        if (p.getId() == null) {
                                            p.setStatus("pending_approval");
                                            supplierPmService.createPm(supplierPmMapper.toDto(p), projectNodeInfo.getProjectId());
                                        }else {
                                            supplierPmService.updateById(p);
                                        }
                                    }
                                });
                            }
                        }
                        if ("eng-00103062".equals(projectNodeInfo.getNodeCode()) && StringUtils.isNotEmpty(projectNodeInfo.getRemark())) {
                            //插入设计的计划事件日期
                            updatePlanRelationTime(list.get(0).getProjectId(), projectNodeInfo.getRemark(), "1");
                        }
                        LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getNodeId, projectNodeInfo.getNodeId())
                                .set(ProjectNodeInfo::getRemark, projectNodeInfo.getRemark());
                        update(projectNodeInfoLambdaUpdateWrapper);
                        //TODO 保存的updateRelationCode未修改
                        if (StringUtils.isNotEmpty(projectNodeInfo.getRelationCode()) && "1".equals(projectNodeInfo.getIsEdit())) {
                            LambdaUpdateWrapper<ProjectNodeInfo> relationLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class).eq(ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId()).eq(ProjectNodeInfo::getNodeCode, projectNodeInfo.getRelationCode()).set(ProjectNodeInfo::getRemark, projectNodeInfo.getRemark());
                            update(relationLambdaUpdateWrapper);
                        }


                    } else if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(projectNodeInfo.getNodeType())) {

//                        //文件
//                        ProjectInfoDto project = projectInfoService.findById(projectNodeInfo.getProjectId());
//                        if (ObjectUtil.isNotEmpty(project) && JhSystemEnum.storeTypeEnum.WAREHOUSE.getKey().equals(project.getStoreType())) {
//                            //限定大仓的设计图节点的附件可以修改nodeName
//                            List<String> allowNameUpdates = Arrays.asList("con-0012308", "con-0012309", "con-0012310", "con-0012311", "con-0012312", "con-0012313", "con-0012314", "con-0012315", "con-0012316", "con-0012317", "con-0012318", "con-0012319", "con-0012320", "con-0012321", "con-0012322", "con-0012323", "con-0012324");
//                            if (ObjectUtil.isNotEmpty(projectNodeInfo.getNodeCode()) && allowNameUpdates.contains(projectNodeInfo.getNodeCode())) {
//                                LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class).eq(ProjectNodeInfo::getNodeId, projectNodeInfo.getNodeId()).set(ProjectNodeInfo::getNodeName, projectNodeInfo.getNodeName());
//                                update(projectNodeInfoLambdaUpdateWrapper);
//                            }
//                        }
                        continue;
                    }
                    //关联查询出干系人和其他页面数据
                    if (StringUtils.isNotEmpty(projectNodeInfo.getRelationType()) && isCommit) {
                        if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(projectNodeInfo.getRelationType()) && projectNodeInfo.getRemark() != null) {
//                            Long projectId = projectNodeInfo.getProjectId();
                            String roleCode = projectNodeInfo.getJobCode();
                            String downCode = projectNodeInfo.getDownCode();
                            Long userId = Long.parseLong(projectNodeInfo.getRemark());
                            projectInfoService.createProjectStakeholders(projectId, roleCode, userId, downCode);
                            //干系人
                        }
//                        else if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(projectNodeInfo.getRelationType()) && JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(projectNodeInfo.getJobCode())) {
//                            //合同责任人入刘群
//                            Long projectId = projectNodeInfo.getProjectId();
//                            String roleCode = projectNodeInfo.getJobCode();
//                            String downCode = projectNodeInfo.getDownCode();
//                            Long userId = 0L;
//                            projectInfoService.createProjectStakeholders(projectId, roleCode, userId, downCode);
//                        }
                    }
                    ProjectInfo projectInfo = projectInfoRepository.selectById(getSubmeterProjectId(projectNodeInfo.getProjectId()));

                    if (projectInfo != null) projectInfoRepository.updateById(projectInfo);
                    if (null != projectNodeInfo.getNodeIsfin() && isUpdateNodeStatus) {
                        //判断任意一个三级节点状态不为null，即可更新 二级节点 状态
                        LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectNodeInfo.getProjectId(), ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId()).eq(ProjectNodeInfo::getTemplateId, projectNodeInfo.getParentId());
                        ProjectNodeInfoDto parent = projectNodeInfoMapper.toDto(getOne(project));
                        if (null != parent) {
                            LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                    .eq(ProjectNodeInfo::getNodeId, parent.getNodeId())
                                    .set(ProjectNodeInfo::getNodeIsfin, projectNodeInfo.getNodeIsfin());
                            update(projectNodeInfoLambdaUpdateWrapper);
                            //更新一次后即可
                            isUpdateNodeStatus = false;
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
                //修改签证报备列表
                projectVisaFilingRepository.updateById(projectVisaFiling);
            }

            //发起人
            Long initiatUser = SecurityUtils.getCurrentUserId();
            List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(initiatUser);
            ProjectGroup projectGroup = new ProjectGroup();
            if (ObjectUtil.isNotEmpty(userRoleCodes) && userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey()) || userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())) {
                LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectId, projectId)
                        .eq(ProjectGroup::getTemplateId, list.get(0).getParentId())
                        .last("limit 1");
                projectGroup = projectGroupRepository.selectOne(queryWrapper);
                if (ObjectUtil.isNotEmpty(projectGroup) && projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
                    //超级管理员角色进行保存后
                    //【开工申请】 样板间房号存进施工照片  更新新增施工照片
                    if (AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey().equals(projectGroup.getNodeCode())) {
                        final LambdaUpdateWrapper<ProjectRoom> wrapper = Wrappers.lambdaUpdate(ProjectRoom.class)
                                .eq(ProjectRoom::getProjectId, projectId)
                                .eq(ProjectRoom::getIsUsed, "1");
                        final List<ProjectRoom> rooms = projectRoomService.list(wrapper);
                        this.saveConstructionPhotograph(projectId, rooms);
                    }
                    //客房平面审核
                    if (AtourSystemEnum.DesignNodeTow.DES00111.getKey().equals(projectGroup.getNodeCode())) {
                        this.roomPlanDrawing(projectId, projectGroup);
                    }
                }
            }
        } else {
            throw new BadRequestException("无修改的数据！");
        }
        return list;
    }

    @Override
    public List<ProjectNodeInfo> updateData(List<ProjectNodeInfo> list, Boolean isCommit) {//供应商和人员数据同步
        if (list.get(0).getNodeCode().startsWith("sup")||list.get(0).getNodeCode().startsWith("spm")) {
            return updateDataNew(list, isCommit);
        }
        //判断是否存在数据
        Boolean isUpdateNodeStatus = true;
        if (CollectionUtils.isNotEmpty(list)) {
            //用于添加 用户
            Map<String, List<ProjectNodeInfo>> pieceMap = list.stream().collect(Collectors.groupingBy(p -> StringUtils.isEmpty(p.getPieceIndex()) ? "" : p.getPieceIndex()));
            if (MapUtils.isNotEmpty(pieceMap)) {
                Set<String> pieceIndexSet = pieceMap.keySet();
                if (CollectionUtils.isNotEmpty(pieceIndexSet)) {
                    for (String pieceIndex : pieceIndexSet) {
                        if (StringUtils.isEmpty(pieceIndex)) {
                            continue;
                        }
                        this.createUserAddData(projectNodeInfoMapper.toDto(pieceMap.get(pieceIndex)));
                    }
                }
            }

            //保存
            Long projectId = getSubmeterProjectId(list.get(0).getProjectId());
            util.initialize(getSubmeterProjectId(list.get(0).getProjectId()));

            //查询签证报备列表
            LambdaQueryWrapper<ProjectVisaFiling> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(ProjectVisaFiling::getId, list.get(0).getProjectId());
            ProjectVisaFiling projectVisaFiling = projectVisaFilingRepository.selectOne(queryWrapper1);

            //判断进度描述（是否存在擅自施工），是否等于 yes
            Boolean unautor = false;
            for (ProjectNodeInfo projectNodeInfo : list) {
                //签证报备模版修改数据的时候，同步到签证报备列表中
                if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
                    this.upVisaFiling(projectNodeInfo, projectVisaFiling);
                }

                if (JhSystemEnum.unauthorizedConstruction.getNodeCode(projectNodeInfo.getNodeCode()) && (ObjectUtils.isNotEmpty(projectNodeInfo.getRemark()) && projectNodeInfo.getRemark().equals("yes"))) {
                    unautor = true;
                }
                if (JhSystemEnum.unauthorizedConstructionList.getNodeCode(projectNodeInfo.getNodeCode()) && unautor) {
                    if (ObjectUtil.isEmpty(projectNodeInfo.getUnauthorizedConstructions())) {
                        UnauthorizedConstructionQueryCriteria criteria = new UnauthorizedConstructionQueryCriteria();
                        criteria.setProjectId(list.get(0).getProjectId());
                        criteria.setNodeCode(projectNodeInfo.getNodeCode());
                        List<UnauthorizedConstructionDto> constructionDtos = unauthorizedConstructionService.queryByProjectId(criteria);
                        if (ObjectUtils.isEmpty(constructionDtos)) {
                            throw new BadRequestException("保存失败，进度描述已选择存在擅自施工，请确定是否已添加擅自施工列表");
                        }
                    }
                }
//                //【客房平面及效果图】选择的两个房号存入remake;des-00107057;des-00107060
//                if (AtourSystemEnum.DesignDrawing.DES00107057.getKey().equals(projectNodeInfo.getNodeCode())
//                        || AtourSystemEnum.DesignDrawing.DES00107060.getKey().equals(projectNodeInfo.getNodeCode())) {
//                    saveRemarkByRoom(projectNodeInfo);
//                }

                //【客房样板间施工图】选择的两个房号存入remake;des-00111033;des-00111036
                if (AtourSystemEnum.DesignDrawingTow.DES00111033.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.DesignDrawingTow.DES00111036.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.DesignDrawingTow.DES00111040.getKey().equals(projectNodeInfo.getNodeCode())) {
                    saveRemarkByRoom(projectNodeInfo);
                }
                //【竣工验收申请】【弱电验收申请】选择的房号存入remake;eng-00129025;eng-00137008
                if (AtourSystemEnum.CompletionWeakRoom.ENG00129025.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00137008.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00139009.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00141011.getKey().equals(projectNodeInfo.getNodeCode())
                        || AtourSystemEnum.CompletionWeakRoom.ENG00133051.getKey().equals(projectNodeInfo.getNodeCode())) {
                    saveRemarkByRoom(projectNodeInfo);
                }
                //筹建启动会-擅自施工
                if (projectNodeInfo.getUnauthorizedConstructions() != null && !projectNodeInfo.getUnauthorizedConstructions().isEmpty()) {
                    //先删除
                    unauthorizedConstructionService.deleteByIds(projectNodeInfo.getUnauthorizedConstructions().stream().map(UnauthorizedConstruction::getUnauthorizedId).distinct().collect(Collectors.toList()));
                    //在新增
                    unauthorizedConstructionService.saveOrUpdateBatch(projectNodeInfo.getUnauthorizedConstructions());
                }
                //项目进度
                if (projectNodeInfo.getNodeInfoDtos() != null && !projectNodeInfo.getNodeInfoDtos().isEmpty()) {
                    projectGroupService.saveOrUpdateBatch(projectNodeInfo.getNodeInfoDtos());
                }
                //质量管理
                if (projectNodeInfo.getQualityControls() != null && !projectNodeInfo.getQualityControls().isEmpty()) {
                    //先删除
                    qualityControlService.deleteByIds(projectNodeInfo.getQualityControls().stream().map(QualityControl::getQualityControlId).distinct().collect(Collectors.toList()));
                    qualityControlService.saveOrUpdateBatch(projectNodeInfo.getQualityControls());
                    if (projectNodeInfo.getQualityControls() != null && projectNodeInfo.getQualityControls().size() > 0) {
                        //如果检查内容字典表没有，则要插入字典表
                        List<DictDetail> descList = dictDetailRepository.findDictDetailByDictName("check_detail");
                        for (QualityControl control : projectNodeInfo.getQualityControls()) {
                            String desc = control.getQualityDescribe();//检查内容详情
                            String quaityName = control.getQualityName();//内容 completion_content
                            String importline = control.getIssueImportance();//重要程度 importance_of_the_problem
                            List<DictDetail> querylist = dictDetailRepository.getDictByNameAndParentInfo(desc, quaityName, "completion_content");
                            //如果没查出来，则需要在字典表中新增一条
                            if (querylist == null || querylist.size() < 1) {
                                DictDetail newOne = new DictDetail();
                                newOne.setDictSort(5);
                                newOne.setDict(descList.get(0).getDict());
                                newOne.setLabel(desc);
                                newOne.setValue("detail" + (dictDetailRepository.findMymaxvalue() + 1));
                                newOne.setParentId(dictDetailRepository.getIdByParentInfo(quaityName, "completion_content"));
                                newOne.setNextId(dictDetailRepository.getIdByParentInfo(importline, "importance_of_the_problem"));
                                newOne.setCreateBy("admin");
                                newOne.setCreateTime(new Timestamp(new Date().getTime()));
                                dictDetailRepository.save(newOne);
                            }
                        }
                    }
                }
                //竣工常规项目自检表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getRoutineSelfInspection())) {
                    //先删除
                    qualityControlService.deleteByIds(projectNodeInfo.getRoutineSelfInspection().stream().map(QualityControl::getQualityControlId).distinct().collect(Collectors.toList()));
                    qualityControlService.saveOrUpdateBatch(projectNodeInfo.getRoutineSelfInspection());
                }
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectSystemSelfInspection())) {
                    //先删除
                    projectSystemSelfInspectionService.deleteByIds(projectNodeInfo.getProjectSystemSelfInspection().stream().map(ProjectSystemSelfInspection::getSystemSelfInspectionId).distinct().collect(Collectors.toList()));
                    projectSystemSelfInspectionService.saveOrUpdateBatch(projectNodeInfo.getProjectSystemSelfInspection());
                }
                //施工照片
                if (projectNodeInfo.getConstructionPhotographs() != null && !projectNodeInfo.getConstructionPhotographs().isEmpty()) {
                    //没有删除，不需要删除
//                    constructionPhotographService.deleteByIds(projectNodeInfo.getConstructionPhotographs().stream().map(ConstructionPhotograph::getConstructionId).distinct().collect(Collectors.toList()));
                    constructionPhotographService.saveOrUpdateBatch(projectNodeInfo.getConstructionPhotographs());
                }
                //设计交底问题汇总
                if (projectNodeInfo.getProjectDisclosureProblems() != null && !projectNodeInfo.getProjectDisclosureProblems().isEmpty()) {
                    //先删除
                    projectDisclosureProblemService.deleteByIds(projectNodeInfo.getProjectDisclosureProblems().stream().map(ProjectDisclosureProblem::getDisclosureProblemId).distinct().collect(Collectors.toList()));
                    projectDisclosureProblemService.saveOrUpdateBatch(projectNodeInfo.getProjectDisclosureProblems());
                }
                //现场特殊情况暂无法解决事项
                if (projectNodeInfo.getProjectInsolubleMatter() != null && !projectNodeInfo.getProjectInsolubleMatter().isEmpty()) {
                    //先删除
                    projectInsolubleMatterService.deleteByIds(projectNodeInfo.getProjectInsolubleMatter().stream().map(ProjectInsolubleMatter::getInsolubleMatterId).distinct().collect(Collectors.toList()));
                    projectInsolubleMatterService.saveOrUpdateBatch(projectNodeInfo.getProjectInsolubleMatter());
                }
                //擅自施工与图纸不符部分
                if (projectNodeInfo.getProjectUnauthorizedConstructions() != null && !projectNodeInfo.getProjectUnauthorizedConstructions().isEmpty()) {
                    //先删除
                    projectUnauthorizedConstructionService.deleteByIds(projectNodeInfo.getProjectUnauthorizedConstructions().stream().map(ProjectUnauthorizedConstruction::getUnauthorizedConstructionId).distinct().collect(Collectors.toList()));
                    projectUnauthorizedConstructionService.saveOrUpdateBatch(projectNodeInfo.getProjectUnauthorizedConstructions());
                }
                //项目特殊情况说明表
                if (projectNodeInfo.getProjectSpecialCaseDescription() != null && !projectNodeInfo.getProjectSpecialCaseDescription().isEmpty()) {
                    //先删除
                    projectSpecialCaseDescriptionService.deleteByIds(projectNodeInfo.getProjectSpecialCaseDescription().stream().map(ProjectSpecialCaseDescription::getId).distinct().collect(Collectors.toList()));
                    projectSpecialCaseDescriptionService.saveOrUpdateBatch(projectNodeInfo.getProjectSpecialCaseDescription());
                }
                //项目的特许商特殊需求表
                if (projectNodeInfo.getProjectSpecialNeedsFranchisors() != null && !projectNodeInfo.getProjectSpecialNeedsFranchisors().isEmpty()) {
                    //先删除
                    projectSpecialNeedsFranchisorsService.deleteByIds(projectNodeInfo.getProjectSpecialNeedsFranchisors().stream().map(ProjectSpecialNeedsFranchisors::getFranchisorsId).distinct().collect(Collectors.toList()));
                    projectSpecialNeedsFranchisorsService.saveOrUpdateBatch(projectNodeInfo.getProjectSpecialNeedsFranchisors());
                }
                //【开工申请】的样板间确定
                if (projectNodeInfo.getProjectRoomSure() != null && !projectNodeInfo.getProjectRoomSure().isEmpty()) {
                    List<ProjectRoom> projectRoomSure = projectNodeInfo.getProjectRoomSure();
                    final LambdaQueryWrapper<ProjectRoom> wrapper = Wrappers.lambdaQuery(ProjectRoom.class)
                            .eq(ProjectRoom::getProjectId, projectNodeInfo.getProjectId());
                    final List<ProjectRoom> rooms = projectRoomRepository.selectList(wrapper);
                    rooms.stream().forEach(room -> projectRoomSure.stream().forEach(projectRoom -> {
                        if (ObjectUtil.isNotEmpty(projectRoom.getRoomNum()) && room.getRoomNum().equals(projectRoom.getRoomNum())) {
                            room.setIsModelRoom("1");
                            room.setIsUsed("1");
                            room.setIsMandatory(projectRoom.getIsMandatory());
                            projectRoomService.update(room);
                        }
                    }));
                }
                //物资管理材料表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectMaterialManagement())) {
                    //先删除
                    projectMaterialManagementService.deleteByIds(projectNodeInfo.getProjectMaterialManagement().stream().map(ProjectMaterialManagement::getProjectManagementId).distinct().collect(Collectors.toList()));
                    projectMaterialManagementService.saveOrUpdateBatch(projectNodeInfo.getProjectMaterialManagement());
                }
                //弱电设施设备信息表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectWeakCurrent())) {
                    //先删除
                    projectWeakCurrentService.deleteAll(projectNodeInfo.getProjectWeakCurrent().stream().map(ProjectWeakCurrent::getProjectWeakCurrentId).distinct().toArray(Long[]::new));
                    projectWeakCurrentService.saveOrUpdateBatch(projectNodeInfo.getProjectWeakCurrent());
                }
                //证照管理表
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectCateInfoList())) {
                    //先删除
                    projectCateInfoService.deleteByIds(projectNodeInfo.getProjectCateInfoList().stream().map(ProjectCateInfo::getCateId).distinct().collect(Collectors.toList()));
                    //将每个cate_type set为 new，新开店
                    projectNodeInfo.getProjectCateInfoList().stream().forEach(m -> {
                        //如果是证照新增的 为空就是可以删除
                        if (ObjectUtil.isEmpty(m.getNotDelete())) {
                            m.setNotDelete("0");
                        }
                        //1.17改动-只保留正式开工的证照
                        m.setThreeNodeCode(cateKey.CATE_THREE_1.getKey());
                        m.setTwoNodeCode(cateKey.CATE_TWO_1.getKey());
                        m.setCateType(cateKey.CATE_NEW.getKey());
                        m.setIsDelete(0);
                    });
                    projectCateInfoService.saveOrUpdateBatch(projectNodeInfo.getProjectCateInfoList());
                }
                //竣工验收验收单保存数据
                if (ObjectUtil.isNotEmpty(projectNodeInfo.getProjectCompletionReceiptListDto())) {
                    projectCompletionReceiptService.updateCompletionReceipt(projectNodeInfo.getProjectCompletionReceiptListDto());
                }


                //判断节点等级，若等级为1,2 则不可以修改 跳过
                if (null != projectNodeInfo.getNodeLevel() && projectNodeInfo.getNodeLevel() > 2) {
                    if (JhSystemEnum.NodeType.TEXTAREA.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_SELECT.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_DATE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.INPUT_SHOW_BLOCK.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.RADIO_BUTTON.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.SINGLE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.DIGITAL_SHOW_BLOCK.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.TABLE_VALUE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_SEARCH.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.MULTIPLE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_MULTI_INTER.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.LIST.getValue().equals(projectNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_SELECT_FILE.getValue().equals(projectNodeInfo.getNodeType())
                    ) {
                        if ("eng-00103060".equals(projectNodeInfo.getNodeCode()) && StringUtils.isNotEmpty(projectNodeInfo.getRemark())) {
                            updatePlanRelationTime(list.get(0).getProjectId(), projectNodeInfo.getRemark(), "2");
                        }
                        if ("sup-00101031".equals(projectNodeInfo.getNodeCode())) {
                            List<SupplierPm> supplierPmList = projectNodeInfo.getSupplierPmList();
                            if (supplierPmList != null && !supplierPmList.isEmpty()){
                                supplierPmList.forEach(p->{
                                SupplierInfo info = supplierInfoRepository.selectById(projectNodeInfo.getProjectId());
                                if (info != null) {
                                    String availableAmountStart = p.getAvailableAmountStart();
                                    String availableAmountEnd = p.getAvailableAmountEnd();
                                    StringBuilder builder = new StringBuilder();
                                    if (availableAmountStart != null) {
                                        builder.append(availableAmountStart);
                                    }
                                    builder.append(" , ");
                                    if (availableAmountEnd != null) {
                                        builder.append(availableAmountEnd);
                                    }
                                    p.setAvailableAmount(builder.toString());
                                    p.setFisyear(info.getReferrer());
                                    if (p.getSupplierId() == null) {
                                        p.setSupplierId(info.getId());
                                    }
//                                    if (StringUtils.isNotEmpty(p.getProjectScope())) {
//                                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
//                                                .in(LocalStorage::getId, p.getProjectScope().split(","))
//                                                .set(LocalStorage::getNodeId, projectNodeInfo.getNodeId()));
//                                    }
//                                    if (StringUtils.isNotEmpty(p.getPlanAmount())) {
//                                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
//                                                .in(LocalStorage::getId, p.getPlanAmount().split(","))
//                                                .set(LocalStorage::getNodeId, projectNodeInfo.getNodeId()));
//                                    }
                                    if (p.getSupplierPersonnelRoles() != null && p.getSupplierPersonnelRoles().length > 0) {
                                        StringBuilder sb = new StringBuilder();
                                        for (String role : p.getSupplierPersonnelRoles()) {
                                            sb.append(role).append(",");
                                        }
                                        p.setSupplierPersonnelRole(sb.substring(0, sb.length()-1));
                                    }
                                    if (p.getId() == null) {
                                        p.setStatus("pending_approval");
                                        supplierPmService.createPm(supplierPmMapper.toDto(p), projectNodeInfo.getProjectId());
                                    }else {
                                        supplierPmService.updateById(p);
                                    }
                                }
                            });
                            }
                        }
                        if ("eng-00103062".equals(projectNodeInfo.getNodeCode()) && StringUtils.isNotEmpty(projectNodeInfo.getRemark())) {
                            //插入设计的计划事件日期
                            updatePlanRelationTime(list.get(0).getProjectId(), projectNodeInfo.getRemark(), "1");
                        }
                        LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getNodeId, projectNodeInfo.getNodeId())
                                .set(ProjectNodeInfo::getRemark, projectNodeInfo.getRemark());
                        update(projectNodeInfoLambdaUpdateWrapper);
                        //TODO 保存的updateRelationCode未修改
                        if (StringUtils.isNotEmpty(projectNodeInfo.getRelationCode()) && "1".equals(projectNodeInfo.getIsEdit())) {
                            LambdaUpdateWrapper<ProjectNodeInfo> relationLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class).eq(ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId()).eq(ProjectNodeInfo::getNodeCode, projectNodeInfo.getRelationCode()).set(ProjectNodeInfo::getRemark, projectNodeInfo.getRemark());
                            update(relationLambdaUpdateWrapper);
                        }


                    } else if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(projectNodeInfo.getNodeType())) {

//                        //文件
//                        ProjectInfoDto project = projectInfoService.findById(projectNodeInfo.getProjectId());
//                        if (ObjectUtil.isNotEmpty(project) && JhSystemEnum.storeTypeEnum.WAREHOUSE.getKey().equals(project.getStoreType())) {
//                            //限定大仓的设计图节点的附件可以修改nodeName
//                            List<String> allowNameUpdates = Arrays.asList("con-0012308", "con-0012309", "con-0012310", "con-0012311", "con-0012312", "con-0012313", "con-0012314", "con-0012315", "con-0012316", "con-0012317", "con-0012318", "con-0012319", "con-0012320", "con-0012321", "con-0012322", "con-0012323", "con-0012324");
//                            if (ObjectUtil.isNotEmpty(projectNodeInfo.getNodeCode()) && allowNameUpdates.contains(projectNodeInfo.getNodeCode())) {
//                                LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class).eq(ProjectNodeInfo::getNodeId, projectNodeInfo.getNodeId()).set(ProjectNodeInfo::getNodeName, projectNodeInfo.getNodeName());
//                                update(projectNodeInfoLambdaUpdateWrapper);
//                            }
//                        }
                        continue;
                    }
                    //关联查询出干系人和其他页面数据
                    if (StringUtils.isNotEmpty(projectNodeInfo.getRelationType()) && isCommit) {
                        if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(projectNodeInfo.getRelationType()) && projectNodeInfo.getRemark() != null) {
//                            Long projectId = projectNodeInfo.getProjectId();
                            String roleCode = projectNodeInfo.getJobCode();
                            String downCode = projectNodeInfo.getDownCode();
                            Long userId = Long.parseLong(projectNodeInfo.getRemark());
                            projectInfoService.createProjectStakeholders(projectId, roleCode, userId, downCode);
                            //干系人
                        }
//                        else if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(projectNodeInfo.getRelationType()) && JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(projectNodeInfo.getJobCode())) {
//                            //合同责任人入刘群
//                            Long projectId = projectNodeInfo.getProjectId();
//                            String roleCode = projectNodeInfo.getJobCode();
//                            String downCode = projectNodeInfo.getDownCode();
//                            Long userId = 0L;
//                            projectInfoService.createProjectStakeholders(projectId, roleCode, userId, downCode);
//                        }
                    }
                    ProjectInfo projectInfo = projectInfoRepository.selectById(getSubmeterProjectId(projectNodeInfo.getProjectId()));

                    if (projectInfo != null) projectInfoRepository.updateById(projectInfo);
                    if (null != projectNodeInfo.getNodeIsfin() && isUpdateNodeStatus) {
                        //判断任意一个三级节点状态不为null，即可更新 二级节点 状态
                        LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectNodeInfo.getProjectId(), ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId()).eq(ProjectNodeInfo::getTemplateId, projectNodeInfo.getParentId());
                        ProjectNodeInfoDto parent = projectNodeInfoMapper.toDto(getOne(project));
                        if (null != parent) {
                            LambdaUpdateWrapper<ProjectNodeInfo> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                    .eq(ProjectNodeInfo::getNodeId, parent.getNodeId())
                                    .set(ProjectNodeInfo::getNodeIsfin, projectNodeInfo.getNodeIsfin());
                            update(projectNodeInfoLambdaUpdateWrapper);
                            //更新一次后即可
                            isUpdateNodeStatus = false;
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
                //修改签证报备列表
                projectVisaFilingRepository.updateById(projectVisaFiling);
            }

            //发起人
            Long initiatUser = SecurityUtils.getCurrentUserId();
            List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(initiatUser);
            ProjectGroup projectGroup = new ProjectGroup();
            if (ObjectUtil.isNotEmpty(userRoleCodes) && userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey()) || userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())) {
                LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectId, projectId)
                        .eq(ProjectGroup::getTemplateId, list.get(0).getParentId())
                        .last("limit 1");
                projectGroup = projectGroupRepository.selectOne(queryWrapper);
                if (ObjectUtil.isNotEmpty(projectGroup) && projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
                    //超级管理员角色进行保存后
                    //【开工申请】 样板间房号存进施工照片  更新新增施工照片
                    if (AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey().equals(projectGroup.getNodeCode())) {
                        final LambdaUpdateWrapper<ProjectRoom> wrapper = Wrappers.lambdaUpdate(ProjectRoom.class)
                                .eq(ProjectRoom::getProjectId, projectId)
                                .eq(ProjectRoom::getIsUsed, "1");
                        final List<ProjectRoom> rooms = projectRoomService.list(wrapper);
                        this.saveConstructionPhotograph(projectId, rooms);
                    }
                    //客房平面审核
                    if (AtourSystemEnum.DesignNodeTow.DES00111.getKey().equals(projectGroup.getNodeCode())) {
                        this.roomPlanDrawing(projectId, projectGroup);
                    }
                }
            }
        } else {
            throw new BadRequestException("无修改的数据！");
        }
        return list;
    }

    private void updateSupplier(List<ProjectNodeInfo> list) {
        if (list.get(0).getNodeCode().startsWith("sup")){
            SupplierInfo supplierInfo = supplierInfoService.getById(list.get(0).getProjectId());
            if (supplierInfo == null) {
                return;
            }
            list.forEach(nodeInfo->{
                if ("sup-00101002".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setSupNameCn(nodeInfo.getRemark());
                }
                if ("sup-00101003".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setSupplierType(nodeInfo.getRemark());
                }
                if ("sup-00101004".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setCompRegAddr(nodeInfo.getRemark());
                }
                if ("sup-00101005".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setPlatformProviderNot(nodeInfo.getRemark());
                }
                if ("sup-00101006".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setContact(nodeInfo.getRemark());
                }
                if ("sup-00101007".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setPhone(nodeInfo.getRemark());
                }
                if ("sup-00101008".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setEmail(nodeInfo.getRemark());
                }
                if ("sup-00101009".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setSupPostCode(nodeInfo.getRemark());
                }
                if ("sup-00101010".equals(nodeInfo.getNodeCode()) && ObjectUtils.isNotEmpty(nodeInfo.getRemark())) {
                    com.alibaba.fastjson.JSONArray array = JSON.parseObject(nodeInfo.getRemark(), com.alibaba.fastjson.JSONArray.class);
                    StringBuilder builder = new StringBuilder();
                    if (array != null && !array.isEmpty()) {
                        for (int i = 0; i < array.size(); i++) {
                            builder.append(array.getString(i)).append(",");
                        }
                    }
                    if (builder.length() > 0) {
                        supplierInfo.setReferrer(builder.substring(0, builder.length() - 1));
                    }
                }
                if ("sup-00101011".equals(nodeInfo.getNodeCode())) {
                    Set<Long> supAreaCodes = areaRepository.getAreaCodeBySupId(nodeInfo.getProjectId());
                    if (ObjectUtils.isNotEmpty(nodeInfo.getRemark())) {
                        com.alibaba.fastjson.JSONArray array = JSON.parseObject(nodeInfo.getRemark(), com.alibaba.fastjson.JSONArray.class);
                        if (array != null && !array.isEmpty() && array.size() != supAreaCodes.size()) {
                            Set<Long> citys = new HashSet<>();
                            for (int i = 0; i < array.size(); i++) {
                                citys.add(array.getLongValue(i));
                            }
                            areaRepository.deleteSupplierCitysBySupplierId(nodeInfo.getProjectId());
                            supplierInfoRepository.insertSupplierCity(citys, nodeInfo.getProjectId());
                        }
                    }
                }
                if ("sup-00101029".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setRemark(nodeInfo.getRemark());
                }
                if ("sup-00101030".equals(nodeInfo.getNodeCode()) && StringUtils.isNotEmpty(nodeInfo.getRemark())) {
                    com.alibaba.fastjson.JSONArray array = JSON.parseObject(nodeInfo.getRemark(), com.alibaba.fastjson.JSONArray.class);
                    StringBuilder builder = new StringBuilder();
                    if (array != null && !array.isEmpty()) {
                        for (int i = 0; i < array.size(); i++) {
                            builder.append(array.getString(i)).append(",");
                        }
                    }
                    if (builder.length() > 0) {
                        supplierInfo.setServiceScope(builder.substring(0, builder.length() - 1));
                    }
                }
                if ("sup-00101012".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setSupShortName(nodeInfo.getRemark());
                }
                if ("sup-00101013".equals(nodeInfo.getNodeCode())) {
                    if ("true".equals(nodeInfo.getRemark())) {
                        List<LocalStorage> storages = localStorageRepository.getByNodeIdList(nodeInfo.getNodeId().toString());
                        if (storages != null && !storages.isEmpty()) {
                            StringBuilder builder = new StringBuilder();
                            storages.forEach(s->{
                                builder.append(s.getId()).append(",");
                            });
                            supplierInfo.setBusinessLicense(builder.substring(0, builder.length()-1));
                        }
                    }
                }
                if ("sup-00101014".equals(nodeInfo.getNodeCode())) {
                    if ("long".equals(supplierInfo.getRemark()))
                        supplierInfo.setBusinessExpDate(nodeInfo.getRemark());
                }
                if ("sup-00101033".equals(nodeInfo.getNodeCode())) {
                    if (!"long".equals(supplierInfo.getBusinessExpDate()))
                        supplierInfo.setBusinessExpDate(nodeInfo.getRemark());
                }
                if ("sup-00101015".equals(nodeInfo.getNodeCode())) {
                    if ("true".equals(nodeInfo.getRemark())) {
                        List<LocalStorage> storages = localStorageRepository.getByNodeIdList(nodeInfo.getNodeId().toString());
                        if (storages != null && !storages.isEmpty()) {
                            StringBuilder builder = new StringBuilder();
                            storages.forEach(s->{
                                builder.append(s.getId()).append(",");
                            });
                            supplierInfo.setBusinessLicense(builder.substring(0, builder.length()-1));
                        }
                    }
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setDecorationExpDate(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setTaxNumber(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setBankNameCn(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setBankAccountNumber(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setBankNameEn(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    supplierInfo.setBankBranchCityCn(nodeInfo.getRemark());
                }
                if ("sup-********".equals(nodeInfo.getNodeCode())) {
                    nodeInfo.setRemark(supplierInfo.getMarketType());
                }
            });
            supplierInfoService.updateById(supplierInfo);
        } else if (list.get(0).getNodeCode().startsWith("spm")) {
            SupplierPm supplierPm = supplierPmService.getById(list.get(0).getProjectId());
            if (supplierPm == null) {
                return;
            }
            StringBuilder sb = new StringBuilder();
            list.forEach(nodeInfo -> {
                if ("spm-********".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setPhone(nodeInfo.getRemark());
                }
                if ("spm-********".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setPhone(nodeInfo.getRemark());
                }
                if ("spm-********".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setPhone(nodeInfo.getRemark());
                }
                if ("spm-00201005".equals(nodeInfo.getNodeCode()) && StringUtils.isNotEmpty(nodeInfo.getRemark())) {
                    com.alibaba.fastjson.JSONArray array = JSON.parseObject(nodeInfo.getRemark(), com.alibaba.fastjson.JSONArray.class);
                    StringBuilder builder = new StringBuilder();
                    if (array != null && !array.isEmpty()) {
                        for (int i = 0; i < array.size(); i++) {
                            builder.append(array.getString(i)).append(",");
                        }
                    }
                    if (builder.length() > 0) {
                        supplierPm.setSupplierPersonnelRole(builder.substring(0, builder.length() - 1));
                    }
                }
                if ("spm-00201006".equals(nodeInfo.getNodeCode())) {
                        Long supplierId = supplierInfoService.getByName(nodeInfo.getRemark());
                        if (supplierId != null)
                            supplierPm.setSupplierId(supplierId);
                }
                if ("spm-00201017".equals(nodeInfo.getNodeCode()) && StringUtils.isNotEmpty(nodeInfo.getRemark())) {
                    com.alibaba.fastjson.JSONArray array = JSON.parseObject(nodeInfo.getRemark(), com.alibaba.fastjson.JSONArray.class);
                    StringBuilder builder = new StringBuilder();
                    if (array != null && !array.isEmpty()) {
                        for (int i = 0; i < array.size(); i++) {
                            builder.append(array.getString(i)).append(",");
                        }
                    }
                    if (builder.length() > 0) {
                        supplierPm.setFisyear(builder.substring(0, builder.length() - 1));
                    }
                }
                if ("spm-00201007".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setProjectScope(nodeInfo.getRemark());
                }
                if ("spm-00201018".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setRemark(nodeInfo.getRemark());
                }
                if ("spm-00201008".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setPlanAmount(nodeInfo.getRemark());
                }
                if ("spm-00201009".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setAssignAmount(nodeInfo.getRemark());
                }
                if ("spm-00201009".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setAssignAmount(nodeInfo.getRemark());
                }

                if ("spm-00201010".equals(nodeInfo.getNodeCode())) {
                    sb.append(nodeInfo.getRemark()).append(",");
                }
                if ("spm-00201011".equals(nodeInfo.getNodeCode())) {
                    sb.append(nodeInfo.getRemark());
                }
                if ("spm-00201013".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setFinishAmount(nodeInfo.getRemark());
                }
                if ("spm-00201014".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setCurrentQualityScore(nodeInfo.getRemark());
                }
                if ("spm-00201015".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setAvgQualityScore(nodeInfo.getRemark());
                }
                if ("spm-00201016".equals(nodeInfo.getNodeCode())) {
                    supplierPm.setEvaluationScore(nodeInfo.getRemark());
                }
            });
            supplierPm.setAvailableAmount(sb.toString());
            supplierPmService.updateById(supplierPm);
        }

    }

    private void upVisaFiling(ProjectNodeInfo projectNodeInfo, ProjectVisaFiling projectVisaFiling) {

        if (ObjectUtil.isNotEmpty(projectVisaFiling)) {
            if ((projectNodeInfo.getNodeType().equals(JhSystemEnum.NodeType.TEXTAREA.getValue()) && projectNodeInfo.getNodeCode().contains("03"))) {
                projectVisaFiling.setPresentationOndition(projectNodeInfo.getRemark());
            }

            if (projectNodeInfo.getNodeType().equals(JhSystemEnum.NodeType.FILE_UPLOAD.getValue())) {
                List<LocalStorage> byNodeId = localStorageService.findByNodeId(projectNodeInfo.getNodeId().toString());
                if (ObjectUtil.isNotEmpty(byNodeId)) {
                    List<Long> collect = byNodeId.stream().map(LocalStorage::getId).collect(Collectors.toList());
                    projectVisaFiling.setAttachment(collect.stream().map(String::valueOf).collect(Collectors.joining(",")));
                }
            }
        }

    }

    /**
     * 弹窗类型的需要手动存值remark
     */
    private void saveRemarkByRoom(ProjectNodeInfo projectNodeInfo) {
        final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, projectNodeInfo.getNodeCode());
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(wrapper);
        nodeInfo.setRemark(projectNodeInfo.getRemark());
        projectNodeInfoRepository.update(nodeInfo, wrapper);

    }

    /**
     * @param projectId
     * @param timeStr
     * @param type
     */
    private void updatePlanRelationTime(Long projectId, String timeStr, String type) {
        java.sql.Date setDate = null;
        try {
            setDate = java.sql.Date.valueOf(timeStr);
        } catch (Exception e) {
        }
        //插入施工的计划事件日期
        if (setDate != null) {
            int day = 0;
            if ("2".equals(type)) {
                final LambdaQueryWrapper<ProjectRoom> eq = Wrappers.lambdaQuery(ProjectRoom.class)
                        .eq(ProjectRoom::getProjectId, projectId);
                final List<ProjectRoom> rooms = projectRoomRepository.selectList(eq);
                final ArrayList<ProjectRoom> collect = rooms.stream().collect(Collectors
                        .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProjectRoom::getRoomNum))),
                                ArrayList::new));
                if (collect != null && collect.size() > 0) {
                    day = (int) Math.round((collect.size() - 140) * 2d / 5d);
                }
            }
            List<TProjectPlaneventRelation> relations = planeventRelationRepository.selectList(new QueryWrapper<TProjectPlaneventRelation>().eq("project_id", projectId));
            if (relations != null && relations.size() > 0) {
                List<Long> eventIds = new ArrayList<>();
                for (TProjectPlaneventRelation relation : relations) {
                    eventIds.add(relation.getPlanEventId());
                }
                List<TPlanEventInfo> planEventInfoList = planEventInfoRepository.selectList(new QueryWrapper<TPlanEventInfo>().in("plan_event_id", eventIds));
                for (TProjectPlaneventRelation relation : relations) {
                    TPlanEventInfo eventInfo = null;
                    for (TPlanEventInfo info : planEventInfoList) {
                        if (info.getPlanEventId().equals(relation.getPlanEventId())) {
                            eventInfo = info;
                            break;
                        }
                    }
                    if (eventInfo != null && type.equals(eventInfo.getPlanEventBelong())) {
                        if (day != 0 && eventInfo.getPlanEventName().contains("家具施工")) {
                            relation.setPlanStartDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() - eventInfo.getPlanEventDuration()))));
                            relation.setPlanEndDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() + day))));
                        } else if (day != 0 && (eventInfo.getPlanEventName().contains("形象进度") || eventInfo.getPlanEventName().contains("竣工自检"))) {
                            relation.setPlanStartDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() + day - eventInfo.getPlanEventDuration()))));
                            relation.setPlanEndDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() + day))));
                        } else {
                            relation.setPlanStartDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() - eventInfo.getPlanEventDuration()))));
                            relation.setPlanEndDate(new java.sql.Date(setDate.getTime() + (1000l * 60 * 60 * 24 * eventInfo.getAllDuration())));
                        }
                        planeventRelationRepository.update(relation, new QueryWrapper<TProjectPlaneventRelation>().eq("node_code", relation.getNodeCode()).eq("plan_event_id", relation.getPlanEventId()).eq("project_id", relation.getProjectId()));
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createUserAddData(List<ProjectNodeInfoDto> listDto) {
        List<ProjectNodeInfo> list = projectNodeInfoMapper.toEntity(listDto);
        Boolean flag = false;
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        if (ObjectUtils.isNotEmpty(list) && list.size() > 0) {
            // ProjectNodeInfo maxInfo =  list.stream().max((p1, p2) -> p1.getNodeIndex() - p2.getNodeIndex()).get();
            ProjectNodeInfo maxInfo = list.stream().max(Comparator.comparing(ProjectNodeInfo::getNodeIndex)).orElse(null);

            String lastCode = maxInfo.getNodeCode();
            Long parentId = list.get(0).getParentId();
            Long projectId = list.get(0).getProjectId();
            String suffix = "";
            Integer maxAddedVersion = projectNodeInfoRepository.getMaxAddedVersion(projectId, parentId);
            if (ObjectUtils.isEmpty(maxAddedVersion)) {
                maxAddedVersion = 0;
            }
            maxAddedVersion = maxAddedVersion + 1;
            String versionStr = String.valueOf(maxAddedVersion);
            if (versionStr.length() == 1) {
                versionStr = "0" + versionStr;
            }
            for (ProjectNodeInfo nodeInfo : list) {
//                Long nodeId = snowflake.nextId();
//                nodeInfo.setNodeId(nodeId);
                //用户新增的节点信息块  需要重新给templateId 根据TreeNode的结构，为了查询的时候可以显示 否则会被之前的原数据覆盖
                nodeInfo.setNodeId(snowflake.nextId());
                nodeInfo.setTemplateId(snowflake.nextId());
                nodeInfo.setNodeCode(nodeInfo.getNodeCode() + versionStr);
                nodeInfo.setAddedVersion(maxAddedVersion);
                nodeInfo.setLastCode(lastCode);
                nodeInfo.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
                nodeInfo.setUpdateBy(null);
                nodeInfo.setUpdateTime(null);

                if (ObjectUtil.isNotEmpty(nodeInfo.getFileId())) {
                    Long aLong = Long.valueOf(nodeInfo.getFileId());
                    LocalStorageDto byId = localStorageService.findById(aLong);
                    byId.setNodeId(nodeInfo.getNodeId().toString());
                    localStorageService.update(localStorageMapper.toEntity(byId));
                }
            }
            flag = saveBatch(list);
        }
        return flag;
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoDto submit(ProjectNodeInfoDto projectNodeInfoD) {
        Long initiatUser = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<ProjectGroup> gLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(null != projectNodeInfoD.getProjectGroupId(),
                        ProjectGroup::getProjectGroupId, projectNodeInfoD.getProjectGroupId());
        ProjectGroup pg = projectGroupRepository.selectOne(gLambdaQueryWrapper);
        ProjectGroupDto projectGroupDto = projectGroupMapper.toDto(pg);
        if (null == projectGroupDto || null == projectGroupDto.getNodeLevel() || projectGroupDto.getNodeLevel() != 2) {
            throw new BadRequestException("传递的nodeId 节点参数值 不正确，不是2级节点");
        }

        String projectId = projectNodeInfoD.getProjectId();
        if (projectNodeInfoD.getList() != null && !projectNodeInfoD.getList().isEmpty()) {
            List<ProjectNodeInfoDto> listDto = projectNodeInfoD.getList();
            List<ProjectNodeInfo> list = projectNodeInfoMapper.toEntity(listDto);
            updateData(list, Boolean.TRUE);
        }
        //提交验证
        this.submitVerification(projectGroupDto, projectNodeInfoD);


        //创建审批
        //查询是否有关联的审批节点
        //Boolean hasApprove = projectAppTemplateService.hasApprove(projectNodeInfoD);
        Boolean hasApprove = Boolean.FALSE;
        List<ProjectTemplateApproveRelationDto> appRelation = projectTemplateApproveRelationService.getAppRelation(Long.valueOf(projectGroupDto.getTemplateId()), Long.valueOf(projectGroupDto.getTemplateGroupId()));
        if (!appRelation.isEmpty()) {
            hasApprove = Boolean.TRUE;
        }
        Boolean thirdGroup = projectGroupService.isThirdGroup(projectGroupDto);

        //开工申请任务完成后
        if (hasApprove) {

            // 判断审批
            boolean estimate = projectApproveService.estimate(projectNodeInfoD);
            if (estimate) {
                throw new BadRequestException("当前节点审批中，请核实后重新提交");
            }

            projectApproveService.createApprove(projectGroupDto);

            //新增审批数据
            logger.info(projectGroupDto.getNodeName() + "节点有审批");
            //发送消息通知-提交-需要审批
            projectNoticeService.generateNotice(Long.valueOf(projectNodeInfoD.getNodeId()), pg, JhSystemEnum.MessageTemplate.*********, initiatUser);
        } else if (thirdGroup) {
            //若存在调用中台接口情况
            List<ProjectApprove> approveList = Optional.ofNullable(projectApproveService.queryEmpByNodeId(Long.valueOf(projectGroupDto.getProjectGroupId()))).orElseGet(LinkedList::new);
            if (!approveList.isEmpty()) {
                if (thirdGroup && (JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(approveList.get(0).getApproveResult()) || JhSystemEnum.approveResultEnum.APPROVE_REJECT.getKey().equals(approveList.get(0).getApproveResult()) || JhSystemEnum.approveResultEnum.APPROVE_STOP.getKey().equals(approveList.get(0).getApproveResult()))) {
                    kwThirdService.submitKoaContractProcess(projectGroupMapper.toEntity(projectGroupDto), Boolean.TRUE);
                }
            } else {
                kwThirdService.submitKoaContractProcess(projectGroupMapper.toEntity(projectGroupDto), Boolean.FALSE);
            }
            //发送消息通知-提交-不需要审批
            projectNoticeService.generateNotice(Long.valueOf(projectNodeInfoD.getNodeId()), pg, JhSystemEnum.MessageTemplate.*********, initiatUser);
        } else {
            //提交后进行的操作
            this.updateGroupStatusNext(projectGroupMapper.toEntity(projectGroupDto));
            //发送消息通知-提交-不需要审批
            projectNoticeService.generateNotice(Long.valueOf(projectNodeInfoD.getNodeId()), pg, JhSystemEnum.MessageTemplate.*********, initiatUser);
        }

        // 有审批的任务需要直接发送通知，手机短信以及企微
        ProjectGroup projectGroup = projectGroupMapper.toEntity(projectGroupDto);
        if (AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey().equals(projectGroup.getNodeCode()) || AtourSystemEnum.EngineeringNodeTow.ENG00113.getKey().equals(projectGroup.getNodeCode()) || AtourSystemEnum.EngineeringNodeTow.ENG00115.getKey().equals(projectGroup.getNodeCode())) {
            // 发送短信和企微
            String messageText = "";
            StringBuffer messageTitle = new StringBuffer("");
            messageTitle.append("【营建新系统】待办提醒：");
            sendMyMessage(projectGroup.getProjectId(),projectGroup.getProjectGroupId().toString(),
                    projectGroup.getNodeCode(), messageTitle, AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey()
                    , "01");
        }

        //是否逾期
        Boolean isOverDue = Boolean.FALSE;
       /* if (projectNodeInfoDto.getNodeIsfin()==null|| (!projectNodeInfoDto.getNodeIsfin())) {
            throw new BadRequestException("传递的nodeId 节点参数值 对应节点状态不正确，请先保存完成状态");
        }*/


        Boolean isUpdateNodeStatus = true;
        if (isUpdateNodeStatus) {
            //更新已完成字段
            //判断紧前任务是否都已完成
            String frontWbsConfig = projectGroupDto.getFrontWbsConfig();
            if (ObjectUtils.isNotEmpty(frontWbsConfig)) {
                List<FrontWbsConfigDto> frontList = new ArrayList<>();
                JSONArray jsonArray = new JSONArray(frontWbsConfig);
                Boolean statusFlag = Boolean.TRUE;
                String unFinish = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
                    JSONObject object = jsonArray.getJSONObject(i);
                    String type = object.getStr("type");
                    String wbs = object.getStr("wbs");
                    if ("FS".equals(type)) {
                        //查找当前节点的紧前是否已完成
                        LambdaQueryWrapper<ProjectGroup> lastLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                                .eq(ProjectGroup::getProjectId, projectId).eq(ProjectGroup::getNodeCode, wbs);
                        ProjectGroup one = projectGroupService.getOne(lastLambdaQueryWrapper);
                        if (one == null || (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(one.getNodeStatus()))) {
                            statusFlag = Boolean.FALSE;
                            unFinish = unFinish + (ObjectUtil.isNotEmpty(one) ? one.getNodeName() + "," : "当前上一个节点数据没查询到");
                        }
                    }
                }
                if (!statusFlag) {
                    unFinish = unFinish.substring(0, unFinish.length() - 1);
                    throw new BadRequestException(unFinish + "节点尚未完成，请先处理前置节点");
                }

//                因决策信息项目模版初始化状态为已完成，所以不需要根据项目任务来修改对应决策信息的状态
//                //修改项目任务对应的决策信息状态
//                this.updateDecMaking(projectGroupDto);
            }

            if (null != projectGroupDto) {



                LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectGroupId, projectGroupDto.getProjectGroupId())
                        .set(ProjectGroup::getNodeIsfin, Boolean.TRUE)
                        .set(ProjectGroup::getIsSubmit, Boolean.TRUE)
                        .set(ProjectGroup::getActualEndDate, new Date());
                if (hasApprove || thirdGroup) {
                    projectNodeInfoLambdaUpdateWrapper.set(ProjectGroup::getActualEndDate, null);
                    projectNodeInfoLambdaUpdateWrapper.set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
//                    深化列表中的审图状态，目前不根据深化任务来进行判断
//                    if (projectGroupDto.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey())) {
//                        //当前二级为 深化方案的话，修改当前审图记录的状态为审图中
//                        this.upDeepeningPlanStatus(projectGroupDto.getProjectId(),projectGroupDto.getNodeCode(),AtourSystemEnum.ReviewDrawingStatus.IN_THE_REVIEW_OF_DRAWINGS.getKey());
//                    }
                }
                projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);

//                //二级节点提交时，   当前系统不存在联合任务
//                //1.当前二级节点作为不确定方，查询是否已经配置了联合任务、是否满足联合条件，满足的话，修改当前二级节点的状态为不确定状态、联合二级为未完成状态
//                this.upProjectInfoJointTaskByNodeCode(projectNodeInfoD);
//                //2.当前二级节点作为联合二级，查询是否已经配置了联合任务的不确定方，配置了的话，查询不确定方的二级下的所有联合二级是否已经完成，全部完成则修改不确定方的二级为未完成的状态
//                this.upProjectInfoJointTaskByRelevanceNodeCode(projectNodeInfoD);

                //更新一次后即可
                isUpdateNodeStatus = false;
            }

        }
        projectTaskService.finishTask(projectGroup);
        return projectNodeInfoD;
    }

    private void submitVerification(ProjectGroupDto projectGroupDto, ProjectNodeInfoDto projectNodeInfoD) {
        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00135.getKey())) {
            //1.整改清单存在【整改中】状态不允许提交
            final LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                    .eq(ProjectCompletionReceipt::getProjectId, projectGroupDto.getProjectId())
                    .eq(ProjectCompletionReceipt::getAcceptance, "unqualified")
                    .and(wrapper -> wrapper.isNull(ProjectCompletionReceipt::getRectificationInstructions)
                            .or().eq(ProjectCompletionReceipt::getRectificationInstructions, "in_process"));
//                    .eq(ProjectCompletionReceipt::getRectificationInstructions, "in_process");
            List<ProjectCompletionReceipt> receipts = projectCompletionReceiptRepository.selectList(queryWrapper);
            if (receipts != null && receipts.size() > 0) {
                throw new BadRequestException("整改清单中没有选择“整改说明”或者存在【整改中】数据，不允许提交！");
            }
//            //2.资料审核存在非【通过】状态，不允许提交
//            String[] nodeCode = {"eng-00129191", "eng-00129195"};
//            util.initialize(this.getSubmeterProjectId(Long.valueOf(projectId)));
//            LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class);
//            wrapper.eq(ProjectNodeInfo::getProjectId, projectNodeInfoD.getProjectId());
//            wrapper.in(ProjectNodeInfo::getNodeCode, nodeCode);
//            wrapper.ne(ProjectNodeInfo::getRemark, "pass");
//            List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(wrapper);
//            if (ObjectUtil.isNotEmpty(nodeInfos) && nodeInfos.size() > 0) {
//                throw new BadRequestException("当前资料审核必须为【通过】，不允许提交！");
//            }
        }


        //隐蔽验收申请 任务完成后
        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00115.getKey())) {
            //1. 判断图纸文件是否存在 提示哪些客房图纸、深化图纸任务没有已完成 且不允许提交。   获取是否存在未完成的深化任务
            LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class);
            queryWrapper.eq(ProjectGroup::getProjectId, projectGroupDto.getProjectId());
            queryWrapper.and(queryWrapper1 -> queryWrapper1.eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey())
                    .or().eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey())
                    .or().eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey())
            );
            queryWrapper.eq(ProjectGroup::getTemplateCode, JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey());
            queryWrapper.eq(ProjectGroup::getNodeLevel, 2);
            List<ProjectGroup> projectGroups = projectGroupRepository.selectList(queryWrapper);
            String msg= "";
            for (int i = 0; i < projectGroups.size(); i++) {
                ProjectGroup projectGroup = projectGroups.get(i);
                if (StringUtils.isNotEmpty(msg)) {
                    msg=","+ projectGroup.getNodeName()+JhSystemEnum.NodeStatusEnum.getNodeStatus(projectGroup.getNodeStatus());
                }else{
                    msg=projectGroup.getNodeName()+JhSystemEnum.NodeStatusEnum.getNodeStatus(projectGroup.getNodeStatus());
                }
            }
            if(StringUtils.isNotEmpty(msg)&&msg.length()>2){
                throw new BadRequestException("当前深化方案存在未完成任务："+msg);
            }
        }

        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00129.getKey())) {
            String[] nodeCode = {AtourSystemEnum.completionAcceptanceEnum.ENG00129188.getKey()
                    , AtourSystemEnum.completionAcceptanceEnum.ENG00129189.getKey(), AtourSystemEnum.completionAcceptanceEnum.ENG00129192.getKey()
                    , AtourSystemEnum.completionAcceptanceEnum.ENG00129193.getKey()};
            util.initialize(this.getSubmeterProjectId(Long.valueOf(projectGroupDto.getProjectId())));
            LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class);
            queryWrapper.eq(ProjectNodeInfo::getProjectId, Long.valueOf(projectGroupDto.getProjectId()));
            queryWrapper.in(ProjectNodeInfo::getNodeCode, nodeCode);
            List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(queryWrapper);

            String tmp1 = "";
            String tmp2 = "";
            String tmp3 = "";
            String tmp4 = "";
            if (nodeInfos != null && nodeInfos.size() != 0) {
                for (ProjectNodeInfo dto : nodeInfos) {
                    if (dto.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129188.getKey())) {
                        List<LocalStorage> localStorages = new LinkedList<>();
                        localStorages = localStorageService.findByNodeId(dto.getNodeId().toString());
                        if (localStorages != null && localStorages.size() > 0) {
                            tmp1 = "have";
                        }
                    } else if (dto.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129189.getKey())) {
                        tmp2 = dto.getRemark();
                    } else if (dto.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129192.getKey())) {
                        List<LocalStorage> localStorages = new LinkedList<>();
                        localStorages = localStorageService.findByNodeId(dto.getNodeId().toString());
                        if (localStorages != null && localStorages.size() > 0) {
                            tmp3 = "have";
                        }
                    } else if (dto.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129193.getKey())) {
                        tmp4 = dto.getRemark();
                    }
                }
            }
            if (StringUtils.isBlank(tmp1) && StringUtils.isBlank(tmp2)) {
                throw new BadRequestException("门店紧急维修联系表(按照固定模版)和提供日期必须二选一！");
            }
            if (StringUtils.isBlank(tmp3) && StringUtils.isBlank(tmp4)) {
                throw new BadRequestException("合伙人满意度评分表和提供日期必须二选一！");
            }
        }

//        //【开工申请】提交判断是否存在样板间房间号数据
//        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey())) {
//            int count = projectRoomRepository.getRoomisModelRoomCount(Long.valueOf(projectId));
//            if (count <= 0) {
//                throw new BadRequestException("样板间工程房号为空，请选择样板间工程房号！");
//            }
//        }
        //【客房样板间施工图】提交判断是否存在房间号数据
        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00111.getKey())) {
            int count = projectRoomRepository.getRoomCount(Long.valueOf(projectGroupDto.getProjectId()));
            if (count <= 0) {
                throw new BadRequestException("房间号数据不存在，请重新上传房间号文件！");
            }
        }

        //【竣工验收】提交判断所有验收单是否已经选择【验收】了
        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00133.getKey())) {
            int count = projectCompletionReceiptRepository.getAcceptanceCount(Long.valueOf(projectGroupDto.getProjectId()));
            if (count > 0) {
                throw new BadRequestException("当前【验收单】存在未验收项，请完整填写验收项！");
            }

            ProjectCompletionReceiptDescriptionQueryCriteria criteria = new ProjectCompletionReceiptDescriptionQueryCriteria();
            criteria.setProjectId(Long.valueOf(projectGroupDto.getProjectId()));
            ProjectCompletionReceiptDescription byProjectId = projectCompletionReceiptDescriptionService.getByProjectId(criteria);
            if (ObjectUtil.isEmpty(byProjectId)
                    || (ObjectUtil.isNotEmpty(byProjectId) && (ObjectUtil.isEmpty(byProjectId.getHotelBuilding()) || ObjectUtil.isEmpty(byProjectId.getRiskManagement())
                    || ObjectUtil.isEmpty(byProjectId.getFacilitiesManagement()) || ObjectUtil.isEmpty(byProjectId.getPropertyDivision()) || ObjectUtil.isEmpty(byProjectId.getNetwork())
                    || ObjectUtil.isEmpty(byProjectId.getSoundInsulation()) || ObjectUtil.isEmpty(byProjectId.getPeculiarSmell()) || ObjectUtil.isEmpty(byProjectId.getAirConditioner())
                    || ObjectUtil.isEmpty(byProjectId.getHotWater()) || ObjectUtil.isEmpty(byProjectId.getRests())))) {
                throw new BadRequestException("提交失败，请完整填写【项目说明】！");
            }
            ProjectCompletionReceiptSummary receiptSummary = projectCompletionReceiptSummaryService.selectByProjectId(Long.valueOf(projectGroupDto.getProjectId()));
            if (ObjectUtil.isEmpty(receiptSummary) ||
                    (ObjectUtil.isNotEmpty(receiptSummary) && (ObjectUtil.isEmpty(receiptSummary.getInstallationStandards()) || ObjectUtil.isEmpty(receiptSummary.getLinkageTest()) || ObjectUtil.isEmpty(receiptSummary.getFireBroadcast())
                            || ObjectUtil.isEmpty(receiptSummary.getFireWater()) || ObjectUtil.isEmpty(receiptSummary.getFireEvacuation()) || ObjectUtil.isEmpty(receiptSummary.getThreeCalling()) || ObjectUtil.isEmpty(receiptSummary.getLadderControl())
                            || ObjectUtil.isEmpty(receiptSummary.getCertificate()) || ObjectUtil.isEmpty(receiptSummary.getAccessControl())
                    ))) {
                throw new BadRequestException("提交失败，请完整填写【验收汇总】！");
            }
        }

        //【竣工验收申请】触发待办逻辑为：所有质量问题整改完成，才可发起待办任务；
        if (projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00129.getKey())) {
            int count = qualityControlRepository.getByrectificationConfirmation(Long.valueOf(projectGroupDto.getProjectId()));
            if (count > 0) {
                throw new BadRequestException("【品质-工程整改问题】中的所有质量问题需要整改完成，才可以提交【竣工验收申请】任务！");
            }
        }

        //深化方案任务、设计任务提交前，对当前页面图纸做必填校验
        if (projectNodeInfoD.getNodeCode().contains("dep") || projectNodeInfoD.getNodeCode().contains("des") || projectNodeInfoD.getNodeCode().contains("pad")) {
            final ArrayList<String> file = new ArrayList<>();
            final LambdaQueryWrapper<ProjectGroupExpand> eq = Wrappers.lambdaQuery(ProjectGroupExpand.class)
                    .eq(ProjectGroupExpand::getProjectId, projectNodeInfoD.getProjectId())
                    .eq(ProjectGroupExpand::getGroupNodeCode, projectNodeInfoD.getNodeCode());
            final List<ProjectGroupExpand> list = projectGroupExpandService.list(eq);
            if (ObjectUtil.isNotEmpty(list)) {
                list.forEach(a -> {
                    if (ObjectUtil.isNotEmpty(a.getDrawingStatus()) && !AtourSystemEnum.ReviewDrawingStatus.NOT_APPLICABLE.getKey().equals(a.getDrawingStatus())
                            && ObjectUtil.isEmpty(a.getDrawingFile())) {
                        //当前项目任务的审图状态不等于【非适应】且审图文件为空
                        file.add(a.getFileHeader());
                    }
                });
                if (ObjectUtil.isNotEmpty(file)) {
                    throw new BadRequestException("当前页面的【" + String.join(", ", file) + "】未上传，请上传后再提交");
                }
                //修改当前二级任务的审图轮次的是否提交审批字段为 1 已提交
                List<Long> collect = list.stream().map(ProjectGroupExpand::getExpandId).collect(Collectors.toList());
                projectCommunicationService.upCommunicationFromNew(collect);
            }
        }
    }

    private void upDeepeningPlanStatus(String projectId, String nodeCode, String drawingStatus) {
        LambdaQueryWrapper<ProjectGroupExpand> queryWrapper = Wrappers.lambdaQuery(ProjectGroupExpand.class);
        queryWrapper.eq(ProjectGroupExpand::getProjectId, projectId)
                .eq(ProjectGroupExpand::getGroupNodeCode, nodeCode)
                .last("limit 1");
        ProjectGroupExpand projectGroupExpand = projectGroupExpandService.getOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(projectGroupExpand)) {
            projectGroupExpand.setDrawingStatus(drawingStatus);
            projectGroupExpandService.updateById(projectGroupExpand);
        }
    }

    /*
        设计阶段附件《客房平面布置图、客房样板间施工图、客房隔墙尺寸图》，此三份图纸审核通过，方可完成本任务【开工申请】
    */
    private void reviewDrawingByDes(ProjectGroupDto projectGroupDto, String projectId) {
        String statusA = getProjectGroupStatus(AtourSystemEnum.DesignNodeTow.DES00107.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
        String statusB = getProjectGroupStatus(AtourSystemEnum.DesignNodeTow.DES00111.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
        final ArrayList<String> statusAList = new ArrayList<>();
        final ArrayList<String> statusBList = new ArrayList<>();
        final String file1 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.RoomPlanReview.DES00107015.getKeyA());
        statusAList.add(file1);
        final String file2 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.RoomPlanReview.DES00107015.getKeyB());
        statusAList.add(file2);
        final String file3 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.ReviewOfConstructionDrawingsForGuestRoomModelRooms.DES00111018.getKeyA());
        statusBList.add(file3);
        final String file4 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.ReviewOfConstructionDrawingsForGuestRoomModelRooms.DES00111018.getKeyB());
        statusBList.add(file4);
        final String file5 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.ReviewOfConstructionDrawingsForGuestRoomModelRooms.DES00111019.getKeyA());
        statusBList.add(file5);
        final String file6 = projectNodeInfoRepository.getNodeIdByNodeCode(Long.valueOf(projectId), AtourSystemEnum.ReviewOfConstructionDrawingsForGuestRoomModelRooms.DES00111019.getKeyB());
        statusBList.add(file6);

        if (!statusA.equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
            throw new BadRequestException("请先完成【客房平面及效果图】任务");
        } else {
            statusAList.stream().forEach(e -> {
                final List<LocalStorage> storages = localStorageRepository.findByNodeId(e);
                if (ObjectUtil.isEmpty(storages)) {
                    throw new BadRequestException("【客房平面及效果图】任务的【客房平面图】未确认，请先确认图纸");
                }
            });
        }
        if (!statusB.equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
            throw new BadRequestException("请先完成【客房样板间施工图】任务");
        } else {
            statusBList.stream().forEach(e -> {
                final List<LocalStorage> storages = localStorageRepository.findByNodeId(e);
                if (ObjectUtil.isEmpty(storages)) {
                    throw new BadRequestException("【客房样板间施工图】任务的【平面隔墙尺寸图】或【样板间施工图】未确认，请先确认图纸");
                }
            });
        }
    }

    private void updateStakeholders(String projectId, String nodeCode, String role) {
        LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode);
        ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(eq);
        if (ObjectUtil.isNotEmpty(projectNodeInfo) && ObjectUtil.isNotEmpty(projectNodeInfo.getRemark())) {
            ProjectStakeholders stakeholders = new ProjectStakeholders();
            LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                    .eq(ProjectStakeholders::getProjectId, projectId)
                    .eq(ProjectStakeholders::getRoleCode, role);
            stakeholders.setUserId(Long.valueOf(projectNodeInfo.getRemark()));
            stakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            projectStakeholdersRepository.update(stakeholders, wrapper);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoDto adjustSubmit(ProjectNodeInfoDto projectNodeInfoD) {
        LambdaQueryWrapper<ProjectGroup> gLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != projectNodeInfoD.getProjectGroupId(), ProjectGroup::getProjectGroupId, projectNodeInfoD.getProjectGroupId());
        ProjectGroup projectGroup = projectGroupRepository.selectOne(gLambdaQueryWrapper);
        ProjectGroupDto projectGroupDto = projectGroupMapper.toDto(projectGroup);

        if (null == projectGroupDto || null == projectGroupDto.getNodeLevel() || projectGroupDto.getNodeLevel() != 2) {
            throw new BadRequestException("传递的nodeId 节点参数值 不正确，不是2级节点");
        }
        String projectId = projectNodeInfoD.getProjectId();
        if (projectNodeInfoD.getList() != null && projectNodeInfoD.getList().size() != 0) {
            List<ProjectNodeInfoDto> listDto = projectNodeInfoD.getList();
            List<ProjectNodeInfo> list = projectNodeInfoMapper.toEntity(listDto);
            updateData(list, Boolean.TRUE);
        }


        Boolean isUpdateNodeStatus = true;
        if (isUpdateNodeStatus) {
            //更新已完成字段
            //判断紧前任务是否都已完成
            String frontWbsConfig = projectGroupDto.getFrontWbsConfig();
            if (frontWbsConfig != null) {
                List<FrontWbsConfigDto> frontList = new ArrayList<>();
                JSONArray jsonArray = new JSONArray(frontWbsConfig);
                Boolean statusFlag = Boolean.TRUE;
                String unFinish = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
                    JSONObject object = jsonArray.getJSONObject(i);
                    String type = object.getStr("type");
                    String wbs = object.getStr("wbs");
                    if ("FS".equals(type)) {
                        //查找当前节点的紧前是否已完成
                        LambdaQueryWrapper<ProjectGroup> lastLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getProjectId, projectId).eq(ProjectGroup::getNodeCode, wbs);
                        ProjectGroup one = projectGroupService.getOne(lastLambdaQueryWrapper);
                        if (one == null || (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(one.getNodeStatus()))) {
                            statusFlag = Boolean.FALSE;
                            unFinish = unFinish + one.getNodeName() + ",";
                        }

                    }

                }
                if (!statusFlag) {
                    unFinish = unFinish.substring(0, unFinish.length() - 1);
                    throw new BadRequestException(unFinish + "节点尚未完成，请先处理前置节点");
                }

            }
        }
        return projectNodeInfoD;
    }

    /**
     * 设计勘测带出，查询【设计勘测】节点，将部分字段带出到工程的【筹建启动会】
     */
    public void broughtOutDesignSurvey(ProjectNodeInfoDto projectNodeInfoD) {
        AtourSystemEnum.PreparationKickoffMeeting[] into = AtourSystemEnum.PreparationKickoffMeeting.values();
        AtourSystemEnum.DesignSurvey[] out = AtourSystemEnum.DesignSurvey.values();
        for (int i = 0; i < (into.length > out.length ? into.length : out.length); i++) {
            LambdaQueryWrapper<ProjectNodeInfo> outLambdaQueryWrapper = Wrappers
                    .lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getNodeCode, out[i].getKey())
                    .eq(ProjectNodeInfo::getProjectId, projectNodeInfoD.getProjectId());
            ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(outLambdaQueryWrapper);
            LambdaQueryWrapper<ProjectNodeInfo> intoLambdaQueryWrapper = Wrappers
                    .lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getNodeCode, into[i].getKey())
                    .eq(ProjectNodeInfo::getProjectId, projectNodeInfoD.getProjectId());
            ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(intoLambdaQueryWrapper);
            //将带出的remark插入到【筹建启动会】对应的节点
            if (!projectNodeInfo.getRemark().isEmpty()) {
                nodeInfo.setRemark(projectNodeInfo.getRemark());
            }
        }
    }

    /**
     * 节点之间的带出或同步
     */
    private void broughtOut(ProjectGroup projectGroup, String outKey, String intoKey) {
        //从哪里带出的节点
        LambdaQueryWrapper<ProjectNodeInfo> outLambdaQueryWrapper = Wrappers
                .lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getNodeCode, outKey)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId());
        ProjectNodeInfo projectNodeInfoOut = projectNodeInfoRepository.selectOne(outLambdaQueryWrapper);

        //带出到那里的节点
        LambdaQueryWrapper<ProjectNodeInfo> intoLambdaQueryWrapper = Wrappers
                .lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getNodeCode, intoKey)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId());
        ProjectNodeInfo projectNodeInfoIn = projectNodeInfoRepository.selectOne(intoLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(projectNodeInfoIn) && ObjectUtil.isNotEmpty(projectNodeInfoOut) && ObjectUtil.isNotEmpty(projectNodeInfoOut.getRemark())) {
            projectNodeInfoIn.setRemark(projectNodeInfoOut.getRemark());
        }
        if (ObjectUtil.isNotEmpty(projectNodeInfoIn) && projectNodeInfoIn.getNodeType().equals(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST.getValue())) {
            projectNodeInfoIn.setIsEdit("0");
        }
        Optional.ofNullable(projectNodeInfoIn).ifPresent(p -> projectNodeInfoRepository.updateById(p));
        //【设计启动】的联系人信息带出
        if (intoKey.equals(AtourSystemEnum.DesignStart.DES00105003.getKey()) && ObjectUtil.isNotEmpty(projectNodeInfoIn)) {
            getUserPhoneEmail(projectGroup, projectNodeInfoIn, AtourSystemEnum.DesignStart.DES00105004.getKey()
                    , AtourSystemEnum.DesignStart.DES00105005.getKey());
        }
        if (intoKey.equals(AtourSystemEnum.DesignStart.DES00105007.getKey()) && ObjectUtil.isNotEmpty(projectNodeInfoIn)) {
            getUserPhoneEmail(projectGroup, projectNodeInfoIn, AtourSystemEnum.DesignStart.DES00105008.getKey()
                    , AtourSystemEnum.DesignStart.DES00105009.getKey());
        }
        //【安全文明施工】动态表图纸的带出
        if (outKey.contains("table") && ObjectUtil.isNotEmpty(projectNodeInfoOut) && ObjectUtil.isNotEmpty(projectNodeInfoIn)) {
            final List<LocalStorage> localStorages = localStorageRepository.findByNodeId(projectNodeInfoOut.getNodeId() + "_site_photos");
            if (ObjectUtil.isNotEmpty(localStorages)) {
                localStorages.stream().forEach(ls -> {
                    //插入带出后节点的图纸
                    final LocalStorage localStorage = new LocalStorage();
                    BeanUtil.copyProperties(ls, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                    localStorage.setNodeId(projectNodeInfoIn.getNodeId() + "_site_photos");
                    //主键设为null，才能插入
                    localStorage.setId(null);
                    localStorageRepository.insert(localStorage);
                });
            }
        }
    }

    private void getUserPhoneEmail(ProjectGroup projectGroup, ProjectNodeInfo projectNodeInfoIn, String
            keyPhone, String keyEmail) {
        if (ObjectUtils.isNotEmpty(projectNodeInfoIn.getRemark())) {
            final SupplierPm one = supplierPmRepository.selectById(Long.valueOf(projectNodeInfoIn.getRemark()));
            final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers
                    .lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getNodeCode, keyPhone)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId());
            final ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
            info.setRemark(one.getPhone());
            projectNodeInfoRepository.updateById(info);
            final LambdaQueryWrapper<ProjectNodeInfo> eqE = Wrappers
                    .lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getNodeCode, keyEmail)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId());
            final ProjectNodeInfo infoE = projectNodeInfoRepository.selectOne(eqE);
            infoE.setRemark(one.getEmail());
            projectNodeInfoRepository.updateById(infoE);
        }
    }

    /**
     * ,ljp 修改了查询项目的各种计划事件时间接口
     *
     * @param projectId
     * @return
     */
    @Override
    public Map<String, List> projectTreeForSchedule(Long projectId) {
        Map<String, List> result = new HashMap<>();
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        Long submeterProjectId = this.getSubmeterProjectId(projectId);
        ProjectInfo projectInfo = projectInfoRepository.selectById(submeterProjectId);
        ProjectInfoDto infoDto = new ProjectInfoDto();
        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
        infoDto.setProjectId(projectInfo.getProjectId());
        try {
            //借用别人的方法，查找到这个项目属于哪个模版分组
            List<TemplateCollection> templateCollections = projectInfoService.findTemplateByConditionCode(infoDto, null);
            if (ObjectUtil.isNotEmpty(templateCollections)) {
                List<ProjectGroup> queryResult = projectGroupRepository.getProjectTreeForScheule(templateCollections.get(0).getGroupId(), projectInfo.getProjectId());
                if (queryResult != null && queryResult.size() > 0) {
                    for (ProjectGroup group : queryResult) {
                        List thisList = null;
                        if ("2".equals(group.getStencilLevel())) {
                            if (result.get("项目施工计划") == null) {
                                thisList = new LinkedList();
                                result.put("项目施工计划", thisList);
                            } else {
                                thisList = result.get("项目施工计划");
                            }
                            thisList.add(group);
                        } else if ("1".equals(group.getStencilLevel())) {
                            if (result.get("项目设计计划") == null) {
                                thisList = new LinkedList();
                                result.put("项目设计计划", thisList);
                            } else {
                                thisList = result.get("项目设计计划");
                            }
                            thisList.add(group);
                        } else {
                            if (result.get("项目启动计划") == null) {
                                thisList = new LinkedList();
                                result.put("项目启动计划", thisList);
                            } else {
                                thisList = result.get("项目启动计划");
                            }
                            thisList.add(group);
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return result;
//        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class)
//                .eq(null != projectId, ProjectGroup::getProjectId, projectId)
//                .ne(ProjectGroup::getTemplateCode, JhSystemEnum.TaskPhaseEnum.DECISION_MAKING.getKey())
//                .isNull(ProjectGroup::getParentId).isNull(ProjectGroup::getOrderId)
//                .orderBy(true, true, ProjectGroup::getCreateTime);
//        List<ProjectGroup> projectGroups = projectGroupRepository.selectList(project);
//        List<ProjectGroupDto> parents = projectGroupMapper.toDto(projectGroups);
//        LambdaQueryWrapper<ProjectNodeInfo> query = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectId, ProjectNodeInfo::getProjectId, projectId);
//        util.initialize(getSubmeterProjectId(projectId));
//        //todo add
//        List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo(projectId, null, null, null);
//        if (null == parents || CollectionUtils.isEmpty(dtos)) {
//            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
//        }
//
//        for (ProjectNodeInfoDto dto : dtos) {
//            if (dto.getIsWrite() != null && dto.getIsWrite() && dto.getIsOpen() != null && dto.getIsOpen()) {
//                dto.setIsWrite(Boolean.TRUE);
//            } else {
//                dto.setIsWrite(Boolean.FALSE);
//            }
//            //临时将groupid 给nodeId
//            dto.setNodeId(dto.getProjectGroupId());
//        }
//
//        //调整工期 去掉施工日志
//        List<ProjectNodeInfoDto> dtot = dtos.stream().filter(n -> "col-00".equals(n.getNodeCode())).collect(Collectors.toList());
//        List<ProjectNodeInfoDto> dtot2 = dtos.stream().filter(n -> "col-001".equals(n.getNodeCode())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(dtot)) {
//            dtos.removeAll(Collections.singleton(dtot.get(0)));
//        }
//        if (CollectionUtils.isNotEmpty(dtot2)) {
//            dtos.removeAll(Collections.singleton(dtot2.get(0)));
//        }
//        Map<String, ProjectNodeInfoDto> projectNodeInfoMap = new HashMap<>();
//        for (ProjectNodeInfoDto dto : dtos) {
//            projectNodeInfoMap.put(dto.getNodeCode(), dto);
//        }
//        List<List<Tree<String>>> builds = new ArrayList<>();
//        dtos.removeIf(Objects::isNull);
//        //转换treeNode
//        List<TreeNode<String>> treeNodes = new ArrayList<>();
//        treeNodes = this.getTreeNode(dtos);
//        this.getBuilds(builds, treeNodes, parents);
//        builds.removeIf(Objects::isNull);
//        return builds;

    }

    private List<TreeNode<String>> getTreeNode(List<ProjectNodeInfoDto> dtos) {
        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends ProjectNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        return treeNodes;
    }

    private void getBuilds
            (List<List<Tree<String>>> builds, List<TreeNode<String>> treeNodes, List<ProjectGroupDto> parents) {
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(3);
        //使用Hutools TreeUtil转换树结构
        if (ObjectUtils.isNotEmpty(parents)) {
            for (ProjectGroupDto parent : parents) {
                List<Tree<String>> build = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                        (treeNode, tree) -> {
                            tree.setId(treeNode.getId());
                            tree.setParentId(treeNode.getParentId());
                            tree.setWeight(treeNode.getWeight());
                            tree.setName(treeNode.getName());
                            // 扩展属性 ...
                            Map<String, Object> extra = treeNode.getExtra();
                            Set<Map.Entry<String, Object>> entries = extra.entrySet();
                            for (Map.Entry<String, Object> entry : entries) {
                                tree.putExtra(entry.getKey(), entry.getValue());
                            }
                        });
                builds.add(build);
            }
        } else {
            List<Tree<String>> build = TreeUtil.build(treeNodes, treeNodes.get(0).getParentId(), treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getId());
                        tree.setParentId(treeNode.getParentId());
                        tree.setWeight(treeNode.getWeight());
                        tree.setName(treeNode.getName());
                        // 扩展属性 ...
                        Map<String, Object> extra = treeNode.getExtra();
                        Set<Map.Entry<String, Object>> entries = extra.entrySet();
                        for (Map.Entry<String, Object> entry : entries) {
                            tree.putExtra(entry.getKey(), entry.getValue());
                        }
                    });
            builds.add(build);
        }
    }


    @Override
    public void updateRoleCode(Long projectId, Long userid, Long oldUserId) {
        projectNodeInfoRepository.updateRoleCode(projectId, userid, oldUserId);
    }

    @Override
    public Boolean fallbackStatus(ProjectNodeInfoDto projectNodeInfoDto) {
        Boolean flag = Boolean.FALSE;
        Boolean isDelay = Boolean.FALSE;
        //回退当前节点
        LambdaQueryWrapper<ProjectGroup> nodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class);
        nodeInfoLambdaQueryWrapper.eq(ProjectGroup::getProjectGroupId, projectNodeInfoDto.getNodeId());
        ProjectGroup projectGroup = projectGroupRepository.selectOne(nodeInfoLambdaQueryWrapper);
        int delayDay = 0;
        if (projectGroup != null) {

            LambdaUpdateWrapper<ProjectGroup> updateNodeWrapper = new LambdaUpdateWrapper<>();
            updateNodeWrapper.eq(ProjectGroup::getProjectGroupId, projectNodeInfoDto.getNodeId())
                    .set(ProjectGroup::getActualEndDate, null)
                    .set(ProjectGroup::getDelayDay, null)
                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey())
                    .set(ProjectGroup::getNodeIsfin, Boolean.FALSE);
            projectGroupService.update(updateNodeWrapper);
            // delayDay=projectGroup.getDelayDay() ;

           /* projectNodeInfo.setActualEndDate(null);
            projectNodeInfo.setDelayDay(null);
            projectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
            projectNodeInfo.setNodeIsfin(false);
            projectNodeInfoRepository.updateById(projectNodeInfo);*/
            flag = Boolean.TRUE;
        }
        //当前节点如果有逾期
        //查找其他节点，若其他节点没有逾期，则更改逾期状态
 /*       if(delayDay>0){
            LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectNodeInfo.class);
            projectNodeInfoLambdaQueryWrapper.eq(ProjectNodeInfo::getProjectId,projectNodeInfo.getProjectId());
            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoLambdaQueryWrapper);
            for(ProjectNodeInfo i:projectNodeInfos){
                if(i.getNodeId()!=Long.parseLong(projectNodeInfoDto.getNodeId())&&i.getDelayDay()>0){
                    isDelay=Boolean.TRUE;
                }
            }
            if(!isDelay){
                LambdaUpdateWrapper<ProjectInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProjectInfo::getProjectId, projectNodeInfo.getProjectId())
                        .set(ProjectInfo::getIsOverdue, Boolean.FALSE);
                projectInfoService.update(updateWrapper);
            }
        }*/

        return flag;
    }

    public Boolean updateParentNode(String projectId, String parentId, String roundMarking) {
        if (null == projectId || null == parentId) {
            throw new BadRequestException("参数 projectId，parentId 不可为空！");
        }
        Boolean flag = true;
        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getParentId, parentId)
                .eq(ObjectUtil.isNotEmpty(roundMarking), ProjectGroup::getRoundMarking, roundMarking);
        List<ProjectGroupDto> list = projectGroupMapper.toDto(projectGroupService.list(project));
        if (CollectionUtils.isNotEmpty(list)) {
            for (ProjectGroupDto dto : list) {
                if (StringUtils.isEmpty(dto.getNodeStatus()) ||
                        !JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    flag = false;
                }
            }
        } else {
            flag = false;
        }

        if (flag) {
            //更新父节点状态
            LambdaQueryWrapper<ProjectGroup> parent = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getProjectId, projectId)
                    .eq(ProjectGroup::getTemplateId, parentId)
                    .eq(ObjectUtil.isNotEmpty(roundMarking), ProjectGroup::getRoundMarking, roundMarking);
            ProjectGroupDto parentDto = projectGroupMapper.toDto(projectGroupService.getOne(parent));
            if (null != parentDto) {
                LambdaUpdateWrapper<ProjectGroup> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProjectGroup::getProjectGroupId, parentDto.getProjectGroupId())
                        .set(ProjectGroup::getNodeStatus, list.get(0).getNodeStatus())
                        .set(ProjectGroup::getActualEndDate, new Timestamp(System.currentTimeMillis()));
               /* if (null != parentDto.getPlanEndDate()) {
                    String nowDate= DateUtil.getNowDate();
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    Date nowEnd = null;
                    try {
                        nowEnd = format.parse(nowDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    LocalDateTime start= DateUtil.dateToLocalDateTime(parentDto.getPlanEndDate());
                    LocalDateTime end= DateUtil.dateToLocalDateTime(nowEnd);
                    long day = DateUtil.getBetweenDay(start, end);
                    //long day = (System.currentTimeMillis() - parentDto.getPlanEndDate().getTime()) / (24 * 60 * 60 * 1000);
                    System.out.println("相差的日期: " + day);
                    updateWrapper.set(ProjectNodeInfo::getDelayDay, day);
                }*/
                if (projectGroupService.update(updateWrapper)) {
                   /* if("con-007".equals(parentDto.getNodeCode())){
                        //结算完成
                        ProjectInfo projectInfo = projectInfoService.getById(parentDto.getProjectId());
                        if(projectInfo!=null){
                            projectInfo.setProjectStatus(JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey());
                            projectInfoService.updateById(projectInfo);
                        }
                    }
                    //将节点阶段更新到下一个节点
                    String s = updatePhase(parentDto.getNodeCode());
                    ProjectInfo projectInfo = projectInfoService.getById(parentDto.getProjectId());
                    if(projectInfo!=null){
                        projectInfo.setTaskPhase(s);
                        projectInfoService.updateById(projectInfo);
                    }*/
                    //更新此级节点成功后，更新父节点
                    if (null != projectId && null != parentDto.getParentId()) {
                        updateParentNode(projectId, parentDto.getParentId(), parentDto.getRoundMarking());
                    }
                }
            }
        }
        return true;
    }


    public String getUserByRole(Long projectId, String roleCode) {
        String userName = "";
        ProjectInfo projectInfo = projectInfoService.getById(projectId);
        if (null != projectInfo && null != projectInfo.getCity() && StringUtils.isNotEmpty(roleCode)) {
            Long city = projectInfo.getCity();
            List<String> jobNames = Arrays.asList(roleCode.split(","));
            List<JobUser> userList = jobUserRepository.findByJobNames(jobNames, city);
            if (CollectionUtils.isNotEmpty(userList)) {
                for (JobUser JobUser : userList) {
                    if (ObjectUtil.isNotEmpty(JobUser.getEnabled()) && JobUser.getEnabled() == 0) {
                        continue;
                    }
                    if (StringUtils.isNotEmpty(JobUser.getUsername())) {
                        if (StringUtils.isNotEmpty(userName)) {
                            userName = userName + "," + JobUser.getUsername();
                        } else {
                            userName = JobUser.getUsername();
                        }
                    }
                }
            }
        }
        return userName;
    }

    /**
     * @param i 除数
     * @param j 被除数
     * @return
     */
    private String taskProgress(int i, int j) {
        if (j == 0) {
            return "0%";
        }
        BigDecimal d = new BigDecimal((float) i / j).setScale(2, BigDecimal.ROUND_HALF_UP);
        DecimalFormat df = new DecimalFormat("0%");
        String percent = df.format(d);
        return percent;
    }

    private String calPhaseStatus(String startTime, String endTime) {
        //当前时间
        //Long time=System.currentTimeMillis();
        String status = "";
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(date);
        if (DateUtil.compareTodate(startTime, dateString) == 1) {
            status = "未开始";
        } else if (DateUtil.compareTodate(dateString, startTime) >= 0 && DateUtil.compareTodate(endTime, dateString) >= 0) {
            status = "进行中";
        } else if (DateUtil.compareTodate(dateString, endTime) > 0) {
            status = "逾期";
        }
        return status;
    }

    private String updatePhase(String nodeCode) {
        //应该更新的阶段
        String nodePhase = null;
        if (JhSystemEnum.TaskCodeEnum.CON_001.getKey().equals(nodeCode)) {
            nodePhase = JhSystemEnum.TaskPhaseEnum.PREPARE_PHASE.getKey();
        } else if (JhSystemEnum.TaskCodeEnum.CON_002.getKey().equals(nodeCode)) {
            nodePhase = JhSystemEnum.TaskPhaseEnum.DESIGN_PHASE.getKey();
        } else if (JhSystemEnum.TaskCodeEnum.CON_003.getKey().equals(nodeCode)) {
            nodePhase = JhSystemEnum.TaskPhaseEnum.CONPREPARE_PHASE.getKey();
        } else if (JhSystemEnum.TaskCodeEnum.CON_004.getKey().equals(nodeCode)) {
            nodePhase = JhSystemEnum.TaskPhaseEnum.CONIMPLEMENT_PHASE.getKey();
        } else if (JhSystemEnum.TaskCodeEnum.CON_005.getKey().equals(nodeCode)) {
            nodePhase = JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey();
        }
        return nodePhase;
    }


    public Boolean compareTo(String s1, String s2, String s3, String type) {
        Boolean flag = Boolean.FALSE;
        BigDecimal data1 = new BigDecimal(s1);
        BigDecimal data2 = new BigDecimal(s2);
        BigDecimal data3 = new BigDecimal(s3);
        if ("".equals(type)) {
            if (data1.compareTo(data2) < 0) {
                //第二位大
                flag = Boolean.TRUE;
            }
        } else if ("add".equals(type)) {
            if (data1.add(data2).compareTo(data3) > 0) {
                //第一位大
                flag = Boolean.TRUE;
            }
        }

        return flag;
    }

    /**
     * 更新之后所有的关键节点
     *
     * @param projectNodeInfo
     * @return
     */
    @Override
    public void updateGrantt(ProjectNodeInfo projectNodeInfo, String type) {
        LambdaQueryWrapper<ProjectNodeInfo> nodeInfoLambdaQueryWrapper = Wrappers.<ProjectNodeInfo>lambdaQuery()
                .eq(ProjectNodeInfo::getProjectId, projectNodeInfo.getProjectId())
                .eq(ProjectNodeInfo::getIsKey, Boolean.TRUE)
                .eq(ProjectNodeInfo::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey())
                .eq(ProjectNodeInfo::getKeyFrontWbs, projectNodeInfo.getNodeCode() + "FS");
        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(nodeInfoLambdaQueryWrapper);
        String countdate = "";
        if (type.equals("actual")) {
            countdate = DateUtil.changeDate(projectNodeInfo.getActualEndDate());
        } else if (type.equals("plan")) {
            countdate = DateUtil.changeDate(projectNodeInfo.getPlanEndDate());
        }

        if (projectNodeInfos.size() > 0) {
            for (ProjectNodeInfo nodeInfo : projectNodeInfos) {
                int planDay = nodeInfo.getPlanDay();
                int totalDay = nodeInfo.getTotalDay();
                int addDay = 0;
                String begin = "";
                String end = "";
                //计算开始日期和结束日期
                try {
                    begin = DateUtil.addDateForNode(countdate, planDay, totalDay, "begin", addDay);
                    end = DateUtil.addDateForNode(countdate, planDay, totalDay, "end", addDay);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                LocalDateTime startTime = DateUtil.parseLocalDateTimeFormatyMd(begin);
                LocalDateTime endTime = DateUtil.parseLocalDateTimeFormatyMd(end);
                nodeInfo.setPlanStartDate(DateUtil.localDateTimeToDate(startTime));
                nodeInfo.setPlanEndDate(DateUtil.localDateTimeToDate(endTime));
                projectNodeInfoRepository.updateById(nodeInfo);
                updateGrantt(nodeInfo, "plan");
            }
        }
        //return null;
    }

    public void openNextNode(Long porjectId, String nodeCode) {
        //todo
        // LambdaQueryWrapper<ProjectGroup>
    }

    //更新节点状态
    @Override
    public void updateNodeStatus(ProjectApprove projectApprove) {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectGroupId, projectApprove.getNodeId());
        ProjectGroup one = projectGroupService.getOne(projectGroupLambdaQueryWrapper);
        //修改当前节点的提交状态

        if (JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(projectApprove.getApproveResult())
                || JhSystemEnum.approveResultEnum.APPROVE_REJECT.getKey().equals(projectApprove.getApproveResult())
                || JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey()
                .equals(projectApprove.getApproveResult())) {
            //审批拒绝修改提交状态
            ProjectNodeInfoDto projectNodeInfoDto = new ProjectNodeInfoDto();
            projectNodeInfoDto.setNodeId(projectApprove.getNodeId().toString());

//            projectNodeInfoDto.setProjectId(projectApprove.getProjectId().toString());
            if (projectApprove.getOrderId() != null) {
                //如果是订单的节点，修改订单状态
                NodeInfoDto nodeInfoDto = new NodeInfoDto();
                nodeInfoDto.setOrderId(projectApprove.getOrderId());
                nodeInfoDto.setNodeCode(one.getNodeCode());
                nodeInfoDto.setType(KidsSystemEnum.OperationType.BACK.getValue());
                orderInfoService.updateStatus(nodeInfoDto);
                orderNodeInfoService.updateOrderGroupStatusBack(one);
            } else {
                projectNodeInfoDto.setProjectId(projectApprove.getProjectId().toString());
                //审批拒绝
                //修改节点状态
                //发当前节点待办
                this.updateGroupStatusBack(one);
            }


        } else if (JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(projectApprove.getApproveResult())
                || JhSystemEnum.approveResultEnum.APPROVE_DIS.getKey().equals(projectApprove.getApproveResult())) {

            Boolean thirdGroup = projectGroupService.isThirdGroup(projectGroupMapper.toDto(one));
            if (thirdGroup) {
                //合同提交中台处理
                kwThirdService.submitKoaContractProcess(one, Boolean.FALSE);
            } else {
                one.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                projectGroupService.update(one);
                //审批通过修改节点状态
                //如果是订单的节点，修改订单状态
                //修改节点状态

                if (projectApprove.getOrderId() != null) {
                    //如果是订单的节点，修改订单状态
                    NodeInfoDto nodeInfoDto = new NodeInfoDto();
                    nodeInfoDto.setOrderId(projectApprove.getOrderId());
                    nodeInfoDto.setNodeCode(one.getNodeCode());
                    nodeInfoDto.setType(KidsSystemEnum.OperationType.FINISH.getValue());

                    orderInfoService.updateStatus(nodeInfoDto);
                    orderNodeInfoService.updateOrderGroupStatusNext(one);

                } else {
                    //审批通过  完成业务操作
                    //修改节点状态
                    //发下一节点待办
                    //审批完成，发送通知
                    this.updateGroupStatusNext(one);
                }

            }


        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGroupStatusNext(ProjectGroup projectGroup) {
        //保存
        Long projectId = projectGroup.getProjectId();
        Long submeterProjectId = this.getSubmeterProjectId(projectId);
        util.initialize(submeterProjectId);
        ProjectInfo projectInfo = projectInfoRepository.selectById(submeterProjectId);

        //更新此二级节点 实际结束时间
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());
        LambdaUpdateWrapper<ProjectGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjectGroup::getProjectGroupId, projectGroup.getProjectGroupId())
                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())
                .set(ProjectGroup::getActualEndDate, currentTime);
        //【筹建启动会】的[业主]制定对接人员是否变更,做update
        if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00103.getKey())) {
            updateStakeholders(String.valueOf(projectGroup.getProjectId()), AtourSystemEnum.UpdateStakeholders.ENG00103056.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey());
        }

        if (projectGroupService.update(updateWrapper)) {

            //信息一致性完成后
            if (projectGroup.getNodeCode().equals(JhSystemEnum.twoNodeCodeEnum.NODE_ENG143.getKey()) ||
                    projectGroup.getNodeCode().equals(JhSystemEnum.twoNodeCodeEnum.NODE_DES167.getKey())) {
                //1.判断是否有报备操作，存在报备操作，则触发签证报备模版
                this.saveVisaFiling(projectInfo, projectGroup);
            }
            //设计阶段任务完成后
//            if (projectGroup.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DESIGN.getKey()) || projectGroup.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.DESIGN.getKey() + "(") ||
//                    projectGroup.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey()) || projectGroup.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey() + "(")
//
//            ) {
            if (!(JhSystemEnum.SupplierTypeEnum.SUPPLIER.getKey().equals(projectGroup.getTemplateCode()) || JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM.getKey().equals(projectGroup.getTemplateCode())))
            //1.查询审图 [审批通过]的话，修改对应的深化模版状态
            this.upProjectGroupExpand(projectId, projectGroup, projectInfo.getBrandId());
//            }

            //设计勘测、筹建启动会任务完成后
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode())
                    || JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(projectGroup.getNodeCode())) {
                //1.判断【设计师】、【基础信息】一致不一致，开启对应的【确认设计单位】【信息一致性】
                this.upModifyValidationDesignerNode(projectGroup);
            }

            //封闭确认验收任务完成后
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG119.getKey().equals(projectGroup.getNodeCode())) {
                //1.查询质量问题有>=1 未整改情况，系统 就给业主发送“法务函件”邮件
                this.sendLegalMail(projectGroup,projectInfo);
            }

            //筹建启动会节点完成后
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode())) {
                //1.擅自施工数据，存进正式开工的擅自施工数据中
                unauthorizedConstructionService.createUnauthorizedConstruction(projectGroup);

                //2.给当前项目赋值  预计开工日期 eng-00103060
                LambdaQueryWrapper wrapperPlanApproachDate = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                        .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG103060.getKey())
                        .last("limit 1");
                final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(wrapperPlanApproachDate);
                final String planApproachDate = Optional.ofNullable(nodeInfo).map(ProjectNodeInfo::getRemark).orElse(null);
                projectInfo.setPlanApproachDate(StringUtils.isEmpty(planApproachDate) ? null : DateUtil.stringToDate(planApproachDate));


                //3.是否变更授权人为是的时候，才向hlm系统推送营建负责人
                LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                        .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG103055.getKey());
                ProjectNodeInfo infos = projectNodeInfoRepository.selectOne(wrapper);
                if (AtourSystemEnum.hasDesignatedLiaisonPersonnelOwnerChanged.yes.getKey().equals(infos.getRemark())) {
                    //向hlm系统推送营建负责人
                    ProjectInfoPushFranchiseUserDTO franchiseUserDTO = new ProjectInfoPushFranchiseUserDTO();
                    List<Integer> chain = new ArrayList<>();
                    chain.add(Integer.valueOf(projectInfo.getProjectNo()));
                    franchiseUserDTO.setChainIds(chain);
                    this.pushBuildContacts(projectGroup, franchiseUserDTO);
                }

                //5.eng-00103055【业主制定对接人员是否变更】为【是】的话，根据用户填写的联系电话eng-00103103，查找用户，并更新干系人，查不到用户就算了
                final LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.UpdateStakeholders.ENG00103055.getKey());
                final ProjectNodeInfo one = projectNodeInfoRepository.selectOne(lambdaQueryWrapper);
                final LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapperPhone = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.UpdateStakeholders.ENG00103103.getKey());
                final ProjectNodeInfo phone = projectNodeInfoRepository.selectOne(lambdaQueryWrapperPhone);
                Optional.ofNullable(one).flatMap(o -> Optional.ofNullable(o.getRemark()).filter(ObjectUtil::isNotEmpty).filter("yes"::equals)
                        .flatMap(r -> Optional.ofNullable(phone).filter(p -> ObjectUtil.isNotEmpty(p)).flatMap(e ->
                                Optional.ofNullable(e.getRemark()).filter(ObjectUtil::isNotEmpty).flatMap(r2 -> {
                                            User byPhone = userRepository.findByPhone(r2);
                                            Optional.ofNullable(byPhone).filter(ObjectUtil::isNotEmpty).ifPresent(b -> projectInfoService.createProjectStakeholders(projectId, JhSystemEnum.JobEnum.YZXMJL.getKey(), b.getId(), null));
                                            return Optional.empty();
                                        }
                                ))
                        ));
            }

            //设计勘测任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00101.getKey())) {
                //1.联系人带出到【设计启动】的联系人，并且查出联系人的手机号和邮箱
                broughtOut(projectGroup, AtourSystemEnum.DesignSurvey.DES00101078.getKey(), AtourSystemEnum.DesignStart.DES00105003.getKey());
                broughtOut(projectGroup, AtourSystemEnum.DesignSurvey.DES00101084.getKey(), AtourSystemEnum.DesignStart.DES00105007.getKey());
                //2.查询出客房/公区设计角度申请的相关设计师的联系方式
                broughtOutSjjdsqCon(projectId, AtourSystemEnum.DesignDisclosureApplication.KF1.getOutKey()
                        , AtourSystemEnum.DesignDisclosureApplication.KF1.getIntoKey(), AtourSystemEnum.DesignDisclosureApplication.GQ1.getIntoKey());
                broughtOutSjjdsqCon(projectId, AtourSystemEnum.DesignDisclosureApplication.KF2.getOutKey()
                        , AtourSystemEnum.DesignDisclosureApplication.KF2.getIntoKey(), AtourSystemEnum.DesignDisclosureApplication.GQ2.getIntoKey());
                //3.将设计单位信息带出到确认设计单位
                final AtourSystemEnum.BroughtOutDesignUnitInformation[] values = AtourSystemEnum.BroughtOutDesignUnitInformation.values();
                for (AtourSystemEnum.BroughtOutDesignUnitInformation value : values) {
                    broughtOut(projectGroup, value.getCodeA(), value.getCodeB());
                }
            }

            //正式开工任务完成后
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG107.getKey().equals(projectGroup.getNodeCode())) {
                if (projectInfo.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue())) {
                    //1.总/分包管理下的单位进展    赋值给  样板间隐蔽验收 的单位进展,
                    this.upUnitProgressSampleAcceptance(projectGroup);
                    //2.质量管理数据   赋值给   业主方整改的质量管理数据中
                    broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00107177.getOutKey()
                            , AtourSystemEnum.AirConditioningBrandType.ENG00107177.getIntoKey());
                    broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00107178.getOutKey()
                            , AtourSystemEnum.AirConditioningBrandType.ENG00107178.getIntoKey());
                    //3.【成本管理】带出到【样板间隐蔽验收】
                    AtourSystemEnum.CostControlZSKG[] costControlZSKG = AtourSystemEnum.CostControlZSKG.values();
                    AtourSystemEnum.CostControlYBJYBYS[] costControlYBJYBYS = AtourSystemEnum.CostControlYBJYBYS.values();
                    for (int i = 0; i < (Math.max(costControlZSKG.length, costControlYBJYBYS.length)); i++) {
                        broughtOut(projectGroup, costControlZSKG[i].getKey(), costControlYBJYBYS[i].getKey());
                    }
                    //4.【安全文明施工】的【安全管理】带出到【样板间隐蔽验收】
                    broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00107.getOutKey()
                            , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00107.getIntoKey());
                    //5.将【物资管理】带出到【样板间隐蔽验收】
                    breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00107149.getKey()
                            , AtourSystemEnum.MaterialManagementCode.ENG00109148.getKey());
                } else if (projectInfo.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue())) {
                    //1.【安全文明施工】的【安全管理】带出到【样板间验收】
                    broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00107MAJOR.getOutKey()
                            , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00107MAJOR.getIntoKey());
                    //2.将【物资管理】带出到【样板间验收】
                    breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00107149.getKey()
                            , AtourSystemEnum.MaterialManagementCode.ENG00113005.getKey());
                }

                //提交的时候，根据nodecode 查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
            }

            //样板间隐蔽验收任务完成后
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG109.getKey().equals(projectGroup.getNodeCode())) {
                //1.总/分包管理下的单位进展赋值给样板间验收的单位进展
                this.upUnitProgressSampleRoomAcceptance(projectGroup);
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00109184.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00109184.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00109185.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00109185.getIntoKey());
            }

            //样板间验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00111.getKey())
                    && projectInfo.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue())) {
                //1.【样板间验收】总/分包管理的单位进展带出到【隐蔽验收】，可编辑的节点
                AtourSystemEnum.SampleRoomAcceptance[] sampleRoomAcceptances = AtourSystemEnum.SampleRoomAcceptance.values();
                AtourSystemEnum.ConcealedAcceptanceA[] concealedAcceptances = AtourSystemEnum.ConcealedAcceptanceA.values();
                for (int i = 0; i < (Math.max(sampleRoomAcceptances.length, concealedAcceptances.length)); i++) {
                    broughtOut(projectGroup, sampleRoomAcceptances[i].getKey(), concealedAcceptances[i].getKey());
                }
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00111265.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00111265.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00111266.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00111266.getIntoKey());
            }

            //隐蔽验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00117.getKey())) {
                //1.总/分包管理的单位进展带出到【封板前验收】，可编辑的节点
                AtourSystemEnum.ConcealedAcceptance[] sampleRoomConcealedAcceptance = AtourSystemEnum.ConcealedAcceptance.values();
                AtourSystemEnum.AcceptanceBeforeSealingTow[] acceptanceBeforeSealing = AtourSystemEnum.AcceptanceBeforeSealingTow.values();
                for (int i = 0; i < (Math.max(sampleRoomConcealedAcceptance.length, acceptanceBeforeSealing.length)); i++) {
                    broughtOut(projectGroup, sampleRoomConcealedAcceptance[i].getKey(), acceptanceBeforeSealing[i].getKey());
                }
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00117316.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00117316.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00117317.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00117317.getIntoKey());
            }

            //封板前验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00119.getKey())) {
                //1.总/分包管理的单位进展带出到【家具进场】，可编辑的节点
                AtourSystemEnum.AcceptanceBeforeSealingTow[] acceptanceBeforeSealing = AtourSystemEnum.AcceptanceBeforeSealingTow.values();
                AtourSystemEnum.FurnitureEntry[] furnitureEntry = AtourSystemEnum.FurnitureEntry.values();
                for (int i = 0; i < (Math.max(acceptanceBeforeSealing.length, furnitureEntry.length)); i++) {
                    broughtOut(projectGroup, acceptanceBeforeSealing[i].getKey(), furnitureEntry[i].getKey());
                }
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00119321.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00119321.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00119322.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00119322.getIntoKey());
            }

            //家具进场任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00123.getKey())) {
                //1.总/分包管理的单位进展带出到【形象进度80%】，可编辑的节点
                AtourSystemEnum.FurnitureEntry[] furnitureEntry = AtourSystemEnum.FurnitureEntry.values();
                AtourSystemEnum.ImageProgress[] imageProgress = AtourSystemEnum.ImageProgress.values();
                for (int i = 0; i < (Math.max(furnitureEntry.length, imageProgress.length)); i++) {
                    broughtOut(projectGroup, furnitureEntry[i].getKey(), imageProgress[i].getKey());
                }
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00123326.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00123326.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.AirConditioningBrandType.ENG00123327.getOutKey()
                        , AtourSystemEnum.AirConditioningBrandType.ENG00123327.getIntoKey());

                //2.家具进场已完成后没有分配飞行质检人员,给项目中的交付中心-工程负责人发送消息提示
                this.sendMsgProjectDirector(projectGroup);
            }

            //竣工验收申请任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00129.getKey())) {
                //1.根据选择验收标准版本eng-00129105生成验收单数据
                //删除以前的
                QueryWrapper queryWrapper = new QueryWrapper<>().eq("project_id", projectId);
                projectCompletionPhotoService.remove(queryWrapper);
                projectCompletionReceiptService.remove(queryWrapper);
                saveCompletionReceiptByVersion(projectId);
            }

            //样板间隐蔽验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00109.getKey())) {
                //1.【成本管理】带出到【样板间验收】
                AtourSystemEnum.CostControlYBJYBYS[] costControlYBJYBYS = AtourSystemEnum.CostControlYBJYBYS.values();
                AtourSystemEnum.CostControlYBJYS[] costControlYBJYS = AtourSystemEnum.CostControlYBJYS.values();
                for (int i = 0; i < (Math.max(costControlYBJYBYS.length, costControlYBJYS.length)); i++) {
                    broughtOut(projectGroup, costControlYBJYBYS[i].getKey(), costControlYBJYS[i].getKey());
                }
                //2.【安全文明施工】的【安全管理】带出到【样板间验收】
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00109.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00109.getIntoKey());
                //3.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
                //4.将【物资管理】带出到【样板间验收申请】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00109148.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00113005.getKey());
            }

            //样板间验收申请任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00113.getKey())) {
                //1.将【物资管理】带出到【样板间验收】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00113005.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00111194.getKey());
            }

            //竣工自检申请任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00145.getKey())) {
                // 发送短信和企微
                //提醒信息
                String messageText = "";
                StringBuffer messageTitle = new StringBuffer("");
                messageTitle.append("【营建新系统】待办提醒：");
                this.sendMyMessage(projectGroup.getProjectId(),projectGroup.getProjectGroupId().toString(),
                        projectGroup.getNodeCode(), messageTitle,
                        AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey(), "01");
            }
            //样板间验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00111.getKey())
                    && projectInfo.getProjectType().equals(KidsSystemEnum.ProjectTypeEnum.NEW.getValue())) {
                //1.【成本管理】带出到【隐蔽验收】
                AtourSystemEnum.CostControlYBJYS[] costControlYBJYS = AtourSystemEnum.CostControlYBJYS.values();
                AtourSystemEnum.CostControlYBYS[] costControlYBYS = AtourSystemEnum.CostControlYBYS.values();
                for (int i = 0; i < (Math.max(costControlYBJYS.length, costControlYBYS.length)); i++) {
                    broughtOut(projectGroup, costControlYBJYS[i].getKey(), costControlYBYS[i].getKey());
                }
                //2.【安全文明施工】的【安全管理】带出到【隐蔽验收】
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00111.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00111.getIntoKey());
                //3.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
                //4.将【物资管理】带出到【隐蔽验收】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00111194.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00117220.getKey());
            }

            //隐蔽验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00117.getKey())) {
                //1.【成本管理】带出到【封板前验收】
                AtourSystemEnum.CostControlYBYS[] costControlYBYS = AtourSystemEnum.CostControlYBYS.values();
                AtourSystemEnum.CostControlFBQYS[] costControlFBQYS = AtourSystemEnum.CostControlFBQYS.values();
                for (int i = 0; i < (Math.max(costControlYBYS.length, costControlFBQYS.length)); i++) {
                    broughtOut(projectGroup, costControlYBYS[i].getKey(), costControlFBQYS[i].getKey());
                }
                //2.【安全文明施工】的【安全管理】带出到【封板节点】
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00117.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00117.getIntoKey());
                //3.【安全文明施工】的【安全管理】带出到【竣工自检】，不可编辑
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00127.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00127.getIntoKey());
                //4.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
                //5.将【物资管理】带出到【封板节点】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00117220.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00119222.getKey());
            }

            //封板前验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00119.getKey())) {
                //1.【成本管理】带出到【家具进场】
                AtourSystemEnum.CostControlFBQYS[] costControlFBQYS = AtourSystemEnum.CostControlFBQYS.values();
                AtourSystemEnum.CostControlJJJC[] costControlJJJC = AtourSystemEnum.CostControlJJJC.values();
                for (int i = 0; i < (Math.max(costControlFBQYS.length, costControlJJJC.length)); i++) {
                    broughtOut(projectGroup, costControlFBQYS[i].getKey(), costControlJJJC[i].getKey());
                }
                //2.【安全文明施工】的【安全管理】带出到【家具进场】
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00119.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00119.getIntoKey());
                //3.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
                //4.将【物资管理】带出到【家具进场】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00119222.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00123224.getKey());
            }
            //家具进场任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00123.getKey())) {
                //1.【成本管理】带出到【形象进度80%】
                AtourSystemEnum.CostControlJJJC[] costControlJJJC = AtourSystemEnum.CostControlJJJC.values();
                AtourSystemEnum.CostControlXXJD[] costControlXXJD = AtourSystemEnum.CostControlXXJD.values();
                for (int i = 0; i < (Math.max(costControlJJJC.length, costControlXXJD.length)); i++) {
                    broughtOut(projectGroup, costControlJJJC[i].getKey(), costControlXXJD[i].getKey());
                }
                //2.【安全文明施工】的【安全管理】带出到【形象进度80】
                broughtOut(projectGroup, AtourSystemEnum.SafeAndCivilizedConstruction.ENG00123.getOutKey()
                        , AtourSystemEnum.SafeAndCivilizedConstruction.ENG00123.getIntoKey());
                //3.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
                //4.将【物资管理】带出到【家具进场】
                breakOutMaterialManagement(projectId, AtourSystemEnum.MaterialManagementCode.ENG00123224.getKey()
                        , AtourSystemEnum.MaterialManagementCode.ENG00125226.getKey());
            }

            //形象进度80%任务提交后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00125.getKey())) {
                //1.总/分包管理的施工单位联系人的电话带出带出到【竣工验收申请】
                broughtOutContactPhone(projectId);
                //2.【成本管理】带出到【竣工自检】
                AtourSystemEnum.CostControlXXJD[] costControlXXJD = AtourSystemEnum.CostControlXXJD.values();
                AtourSystemEnum.CostControlJGZJ[] costControlJGZJ = AtourSystemEnum.CostControlJGZJ.values();
                for (int i = 0; i < (Math.max(costControlXXJD.length, costControlJGZJ.length)); i++) {
                    broughtOut(projectGroup, costControlXXJD[i].getKey(), costControlJGZJ[i].getKey());
                }
                broughtOut(projectGroup, AtourSystemEnum.WeakCurrentApplication.ENG00137012.getKey(), AtourSystemEnum.WeakCurrentApplication.ENG00137012.getIntoKey());
                broughtOut(projectGroup, AtourSystemEnum.WeakCurrentApplication.ENG00137013.getKey(), AtourSystemEnum.WeakCurrentApplication.ENG00137013.getIntoKey());
                //3.【弱电单位】信息带出到【弱电验收申请】
                final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.WeakCurrentApplication.ENG00137013.getIntoKey());
                final ProjectNodeInfo one = projectNodeInfoRepository.selectOne(eq);
                if (ObjectUtil.isNotEmpty(one) && ObjectUtil.isNotEmpty(one.getRemark())) {
                    final LambdaQueryWrapper<SupplierInfo> last = Wrappers.lambdaQuery(SupplierInfo.class)
                            .eq(SupplierInfo::getContact, one.getRemark()).last("limit 1");
                    final SupplierInfo user = supplierInfoRepository.selectOne(last);
                    if (ObjectUtil.isNotEmpty(user) && ObjectUtil.isNotEmpty(user.getPhone())) {
                        final LambdaQueryWrapper<ProjectNodeInfo> eqP = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getProjectId, projectId)
                                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.WeakCurrentApplication.ENG00137014.getIntoKey());
                        final ProjectNodeInfo oneP = projectNodeInfoRepository.selectOne(eqP);
                        if (ObjectUtil.isNotEmpty(oneP)) {
                            oneP.setRemark(user.getPhone());
                            projectNodeInfoRepository.updateById(oneP);
                        }
                    }
                }
                //4.根据nodecode查询当前节点的质量管理数据，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey());
            }

            //弱电验收申请任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00137.getKey())) {
                //1.如果立项的时候弱电工程师未入干系人，【弱电验收申请】提交后，带出到【弱电验收】的验收人eng-00139010
                final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.WeakCurrentAcceptance.ENG00139010.getKey());
                final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(eq);
                final List<String> list = new ArrayList<>();
                final LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                        .eq(ProjectStakeholders::getProjectId, projectId)
                        .eq(ProjectStakeholders::getRoleCode, AtourSystemEnum.engineeringRoleCodeEnum.RDGCS.getKey());
                final List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(wrapper);
                final List<Long> collect = projectStakeholders.stream()
                        .filter(sta -> JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey().equals(sta.getShakeholderStatus()))
                        .map(ProjectStakeholders::getUserId).collect(Collectors.toList());
                Optional.ofNullable(collect).filter(ObjectUtil::isNotEmpty).ifPresent(c -> {
                    if (ObjectUtil.isNotEmpty(c)) {
                        final User one = userRepository.getOne(c.get(0));
                        Optional.ofNullable(one).filter(ObjectUtil::isNotEmpty).ifPresent(o -> list.add(o.getUsername() + "-" + o.getNickName()));
                        Optional.ofNullable(projectNodeInfo).ifPresent(p -> {
                            p.setRemark(String.join(",", list));
                            this.update(p);
                        });
                    } else {
                        throw new BadRequestException("当前项目未配置弱电工程师");
                    }
                });
            }

            //弱电验收任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00139.getKey())) {
                //1.带出具备验收条件房号到弱电整改
                broughtOut(projectGroup, "eng-00139009", "eng-00141011");
                broughtOut(projectGroup, "eng-00139037", "eng-00141012");
            }

            //竣工自检任务完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00127.getKey())) {
                //总分包管理带出到【竣工验收申请】,包括联系电话
                //final AtourSystemEnum.CompAccApplication[] values = AtourSystemEnum.CompAccApplication.values();
                //for (int i = 0; i < values.length; i++) {
                //    broughtOut(projectGroup, values[i].getKey(), values[i].getIntoKey());
                //}
                //
                //final AtourSystemEnum.CompAccApplicationA[] accApplicationAS = AtourSystemEnum.CompAccApplicationA.values();
                //for (int i = 0; i < accApplicationAS.length; i++) {
                //    final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                //            .eq(ProjectNodeInfo::getProjectId, projectId)
                //            .eq(ProjectNodeInfo::getNodeCode, accApplicationAS[i].getKey());
                //    final ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
                //    if (ObjectUtil.isNotEmpty(info.getRemark())) {
                //        final User one = userRepository.getOne(Long.valueOf(info.getRemark()));
                //        if (ObjectUtil.isNotEmpty(one) && ObjectUtil.isNotEmpty(one.getPhone())) {
                //            final LambdaQueryWrapper<ProjectNodeInfo> eqA = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                //                    .eq(ProjectNodeInfo::getProjectId, projectId)
                //                    .eq(ProjectNodeInfo::getNodeCode, accApplicationAS[i].getIntoKey());
                //            final ProjectNodeInfo infoA = projectNodeInfoRepository.selectOne(eqA);
                //            if (ObjectUtil.isNotEmpty(infoA)) {
                //                infoA.setRemark(one.getPhone());
                //            }
                //        }
                //    }
                //}
                //提交的时候，根据nodecode查询当前节点的竣工常规自检数据，存在则发起模版
                //1.查询当前节点的竣工系统自检，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getKey());
                //2.查询当前节点的竣工常规项目自检，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getKey());
            }

            //装饰勘测报告任务完成后
            if (AtourSystemEnum.DesignNodeTow.DES00103.getKey().equals(projectGroup.getNodeCode())) {
                //1.将勘测报告图纸带出到决策信息的【营建勘测报告】dec-00301002
                breakOutDrawingFile(projectId, AtourSystemEnum.SurveyReport.DES00103077.getOutKey()
                        , AtourSystemEnum.SurveyReport.DES00103077.getIntoKey(), AtourSystemEnum.SurveyReport.DES00103077.getSpec());
            }

            //机电勘测报告任务完成后
            if (AtourSystemEnum.DesignNodeTow.DES00165.getKey().equals(projectGroup.getNodeCode())) {
                //1.将勘测报告图纸带出到决策信息的【营建勘测报告】dec-00301003
                breakOutDrawingFile(projectId, AtourSystemEnum.SurveyReport.DES00165109.getOutKey()
                        , AtourSystemEnum.SurveyReport.DES00165109.getIntoKey(), AtourSystemEnum.SurveyReport.DES00165109.getSpec());
            }

            //客房样板间施工图审核 任务完成后
            if (AtourSystemEnum.DesignNodeTow.DES00111.getKey().equals(projectGroup.getNodeCode())) {
                this.roomPlanDrawing(projectId, projectGroup);
            }

            //开工申请任务完成后
            if (AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey().equals(projectGroup.getNodeCode())) {
                final LambdaUpdateWrapper<ProjectRoom> wrapper = Wrappers.lambdaUpdate(ProjectRoom.class)
                        .eq(ProjectRoom::getProjectId, projectId)
                        .eq(ProjectRoom::getIsUsed, "1");
                final List<ProjectRoom> rooms = projectRoomService.list(wrapper);
                //1.在【正式开工】任务的物资管理插入材料模板
                saveOrUpdateTemplateMaterial(projectId, rooms);
                //2.更新新增施工照片
                this.saveConstructionPhotograph(projectId, rooms);
                //3.实际完成时间带到【弱电验收】ENG00139018;ENG00133019;ENG00141018
                broughtOutStartTime(projectId, currentTime, AtourSystemEnum.WeakCurrentAcceptance.ENG00139018.getKey());
                broughtOutStartTime(projectId, currentTime, AtourSystemEnum.CompletionAcceptanceTime.ENG00133019.getKey());
                broughtOutStartTime(projectId, currentTime, AtourSystemEnum.CompletionAcceptanceTime.ENG00141018.getKey());
                //4.施工单位项目经理eng-00105020入到干系人里的zxdw-xmjl和【施工日志】的col-00101005
                broughtOutXmjlToCol(null, projectId);
                //5.施工单位项目经理eng-00105020带出到设计启动：联系人des-00105011；联系电话des-00105012；联系邮箱des-00105013
                broughtOutSgdwManger(projectId);
            }

            //客房样板间施工图审核任务完成后
            if (AtourSystemEnum.DesignNodeTow.DES00111.getKey().equals(projectGroup.getNodeCode())) {
                final ArrayList<String> roomList = new ArrayList<>();
                final AtourSystemEnum.DesignDrawingTow[] values = AtourSystemEnum.DesignDrawingTow.values();
                for (int i = 0; i < values.length; i++) {
                    final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, values[i].getKey());
                    final ProjectNodeInfo room = projectNodeInfoRepository.selectOne(eq);
                    if (ObjectUtil.isNotEmpty(room) && ObjectUtil.isNotEmpty(room.getRemark())) {
                        roomList.add(room.getRemark());
                    }
                }
                roomList.stream().forEach(room -> {
                    final LambdaUpdateWrapper<ProjectRoom> set = Wrappers.lambdaUpdate(ProjectRoom.class)
                            .eq(ProjectRoom::getProjectId, projectId).eq(ProjectRoom::getRoomNum, room)
                            .set(ProjectRoom::getIsUsed, "1");
                    projectRoomService.update(set);
                });
                //1.将其余房间号重置
                removeIsUsedRoom(projectId, roomList);
            }

            //样板间验收任务完成后
            if (AtourSystemEnum.DesignNodeTow.DES00125.getKey().equals(projectGroup.getNodeCode())) {
                //1.根据nodecode查询当前节点的设计样板间验收，存在则发起模版
                this.saveEngineeringRectificationIssues(projectGroup, projectInfo
                        , AtourSystemEnum.AdditionalTemplatesEnum.DESIGN_SAMPLE_ROOM.getKey());
            }

            //竣工验收整改完成后
            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00135.getKey())) {
                //1.向hlm系统推送数据
                this.pushCompletionAcceptance(projectGroup, null);
            }

            //工程常规检查任务提交后
            if (AtourSystemEnum.engineeringRectificationIssuesNodeTwo.ERI00103.getKey().equals(projectGroup.getNodeCode())) {
                //1.质量管理提交后，在未整改汇总里去除
                final LambdaUpdateWrapper<QualityControl> wrapper = Wrappers.lambdaUpdate(QualityControl.class)
                        .eq(QualityControl::getQualityControlId, projectGroup.getProjectId())
                        .last("limit 1");
                final QualityControl control = qualityControlRepository.selectOne(wrapper);
                final LambdaUpdateWrapper<QualityControl> set = wrapper.set(QualityControl::getRectificationConfirmation,
                        AtourSystemEnum.rectificationConfirmationEnum.RECTIFICATION_COMPLETED.getKey());
                qualityControlRepository.update(control, set);
            }


//            //查询是否有分模版，存在分了模板则进入状态的重新判断
//            LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
//                    .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
//                    .like(ProjectGroup::getNodeCode, projectGroup.getNodeCode().split("_")[0] + "%");
//            long count1 = projectGroupService.count(queryWrapper);
//            if (count1 > 1) {
//                this.upProjectGroupNodeStatus(projectGroup);
//            }
//            if (projectGroup.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey())) {
//                //当前二级为 深化方案的话，修改当前审图记录的状态为  审批通过；深化列表中的审图状态，目前不根据深化任务来进行判断
//                this.upDeepeningPlanStatus(projectGroup.getProjectId().toString(),projectGroup.getNodeCode(),AtourSystemEnum.ReviewDrawingStatus.APPROVED.getKey());
//            }
//            //派驻现长任务完成后
//            if (projectGroup.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00121.getKey())) {
//                //开业经理填写的【现长】信息：姓名、手机号、邮箱；并且入干系人  查询当前页面下的三级节点
//                final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
//                        .eq(ProjectNodeInfo::getProjectId, projectId)
//                        .eq(ObjectUtil.isNotEmpty(projectGroup.getTemplateId()), ProjectNodeInfo::getParentId, projectGroup.getTemplateId());
//                final List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(wrapper);
//                nodeInfos.stream().filter(p->ObjectUtil.isNotEmpty(p.getNodeCode())&&AtourSystemEnum.ResidentChief.ENG00123148.getKey().equals(p.getNodeCode()))
//                        .forEach(node->{
//                            if(ObjectUtil.isNotEmpty(node.getRemark())){
//                                final User resident = userRepository.findByPhone(node.getRemark());
//                                if (ObjectUtil.isNotEmpty(resident)){
//                                    projectInfoService.createProjectStakeholders(projectId,AtourSystemEnum.engineeringRoleCodeEnum.XZ.getKey()
//                                            ,resident.getId(),AtourSystemEnum.engineeringRoleCodeEnum.XZ.getKey());
//                                }
//                            }
//                        });
//            }
//            //客房装饰施工图审核任务完成后   必须按照顺序配置动态表格的每行
//            if (projectGroup.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00115.getKey())) {
//                //1.【设计审核参考标准】带出到【客房装饰施工图整改】
//                List<ProjectNodeInfo> outs = playDynamticConditionList(AtourSystemEnum
//                        .ReviewOfGuestRoomDecorationConstructionDrawings.DES00115010.getKey());
//                List<ProjectNodeInfo> intos = playDynamticConditionList(AtourSystemEnum
//                        .RectificationOfGuestRoomDecorationConstructionDrawings.DES00117010.getKey());
//                //必须按照[顺序]配置动态表格的每行
//                for (int i = 0; i < (Math.max(outs.size(), intos.size())); i++) {
//                    broughtOut(projectGroup, outs.get(i).getNodeCode(), intos.get(i).getNodeCode());
//                }
//            }
//
//            //公区概念方案审核任务完成后
//            if (projectGroup.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00127.getKey())) {
//                //【方案汇报文本、公区平面设计质量、公区方案质量】带出到【公区概念方案整改】
//                List<ProjectNodeInfo> outs = playDynamticConditionList(AtourSystemEnum
//                        .ReviewOfPublicAreaConceptPlan.DES00127010.getKey());
//                List<ProjectNodeInfo> intos = playDynamticConditionList(AtourSystemEnum
//                        .RectificationOfPublicAreaConceptPlan.DES00129010.getKey());
//                //必须按照[顺序]配置动态表格的每行
//                for (int i = 0; i < (Math.max(outs.size(), intos.size())); i++) {
//                    broughtOut(projectGroup, outs.get(i).getNodeCode(), intos.get(i).getNodeCode());
//                }
//                List<ProjectNodeInfo> outs1 = playDynamticConditionList(AtourSystemEnum
//                        .ReviewOfPublicAreaConceptPlan.DES00127012.getKey());
//                List<ProjectNodeInfo> intos1 = playDynamticConditionList(AtourSystemEnum
//                        .RectificationOfPublicAreaConceptPlan.DES00129012.getKey());
//                //必须按照[顺序]配置动态表格的每行
//                for (int i = 0; i < (Math.max(outs1.size(), intos1.size())); i++) {
//                    broughtOut(projectGroup, outs1.get(i).getNodeCode(), intos1.get(i).getNodeCode());
//                }
//                List<ProjectNodeInfo> outs2 = playDynamticConditionList(AtourSystemEnum
//                        .ReviewOfPublicAreaConceptPlan.DES00127014.getKey());
//                List<ProjectNodeInfo> intos2 = playDynamticConditionList(AtourSystemEnum
//                        .RectificationOfPublicAreaConceptPlan.DES00129014.getKey());
//                //必须按照[顺序]配置动态表格的每行
//                for (int i = 0; i < (Math.max(outs2.size(), intos2.size())); i++) {
//                    broughtOut(projectGroup, outs2.get(i).getNodeCode(), intos2.get(i).getNodeCode());
//                }
//            }
//
//            //公区装饰施工图审核任务完成后
//            if (projectGroup.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00135.getKey())) {
//                //1.【公区装饰施工图审核】【方公区施工图检查表】带出到【公区装饰施工图整改】
//                List<ProjectNodeInfo> outs = playDynamticConditionList(AtourSystemEnum
//                        .ReviewOfPublicAreaDecorationConstructionDrawings.DES00135010.getKey());
//                List<ProjectNodeInfo> intos = playDynamticConditionList(AtourSystemEnum
//                        .RectificationOfPublicAreaDecorationConstructionDrawings.DES00137010.getKey());
//                //必须按照[顺序]配置动态表格的每行
//                for (int i = 0; i < (Math.max(outs.size(), intos.size())); i++) {
//                    broughtOut(projectGroup, outs.get(i).getNodeCode(), intos.get(i).getNodeCode());
//                }
//            }


//            //发抄送    亚朵的代办逾期后才通过字段 [任务抄送角色：carbon_copy_role_code]和[消息ID：MB1000008] 来实现发送抄送
//            projectTaskService.createNoticeCC(projectGroup, JhSystemEnum.TaskNameEnum.MESSAGE_NOTICE, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
            //改待办为已办
            projectTaskService.finishTask(projectGroup);
            //更新此二级节点成功后，判断同级二级节点是否完成，若是 则更新 父 是
            updateParentNode(projectGroup.getProjectId().toString(), projectGroup.getParentId().toString(), projectGroup.getRoundMarking());


            //将是各门店新开店的最后一个节点时，门店状态置为营业中；是各门店闭店的最后一个节点时，门店状态置为已闭店，
            // 并修改项目状态为已完成
            projectGroupService.updateStoreStatus(projectGroup);

            //更新项目的阶段和状态，此更新需要在节点更新后，更新
            updateProjectPhaseAndStatus(projectGroup);

            //当前二级任务的nodecode
            String nodeCode = projectGroup.getNodeCode();


            List<String> taskPhase = new ArrayList<String>();
            if (ObjectUtil.isNotEmpty(projectInfo) && ObjectUtils.isNotEmpty(projectInfo.getTaskPhase())) {
                taskPhase = new ArrayList<String>(Arrays.asList(projectInfo.getTaskPhase().split(",")));
            }
            //项目任务阶段一级code
            List<String> projectTaskPhases = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(projectInfo) && ObjectUtils.isNotEmpty(projectInfo.getProjectTaskPhase())) {
                String[] split = projectInfo.getProjectTaskPhase().split(",");
                projectTaskPhases = new ArrayList<>(Arrays.asList(split));
                ProjectGroup groupServiceById = projectTemplateRepository.selectParentId(projectGroup.getParentId());
                if (ObjectUtil.isNotEmpty(groupServiceById) && ObjectUtil.isNotEmpty(groupServiceById.getNodeCode())) {
                    projectTaskPhases.remove(groupServiceById.getNodeCode());  //删掉当时完成的一级code
                    if (ObjectUtil.isNotEmpty(taskPhase)) {
                        taskPhase.remove(groupServiceById.getTemplateCode());  //删掉当时完成的一级code的模版阶段code
                    }
                }
            }

            //查找当前项目所对应的紧前
            //查找所有节点
            List<ProjectGroup> wbsConfigs = new ArrayList<>();
            if (redisUtils.hasKey("wbs" + projectId)) {
                wbsConfigs = (List<ProjectGroup>) redisUtils.get("wbs" + projectId);
            } else {
                LambdaQueryWrapper<ProjectGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectId, projectId)
                        .eq(ProjectGroup::getNodeLevel, 2);
//                        .eq(ObjectUtil.isNotEmpty(projectGroup.getRoundMarking()), ProjectGroup::getRoundMarking, projectGroup.getRoundMarking())
                List<ProjectGroup> projectGroups = projectGroupRepository.selectList(groupLambdaQueryWrapper);
                for (ProjectGroup group : projectGroups) {
                    ProjectGroup wbs = new ProjectGroup();
                    wbs.setNodeCode(group.getNodeCode());
                    wbs.setProjectId(group.getProjectId());
                    wbs.setParentId(group.getParentId());
                    wbs.setNodeName(group.getNodeName());
//                    wbs.setNodeStatus(group.getNodeStatus());
                    wbs.setFrontWbsConfig(group.getFrontWbsConfig());
                    wbs.setProjectGroupId(group.getProjectGroupId());
                    wbs.setRoleCode(group.getRoleCode());
                    wbsConfigs.add(wbs);
                }
                redisUtils.setIfAbsent("wbs" + projectId, wbsConfigs, 0);
            }

            //获取所有已完成的二级任务的nodeCode
            LambdaQueryWrapper<ProjectGroup> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(ProjectGroup::getProjectId, projectId);
            queryWrapper1.eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
            queryWrapper1.eq(ProjectGroup::getNodeLevel, 2);
//            queryWrapper1.eq(ObjectUtil.isNotEmpty(projectGroup.getRoundMarking()), ProjectGroup::getRoundMarking, projectGroup.getRoundMarking());
            List<ProjectGroup> list = projectGroupService.list(queryWrapper1);
            List<String> collect = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(list)) {
                collect = list.stream().map(ProjectGroup::getNodeCode).distinct().collect(Collectors.toList());
            }

            for (ProjectGroup group : wbsConfigs) {
                //循环这个项目的所有二级任务
                String frontWbsConfig = group.getFrontWbsConfig(); //获取当前二级任务的前置任务
                String nodeCodeForOpen = group.getNodeCode();
                Long projectGroupId = group.getProjectGroupId();  //当前二级任务的id
                ProjectGroupDto groupStatus = projectGroupService.findById(projectGroupId); //当前二级任务的详情
                String nodeStatus = groupStatus.getNodeStatus();  //当前二级任务的状态
                if (ObjectUtil.isNotEmpty(frontWbsConfig)) {
                    List<FrontWbsConfigDto> frontList = new ArrayList<>();
                    JSONArray jsonArray = new JSONArray(frontWbsConfig);
                    List<String> wbsList = new ArrayList<>();
                    for (Object obj : jsonArray) {
                        JSONObject jsonObject = (JSONObject) obj;
                        String wbs = null;
                        try {
                            wbs = jsonObject.getStr("wbs");
                        } catch (Exception e) {
                            //紧前错误的话，清掉紧前的缓存
                            redisUtils.del("wbs" + projectId);
                        }
                        wbsList.add(wbs);
                    }

                    if (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(nodeStatus) &&
                            !JhSystemEnum.NodeStatusEnum.NODE_STATUS5.getKey().equals(nodeStatus)
                            && !JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey().equals(nodeStatus)
                            && collect.containsAll(wbsList)
                    ) {
                        //状态不等于  已完成和   --   发送代办
                        LambdaUpdateWrapper<ProjectGroup> openLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        openLambdaUpdateWrapper.eq(ProjectGroup::getProjectGroupId, projectGroupId)
//                                .eq(ObjectUtil.isNotEmpty(projectGroup.getRoundMarking()), ProjectGroup::getRoundMarking, projectGroup.getRoundMarking())
//                                .and(o -> o.eq(ProjectGroup::getIsOpen, Boolean.FALSE).or().isNull(ProjectGroup::getIsOpen))
                                .set(ProjectGroup::getIsOpen, Boolean.TRUE)
                                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey());
                        projectGroupService.update(openLambdaUpdateWrapper);
                        //任务提交已完成状态下发起代办
                        projectTaskService.generateTodoTask(group);

                        if (ObjectUtils.isNotEmpty(group.getParentId())) {
                            ProjectGroup groupDto = projectTemplateRepository.selectParentId(group.getParentId());
                            if (ObjectUtils.isNotEmpty(groupDto)) {
                                projectTaskPhases.add(groupDto.getNodeCode());
                                taskPhase.add(groupDto.getTemplateCode());
                            }
                        }
                    }

//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
//                        JSONObject object = null;
//                        try {
//                            object = jsonArray.getJSONObject(i);
//                        } catch (Exception e) {
//                            //紧前错误的话，清掉紧前的缓存
//                            redisUtils.del("wbs" + projectId);
//                        }
//                        String type = object.getStr("type");
//                        String wbs = object.getStr("wbs");
//                        if ("FS".equals(type) && nodeCode.equals(wbs)) {
//                            if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(nodeStatus)) {
// //                                nodeCode = nodeCodeForOpen;
//                            } else {
//                                isOpen = true;
//                            }
//                        }
//                        // projectGroupService.update(openLambdaUpdateWrapper);
//                        //projectTaskService.generateTodoTask(group);
//                    }
                }
            }


            //项目任务阶段   项目任务阶段code
            if (ObjectUtil.isNotEmpty(projectInfo) && ObjectUtil.isNotEmpty(projectTaskPhases)) {
                List<String> collect1 = projectTaskPhases.stream().distinct().collect(Collectors.toList());
                projectInfo.setProjectTaskPhase(String.join(",", collect1));
            }
            if (ObjectUtil.isNotEmpty(projectInfo) && ObjectUtil.isNotEmpty(taskPhase)) {
                List<String> collect2 = taskPhase.stream().distinct().collect(Collectors.toList());
                projectInfo.setTaskPhase(String.join(",", collect2));
            }
        }

        //查询当前项目里的还没有完成的二级任务最近的计划结束日期
        LambdaQueryWrapper<ProjectGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeLevel, 2)
                .ne(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1)
                .isNotNull(ProjectGroup::getPlanEndDate)
                .orderByAsc(ProjectGroup::getPlanEndDate).last("limit 1");
        ProjectGroup groupServiceOne = projectGroupService.getOne(wrapper);
        if (ObjectUtil.isNotEmpty(projectInfo) &&ObjectUtil.isNotEmpty(groupServiceOne)) {
            projectInfo.setStairPlanEndDate(groupServiceOne.getPlanEndDate());
        }
        if (ObjectUtil.isNotEmpty(projectInfo))
        projectInfoRepository.updateById(projectInfo);

        //决算节点
//        if (JhSystemEnum.NodeCodeSEEnum.NODE_113.getKey().equals(projectGroup.getNodeCode())) {
//            Long storeId = projectInfo.getStoreId();
//            StoreMasterInfo storeMasterInfo = new StoreMasterInfo();
//            MasterContractInfo masterContractInfo = new MasterContractInfo();
//            MasterAreaInfo masterAreaInfo = new MasterAreaInfo();
//            LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            projectNodeInfoLambdaQueryWrapper.eq(ProjectNodeInfo::getProjectId, projectId);
//            List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoLambdaQueryWrapper);
//            for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
//                projectToMasterService.byProjectInfoUpdateStore(projectNodeInfo, storeMasterInfo, masterContractInfo, masterAreaInfo, storeId);
////                projectToMasterService.byProjectInfoUpdateStoreOrder(projectNodeInfo, projectId, storeId);
//            }
//        }
    }

    /**
     * @param messageTitle
     */
    private void sendMyMessage(Long projectId, String projectGroupId,String nodeCode, StringBuffer messageTitle,
                               String roleCode, String type) {
        //发送信息对应负责任信息
        try {
            ProjectInfo myInfo = projectInfoRepository.getPrjectNameAndNodeNameRolCode(projectId, nodeCode,projectGroupId);
            //                        设置条件lambdaQueryWrapper
            LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
            lambdaQueryWrapper.eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                    .eq(ProjectStakeholders::getProjectId, getSubmeterProjectId(projectId))
                    .isNull(ProjectStakeholders::getOrderId);

            //根据条件lambdaQueryWrapper，查询干系人数据
            List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);

            //对获取的干系人去重
            List<Long> collect2 = stakeholderslist.stream().map(ProjectStakeholders::getUserId).distinct().collect(Collectors.toList());
            List<UserDto> byIds = userMapper.toDto(userService.findByIds(collect2));

            //转换成map
            Map<Long, UserDto> collect3 = byIds.stream().collect(Collectors.toMap(UserDto::getId, e -> e));

            //转换成values为ProjectStakeholders的map
            Map<String, List<ProjectStakeholders>> collect1 = stakeholderslist.stream().collect(Collectors.groupingBy(ProjectStakeholders::getRoleCode));
            List<ProjectStakeholders> projectStakeholders = collect1.get(roleCode);
            List<String> mobileList = new ArrayList<>();  //手机号发送短信
            List<SendNotifyCenterTargetsVo> centerTargetsVos = new ArrayList<>();
            if (CollUtil.isNotEmpty(projectStakeholders)) {
                for (ProjectStakeholders one : projectStakeholders) {
                    if (one != null && null != one.getUserId()) {
                        UserDto userDto = collect3.get(one.getUserId());
                        if (StringUtils.isNotEmpty(userDto.getPhone())) {
                            mobileList.add(userDto.getPhone());
                            SendNotifyCenterTargetsVo targetsVo = new SendNotifyCenterTargetsVo();
                            targetsVo.setMobile(userDto.getPhone());
                            centerTargetsVos.add(targetsVo);
                        }
                    }
                }
                String description = projectTaskService.getDescription(myInfo, null, type);
                if (mobileList.size() > 0) {
                    portalService.sendMessage(mobileList, messageTitle.toString(), description);
                }
                if (ObjectUtil.isNotEmpty(centerTargetsVos)) {
                    projectTaskService.sendAsynchronous(centerTargetsVos, messageTitle.append(description).toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendMsgProjectDirector(ProjectGroup projectGroup) {
        //先查询当前项目是否存在交付中心-工程负责人
        List<ProjectStakeholders> listByRoleCodes = projectStakeholdersRepository.selectListByRoleCodes(projectGroup.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.JFZXFZR.getKey());
        if (ObjectUtil.isEmpty(listByRoleCodes) || listByRoleCodes.size() == 0) {
            // 1.不存在，查询当前项目的交付中心-工程负责人
            List<ProjectStakeholders> gcListSta = projectStakeholdersRepository.selectListByRoleCodes(projectGroup.getProjectId(), AtourSystemEnum.constructionDockingStartedRole.JFZX_GCFZR.getKey());
            for (ProjectStakeholders projectStakeholders : gcListSta) {
                // 2.不存在则发送消息通知
                projectNoticeService.generateNotice(projectGroup.getProjectGroupId(), projectGroup, JhSystemEnum.MessageTemplate.MB1000010, projectStakeholders.getUserId());
            }
        }

    }

    private void roomPlanDrawing(Long projectId, ProjectGroup projectGroup) {
        //1.将工程房号所选的房号的状态改成已使用，并带出到【客房样板间施工图】
        final ArrayList<String> roomList = new ArrayList<>();
        final ArrayList<ProjectRoom> projectRoomArrayList = new ArrayList<>();
        final AtourSystemEnum.DesignDrawingTow[] values = AtourSystemEnum.DesignDrawingTow.values();
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(ProjectNodeInfo::getProjectId, projectId)
                .in(ProjectNodeInfo::getNodeCode, Arrays.stream(values).map(AtourSystemEnum.DesignDrawingTow::getKey).collect(Collectors.toList()));
        final List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(eq);
        final LambdaQueryWrapper<ProjectRoom> roomLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectRoom.class).eq(ProjectRoom::getProjectId, projectId);
        final List<ProjectRoom> projectRooms = projectRoomRepository.selectList(roomLambdaQueryWrapper);
        for (int i = 0; i < values.length; i++) {
//            int finalI = i;
//            final List<ProjectNodeInfo> collect = nodeInfos.stream().filter(nodeInfo -> values[finalI].getKey().equals(nodeInfo.getNodeCode())).limit(1).collect(Collectors.toList());
//            final ProjectNodeInfo room = Optional.ofNullable(collect).flatMap(c -> Optional.ofNullable(c.get(0))).orElse(null);
            ProjectNodeInfo room = null;
            for(ProjectNodeInfo nodeInfo:nodeInfos){
                if(values[i].getKey().equals(nodeInfo.getNodeCode())){
                    room = nodeInfo;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(room) && ObjectUtil.isNotEmpty(room.getRemark())) {
                roomList.add(room.getRemark());
            }
        }
        try {
            roomList.stream().forEach(room -> {
                final List<ProjectRoom> collect = projectRooms.stream().filter(pr -> room.equals(pr.getRoomNum())).limit(1).collect(Collectors.toList());
                Optional.ofNullable(collect).ifPresent(c -> Optional.ofNullable(c.get(0)).ifPresent(r -> {
                    r.setIsUsed("1");
                    r.setIsRoomPlanEffectDrawing("1");
                    projectRoomArrayList.add(r);
                }));
            });
        } catch (Exception e) {
            throw new BadRequestException("请重新选择样板间房号！");
        }
        projectRoomService.saveOrUpdateBatch(projectRoomArrayList);
//        //2.房间号信息带出到【客房样板间施工图】
//        final AtourSystemEnum.DesignDrawingOut[] designDrawingOuts = AtourSystemEnum.DesignDrawingOut.values();
//        for (int i = 0; i < designDrawingOuts.length; i++) {
//            broughtOut(projectGroup, designDrawingOuts[i].getOutKey(), designDrawingOuts[i].getIntoKey());
//        }
        //3.带出样板间房间号到【施工照片】
        final LambdaUpdateWrapper<ProjectRoom> wrapper = Wrappers.lambdaUpdate(ProjectRoom.class)
                .eq(ProjectRoom::getProjectId, projectId);
        final List<ProjectRoom> rooms = projectRoomService.list(wrapper);
        final List<ProjectRoom> collect = rooms.stream().filter(e -> "1".equals(e.getIsUsed())).collect(Collectors.toList());
        this.saveOrUpdateConstructionPhotograph(projectId, collect);
        //4.将房间总数带出到【竣工验收申请】的设计房间数eng-00129023;eng-00139038;eng-00141013
        if (ObjectUtil.isNotEmpty(rooms)) {
            broughtOutRoomSum(AtourSystemEnum.CompletionWeakRoom.ENG00129023.getKey(), projectId, rooms);
            broughtOutRoomSum(AtourSystemEnum.CompletionWeakRoom.ENG00139038.getKey(), projectId, rooms);
            broughtOutRoomSum(AtourSystemEnum.CompletionWeakRoom.ENG00141013.getKey(), projectId, rooms);
        }
    }

    private void broughtOutSgdwManger(Long projectId) {
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getNodeCode, "eng-00105020").eq(ProjectNodeInfo::getProjectId, projectId);
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(eq);
        Optional.ofNullable(nodeInfo).map(ProjectNodeInfo::getRemark).ifPresent(remark -> {
                    try {
                        Long.parseLong(remark);
                    } catch (NumberFormatException e) {
                        System.out.println(remark + "无法转换为Long类型");
                    }
                    final User one = userRepository.getOne(Long.valueOf(remark));
                    Optional.ofNullable(one).ifPresent(o -> {
                        final LambdaUpdateWrapper<ProjectNodeInfo> lambdaUpdateWrapper1 = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getProjectId, projectId)
                                .eq(ProjectNodeInfo::getNodeCode, "des-00105011").set(ProjectNodeInfo::getRemark, o.getUsername() + "-" + o.getNickName());
                        this.update(lambdaUpdateWrapper1);
                        final LambdaUpdateWrapper<ProjectNodeInfo> lambdaUpdateWrapper2 = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getProjectId, projectId)
                                .eq(ProjectNodeInfo::getNodeCode, "des-00105012").set(ProjectNodeInfo::getRemark, o.getPhone());
                        this.update(lambdaUpdateWrapper2);
                        final LambdaUpdateWrapper<ProjectNodeInfo> lambdaUpdateWrapper3 = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getProjectId, projectId)
                                .eq(ProjectNodeInfo::getNodeCode, "des-00105013").set(ProjectNodeInfo::getRemark, o.getEmail());
                        this.update(lambdaUpdateWrapper3);
                    });
                }
        );
    }

    private void saveCompletionReceiptByVersion(Long projectId) {
        final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, "eng-00129105");
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(wrapper);
        Optional.ofNullable(nodeInfo).ifPresent(node -> Optional.ofNullable(node.getRemark()).filter(ObjectUtil::isNotEmpty).ifPresent(e -> {
            final List<ProjectCompletionReceipt> list = new ArrayList<>();
            final List<TemplateCompletionReceiptRelationVo> template = templateCompletionReceiptRelationRepository.selectByReceiptGroupId(Long.valueOf(e), null, null, null, null, null);
            template.stream().forEach(t -> {
                final ProjectCompletionReceipt receipt = new ProjectCompletionReceipt();
                BeanUtil.copyProperties(t, receipt, CopyOptions.create().setIgnoreNullValue(true));
                receipt.setProjectId(projectId);
                receipt.setIsOperation(1);
                receipt.setIsEnabled("1");
                receipt.setIsDelete("0");
                receipt.setReceiptGroupId(Long.valueOf(e));
                list.add(receipt);
            });
            projectCompletionReceiptService.saveOrUpdateBatch(list);

            //生成其他现场照片数据
            List<TemplateCompletionReceipt> subItemList = templateCompletionReceiptRepository.getSubItemList(Long.valueOf(e));
            List<ProjectCompletionPhoto> completionPhotoList = new ArrayList<>();
            subItemList.forEach(s -> {
                ProjectCompletionPhoto projectCompletionPhoto = new ProjectCompletionPhoto();
                projectCompletionPhoto.setProjectId(projectId);
                projectCompletionPhoto.setTotalItem(s.getTotalItem());
                projectCompletionPhoto.setSubItem(s.getSubItem());
                completionPhotoList.add(projectCompletionPhoto);
            });
            projectCompletionPhotoService.saveOrUpdateBatch(completionPhotoList);
        }));

    }

    @Override
    @Transactional
    public void saveCompletionReceiptByNewVersion(Long projectId) {
        //查询最新的验收单数据
        List<TemplateGroupCompletionReceipt> completeVersion = templateGroupCompletionReceiptService.getCompleteVersion(projectId);
        if (completeVersion.size() == 0) {
            return;
        }
        Long receiptGroupId = completeVersion.get(completeVersion.size() - 1).getReceiptGroupId();
        final List<ProjectCompletionReceipt> list = new ArrayList<>();
        QueryWrapper queryWrapper = new QueryWrapper<>().eq("project_id", projectId);
        long countCompletionReceipt = projectCompletionReceiptService.count(queryWrapper);
        if (countCompletionReceipt == 0) {
            List<TemplateCompletionReceiptRelationVo> template = templateCompletionReceiptRelationRepository.selectByReceiptGroupId(Long.valueOf(receiptGroupId), null, null, null, null, null);
            template.stream().forEach(t -> {
                final ProjectCompletionReceipt receipt = new ProjectCompletionReceipt();
                BeanUtil.copyProperties(t, receipt, CopyOptions.create().setIgnoreNullValue(true));
                receipt.setProjectId(projectId);
                receipt.setIsOperation(1);
                receipt.setIsEnabled("1");
                receipt.setIsDelete("0");
                receipt.setReceiptGroupId(receiptGroupId);
                list.add(receipt);
            });
            projectCompletionReceiptService.saveOrUpdateBatch(list);
        }

        long countCompletionPhoto = projectCompletionPhotoService.count(queryWrapper);
        if (countCompletionPhoto == 0) {
            //生成其他现场照片数据
            List<TemplateCompletionReceipt> subItemList = templateCompletionReceiptRepository.getSubItemList(receiptGroupId);
            List<ProjectCompletionPhoto> completionPhotoList = new ArrayList<>();
            subItemList.forEach(s -> {
                ProjectCompletionPhoto projectCompletionPhoto = new ProjectCompletionPhoto();
                projectCompletionPhoto.setProjectId(projectId);
                projectCompletionPhoto.setTotalItem(s.getTotalItem());
                projectCompletionPhoto.setSubItem(s.getSubItem());
                completionPhotoList.add(projectCompletionPhoto);
            });
            projectCompletionPhotoService.saveOrUpdateBatch(completionPhotoList);
        }
    }

    @Override
    public void updateRemark(ProjectNodeInfo projectNodeInfo) {
        util.initialize(this.getSubmeterProjectId(projectNodeInfo.getProjectId()));
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id", projectNodeInfo.getProjectId());
        queryWrapper.eq("node_code", projectNodeInfo.getNodeCode());
        List<ProjectNodeInfo> nodeInfos = list(queryWrapper);
        if (nodeInfos.size() > 0) {
            ProjectNodeInfo nodeInfo = nodeInfos.get(0);
            nodeInfo.setRemark(projectNodeInfo.getRemark());
            updateById(nodeInfo);
        }
    }

    private void broughtOutSjjdsqCon(Long projectId, String nodeCode, String intoKey, String intoKeySec) {
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode)
                .eq(ProjectNodeInfo::getProjectId, projectId);
        final ProjectNodeInfo nodeInfoB = projectNodeInfoRepository.selectOne(eq);
        Optional.ofNullable(nodeInfoB).map(ProjectNodeInfo::getRemark).filter(r -> ObjectUtil.isNotEmpty(r))
                .ifPresent(e -> {
                    final User one = userRepository.getOne(Long.valueOf(e));
                    Optional.ofNullable(one).filter(ObjectUtil::isNotEmpty).ifPresent(o -> Optional.ofNullable(one.getPhone()).filter(ObjectUtil::isNotEmpty).ifPresent(p -> {
                        final LambdaQueryWrapper<ProjectNodeInfo> eqA = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getNodeCode, intoKey)
                                .eq(ProjectNodeInfo::getProjectId, projectId);
                        final ProjectNodeInfo nodeInfoAA = projectNodeInfoRepository.selectOne(eqA);
                        final LambdaQueryWrapper<ProjectNodeInfo> eqB = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                                .eq(ProjectNodeInfo::getNodeCode, intoKeySec)
                                .eq(ProjectNodeInfo::getProjectId, projectId);
                        final ProjectNodeInfo nodeInfoAB = projectNodeInfoRepository.selectOne(eqB);
                        nodeInfoAA.setRemark(p);
                        nodeInfoAB.setRemark(p);
                        update(nodeInfoAA);
                        update(nodeInfoAB);
                    }));
                });
    }

    @Override
    public void broughtOutXmjlToCol(ProjectNodeInfo projectNodeInfo, Long projectId) {
        util.initialize(getSubmeterProjectId(projectId));
        final ArrayList<ProjectNodeInfo> list = new ArrayList<>();
        final LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.ZXDWXMJL.ENG00105020.getKey())
                .last("limit 1");
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(lambdaQueryWrapper);
        Optional.ofNullable(nodeInfo).ifPresent(n -> Optional.ofNullable(n.getRemark()).ifPresent(user -> {
            final User one = userRepository.getOne(Long.valueOf(user));
            Optional.ofNullable(one).ifPresent(o -> {
                projectInfoService.createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZXDWXMJL.getKey(), o.getId(), null);
                //总包单位-项目经理入干系人
                projectInfoService.createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZBDWXMJL.getKey(), o.getId(), null);
                //带出到【施工日志】
                final LambdaQueryWrapper<ProjectConstructionLog> logLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectConstructionLog.class)
                        .eq(ProjectConstructionLog::getProjectId, projectId);
                final List<ProjectConstructionLog> logs = projectConstructionLogRepository.selectList(logLambdaQueryWrapper);
                Optional.ofNullable(logs).ifPresent(l ->
                        l.stream().map(ProjectConstructionLog::getId).forEach(id -> {
                            final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                                    .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.ZXDWXMJL.COL00101005.getKey())
                                    .last("limit 1");
                            final ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
                            Optional.ofNullable(info).ifPresent(i -> {
                                i.setRemark(one.getUsername() + "-" + one.getNickName());
                                list.add(i);
                            });
                        }));
//
//                if (ObjectUtil.isNotEmpty(projectNodeInfo) ) {
////                    if (projectNodeInfo.getNodeCode().equals(AtourSystemEnum.ZXDWXMJL.COL00101005.getKey())) {
////                        projectNodeInfo.setRemark(one.getUsername() + "-" + one.getNickName());
////                    }
////                }else{
////                    final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
////                            .eq(ProjectNodeInfo::getProjectId, projectId)
////                            .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.ZXDWXMJL.COL00101005.getKey());
////                    List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(eq);
////                    for (ProjectNodeInfo info : infos) {
////                        Optional.ofNullable(info).ifPresent(i -> {
////                            i.setRemark(one.getUsername() + "-" + one.getNickName());
////                            list.add(i);
////                        });
////                    }
////                    this.saveOrUpdateBatch(list);
////                }
            });
        }));
        this.saveOrUpdateBatch(list);
    }

    private void broughtOutStartTime(Long projectId, Timestamp currentTime, String key) {
        final LambdaQueryWrapper<ProjectNodeInfo> wrapperTime = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, key);
        final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(wrapperTime);
        if (ObjectUtil.isNotEmpty(projectNodeInfo)) {
            projectNodeInfo.setRemark(new SimpleDateFormat("yyyy-MM-dd").format(currentTime));
            this.update(projectNodeInfo);
        }
    }

    private void broughtOutRoomSum(String nodeCode, Long projectId, List<ProjectRoom> rooms) {
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode);
        final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(eq);
        Optional.ofNullable(projectNodeInfo).ifPresent(p -> {
            p.setRemark(String.valueOf(rooms.size()));
            this.update(p);
        });
    }

    private void saveConstructionPhotograph(Long projectId, List<ProjectRoom> rooms) {
        final List<ConstructionPhotograph> objects = new ArrayList<>();
        final LambdaQueryWrapper<ConstructionPhotograph> eq = Wrappers.lambdaQuery(ConstructionPhotograph.class)
                .eq(ConstructionPhotograph::getProjectId, projectId);
        final List<ConstructionPhotograph> list = constructionPhotographService.list(eq);
        final List<String> collect = list.stream().map(ConstructionPhotograph::getBuildingNumber).collect(Collectors.toList());
        final List<String> roomNums = rooms.stream().map(ProjectRoom::getRoomNum).filter(r -> !collect.contains(r)).collect(Collectors.toList());
        roomNums.stream().forEach(num -> {
            final ConstructionPhotograph constructionPhotograph = new ConstructionPhotograph();
            constructionPhotograph.setProjectId(projectId);
            constructionPhotograph.setBuildingNumber(num);
            objects.add(constructionPhotograph);
        });
        constructionPhotographService.saveBatch(objects);
    }

    private void broughtOutContactPhone(Long projectId) {
        final AtourSystemEnum.ContactsPhone[] values = AtourSystemEnum.ContactsPhone.values();
        final ArrayList<ProjectNodeInfo> list = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, values[i].getUnit());
            final ProjectNodeInfo unit = projectNodeInfoRepository.selectOne(wrapper);
            final LambdaQueryWrapper<ProjectNodeInfo> wrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, values[i].getContact());
            final ProjectNodeInfo contact = projectNodeInfoRepository.selectOne(wrapper1);
            int finalI = i;
            int finalI1 = i;
            if (ObjectUtil.isNotEmpty(unit) && ObjectUtil.isNotEmpty(contact)
                    && ObjectUtil.isNotEmpty(unit.getRemark()) && ObjectUtil.isNotEmpty(contact.getRemark())) {
                final LambdaQueryWrapper<SupplierInfo> eq = Wrappers.lambdaQuery(SupplierInfo.class)
                        .eq(SupplierInfo::getId, unit.getRemark()).eq(SupplierInfo::getContact, contact.getRemark()).last("limit 1");
                final SupplierInfo supplierInfo = supplierInfoRepository.selectOne(eq);
                if (ObjectUtil.isNotEmpty(supplierInfo) && ObjectUtil.isNotEmpty(supplierInfo.getPhone())) {
                    final LambdaQueryWrapper<ProjectNodeInfo> wrapper2 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, values[finalI1].getPhone());
                    final ProjectNodeInfo phone = projectNodeInfoRepository.selectOne(wrapper2);
                    if (ObjectUtil.isNotEmpty(phone)) {
                        phone.setRemark(supplierInfo.getPhone());
                        list.add(phone);
                    }
                }
            }
        }
        this.saveOrUpdateBatch(list);
    }

    private void breakOutMaterialManagement(Long projectId, String outKey, String intoKey) {
        final LambdaQueryWrapper<ProjectMaterialManagement> wrapper = Wrappers.lambdaQuery(ProjectMaterialManagement.class)
                .eq(ProjectMaterialManagement::getProjectId, projectId);
        final List<ProjectMaterialManagement> list = projectMaterialManagementService.list(wrapper);
        final List<ProjectMaterialManagement> objects = new ArrayList<>();
        final List<ProjectMaterialManagement> collect = list.stream().filter(l -> outKey.equals(l.getNodeCode())).collect(Collectors.toList());
        final List<ProjectMaterialManagement> collect1 = list.stream().filter(l -> intoKey.equals(l.getNodeCode())).collect(Collectors.toList());
        collect1.stream().forEach(into -> {
            final List<String> strings = collect.stream().filter(out -> out.getRoomNumber().equals(into.getRoomNumber())
                    && into.getMaterialCategory().equals(out.getMaterialCategory())).map(ProjectMaterialManagement::getAgreeTake).collect(Collectors.toList());
            into.setAgreeTake(ObjectUtil.isNotEmpty(strings) ? strings.get(0) : null);
            final List<String> strings1 = collect.stream().filter(out -> out.getRoomNumber().equals(into.getRoomNumber())
                    && out.getMaterialCategory().equals(into.getMaterialCategory())).map(ProjectMaterialManagement::getPlatformPurchaseNot).collect(Collectors.toList());
            into.setPlatformPurchaseNot(ObjectUtil.isNotEmpty(strings1) ? strings1.get(0) : null);
            final List<String> strings2 = collect.stream().filter(out -> out.getRoomNumber().equals(into.getRoomNumber())
                    && out.getMaterialCategory().equals(into.getMaterialCategory())).map(ProjectMaterialManagement::getRemark).collect(Collectors.toList());
            into.setRemark(ObjectUtil.isNotEmpty(strings2) ? strings2.get(0) : null);
            objects.add(into);
        });
        projectMaterialManagementService.updateBatchById(objects);
        //额外新增的物资需要插入
        final ArrayList<ProjectMaterialManagement> managements = new ArrayList<>();
        final List<String> stringList = list.stream().filter(l -> intoKey.equals(l.getNodeCode())).map(ProjectMaterialManagement::getMaterialCategory).collect(Collectors.toList());
        final List<ProjectMaterialManagement> materialManagements = collect.stream().filter(l -> !stringList.contains(l.getMaterialCategory())).collect(Collectors.toList());
        materialManagements.stream().forEach(m -> {
            final ProjectMaterialManagement projectMaterialManagement = new ProjectMaterialManagement();
            BeanUtil.copyProperties(m, projectMaterialManagement, CopyOptions.create().setIgnoreNullValue(true));
            projectMaterialManagement.setProjectManagementId(null);
            projectMaterialManagement.setNodeCode(intoKey);
            managements.add(projectMaterialManagement);
        });
        projectMaterialManagementService.saveBatch(managements);
    }

    private void breakOutDrawingFile(Long projectId, String outKey, String intoKey, String fileHeader) {
        //获取审图图纸信息
        final LambdaQueryWrapper<ProjectGroupExpand> wrapper = Wrappers.lambdaQuery(ProjectGroupExpand.class).eq(ProjectGroupExpand::getProjectId, projectId)
                .eq(ProjectGroupExpand::getNodeCode, outKey)
                .eq(ProjectGroupExpand::getFileHeader, fileHeader);
        final ProjectGroupExpand one = projectGroupExpandService.getOne(wrapper);
        Optional.ofNullable(one).ifPresent(f -> Optional.ofNullable(f.getDrawingFile()).filter(ObjectUtil::isNotEmpty).ifPresent(e -> {
            Arrays.stream(e.split(",")).forEach(s -> {
                final LocalStorageDto localStorageDto = localStorageService.findById(Long.valueOf(s));
                final String id = projectNodeInfoRepository.getNodeIdByNodeCode(projectId, intoKey);
                final LocalStorage localStorage = new LocalStorage();
                BeanUtil.copyProperties(localStorageDto, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                localStorage.setId(null);
                localStorage.setNodeId(id);
                localStorageRepository.insert(localStorage);
            });
        }));
    }

    private void removeIsUsedRoom(Long projectId, ArrayList<String> roomList) {
        final LambdaUpdateWrapper<ProjectRoom> wrapper = Wrappers.lambdaUpdate(ProjectRoom.class)
                .eq(ProjectRoom::getProjectId, projectId).eq(ProjectRoom::getIsUsed, "1");
        final List<ProjectRoom> rooms = projectRoomService.list(wrapper);
        final List<ProjectRoom> list = new ArrayList<>();
        final List<ProjectRoom> collect = rooms.stream().filter(r -> !roomList.contains(r.getRoomNum()) && "1".equals(r.getIsUsed())).collect(Collectors.toList());
        collect.stream().forEach(r -> {
            r.setIsUsed("0");
            r.setIsModelRoom("0");
            r.setIsMandatory(null);
            list.add(r);
        });
        projectRoomService.saveOrUpdateBatch(list);
    }

    private void saveOrUpdateConstructionPhotograph(Long projectId, List<ProjectRoom> rooms) {
        final List<String> collect = rooms.stream().map(ProjectRoom::getRoomNum).collect(Collectors.toList());
        final ArrayList<ConstructionPhotograph> list = new ArrayList<>();
        collect.stream().forEach(room -> {
            final ConstructionPhotograph photograph = new ConstructionPhotograph();
            photograph.setBuildingNumber(room);
            photograph.setProjectId(projectId);
            list.add(photograph);
        });
        constructionPhotographService.saveOrUpdateBatch(list);
    }

    private void saveOrUpdateTemplateMaterial(Long projectId, List<ProjectRoom> rooms) {
        final Object[] objects = rooms.stream().map(ProjectRoom::getRoomNum).toArray();
        final Map<String, List<ProjectRoom>> collect = rooms.stream().collect(Collectors.groupingBy(ProjectRoom::getRoomNum));
        final LambdaQueryWrapper<MaterialManagement> wrapper = Wrappers.lambdaQuery(MaterialManagement.class)
                .eq(MaterialManagement::getIsDelete, 0);
        final List<MaterialManagement> materialManagements = materialManagementService.list(wrapper);
        final ArrayList<ProjectMaterialManagement> list = new ArrayList<>();
        final AtourSystemEnum.MaterialManagementCode[] codes = AtourSystemEnum.MaterialManagementCode.values();
        for (int j = 0; j < codes.length; j++) {
            for (int i = 0; i < objects.length; i++) {
                int finalI = i;
                int finalJ = j;
                materialManagements.stream().forEach(m -> {
                    ProjectMaterialManagement projectMaterialManagement = new ProjectMaterialManagement();
                    BeanUtil.copyProperties(m, projectMaterialManagement, CopyOptions.create().setIgnoreNullValue(true));
                    projectMaterialManagement.setProjectId(projectId);
                    projectMaterialManagement.setRoomNumber(String.valueOf(objects[finalI]));
                    if ("must_pay".equals(collect.get((String) objects[finalI]).get(0).getIsMandatory())) {
                        projectMaterialManagement.setPlatformPurchaseNot("yes");
                        projectMaterialManagement.setAgreeTake("must_pay");
                    }
                    projectMaterialManagement.setNodeCode(codes[finalJ].getKey());
                    list.add(projectMaterialManagement);
                });
            }
            //插入材料数据之前，先隐藏列表，插入之后再打开
            final LambdaUpdateWrapper<ProjectNodeInfo> set = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, codes[j].getKey())
                    .set(ProjectNodeInfo::getIsShow, "0");
            this.update(set);
        }
        projectMaterialManagementService.saveBatch(list);
    }

    private void upModifyValidationDesignerNode(ProjectGroup projectGroup) {
        util.initialize(getSubmeterProjectId(projectGroup.getProjectId()));
        //任务“设计勘测“完成后，   【筹建启动会】提交的话查看选择的设计师是否一致，不一致打开【确认设计师】节点，修改【勘测报告】的紧前为【确认设计师】，一致关闭【确认设计师】节点
        //                     【筹建启动会】没有提交的话，不需要判断是否一致，直接进行【设计勘测】入干系人，关闭【确认设计师】节点
        if (projectGroup.getNodeCode().equals(JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey())) {
            LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
                    .eq(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey())
                    .eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
            Long aLong = projectGroupRepository.selectCount(queryWrapper);
            if (aLong == 1) {
                //筹建启动会已经提交的话，判断是否一致
                boolean isConsistent = this.getStylistNodeCodeEnum(projectGroup.getProjectId());
                if (isConsistent) {
                    //设计师选择一致，关闭“确认设计单位”二级任务，
                    this.cloneConfirmDesignUnit(projectGroup.getProjectId());
                } else {
                    //设计师选择不一致，“确认设计单位”任务展示，并且把设装饰勘测报告/机电勘测报告的紧前设置为 “报告”的两个任务
                    this.showconfirmDesignUnit(projectGroup.getProjectId());
                }
            } else {
                //筹建启动会没有提交的话，不需要考虑一致不一致 关闭“确认设计单位”二级任务，
                this.cloneConfirmDesignUnit(projectGroup.getProjectId());
            }
        }
//        String[] nodes = {JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey(), JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey()};
//        LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
//                .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
//                .in(ProjectGroup::getNodeCode, nodes)
//                .eq(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
//        Long aLong = projectGroupRepository.selectCount(queryWrapper);
//        if (aLong == 2) {
//            util.initialize(getSubmeterProjectId(projectGroup.getProjectId()));
//            boolean isConsistent = this.getStylistNodeCodeEnum(projectGroup.getProjectId());
//            if (isConsistent) {
//                //设计师选择一致，关闭“确认设计单位”二级任务，
//                this.cloneConfirmDesignUnit(projectGroup.getProjectId());
//            } else {
//                //设计师选择不一致，“确认设计单位”任务展示，并且把设装饰勘测报告/机电勘测报告的紧前设置为 “报告”的两个任务
//                this.showconfirmDesignUnit(projectGroup.getProjectId());
//            }
//        }

        //基础信息不一致，打开信息一致性，一致，继续流程
        boolean isAccord = this.getBaseInfoNodeCodeEnum(projectGroup.getProjectId(), projectGroup.getNodeCode());
        if (isAccord) {
            //基础信息不一致,打开信息一致性
            //展示二级节点
            LambdaQueryWrapper<ProjectGroup> groupWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
                    .eq(JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode()), ProjectGroup::getNodeCode, "eng-00143")
                    .eq(JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(projectGroup.getNodeCode()), ProjectGroup::getNodeCode, "des-00167");
            ProjectGroup group = projectGroupRepository.selectOne(groupWrapper);
            if (ObjectUtil.isNotEmpty(group)) {
                group.setIsShow(0);
                projectGroupRepository.updateById(group);
            }
            //展示不一致的三级节点
            LambdaQueryWrapper<ProjectNodeInfo> nodeInfoWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                    .in(JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode()), ProjectNodeInfo::getNodeCode, JhSystemEnum.EngBaseInfo.getNodeCodes())
                    .in(JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(projectGroup.getNodeCode()), ProjectNodeInfo::getNodeCode, JhSystemEnum.DesBaseInfo.getNodeCodes());
            List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(nodeInfoWrapper);
            nodeInfos.forEach(ni -> {
                if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode())) {
                    for (JhSystemEnum.EngBaseInfo baseInfo : JhSystemEnum.EngBaseInfo.values()) {
                        if (ni.getNodeCode().equals(baseInfo.getNodeCode())) {
                            if (StringUtils.isNotEmpty(ni.getRemark()) &&
                                    ("change".equals(ni.getRemark()) ||
                                            "not_effective".equals(ni.getRemark()) ||
                                            "reduce".equals(ni.getRemark()) ||
                                            "increase".equals(ni.getRemark()) ||
                                            "inconsistent".equals(ni.getRemark()) ||
                                            "no".equals(ni.getRemark()))
                            ) {
                                projectNodeInfoRepository.update(null, Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                                        .in(ProjectNodeInfo::getNodeCode, baseInfo.getRelationNodeCodes())
                                        .set(ProjectNodeInfo::getIsShow, 0));
                            }
                        }
                    }
                }
                if (JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(projectGroup.getNodeCode())) {
                    for (JhSystemEnum.DesBaseInfo baseInfo : JhSystemEnum.DesBaseInfo.values()) {
                        if (ni.getNodeCode().equals(baseInfo.getNodeCode())) {
                            if (StringUtils.isNotEmpty(ni.getRemark()) &&
                                    ("change".equals(ni.getRemark()) ||
                                            "not_effective".equals(ni.getRemark()) ||
                                            "reduce".equals(ni.getRemark()) ||
                                            "increase".equals(ni.getRemark()) ||
                                            "inconsistent".equals(ni.getRemark()) ||
                                            "no".equals(ni.getRemark()))
                            ) {
                                projectNodeInfoRepository.update(null, Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                                        .in(ProjectNodeInfo::getNodeCode, baseInfo.getRelationNodeCodes())
                                        .set(ProjectNodeInfo::getIsShow, 0));
                            }
                        }
                    }
                }
            });
        } else {
            if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(projectGroup.getNodeCode())) {
                //筹建启动会，信息一致性改为已完成
                projectGroupService.update(Wrappers.lambdaUpdate(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
                        .eq(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_ENG143.getKey())
                        .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey()));
            }
            if (JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(projectGroup.getNodeCode())) {
                //设计勘测，信息一致性改为已完成
                projectGroupService.update(Wrappers.lambdaUpdate(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
                        .eq(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_DES167.getKey())
                        .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey()));
            }

        }
    }

    private void showconfirmDesignUnit(Long projectId) {

        LambdaQueryWrapper<ProjectGroup> wrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .in(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_DES127.getKey());
        ProjectGroup projectGroup = projectGroupRepository.selectOne(wrapper);
        projectGroup.setIsShow(0);
        projectGroupService.update(projectGroup);

        LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .in(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_DES103.getKey());
        ProjectGroup projectGroup1 = projectGroupRepository.selectOne(queryWrapper);
        projectGroup1.setFrontWbsConfig("[{\"wbs\":\"des-00127\",\"type\":\"FS\"}]");
        projectGroupService.update(projectGroup1);

        LambdaQueryWrapper<ProjectGroup> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .in(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_DES165.getKey());
        ProjectGroup projectGroup2 = projectGroupRepository.selectOne(lambdaQueryWrapper);
        projectGroup2.setFrontWbsConfig("[{\"wbs\":\"des-00127\",\"type\":\"FS\"}]");
        projectGroupService.update(projectGroup2);

        //TODO 不一致 "确认设计单位"的某些信息赋值给"正式开工"
        for (JhSystemEnum.ConfirmDesignUnit cdu : JhSystemEnum.ConfirmDesignUnit.values()) {
            util.initialize(getSubmeterProjectId(projectId));
            //确认设计单位
            LambdaQueryWrapper<ProjectNodeInfo> confirmWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, cdu.getConfirmCode())
                    .eq(ProjectNodeInfo::getIsDelete, 0);
            ProjectNodeInfo confirm = this.baseMapper.selectOne(confirmWrapper);
            //正式开工
            LambdaQueryWrapper<ProjectNodeInfo> formalWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, cdu.getFormalCode())
                    .eq(ProjectNodeInfo::getIsDelete, 0);
            ProjectNodeInfo formal = this.baseMapper.selectOne(formalWrapper);
            if (Objects.nonNull(formal) && Objects.nonNull(confirm)) {
                formal.setRemark(confirm.getRemark());
                this.baseMapper.updateById(formal);
            }
        }
    }

    private void cloneConfirmDesignUnit(Long projectId) {
        util.initialize(getSubmeterProjectId(projectId));
        //关闭“确认设计单位”二级任务
        LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .in(ProjectGroup::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_DES127.getKey());
        ProjectGroup projectGroup = projectGroupRepository.selectOne(queryWrapper);
        projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
        projectGroup.setFrontWbsConfig("");
        projectGroupService.update(projectGroup);
        //关闭“确认设计单位”二级任务的代办
        projectTaskService.finishTask(projectGroup);
        //设计勘测选择的设计师，入干系人
        JhSystemEnum.stylistNodeCodeEnum[] nodeCodeEnums = JhSystemEnum.stylistNodeCodeEnum.values();
        for (JhSystemEnum.stylistNodeCodeEnum nodeCodeEnum : nodeCodeEnums) {
            LambdaQueryWrapper<ProjectNodeInfo> queryWrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, nodeCodeEnum.getNodeCodeDes());
            ProjectNodeInfo nodeInfoDes = projectNodeInfoRepository.selectOne(queryWrapper1);
            if (ObjectUtil.isNotEmpty(nodeInfoDes)) {
                String roleCode = ObjectUtils.isNotEmpty(nodeInfoDes.getJobCode()) ? nodeInfoDes.getJobCode() : nodeCodeEnum.getRoleCode();
                String downCode = nodeInfoDes.getDownCode();
                Long userId = Long.parseLong(nodeInfoDes.getRemark());
                projectInfoService.createProjectStakeholders(projectId, roleCode, userId, downCode);
            }
        }
        //TODO 一致 "设计勘测"的某些数据信息赋值给"正式开工"

        for (JhSystemEnum.ConfirmDesignUnit cdu : JhSystemEnum.ConfirmDesignUnit.values()) {
            //设计勘测
            LambdaQueryWrapper<ProjectNodeInfo> preparationWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, cdu.getDesignCode())
                    .eq(ProjectNodeInfo::getIsDelete, 0);
            ProjectNodeInfo confirm = this.baseMapper.selectOne(preparationWrapper);
            //正式开工
            LambdaQueryWrapper<ProjectNodeInfo> formalWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, cdu.getFormalCode())
                    .eq(ProjectNodeInfo::getIsDelete, 0);
            ProjectNodeInfo formal = this.baseMapper.selectOne(formalWrapper);
            if (Objects.nonNull(formal) && Objects.nonNull(confirm)) {
                formal.setRemark(confirm.getRemark());
                this.baseMapper.updateById(formal);
            }
        }
    }


    private boolean getStylistNodeCodeEnum(Long projectId) {
        boolean isConsistent = true;
        JhSystemEnum.stylistNodeCodeEnum[] nodeCodeEnums = JhSystemEnum.stylistNodeCodeEnum.values();
        for (JhSystemEnum.stylistNodeCodeEnum nodeCodeEnum : nodeCodeEnums) {
            LambdaQueryWrapper<ProjectNodeInfo> queryWrapper2 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, nodeCodeEnum.getNodeCodeEng());
            ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(queryWrapper2);

            LambdaQueryWrapper<ProjectNodeInfo> queryWrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, nodeCodeEnum.getNodeCodeDes());
            ProjectNodeInfo nodeInfoDes = projectNodeInfoRepository.selectOne(queryWrapper1);

            if (ObjectUtil.isNotEmpty(nodeInfo) && ObjectUtil.isNotEmpty(nodeInfoDes) && !nodeInfo.getRemark().equals(nodeInfoDes.getRemark())) {
                isConsistent = false;
                break;
            }
        }
        return isConsistent;
    }

    private boolean getBaseInfoNodeCodeEnum(Long projectId, String nodeCode) {
        boolean isConsistent = false;
        if (JhSystemEnum.twoNodeCodeEnum.NODE_ENG103.getKey().equals(nodeCode)) {
            JhSystemEnum.EngBaseInfo[] baseInfoEnums = JhSystemEnum.EngBaseInfo.values();
            for (JhSystemEnum.EngBaseInfo baseInfoEnum : baseInfoEnums) {
                LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, baseInfoEnum.getNodeCode());
                ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);

                if (
                        ObjectUtil.isNotEmpty(nodeInfo) &&
                                ObjectUtil.isNotEmpty(nodeInfo.getRemark()) &&
                                ("change".equals(nodeInfo.getRemark()) ||
                                        "not_effective".equals(nodeInfo.getRemark()) ||
                                        "reduce".equals(nodeInfo.getRemark()) ||
//                                        "increase".equals(nodeInfo.getRemark()) ||
                                        "inconsistent".equals(nodeInfo.getRemark()) ||
                                        "no".equals(nodeInfo.getRemark())
                                )
                ) {
                    isConsistent = true;
                    break;
                }
            }
        }
        if (JhSystemEnum.twoNodeCodeEnum.NODE_DES101.getKey().equals(nodeCode)) {
            JhSystemEnum.DesBaseInfo[] baseInfoEnums = JhSystemEnum.DesBaseInfo.values();
            for (JhSystemEnum.DesBaseInfo baseInfoEnum : baseInfoEnums) {
                LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, baseInfoEnum.getNodeCode());
                ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);

                if (
                        ObjectUtil.isNotEmpty(nodeInfo) &&
                                ObjectUtil.isNotEmpty(nodeInfo.getRemark()) &&
                                ("change".equals(nodeInfo.getRemark()) ||
                                        "not_effective".equals(nodeInfo.getRemark()) ||
                                        "reduce".equals(nodeInfo.getRemark()) ||
//                                        "increase".equals(nodeInfo.getRemark()) ||
                                        "inconsistent".equals(nodeInfo.getRemark()) ||
                                        "no".equals(nodeInfo.getRemark())
                                )
                ) {
                    isConsistent = true;
                    break;
                }
            }
        }
        return isConsistent;
    }

    private void sendLegalMail(ProjectGroup projectGroup,ProjectInfo projectInfo) {
        //查询质量问题有>=1 未整改情况，系统 就给当前项目的业主项目经理角色的干系人发送“法务函件”邮件
        Integer confirmation = qualityControlRepository.getByrectificationConfirmation(projectGroup.getProjectId());
        if (confirmation >= 1) {
            //存在未整改数据，获取当前项目的业主项目经理的干系人
            List<String> stringList = projectStakeholdersRepository.selectListByOwner(projectGroup.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.YJQYFZR.getKey());
            if (ObjectUtil.isNotEmpty(stringList)) {
                EmailVo emailVo = new EmailVo();
                emailVo.setContent("ACMS营建管理系统通知：【酒店ID："+projectInfo.getProjectNo()+"】的质量问题有 >=1 未整改情况。");
                emailVo.setSubject("法务函件");
                emailVo.setTos(stringList);
                emailService.sendEmail(emailVo, emailService.find());
            }
        }
    }

    @Override
    public String pushCompletionAcceptance(ProjectGroup projectGroup, ProjectInfoPushCompletionAcceptanceDTO
            acceptanceDTO) {
        //当前竣工验收已完成的话,往hlm系统推送数据
        if (ObjectUtils.isNotEmpty(projectGroup)) {
            acceptanceDTO = this.initializeCompletionAcceptance(projectGroup);
        } else {
            acceptanceDTO = ProjectInfoPushCompletionAcceptanceDTO.initialize();
        }


        //将对象（vo）转为JSONObject
        // 反序列化
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = null;
        try {
            jsonString = objectMapper.writeValueAsString(acceptanceDTO);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } finally {
        }
        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(acceptanceDTO);
        //将对象数据转为字符串
        String post = HttpUtils.post(hlmClientUtil.getCompletionAcceptance(), jsonString);
        com.alibaba.fastjson.JSONObject parseObject = com.alibaba.fastjson.JSONObject.parseObject(post);
        try{
            final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ObjectUtil.isNotEmpty(projectGroup.getProjectId()), ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00135014.getKey());
            final ProjectNodeInfo info = new ProjectNodeInfo();
            info.setRemark(post);
            projectNodeInfoRepository.update(info, eq);
        }catch(Exception e){}

        return post;
    }

    @Override
    public String pushBuildContacts(ProjectGroup projectGroup, ProjectInfoPushFranchiseUserDTO franchiseUserDTO) {
        //当前竣工验收已完成的话,往hlm系统推送数据
        UserDetails currentUser = SecurityUtils.getCurrentUser();
        if (ObjectUtils.isNotEmpty(projectGroup)) {
            franchiseUserDTO.setOperatorFlowerName(currentUser.getUsername());

            LambdaQueryWrapper wrapperEmail = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG103104.getKey());
            ProjectNodeInfo infosEmail = projectNodeInfoRepository.selectOne(wrapperEmail);
            franchiseUserDTO.setEmail(infosEmail.getRemark());

            LambdaQueryWrapper wrapperUsername = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG103102.getKey());
            ProjectNodeInfo infosUsername = projectNodeInfoRepository.selectOne(wrapperUsername);
            franchiseUserDTO.setUsername(infosUsername.getRemark());

            LambdaQueryWrapper wrapperMobile = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG103103.getKey());
            ProjectNodeInfo infosMobile = projectNodeInfoRepository.selectOne(wrapperMobile);
            franchiseUserDTO.setMobile(infosMobile.getRemark());
        } else {
            franchiseUserDTO = ProjectInfoPushFranchiseUserDTO.initialize();
        }


        //将对象转为JSONObject
        // 反序列化
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = null;
        try {
            jsonString = objectMapper.writeValueAsString(franchiseUserDTO);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } finally {
        }
        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(franchiseUserDTO);
        //将对象数据转为字符串
        String post = HttpUtils.post(hlmClientUtil.getBuildContacts(), jsonString);
        com.alibaba.fastjson.JSONObject parseObject = com.alibaba.fastjson.JSONObject.parseObject(post);


        return post;
    }

    @Override
    public ProjectNodeInfo getThreeRoleCode(Long projectId, String parentId, String roleCode) {
        util.initialize(getSubmeterProjectId(projectId));
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getParentId, parentId)
                .in(ProjectNodeInfo::getRoleCode, roleCode.split(","));
        ProjectNodeInfo infosMobile = projectNodeInfoRepository.selectOne(wrapper);
        return infosMobile;
    }

    @Override
    public List<ProjectNodeInfo> getConventionalInformation(Long projectId) {
        //动态获取 t_project_node_info 表名
        util.initialize(getSubmeterProjectId(projectId));
        //        获取当前用户id，并判断用户是否存在
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getIsDelete, 0)
                .in(ProjectNodeInfo::getNodeCode, JhSystemEnum.ConventionalInformation.getNodeCodeList())
                .orderByAsc(ProjectNodeInfo::getNodeIndex);
        List<ProjectNodeInfo> projectNodeInfoList = projectNodeInfoRepository.selectList(wrapper);
        for (ProjectNodeInfo i : projectNodeInfoList) {
            if (StringUtils.isNotEmpty(i.getRelationCode())) {
                LambdaQueryWrapper<ProjectNodeInfo> infoWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getIsDelete, 0)
                        .eq(ProjectNodeInfo::getNodeCode, i.getRelationCode())
                        .orderByAsc(ProjectNodeInfo::getNodeIndex);
                ProjectNodeInfo info = projectNodeInfoRepository.selectOne(infoWrapper);
                i.setRemark(info.getRemark());
                i.setNodeType(info.getNodeType());
                i.setNodeId(info.getNodeId());
            }
            if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(i.getNodeType())) {
//                s_tool_local_storage,查找文件地址
                LocalStorage storage = localStorageRepository.getByNodeId(String.valueOf(i.getNodeId()));
                if (storage != null) {
                    i.setRemark(String.valueOf(storage.getId()));
                }
            }
            if (JhSystemEnum.ConventionalInformation.getUnitCodeList().contains(i.getNodeCode())) {
                //t_supplier_info,查找设计单位
                SupplierInfo supplierInfo = supplierInfoRepository.selectById(i.getRemark());
                if (supplierInfo != null) {
                    i.setRemark(supplierInfo.getSupNameCn());
                }
            }
            if (JhSystemEnum.ConventionalInformation.getDesignCodeList().contains(i.getNodeCode())) {
                //t_supplier_pm,查找设计师
                SupplierPm supplierPm = supplierPmRepository.selectById(i.getRemark());
                if (supplierPm != null) {
                    i.setRemark(supplierPm.getPmName());
                }
            }
            if (JhSystemEnum.ConventionalInformation.getJobCodeList().contains(i.getNodeCode())) {
                //s_user_info,干系人
                if (StringUtils.isNotEmpty(i.getRemark())) {
                    User user = userRepository.getOne(Long.parseLong(i.getRemark()));
                    if (user != null) {
                        i.setRemark(user.getUsername() + "-" + user.getNickName());
                    }
                }
            }
        }
        return projectNodeInfoList;
    }

    @Override
    public List<ProjectNodeInfo> getDeepeningList(Long projectId) {
        //获取当前项目的深化列表
        List<ProjectGroup> deepeningList = projectGroupRepository.getDeepeningList(projectId);
        return null;
    }

    @Override
    public List<ProjectNodeInfo> documentManage(Long projectId) {
        //        获取当前用户id，并判断用户是否存在
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        Long submeterProjectId = getSubmeterProjectId(projectId);
        util.initialize(submeterProjectId);
        LambdaQueryWrapper<ProjectNodeInfo> nodeInfoQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, submeterProjectId)
                .in(ProjectNodeInfo::getNodeCode, JhSystemEnum.DocumentManage.getNodeCodeList());
        return this.baseMapper.selectList(nodeInfoQueryWrapper);
    }

    @Override
    public List<ProjectNodeInfo> getEngineeringRectificationIssues(Long projectId) {
        final List<ProjectNodeInfo> list = new ArrayList<>();
        //所有二级节点
        final LambdaQueryWrapper<ProjectGroup> wrapperTwo = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeLevel, "2");
        final List<ProjectGroup> groups = projectGroupRepository.selectList(wrapperTwo);
        List<String> templateCodes = groups.stream().map(ProjectGroup::getTemplateCode).distinct().collect(Collectors.toList());
        //质量管理
        final LambdaQueryWrapper<QualityControl> wrapperA = Wrappers.lambdaQuery(QualityControl.class).eq(QualityControl::getProjectId, projectId);
        final List<QualityControl> listA = qualityControlService.list(wrapperA);
        listA.stream().filter(q -> ObjectUtil.isEmpty(q.getIsNonStandardApproval())).forEach(q -> {
            final ProjectNodeInfo nodeInfo = new ProjectNodeInfo();
            setTaskNodes("质量管理", projectId, groups, templateCodes, q.getNodeCode(), q.getCreateTime(), q.getQualityControlId(), nodeInfo);
            list.add(nodeInfo);
        });
        //系统自检
        final LambdaQueryWrapper<ProjectSystemSelfInspection> wrapperB = Wrappers.lambdaQuery(ProjectSystemSelfInspection.class).eq(ProjectSystemSelfInspection::getProjectId, projectId)
                .eq(ProjectSystemSelfInspection::getConclusion, "false");
        final List<ProjectSystemSelfInspection> listB = projectSystemSelfInspectionService.list(wrapperB);
        listB.stream().forEach(q -> {
            final ProjectNodeInfo nodeInfo = new ProjectNodeInfo();
            setTaskNodes("竣工系统自检", projectId, groups, templateCodes, q.getNodeCode(), q.getCreateTime(), q.getSystemSelfInspectionId(), nodeInfo);
            list.add(nodeInfo);
        });
        //常规自检
        final LambdaQueryWrapper<QualityControl> wrapperC = Wrappers.lambdaQuery(QualityControl.class).eq(QualityControl::getProjectId, projectId);
        final List<QualityControl> listC = qualityControlService.list(wrapperC);
        listC.stream().filter(q -> ObjectUtil.isNotEmpty(q.getIsNonStandardApproval())).forEach(q -> {
            final ProjectNodeInfo nodeInfo = new ProjectNodeInfo();
            setTaskNodes("竣工常规自检", projectId, groups, templateCodes, q.getNodeCode(), q.getCreateTime(), q.getQualityControlId(), nodeInfo);
            list.add(nodeInfo);
        });
        //设计样板间验收
        final LambdaQueryWrapper<ProjectTableNodeInfo> wrapperD = Wrappers.lambdaQuery(ProjectTableNodeInfo.class)
                .eq(ProjectTableNodeInfo::getProjectId, projectId)
                .like(ProjectTableNodeInfo::getNodeCode, AtourSystemEnum.DesignNodeTow.DES00125.getKey());
        final List<ProjectTableNodeInfo> listD = projectTableNodeInfoRepository.selectList(wrapperD);
        listD.stream().filter(p -> !p.getNodeCode().contains("table") && "non_compliant".equals(getListRemark(p, 1, "$.tertiaryValue")))
                .forEach(q -> {
                    final ProjectNodeInfo nodeInfo = new ProjectNodeInfo();
                    setTaskNodes("设计样板间验收", projectId, groups, templateCodes, q.getNodeCode().substring(0, 12), q.getCreateTime(), q.getNodeId(), nodeInfo);
                    list.add(nodeInfo);
                });
        return list;
    }

    private void setTaskNodes(String name, Long projectId, List<ProjectGroup> groups, List<String> templateCodes, String nodeCode, Timestamp createTime, Long id, ProjectNodeInfo nodeInfo) {
        nodeInfo.setProblemType(name);
        final LambdaQueryWrapper<TemplateQueue> wrapper = Wrappers.lambdaQuery(TemplateQueue.class)
                .in(TemplateQueue::getTemplateCode, templateCodes)
                .eq(TemplateQueue::getNodeCode, nodeCode).last("limit 1");
        final TemplateQueue templateQueue = templateQueueRepository.selectOne(wrapper);
        if (ObjectUtil.isNotEmpty(templateQueue)) {
            groups.stream().filter(p -> templateQueue.getParentId().equals(p.getTemplateId())).forEach(e -> nodeInfo.setTaskNodes(e.getNodeName()));
        }
        nodeInfo.setCreationTime(String.valueOf(createTime));

        final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getProjectId, id)
                .eq(ProjectGroup::getNodeLevel, "2").last("limit 1");
        final ProjectGroup one = projectGroupRepository.selectOne(eq);
        nodeInfo.setRectificationStatus(Optional.ofNullable(one).map(ProjectGroup::getNodeStatus).orElse(null));
        nodeInfo.setProjectId(id);
        nodeInfo.setNodeCode(Optional.ofNullable(one).map(ProjectGroup::getNodeCode).orElse(null));
        final LambdaQueryWrapper<ProjectGroup> eqOne = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getProjectId, id)
                .eq(ProjectGroup::getNodeLevel, "1").last("limit 1");
        final ProjectGroup oneOne = projectGroupRepository.selectOne(eqOne);
        nodeInfo.setTemplateId(Optional.ofNullable(oneOne).map(ProjectGroup::getTemplateId).orElse(null));
    }

    @Override
    public Map<String, Object> getNodeIdByPdf(Long projectId) {
        final LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        final AtourSystemEnum.PdfBroughtOut[] values = AtourSystemEnum.PdfBroughtOut.values();
        util.initialize(projectId);
        Arrays.stream(values).forEach(value -> {
            final LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, value.getKey());
            final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);
            Optional.ofNullable(projectNodeInfo).ifPresent(node -> {
                final ProjectNodeInfo nodeInfo = new ProjectNodeInfo();
                nodeInfo.setNodeId(node.getNodeId());
                map.put(value.getTwoNodeCode(), nodeInfo);
            });
        });
        return map;
    }

    private ProjectInfoPushCompletionAcceptanceDTO initializeCompletionAcceptance(ProjectGroup projectGroup) {
        //推送竣工验收的数据
        ProjectInfoPushCompletionAcceptanceDTO acceptanceDTO = new ProjectInfoPushCompletionAcceptanceDTO();

        ProjectInfo byId = projectInfoService.getById(projectGroup.getProjectId());
        LambdaQueryWrapper<ProjectInfoExpansion> wrapper = Wrappers.lambdaQuery(ProjectInfoExpansion.class);
        wrapper.eq(ProjectInfoExpansion::getProjectId, byId.getProjectHlmId())
                .eq(ProjectInfoExpansion::getHotelId, byId.getProjectNo());
        ProjectInfoExpansion projectInfoExpansion = projectInfoExpansionRepository.selectOne(wrapper);
        acceptanceDTO.setProjectId(byId.getProjectHlmId());
        acceptanceDTO.setHotelId(projectInfoExpansion.getHotelId().intValue());
        //验收日期-验收日期【竣工验收-验收日期】
        LambdaQueryWrapper checkDateWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00133012.getKey());
        ProjectNodeInfo checkDateProjectNodeInfo = projectNodeInfoRepository.selectOne(checkDateWrapper);
        acceptanceDTO.setCheckDate(checkDateProjectNodeInfo.getRemark());

        //验收时间
        LambdaQueryWrapper checkTimeWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00133047.getKey());
        ProjectNodeInfo checkTimeNodeInfo = projectNodeInfoRepository.selectOne(checkTimeWrapper);
        final AtourSystemEnum.CheckTime[] values = AtourSystemEnum.CheckTime.values();
        for (int i = 0; i < values.length; i++) {
            if (ObjectUtil.isNotEmpty(checkTimeNodeInfo) && ObjectUtil.isNotEmpty(checkTimeNodeInfo.getRemark())
                    && checkTimeNodeInfo.getRemark().equals(values[i].getKey())) {
                acceptanceDTO.setCheckTime(values[i].getValue());
            }
        }


        //验收房间数-本次验收房量
        LambdaQueryWrapper checkRoomNumWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00129024.getKey());
        ProjectNodeInfo checkRoomNumNodeInfo = projectNodeInfoRepository.selectOne(checkRoomNumWrapper);
        if (ObjectUtil.isNotEmpty(checkRoomNumNodeInfo.getRemark())) {
            acceptanceDTO.setCheckRoomNum(ObjectUtil.isNotEmpty(checkRoomNumNodeInfo.getRemark().replaceAll("\\D", "")) ? Integer.valueOf(checkRoomNumNodeInfo.getRemark().replaceAll("\\D", "")) : 0);
        }


        //房间数-情况简介-总房量 #设计房间数
        LambdaQueryWrapper roomCountWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00129023.getKey());
        ProjectNodeInfo roomCountNodeInfo = projectNodeInfoRepository.selectOne(roomCountWrapper);
        if (ObjectUtil.isNotEmpty(roomCountNodeInfo.getRemark())) {
            acceptanceDTO.setRoomCount(ObjectUtil.isNotEmpty(roomCountNodeInfo.getRemark().replaceAll("\\D", "")) ? Integer.valueOf(roomCountNodeInfo.getRemark().replaceAll("\\D", "")) : 0);
        }

        //验收房间号，逗号分隔
        LambdaQueryWrapper checkRoomNoWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00129025.getKey());
        ProjectNodeInfo checkRoomNoInfo = projectNodeInfoRepository.selectOne(checkRoomNoWrapper);
        acceptanceDTO.setCheckRoomNo(ObjectUtil.isNotEmpty(checkRoomNoInfo.getRemark()) ? checkRoomNoInfo.getRemark().split(",") : null);

        //竣工验收人
        LambdaQueryWrapper checkPersonNoWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00129058.getKey());
        ProjectNodeInfo checkPerson = projectNodeInfoRepository.selectOne(checkPersonNoWrapper);
        if (ObjectUtil.isNotEmpty(checkPerson.getRemark())) {
            User one = userRepository.getOne(Long.valueOf(checkPerson.getRemark()));
            if (ObjectUtil.isNotEmpty(one)) {
                acceptanceDTO.setCompleteUserId(ObjectUtil.isNotEmpty(one.getOpenId()) ? one.getOpenId() : null);
                acceptanceDTO.setCheckUserName(ObjectUtil.isNotEmpty(one.getNickName()) ? one.getNickName() : null);
            }
        }


        //项目说明
        acceptanceDTO.setExplanInfo(this.initExplanInfo(projectGroup.getProjectId()));

        //TODO 风险管理 缺少 验收结果、验收照片列表、整改及复核信息
        acceptanceDTO.setRiskList(this.initRiskList(projectGroup.getProjectId()));

        //TODO 整改承诺书
        acceptanceDTO.setPromiseFiles(this.initPromiseFiles(projectGroup.getProjectId()));

        //TODO 整改清单   缺少项目信息列表
        acceptanceDTO.setRectifyInfo(this.initRectifyInfo(projectGroup.getProjectId()));

        //TODO 验收得分--动态列表
        acceptanceDTO.setCheckScoreList(this.initCheckScoreList(projectGroup.getProjectId()));

        //TODO 验收报告确认
        acceptanceDTO.setConfirmInfo(this.initConfirmInfo(projectGroup.getProjectId()));

        //TODO 通知人
        acceptanceDTO.setNotifyList(this.initNotifyList(projectGroup.getProjectId()));

        //TODO 验收报告确认
        acceptanceDTO.setReviewInfo(this.initReviewInfo(projectGroup.getProjectId()));

        ObjectMapper objectMapper = new ObjectMapper();
        // 将对象转换为JSON字符串
        String json = null;
        try {
            json = objectMapper.writeValueAsString(acceptanceDTO);
        } catch (JsonProcessingException e) {

        }
        System.out.println(json);
        return acceptanceDTO;

    }

    private List<ProjectInfoPushPromiseFilesDTO> initPromiseFiles(Long projectId) {
        //整改承诺书
        List<ProjectInfoPushPromiseFilesDTO> promiseFilesDTOS = new ArrayList<>();
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00135013.getKey());
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(eq);
        if (ObjectUtil.isNotEmpty(nodeInfo)) {
            final List<LocalStorage> byNodeId = localStorageRepository.findByNodeId(String.valueOf(nodeInfo.getNodeId()));
            if (ObjectUtil.isNotEmpty(byNodeId)) {
                for (LocalStorage node : byNodeId) {
                    final ProjectInfoPushPromiseFilesDTO projectInfoPushPromiseFilesDTO = new ProjectInfoPushPromiseFilesDTO();
                    projectInfoPushPromiseFilesDTO.setFileName(Optional.ofNullable(node.getName()).orElse(null));
                    projectInfoPushPromiseFilesDTO.setFileUrl(Optional.ofNullable(node.getPath()).orElse(null));
                    promiseFilesDTOS.add(projectInfoPushPromiseFilesDTO);
                }
            }
        }
        return promiseFilesDTOS;
    }

    private ProjectInfoPushReviewInfoDTO initReviewInfo(Long projectId) {
        //复核结果-竣工验收整改-复核结果
        ProjectInfoPushReviewInfoDTO reviewInfoDTO = new ProjectInfoPushReviewInfoDTO();

        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00135011.getKey());
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(eq);

        final LambdaQueryWrapper<ProjectNodeInfo> eqA = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00135012.getKey());
        final ProjectNodeInfo nodeInfoA = projectNodeInfoRepository.selectOne(eqA);

        final AtourSystemEnum.AcceptanceState[] values = AtourSystemEnum.AcceptanceState.values();
        for (int i = 0; i < values.length; i++) {
            if (ObjectUtil.isNotEmpty(nodeInfo) && ObjectUtil.isNotEmpty(nodeInfo.getRemark()) && values[i].getKey().equals(nodeInfo.getRemark())) {
                if(values[i].getValue().equals(3)){
                    reviewInfoDTO.setState(2);
                }else  if(values[i].getValue().equals(2)){
                    reviewInfoDTO.setState(3);
                }else{
                    reviewInfoDTO.setState(values[i].getValue());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(nodeInfoA) && ObjectUtil.isNotEmpty(nodeInfoA.getRemark())) {
            reviewInfoDTO.setRemark(nodeInfoA.getRemark());
        }
        return reviewInfoDTO;
    }

    private List<ProjectInfoPushNotifyListDTO> initNotifyList(Long projectId) {
        //通知人
        List<ProjectInfoPushNotifyListDTO> notifyListDTOS = new ArrayList<>();
        final LambdaQueryWrapper<ProjectStakeholders> eq = Wrappers.lambdaQuery(ProjectStakeholders.class)
                .eq(ProjectStakeholders::getProjectId, projectId);
        final List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(eq);
        /*项目经理   营建区域负责人  开发分区负责人  交付中心-工程负责人   设计负责人   交付中心-技术负责人  交付中心-共享支持  交付中心-部门负责人*/
        final List<ProjectStakeholders> collect = projectStakeholders.stream().filter(p -> JhSystemEnum.JobEnum.GCJL.getKey().equals(p.getRoleCode()) ||
                AtourSystemEnum.engineeringRoleCodeEnum.YJQYFZR.getKey().equals(p.getRoleCode()) || AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey().equals(p.getRoleCode()) ||
                AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey().equals(p.getRoleCode()) || JhSystemEnum.JobEnum.SJFZR.getKey().equals(p.getRoleCode()) ||
                AtourSystemEnum.engineeringRoleCodeEnum.JFZXBMFZR.getKey().equals(p.getRoleCode()) ||
                AtourSystemEnum.engineeringRoleCodeEnum.JFZXJSFZR.getKey().equals(p.getRoleCode()) || AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey().equals(p.getRoleCode())).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(collect)) {
            final List<Long> list = collect.stream().map(ProjectStakeholders::getUserId).collect(Collectors.toList());
            final List<User> all = userRepository.findByUserIds(list);
            for (ProjectStakeholders projectStakeholder : collect) {
                if (ObjectUtil.isNotEmpty(projectStakeholder.getUserId())) {
                    final List<User> users = all.stream().filter(a -> projectStakeholder.getUserId().equals(a.getId())).limit(1).collect(Collectors.toList());
                    final User one = Optional.ofNullable(users).flatMap(u -> Optional.ofNullable(u.get(0))).orElseGet(User::new);
                    if (ObjectUtil.isNotEmpty(one.getOpenId()) && ObjectUtil.isNotEmpty(one.getNickName())) {
                        final ProjectInfoPushNotifyListDTO projectInfoPushNotifyListDTO = new ProjectInfoPushNotifyListDTO();
                        projectInfoPushNotifyListDTO.setEmployeeId(one.getOpenId());
                        projectInfoPushNotifyListDTO.setFlowerName(one.getNickName());
                        notifyListDTOS.add(projectInfoPushNotifyListDTO);
                    }
                }
            }
        }

        return notifyListDTOS;
    }

    private ProjectInfoPushConfirmInfoDTO initConfirmInfo(Long projectId) {
        //验收报告确认
        ProjectInfoPushConfirmInfoDTO confirmInfoDTO = new ProjectInfoPushConfirmInfoDTO();
        confirmInfoDTO.setState(1);
        final LambdaQueryWrapper<ProjectStakeholders> eq = Wrappers.lambdaQuery(ProjectStakeholders.class)
                .eq(ProjectStakeholders::getProjectId, projectId)
                .eq(ProjectStakeholders::getRoleCode, AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey())
                .last("limit 1");
        final ProjectStakeholders stakeholders = projectStakeholdersRepository.selectOne(eq);
        if (ObjectUtil.isNotEmpty(stakeholders) && ObjectUtil.isNotEmpty(stakeholders.getUserId())) {
            User one = userRepository.getOne(Long.valueOf(stakeholders.getUserId()));
            if (ObjectUtil.isNotEmpty(one)) {
                confirmInfoDTO.setEmployeeId(String.valueOf(one.getOpenId()));
            }
        }
        return confirmInfoDTO;
    }

    private List<ProjectInfoPushCheckScoreListDTO> initCheckScoreList(Long projectId) {
        //验收得分  t_project_completion_receipt   项目竣工验收单
        List<ProjectInfoPushCheckScoreListDTO> checkScoreList = new ArrayList<>();
        final AtourSystemEnum.SummaryAcceptanceForms[] values = AtourSystemEnum.SummaryAcceptanceForms.values();
        final LambdaQueryWrapper<ProjectCompletionReceipt> wrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, projectId)
                .eq(ProjectCompletionReceipt::getIsDelete, 0)
                .and(queryWrapper1 -> queryWrapper1.ne(ProjectCompletionReceipt::getIsRealPhotosTaken, 1).or().isNull(ProjectCompletionReceipt::getIsRealPhotosTaken));
        final List<ProjectCompletionReceipt> checkScores = projectCompletionReceiptRepository.selectList(wrapper);

        double totalPoints = 0.0;
        for (int i = 0; i < values.length; i++) {
            //获取分值
            final List<Double> listScore = new ArrayList<>();
            //获取得分
            final List<Double> list = new ArrayList<>();
            int finalI = i;
            //获取分值
            for (ProjectCompletionReceipt checkScore : checkScores) {
                String[] split = values[finalI].getKey().split(",");
                if (Arrays.asList(split).contains(checkScore.getTotalItem())) {
                    //获取分值
                    if (ObjectUtil.isNotEmpty(checkScore.getScore())) {
                        listScore.add(Double.valueOf(checkScore.getScore()));
                    } else {
                        listScore.add(Double.valueOf(0));
                    }
                    //获取得分
                    if (ObjectUtil.isNotEmpty(checkScore.getGetScore())) {
                        list.add(Double.valueOf(checkScore.getGetScore()));
                    } else {
                        list.add(Double.valueOf(0));
                    }
                }
            }
//            checkScores.stream().filter(cs -> values[finalI].getKey().equals(cs.getTotalItem()))
//                    .map(ProjectCompletionReceipt::getScore).forEach(score -> {
//                if (ObjectUtil.isNotEmpty(score)) {
//                    listScore.add(Double.valueOf(score));
//                } else {
//                    list.add(Double.valueOf(0));
//                }
//            });
            double sumScore = listScore.stream().mapToDouble(Double::doubleValue).sum();

            //获取得分
//            checkScores.stream().filter(cs -> values[finalI].getKey().equals(cs.getTotalItem()))
//                    .map(ProjectCompletionReceipt::getGetScore).forEach(score -> {
//                if (ObjectUtil.isNotEmpty(score)) {
//                    list.add(Double.valueOf(score));
//                } else {
//                    list.add(Double.valueOf(0));
//                }
//            });
            double sum = list.stream().mapToDouble(Double::doubleValue).sum();
            final ProjectInfoPushCheckScoreListDTO pushCheckScoreListDTO = new ProjectInfoPushCheckScoreListDTO();
            //条目
            pushCheckScoreListDTO.setItem(values[i].getValue());
            //总分数
            totalPoints = totalPoints + sumScore;
            pushCheckScoreListDTO.setAllScoreData(sumScore);
            //分数-得分
            pushCheckScoreListDTO.setScoreData(sum);

            checkScoreList.add(pushCheckScoreListDTO);
        }

        for (ProjectInfoPushCheckScoreListDTO checkScoreListDTO : checkScoreList) {
            //总分数
            checkScoreListDTO.setAllScore((int) Math.round(checkScoreListDTO.getAllScoreData() / totalPoints * 100d));
            //分项得分-最终得分（百分制）
            if (ObjectUtil.isNotEmpty(checkScoreListDTO.getScoreData()) && 0 != checkScoreListDTO.getScoreData()) {
                double percentageSystem = checkScoreListDTO.getScoreData() / totalPoints * 100d;
                checkScoreListDTO.setScore((int) Math.round(percentageSystem));
            } else if (ObjectUtil.isEmpty(checkScoreListDTO.getScoreData()) || checkScoreListDTO.getScoreData() == 0d) {
                checkScoreListDTO.setScore(0);
            }
        }
        return checkScoreList;
    }

    private ProjectInfoPushRectifyInfoDTO initRectifyInfo(Long projectId) {
        // 反馈截止时间	eng-00133045
        ProjectInfoPushRectifyInfoDTO rectifyInfoDTO = new ProjectInfoPushRectifyInfoDTO();

        LambdaQueryWrapper feedbackDateWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.completionAcceptanceEnum.ENG00141008.getKey());
        ProjectNodeInfo feedbackDateNodeInfo = projectNodeInfoRepository.selectOne(feedbackDateWrapper);
        rectifyInfoDTO.setFeedbackDate(feedbackDateNodeInfo.getRemark());
        List<ProjectInfoPushRectifyListDTO> rectifyList = new ArrayList<>();
        final LambdaQueryWrapper<ProjectCompletionReceipt> unqualified = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, projectId)
                .eq(ProjectCompletionReceipt::getAcceptance, "unqualified");
        //找不合格的项
        final List<ProjectCompletionReceipt> infos = projectCompletionReceiptRepository.selectList(unqualified);
        if (ObjectUtil.isNotEmpty(infos)) {
            final List<DictDetail> all = dictDetailRepository.findAll();
            //验收 照片/ppt 列表；整改及复核信息
            for (ProjectCompletionReceipt info : infos) {
                ProjectInfoPushRectifyListDTO listDTO = new ProjectInfoPushRectifyListDTO();
                //整改项目
                if (ObjectUtil.isNotEmpty(info.getSubItemContent())) {
                    listDTO.setContent(info.getSubItemContent());
                }
//                Optional.ofNullable(info.getAcceptance()).filter(a -> AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getKey()
//                        .equals(a)).ifPresent(i -> {
////                    final List<DictDetail> collect = all.stream().filter(a -> info.getProject().equals(a.getValue()) && Long.valueOf(185).equals(a.getId())).limit(1).collect(Collectors.toList());
////                    final DictDetail detailByValue = ObjectUtil.isNotEmpty(collect) ? collect.get(0) : null;
//
////                    Optional.ofNullable(detailByValue).ifPresent(dbv -> Optional.ofNullable(dbv.getLabel())
////                            .ifPresent(listDTO::setContent));
//                });
                //验收 照片/ppt 列表
                List<ProjectInfoPushCheckImgListDTO> checkImgS = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(info.getCheckAttachments())) {
                    for (String s : info.getCheckAttachments().split(",")) {
                        if (ObjectUtil.isNotEmpty(s)) {
                            final LocalStorage one = localStorageRepository.selectById(Long.valueOf(s));
                            if (ObjectUtil.isNotEmpty(one)) {
                                final ProjectInfoPushCheckImgListDTO imgDTO = new ProjectInfoPushCheckImgListDTO();
                                imgDTO.setFileName(ObjectUtil.isNotEmpty(one.getName())?one.getName():null);
                                imgDTO.setFileUrl(ObjectUtil.isNotEmpty(one.getPath())?one.getPath():null);
                                imgDTO.setIsNew(true);
                                checkImgS.add(imgDTO);
                            }
                        }
                    }
                }
                listDTO.setCheckImgList(checkImgS);

                List<ProjectInfoPushRectifyImgListDTO> rectifyImgList = new ArrayList<>();
                final ProjectInfoPushRectifyReviewListDTO rectifyReview = new ProjectInfoPushRectifyReviewListDTO();
                List<ProjectInfoPushRectifyReviewListDTO> rectifyReviews = new ArrayList<>();
                //整改及复核信息
                setRectificationAttachments(info, rectifyImgList, rectifyReview);
                //整改日期
                setRectificationDate(info, rectifyReview, rectifyReviews);
                rectifyReviews.add(rectifyReview);
                listDTO.setRectifyReviewList(rectifyReviews);
                rectifyList.add(listDTO);
            }
//            infos.stream().forEach(info -> {
//                //整改项目
//                if (ObjectUtil.isNotEmpty(info.getSubItemContent())) {
//                    listDTO.setContent(info.getSubItemContent());
//                }
////                Optional.ofNullable(info.getAcceptance()).filter(a -> AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getKey()
////                        .equals(a)).ifPresent(i -> {
//////                    final List<DictDetail> collect = all.stream().filter(a -> info.getProject().equals(a.getValue()) && Long.valueOf(185).equals(a.getId())).limit(1).collect(Collectors.toList());
//////                    final DictDetail detailByValue = ObjectUtil.isNotEmpty(collect) ? collect.get(0) : null;
////
//////                    Optional.ofNullable(detailByValue).ifPresent(dbv -> Optional.ofNullable(dbv.getLabel())
//////                            .ifPresent(listDTO::setContent));
////                });
//                //验收 照片/ppt 列表
//                List<ProjectInfoPushCheckImgListDTO> checkImgS = new ArrayList<>();
//                Optional.ofNullable(info.getCheckAttachments()).filter(ObjectUtil::isNotEmpty).ifPresent(ca -> {
//                    for (String s : ca.split(",")) {
//                        final LocalStorage one = localStorageRepository.getOne(Long.valueOf(s));
//                        Optional.ofNullable(one).ifPresent(o -> {
//                            final ProjectInfoPushCheckImgListDTO imgDTO = new ProjectInfoPushCheckImgListDTO();
//                            imgDTO.setFileName(Optional.ofNullable(o.getName()).orElse(null));
//                            imgDTO.setFileUrl(Optional.ofNullable(o.getPath()).orElse(null));
//                            imgDTO.setIsNew(true);
//                            checkImgS.add(imgDTO);
//                        });
//                    }
//                });
//                listDTO.setCheckImgList(checkImgS);
//
//                List<ProjectInfoPushRectifyImgListDTO> rectifyImgList = new ArrayList<>();
//                final ProjectInfoPushRectifyReviewListDTO rectifyReview = new ProjectInfoPushRectifyReviewListDTO();
//                List<ProjectInfoPushRectifyReviewListDTO> rectifyReviews = new ArrayList<>();
//                //整改及复核信息
//                setRectificationAttachments(info, rectifyImgList, rectifyReview);
//                //整改日期
//                setRectificationDate(info, rectifyReview, rectifyReviews);
//                rectifyReviews.add(rectifyReview);
//                listDTO.setRectifyReviewList(rectifyReviews);
//                rectifyList.add(listDTO);
//            });
        }
        rectifyInfoDTO.setRectifyList(rectifyList);
        return rectifyInfoDTO;
    }

    private List<ProjectInfoPushRiskListDTO> initRiskList(Long projectId) {
        List<ProjectInfoPushRiskListDTO> riskListDTOS = new ArrayList<>();

        //获取联动测试数据
        ProjectCompletionReceiptQueryCriteria criteria = new ProjectCompletionReceiptQueryCriteria();
        criteria.setProjectId(projectId);
        ProjectCompletionReceiptSummaryDto acceptanceSummary = projectCompletionReceiptService.getAcceptanceSummary(criteria);
        //联动测试
        ProjectInfoPushRiskListDTO linkageDTO = new ProjectInfoPushRiskListDTO();
        linkageDTO.setType(3);

        ProjectCompletionReceiptSingleQualificationDto qualificationDto = acceptanceSummary.getSingleQualificationDto();
        if (ObjectUtil.isNotEmpty(qualificationDto)) {
            String linkageTesting = ObjectUtil.isNotEmpty(qualificationDto.getLinkageTesting()) ? qualificationDto.getLinkageTesting() : "0";
            if (ObjectUtil.isNotEmpty(linkageTesting)) {
                linkageDTO.setRejectDeductScore("合格".equals(linkageTesting) ? 100 : 0);
            }
        }

        linkageDTO.setCheckItem("联动测试");
        getJsonCheckAndAccept(projectId, linkageDTO, "linkage_testing");
        if (ObjectUtil.isNotEmpty(linkageDTO)) {
            final ProjectInfoPushCheckResultDTO putWaterResultDTO9 = new ProjectInfoPushCheckResultDTO();
            if (ObjectUtil.isNotEmpty(linkageDTO.getRejectDeductScore()) && 100 == linkageDTO.getRejectDeductScore()) {
                putWaterResultDTO9.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
                putWaterResultDTO9.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
                putWaterResultDTO9.setDeductScore(0);
            } else {
                putWaterResultDTO9.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getValue());
                putWaterResultDTO9.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getSpec());
                putWaterResultDTO9.setDeductScore(0);
            }
            linkageDTO.setCheckResult(putWaterResultDTO9);
        }
        riskListDTOS.add(linkageDTO);

        //消防广播
        ProjectInfoPushRiskListDTO broadcastDTO = new ProjectInfoPushRiskListDTO();
        broadcastDTO.setType(4);
//        ProjectCompletionReceiptSingleQualificationDto qualificationDto = acceptanceSummary.getSingleQualificationDto();
        if (ObjectUtil.isNotEmpty(qualificationDto)) {
            String fireBroadcasting = ObjectUtil.isNotEmpty(qualificationDto.getFireBroadcasting()) ? qualificationDto.getFireBroadcasting() : "0";
            if (ObjectUtil.isNotEmpty(fireBroadcasting)) {
                linkageDTO.setRejectDeductScore("合格".equals(fireBroadcasting) ? 100 : 0);
            }
        }
        broadcastDTO.setCheckItem("消防广播");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO8 = new ProjectInfoPushCheckResultDTO();
        if (ObjectUtil.isNotEmpty(linkageDTO.getRejectDeductScore()) && 100 == linkageDTO.getRejectDeductScore()) {
            putWaterResultDTO8.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
            putWaterResultDTO8.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
            putWaterResultDTO8.setDeductScore(0);
        } else {
            putWaterResultDTO8.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getValue());
            putWaterResultDTO8.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getSpec());
            putWaterResultDTO8.setDeductScore(0);
        }
        broadcastDTO.setCheckResult(putWaterResultDTO8);
//        getJsonCheckAndAccept(projectId, broadcastDTO, "fire_broadcasting");
        riskListDTOS.add(broadcastDTO);

        //安装标准
        ProjectInfoPushRiskListDTO installationSystemDTO = new ProjectInfoPushRiskListDTO();
        installationSystemDTO.setType(2);
        installationSystemDTO.setCheckItem("安装标准");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO7 = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO7.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO7.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO7.setDeductScore(0);
        installationSystemDTO.setCheckResult(putWaterResultDTO7);
//        getJsonCheckAndAccept(projectId, installationSystemDTO, "installation_standards");

        riskListDTOS.add(installationSystemDTO);


        //消防水系统
        ProjectInfoPushRiskListDTO waterSystemDTO = new ProjectInfoPushRiskListDTO();
        waterSystemDTO.setType(5);

        ProjectCompletionReceiptSingleQualificationDto singleQualificationDto = acceptanceSummary.getSingleQualificationDto();
        if (ObjectUtil.isNotEmpty(singleQualificationDto)) {
            String fireWaterSystem = ObjectUtil.isNotEmpty(singleQualificationDto.getFireWaterSystem()) ? singleQualificationDto.getFireWaterSystem() : "0";
            if (ObjectUtil.isNotEmpty(fireWaterSystem)) {
                linkageDTO.setRejectDeductScore("合格".equals(fireWaterSystem) ? 100 : 0);
            }
        }
        waterSystemDTO.setCheckItem("消防水系统");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO6 = new ProjectInfoPushCheckResultDTO();
        if (ObjectUtil.isNotEmpty(linkageDTO.getRejectDeductScore()) && 100 == linkageDTO.getRejectDeductScore()) {
            putWaterResultDTO6.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
            putWaterResultDTO6.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
            putWaterResultDTO6.setDeductScore(1);
        } else {
            putWaterResultDTO6.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getValue());
            putWaterResultDTO6.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING2.getSpec());
            putWaterResultDTO6.setDeductScore(2);
        }
        waterSystemDTO.setCheckResult(putWaterResultDTO6);

//        getJsonCheckAndAccept(projectId, waterSystemDTO, "fire_water_system");
        riskListDTOS.add(waterSystemDTO);

        //消防喷淋压力
        ProjectInfoPushRiskListDTO putWaterSystemDTO = new ProjectInfoPushRiskListDTO();
        putWaterSystemDTO.setType(6);
        putWaterSystemDTO.setCheckItem("消防喷淋压力");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO.setDeductScore(0);
        putWaterSystemDTO.setCheckResult(putWaterResultDTO);
        riskListDTOS.add(putWaterSystemDTO);

        //消防疏散
        ProjectInfoPushRiskListDTO evacuateSystemDTO = new ProjectInfoPushRiskListDTO();
        evacuateSystemDTO.setType(7);
        evacuateSystemDTO.setCheckItem("消防疏散");

        final ProjectInfoPushCheckResultDTO putWaterResultDTO1 = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO1.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO1.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO1.setDeductScore(0);
        evacuateSystemDTO.setCheckResult(putWaterResultDTO1);

//        getJsonCheckAndAccept(projectId, evacuateSystemDTO, "evacuate");
        riskListDTOS.add(evacuateSystemDTO);

        //大堂区域
        ProjectInfoPushRiskListDTO areaSystemDTO = new ProjectInfoPushRiskListDTO();
        areaSystemDTO.setType(1);
        areaSystemDTO.setCheckItem("大堂区域");
        final ProjectInfoPushCheckResultDTO areaResultDTO = new ProjectInfoPushCheckResultDTO();
        areaResultDTO.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        areaResultDTO.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        areaResultDTO.setDeductScore(0);
        areaSystemDTO.setCheckResult(areaResultDTO);
        riskListDTOS.add(areaSystemDTO);

        //电视投屏
        ProjectInfoPushRiskListDTO tvSystemDTO = new ProjectInfoPushRiskListDTO();
        tvSystemDTO.setType(8);
        tvSystemDTO.setCheckItem("电视投屏");
        final ProjectInfoPushCheckResultDTO tvResultDTO = new ProjectInfoPushCheckResultDTO();
        tvResultDTO.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        tvResultDTO.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        tvResultDTO.setDeductScore(0);
        tvSystemDTO.setCheckResult(tvResultDTO);
        riskListDTOS.add(tvSystemDTO);

        //水系统及锅炉房
        ProjectInfoPushRiskListDTO boilerSystemDTO = new ProjectInfoPushRiskListDTO();
        boilerSystemDTO.setType(9);
        boilerSystemDTO.setCheckItem("水系统及锅炉房");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO2 = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO2.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO2.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO2.setDeductScore(0);
        boilerSystemDTO.setCheckResult(putWaterResultDTO2);
//        getJsonCheckAndAccept(projectId, boilerSystemDTO, "water_system_boiler_room");

        riskListDTOS.add(boilerSystemDTO);

        //供暖
        ProjectInfoPushRiskListDTO warmSystemDTO = new ProjectInfoPushRiskListDTO();
        warmSystemDTO.setType(10);
        warmSystemDTO.setCheckItem("供暖");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO3 = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO3.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO3.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO3.setDeductScore(0);
        warmSystemDTO.setCheckResult(putWaterResultDTO3);
//        getJsonCheckAndAccept(projectId, warmSystemDTO, "heating");
        riskListDTOS.add(boilerSystemDTO);

        //空调
        ProjectInfoPushRiskListDTO airSystemDTO = new ProjectInfoPushRiskListDTO();
        airSystemDTO.setType(11);
        airSystemDTO.setCheckItem("空调");
        final ProjectInfoPushCheckResultDTO putWaterResultDTO4 = new ProjectInfoPushCheckResultDTO();
        putWaterResultDTO4.setState(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getValue());
        putWaterResultDTO4.setRemark(AtourSystemEnum.CompletionAcceptanceScoring.LINKAGE_TESTING1.getSpec());
        putWaterResultDTO4.setDeductScore(0);
        airSystemDTO.setCheckResult(putWaterResultDTO4);
//        getJsonCheckAndAccept(projectId, airSystemDTO, "air_conditioning");
        riskListDTOS.add(airSystemDTO);

        return riskListDTOS;
    }

    private ProjectInfoPushRiskListDTO getListDTO(Long projectId, ProjectInfoPushRiskListDTO linkageDTO, String
            nodeCode, Integer type, String checkItem) {
        ProjectInfoPushRiskListDTO threePartyConferenceDTO = new ProjectInfoPushRiskListDTO();
        LambdaQueryWrapper threePartyConferenceWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode);
        ProjectNodeInfo threePartyConferenceNodeInfo = projectNodeInfoRepository.selectOne(threePartyConferenceWrapper);
        threePartyConferenceDTO.setType(type);
        if (ObjectUtil.isNotEmpty(threePartyConferenceNodeInfo.getRemark())) {
            try {
                linkageDTO.setRejectDeductScore(NumberFormat.getInstance().parse(threePartyConferenceNodeInfo.getRemark()).intValue());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        threePartyConferenceDTO.setCheckItem(checkItem);
        return threePartyConferenceDTO;
    }

    /**
     * 竣工验收的json传数据
     */
    private void getJsonCheckAndAccept(Long projectId, ProjectInfoPushRiskListDTO linkageDTO, String content) {
        final LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, projectId)
                .eq(ProjectCompletionReceipt::getContent, content)
                .last("limit 1");
        final ProjectCompletionReceipt receipt = projectCompletionReceiptRepository.selectOne(queryWrapper);
        Optional.ofNullable(receipt).ifPresent(r -> {
            Optional.ofNullable(r.getAcceptance()).filter(ObjectUtil::isNotEmpty).ifPresent(a -> {
                final ProjectInfoPushCheckResultDTO checkResultDTO = new ProjectInfoPushCheckResultDTO();
                final AtourSystemEnum.CompletionAcceptanceScoring[] values = AtourSystemEnum.CompletionAcceptanceScoring.values();
                for (int i = 0; i < values.length; i++) {
                    if (values[i].getKey().equals(a)) {
                        checkResultDTO.setDeductScore(values[i].getValue());
                        checkResultDTO.setRemark(values[i].getSpec());
                    }
                }
                checkResultDTO.setState((int) Math.round(Double.valueOf(r.getGetScore())));
                linkageDTO.setCheckResult(checkResultDTO);
            });
            //验收照片列表
            setCheckImg(linkageDTO, r);
            //整改及复核信息
            List<ProjectInfoPushRectifyImgListDTO> rectifyImgList = new ArrayList<>();
            final ProjectInfoPushRectifyReviewListDTO rectifyReview = new ProjectInfoPushRectifyReviewListDTO();
            List<ProjectInfoPushRectifyReviewListDTO> rectifyReviews = new ArrayList<>();
            setRectificationAttachments(r, rectifyImgList, rectifyReview);
            //整改日期
            setRectificationDate(r, rectifyReview, rectifyReviews);
            linkageDTO.setRectifyReviewList(rectifyReviews);
        });
    }

    private void setRectificationDate(ProjectCompletionReceipt r, ProjectInfoPushRectifyReviewListDTO
            rectifyReview, List<ProjectInfoPushRectifyReviewListDTO> rectifyReviews) {
        //整改日期
        Timestamp rectificationDate = r.getRectificationDate();
        if (ObjectUtil.isNotEmpty(rectificationDate)) {
            LocalDateTime localDateTime = rectificationDate.toLocalDateTime();
            LocalDate localDate = localDateTime.toLocalDate();
            rectifyReview.setPromiseRectifyDate(ObjectUtil.isNotEmpty(localDate) ? localDate + "" : null);
            rectifyReviews.add(rectifyReview);
        }
//        Optional.ofNullable(r.getRectificationDate()).filter(ObjectUtil::isNotEmpty).ifPresent(rd -> {
//            if (ObjectUtil.isNotEmpty(rd.toLocalDateTime())) {
//                LocalDateTime localDateTime = rd.toLocalDateTime();
//                LocalDate localDate = localDateTime.toLocalDate();
//                rectifyReview.setPromiseRectifyDate(String.valueOf(localDate));
//            }
//            rectifyReviews.add(rectifyReview);
//        });
    }

    private void setRectificationAttachments(ProjectCompletionReceipt
                                                     r, List<ProjectInfoPushRectifyImgListDTO> rectifyImgList, ProjectInfoPushRectifyReviewListDTO rectifyReview) {
        Optional.ofNullable(r.getRectificationAttachments()).filter(ObjectUtil::isNotEmpty).ifPresent(ra -> {
            final String[] split = ra.split(",");
            for (String s : split) {
                final LocalStorage one = localStorageRepository.selectById(Long.valueOf(s));
                Optional.ofNullable(one).ifPresent(o -> {
                    final ProjectInfoPushRectifyImgListDTO imgListDTO = new ProjectInfoPushRectifyImgListDTO();
                    imgListDTO.setFileName(Optional.ofNullable(o.getName()).orElse(null));
                    imgListDTO.setFileUrl(Optional.ofNullable(o.getPath()).orElse(null));
                    imgListDTO.setIsNew(true);
                    rectifyImgList.add(imgListDTO);
                });
            }
            rectifyReview.setRectifyImgList(rectifyImgList);

        });
        if (ObjectUtil.isNotEmpty(r)) {

            //承若整改日期    - 承诺整改日期
            if (ObjectUtil.isNotEmpty(r.getCommitmentTime())) {
                rectifyReview.setPromiseRectifyDate(r.getCommitmentTime() + "");
            }
            //复核状态  1:合格 2:不合格 3: 有条件合格【默认：合格】     - 整改清单审批状态   0不合格、1合格、2承诺项
            if ("0".equals(r.getApprovalStatus())) {
                rectifyReview.setReviewState(2);
            } else if ("2".equals(r.getApprovalStatus())) {
                rectifyReview.setReviewState(3);
            } else {
                rectifyReview.setReviewState(1);
            }
            //复核状态   - 审批意见
            rectifyReview.setReviewRemark(r.getApprovalRemarks());
            //整改备注  -   整改备注
            DictDetail byDictId = dictDetailRepository.findDictDetailByValueA(r.getRectificationInstructions(), 231L);
            if (ObjectUtil.isNotEmpty(byDictId)) {
                rectifyReview.setRectifyRemark(byDictId.getLabel());
            }

        }

    }

    private void setCheckImg(ProjectInfoPushRiskListDTO linkageDTO, ProjectCompletionReceipt r) {
        Optional.ofNullable(r.getCheckAttachments()).filter(ObjectUtil::isNotEmpty).ifPresent(ca -> {
            List<ProjectInfoPushCheckImgListDTO> checkImgS = new ArrayList<>();
            final String[] split = ca.split(",");
            for (String s : split) {
                final LocalStorage one = localStorageRepository.selectById(Long.valueOf(s));
                Optional.ofNullable(one).ifPresent(o -> {
                    final ProjectInfoPushCheckImgListDTO imgDTO = new ProjectInfoPushCheckImgListDTO();
                    imgDTO.setFileName(Optional.ofNullable(o.getName()).orElse(null));
                    imgDTO.setFileUrl(Optional.ofNullable(o.getPath()).orElse(null));
                    imgDTO.setIsNew(true);
                    checkImgS.add(imgDTO);
                });
            }
            linkageDTO.setCheckImgList(checkImgS);
        });
    }

    private String getListRemark(ProjectTableNodeInfo nodeInfo, int key, String path) {
        if (ObjectUtil.isNotEmpty(nodeInfo)) {
            String substring = nodeInfo.getRemark();
            if (!nodeInfo.getRemark().startsWith("[")) {
                substring = nodeInfo.getRemark().substring(1);
            }
            JSONArray jsonArray = new JSONArray(substring);
            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(key);
            final String read = JSONPath.read(jsonObject.toString(), path).toString();
            return read;
        }
        return null;
    }

    private String getListRemarkDto(ProjectTableNodeInfoDto nodeInfo, int key, String path) {
        JSONArray jsonArray = new JSONArray(nodeInfo.getRemark());
        JSONObject jsonObject = jsonArray.getJSONObject(key);
        final String read = JSONPath.read(jsonObject.toString(), path).toString();
        return read;
    }

    private String getListRemarkA(ProjectNodeInfo nodeInfo, int key, String path) {
        JSONArray jsonArray = new JSONArray(nodeInfo.getRemark());
        JSONObject jsonObject = jsonArray.getJSONObject(key);
        final String read = JSONPath.read(jsonObject.toString(), path).toString();
        return read;
    }

    private ProjectInfoPushExplanInfoDTO initExplanInfo(Long projectId) {
        ProjectInfoPushExplanInfoDTO explanInfoDTO = new ProjectInfoPushExplanInfoDTO();
        ProjectCompletionReceiptDescriptionQueryCriteria criteria = new ProjectCompletionReceiptDescriptionQueryCriteria();
        criteria.setProjectId(projectId);
        ProjectCompletionReceiptDescription byProjectId = projectCompletionReceiptDescriptionService.getByProjectId(criteria);
        if (ObjectUtil.isNotEmpty(byProjectId)) {
            //设施管理
            explanInfoDTO.setFacilityManage(byProjectId.getFacilitiesManagement());
            //物业划分
            explanInfoDTO.setProperty(byProjectId.getPropertyDivision());
            //酒店建筑
            explanInfoDTO.setHotelBuild(byProjectId.getHotelBuilding());
            //风险管理
            explanInfoDTO.setRiskManage(byProjectId.getRiskManagement());
        }
        explanInfoDTO.setApprovalVersion("4");
        return explanInfoDTO;
    }

//    private void updateDecMaking(ProjectGroup projectGroupDto) {
//        //节点修改状态【营建对接启动】
//        if ((projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00101.getKey()))
//                && projectGroupDto.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00101.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//        }
//
//        //节点修改状态【筹建启动会】
//        if ((projectGroupDto.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00103.getKey()))
//                && projectGroupDto.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00201.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00401.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00501.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00601.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00701.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//        }
//
//        //节点修改状态【装饰勘测报告】
//        if ((projectGroupDto.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00103.getKey())
//                && projectGroupDto.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey()))) {
//            final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
//                    .eq(ProjectGroup::getNodeCode, AtourSystemEnum.DesignNodeTow.DES00165.getKey());
//            final ProjectGroup group = projectGroupRepository.selectOne(eq);
//            if (group.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
//                updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00301.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            }
//        }
//
//        //节点修改状态【机电勘测报告】
//        if ((projectGroupDto.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00165.getKey())
//                && projectGroupDto.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey()))) {
//            final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
//                    .eq(ProjectGroup::getNodeCode, AtourSystemEnum.DesignNodeTow.DES00103.getKey());
//            final ProjectGroup group = projectGroupRepository.selectOne(eq);
//            if (group.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())) {
//                updateDecMakingStatus(AtourSystemEnum.DecisionMakingTwo.DEC00301.getKey(), Long.valueOf(projectGroupDto.getProjectId()));
//            }
//        }
//    }
//    private void updateDecMakingStatus(String nodeCode, Long projectId) {
//        LambdaQueryWrapper<ProjectGroup> like = Wrappers.lambdaQuery(ProjectGroup.class)
//                .like(ProjectGroup::getNodeCode, nodeCode.substring(0, 7))
//                .eq(ProjectGroup::getProjectId, projectId);
//        ProjectGroup group = new ProjectGroup();
//        group.setIsNotTask(true);
//        group.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
//        group.setNodeIsfin(true);
//        projectGroupRepository.update(group, like);
//    }

    /**
     * 判断节点的remark是否为空，是否有附件 空：true；不空：false
     */
    private Boolean isRemarkByCode(String NodeCode, String projectId) {
        util.initialize(getSubmeterProjectId(Long.valueOf(projectId)));
        LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getNodeCode, NodeCode)
                .eq(ProjectNodeInfo::getProjectId, projectId);

        ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
        if (ObjectUtil.isNotEmpty(info)) {
            if (ObjectUtil.isNotEmpty(info.getRemark()) && JhSystemEnum.NodeType.FILE_UPLOAD.getValue() != info.getNodeType()) {
                return false;
            }
            if (info.getRemark() == null && info.getNodeType().equals(JhSystemEnum.NodeType.FILE_UPLOAD.getValue())) {
                final LambdaQueryWrapper<ProjectNodeInfo> eqL = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getNodeCode, NodeCode)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .last("limit 1");
                final ProjectNodeInfo infoL = projectNodeInfoRepository.selectOne(eqL);
                if (ObjectUtil.isNotEmpty(infoL)) {
                    final List<LocalStorage> localStorages = localStorageRepository.findByNodeId(infoL.getNodeId().toString());
                    //该文件必须是在本地上传的文件，不是由其他节点带出的
                    if (ObjectUtil.isEmpty(localStorages)) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取节点状态
     */
    private String getProjectGroupStatus(String nodeCode, Long projectId) {
        LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getNodeCode, nodeCode)
                .eq(ProjectGroup::getProjectId, projectId);
        ProjectGroup one = projectGroupRepository.selectOne(eq);
        return one.getNodeStatus();
    }

    /**
     * 按顺序获取动态表子节点
     */
    private List<ProjectNodeInfo> playDynamticConditionList(String nodeCode) {
        LambdaQueryWrapper<TemplateTableGroup> eq = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getRelevancyNodeCode, nodeCode)
                .orderByAsc(TemplateTableGroup::getRelevancyNodeCode);
        List<TemplateTableGroup> templateTableGroups = templateTableGroupRepository.selectList(eq);
        final List<Long> arrayList = new ArrayList<>();
        for (TemplateTableGroup templateTableGroup : templateTableGroups) {
            arrayList.add(templateTableGroup.getTemplateTableGroupId());
        }
        LambdaQueryWrapper<ProjectNodeInfo> like = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .in(ProjectNodeInfo::getTemplateId, arrayList)
                .orderByAsc(ProjectNodeInfo::getNodeCode);
        return projectNodeInfoRepository.selectList(like);
    }

    private void upUnitProgressSampleRoomAcceptance(ProjectGroup projectGroup) {
        //样板间隐蔽验收节点完成后，把   样板间隐蔽验收的总/分包管理下的单位进展赋值给样板间验收的单位进展
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_ENG109.getKey());
        List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(wrapper);
        for (ProjectNodeInfo info : infos) {
            JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum uniteEnum = JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum.getNodeCode(info.getNodeCode());
            if (ObjectUtils.isNotEmpty(uniteEnum)) {
                LambdaUpdateWrapper updateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                        .eq(ProjectNodeInfo::getNodeCode, uniteEnum.getUniteKey())
                        .set(ProjectNodeInfo::getRemark, info.getRemark());
                this.update(updateWrapper);
            }
        }
    }

    private void upUnitProgressSampleAcceptance(ProjectGroup projectGroup) {
        //正式开工节点完成后，把   正式开工的总/分包管理下的单位进展 赋值 给样板间隐蔽验收的单位进展
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.twoNodeCodeEnum.NODE_ENG107.getKey());
        List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(wrapper);
        for (ProjectNodeInfo info : infos) {
            JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum uniteEnum = JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum.getNodeCode(info.getNodeCode());
            if (ObjectUtils.isNotEmpty(uniteEnum)) {
                LambdaUpdateWrapper updateWrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                        .eq(ProjectNodeInfo::getNodeCode, uniteEnum.getUniteKey())
                        .set(ProjectNodeInfo::getRemark, info.getRemark());
                this.update(updateWrapper);
            }
        }
    }

    @Override
    public void updateGroupStatusBack(ProjectGroup projectGroup) {
        //审批拒绝修改二级任务为未提交
        projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey());
        projectGroup.setNodeIsfin(false);
        projectGroup.setIsSubmit(Boolean.FALSE);
        projectGroupService.update(projectGroup);
        if (projectGroup.getOrderId() == null) {
            projectTaskService.generateTodoTask(projectGroup);
        }
//        if (projectGroup.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey())) {
//            //当前二级为 深化方案的话，修改当前审图记录的状态为  审批拒绝； 深化列表中的审图状态，目前不根据深化任务来进行判断
//            this.upDeepeningPlanStatus(projectGroup.getProjectId().toString(),projectGroup.getNodeCode(),AtourSystemEnum.ReviewDrawingStatus.APPROVAL_REJECTION.getKey());
//        }

    }

    @Override
    public List<ProjectNodeInfoDto> getMeasureAreaValue(Long projectId, Long parentId) {
        return projectNodeInfoRepository.getMeasureAreaValue(projectId, parentId);
    }

    @Override
    public ProjectNodeInfoDto submitNoApprove(ProjectNodeInfoDto projectNodeInfoD) {
        LambdaQueryWrapper<ProjectGroup> gLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != projectNodeInfoD.getProjectGroupId(), ProjectGroup::getProjectGroupId, projectNodeInfoD.getProjectGroupId());
        ProjectGroup group = projectGroupRepository.selectOne(gLambdaQueryWrapper);
        ProjectGroupDto projectGroupDto = projectGroupMapper.toDto(group);
        if (null == projectGroupDto || null == projectGroupDto.getNodeLevel() || projectGroupDto.getNodeLevel() != 2) {
            throw new BadRequestException("传递的nodeId 节点参数值 不正确，不是2级节点");
        }
        String projectId = projectNodeInfoD.getProjectId();
        if (projectNodeInfoD.getList() != null && projectNodeInfoD.getList().size() != 0) {
            List<ProjectNodeInfoDto> listDto = projectNodeInfoD.getList();
            List<ProjectNodeInfo> list = projectNodeInfoMapper.toEntity(listDto);
            updateData(list, Boolean.TRUE);
        }
        updateGroupStatusNext(projectGroupMapper.toEntity(projectGroupDto));
        Boolean isUpdateNodeStatus = true;
        if (isUpdateNodeStatus) {
            //更新已完成字段
            //判断紧前任务是否都已完成
            String frontWbsConfig = projectGroupDto.getFrontWbsConfig();
            if (frontWbsConfig != null) {
                List<FrontWbsConfigDto> frontList = new ArrayList<>();
                JSONArray jsonArray = new JSONArray(frontWbsConfig);
                Boolean statusFlag = Boolean.TRUE;
                String unFinish = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
                    JSONObject object = jsonArray.getJSONObject(i);
                    String type = object.getStr("type");
                    String wbs = object.getStr("wbs");
                    if ("FS".equals(type)) {
                        //查找当前节点的紧前是否已完成
                        LambdaQueryWrapper<ProjectGroup> lastLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getProjectId, projectId).eq(ProjectGroup::getNodeCode, wbs);
                        ProjectGroup one = projectGroupService.getOne(lastLambdaQueryWrapper);
                        if (one == null || (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(one.getNodeStatus()))) {
                            statusFlag = Boolean.FALSE;
                            unFinish = unFinish + one.getNodeName() + ",";
                        }

                    }

                }
                if (!statusFlag) {
                    unFinish = unFinish.substring(0, unFinish.length() - 1);
                    throw new BadRequestException(unFinish + "节点尚未完成，请先处理前置节点");
                }

            }


            if (null != projectGroupDto) {
                LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectGroupId, projectGroupDto.getProjectGroupId())
                        .set(ProjectGroup::getNodeIsfin, Boolean.TRUE)
                        .set(ProjectGroup::getIsSubmit, Boolean.TRUE);
                projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
                //更新一次后即可
                isUpdateNodeStatus = false;
            }

        }
        ProjectGroup projectGroup = projectGroupMapper.toEntity(projectGroupDto);
        projectTaskService.finishTask(projectGroup);


        return projectNodeInfoD;
    }

    @Override
    public Map<String, Object> getQueryTimeNode(String projectId) {
        final Map<String, Object> map = new LinkedHashMap<>();
        final ArrayList<String> list = new ArrayList<>();
        //获取项目里的模块进度
        List<ProjectScheduleDto> queryTimeNode = projectGroupRepository.getQueryTimeNode(projectId);
        //更改计划事件的工期和，从plan_event_info；project_planevent_relation；planevent_nodecode_relation中获取
        final List<TPlanEventInfo> planEventInfos = planEventInfoRepository.getPlanEventByRelation();
        //获取projectGroup里的一级任务
        final LambdaQueryWrapper<ProjectGroup> wrapperOne = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeLevel, 1);
        final List<ProjectGroup> projectGroupsOne = projectGroupRepository.selectList(wrapperOne);
        for (ProjectScheduleDto projectScheduleDto : queryTimeNode) {
//            if (ObjectUtils.isNotEmpty(projectScheduleDto.getActualEndDate()) && ObjectUtils.isNotEmpty(projectScheduleDto.getPlanStartDate()) &&
//                    ObjectUtils.isNotEmpty(projectScheduleDto.getPlanEndDate())) {
//                projectScheduleDto.setSchedule(
//                        TimePercentUtil.getTimePercent(projectScheduleDto.getActualEndDate(), projectScheduleDto.getPlanStartDate(), projectScheduleDto.getPlanEndDate()) + ""
//                );
//            } else {
//                projectScheduleDto.setSchedule("0");
//            }
            if (projectScheduleDto.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey()) ||
                    projectScheduleDto.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "(")) {
                //修改九宫格节点状态；进行中/已完成/逾期
                //updateBoardStatus(projectScheduleDto, "工程节点", list);
                projectScheduleDto.setTemplateName("工程节点");
                setTotalDay("eng", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            } else if (projectScheduleDto.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey()) ||
                    projectScheduleDto.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.PUBLIC_AREA_DESIGN.getKey() + "(")) {
                //updateBoardStatus(projectScheduleDto, "公区设计节点", list);
                projectScheduleDto.setTemplateName("公区设计节点");
                setTotalDay("pad", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            } else if (projectScheduleDto.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DESIGN.getKey()) ||
                    projectScheduleDto.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.DESIGN.getKey() + "(")) {
                //updateBoardStatus(projectScheduleDto, "设计节点", list);
                projectScheduleDto.setTemplateName("客房设计节点");
                setTotalDay("des", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            } else if (projectScheduleDto.getTemplateCode().equals(AtourSystemEnum.AdditionalTemplatesEnum.DECISION_MAKING.getKey())) {
                //updateBoardStatus(projectScheduleDto, "决策信息", list);
                projectScheduleDto.setTemplateName("决策信息");
                setTotalDay("dec", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            } else if (projectScheduleDto.getTemplateCode().equals(AtourSystemEnum.AdditionalTemplatesEnum.DEEPENING_PLAN.getKey())) {
                //updateBoardStatus(projectScheduleDto, "设计深化方案", list);
                projectScheduleDto.setTemplateName("设计深化方案");
                setTotalDay("dep", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            } else if (projectScheduleDto.getTemplateCode().equals(AtourSystemEnum.AdditionalTemplatesEnum.CONSTRUCTION_LOG.getKey())) {
                //updateBoardStatus(projectScheduleDto, "施工日志", list);
                projectScheduleDto.setTemplateName("施工日志");
                setTotalDay("col", planEventInfos, projectScheduleDto);
                setActualEndDate(projectScheduleDto.getTemplateCode(), projectGroupsOne, projectScheduleDto);
            }

        }
        String mes = null;
        //if (ObjectUtil.isNotEmpty(list)) {
        //    mes = String.join(", ", list) + "未配置工期";
        //}
        map.put("list", queryTimeNode);
        map.put("mes", mes);
        //queryTimeNode.stream().filter(q -> "100".equals(q.getSchedule())).collect(Collectors.toList()).forEach(p -> p.setPhaseStatus(AtourSystemEnum.ScheduleStatus.COMPLETED.getSpec()));
        return map;
    }

    private void setActualEndDate(String templateCode, List<ProjectGroup> projectGroupsOne, ProjectScheduleDto projectScheduleDto) {
        projectGroupsOne.stream().filter(projectGroup -> templateCode.equals(projectGroup.getTemplateCode()))
                .map(ProjectGroup::getNodeIndex).max(Double::compareTo).ifPresent(max -> {
            projectGroupsOne.stream().filter(one -> max.equals(one.getNodeIndex()))
                    .forEach(one -> projectScheduleDto.setActualEndDate(one.getActualEndDate()));
        });
    }

    private void setTotalDay(String nodeCode, List<TPlanEventInfo> planEventInfos, ProjectScheduleDto projectScheduleDto) {
        final List<Integer> duration = new ArrayList<>();
        planEventInfos.stream().filter(planEventInfo -> ObjectUtil.isNotEmpty(planEventInfo.getNodeCode())
                && ObjectUtil.isNotEmpty(planEventInfo.getPlanEventDuration())
                && planEventInfo.getNodeCode().contains(nodeCode)).map(TPlanEventInfo::getPlanEventDuration)
                .forEach(duration::add);
        final int sum = duration.stream().mapToInt(Integer::intValue).sum();
        projectScheduleDto.setTotalDay(sum);
    }

    private void updateBoardStatus(ProjectScheduleDto projectScheduleDto, String
            templateName, ArrayList<String> list) {
        projectScheduleDto.setTemplateName(templateName);
        final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class).gt(ProjectGroup::getDelayDay, 0)
                .eq(ProjectGroup::getTemplateCode, projectScheduleDto.getTemplateCode());
        final List<ProjectGroup> groups = projectGroupRepository.selectList(eq);
        if (ObjectUtil.isEmpty(projectScheduleDto.getPlanEndDate()) || ObjectUtil.isEmpty(projectScheduleDto.getPlanStartDate())) {
            list.add(templateName);
            projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.UNOPENED.getSpec());
        } else {
            //逾期
            //if (groups.size() > 0 && !"100".equals(projectScheduleDto.getSchedule())) {
            //    projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.OVERDUE.getSpec());
            //}
            //进行中
            if (!"100".equals(projectScheduleDto.getSchedule()) && new Date().compareTo(projectScheduleDto.getPlanEndDate()) < 0) {
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.ONGOING.getSpec());
            }
            //未开启
            else if (projectScheduleDto.getPlanStartDate().compareTo(new Date()) > 0 && "0".equals(projectScheduleDto.getSchedule())) {
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.UNOPENED.getSpec());
            }
            //已完成
            else if ("100".equals(projectScheduleDto.getSchedule()) && ObjectUtil.isNotEmpty(projectScheduleDto.getActualEndDate())
                    && projectScheduleDto.getActualEndDate().equals(projectScheduleDto.getPlanEndDate())) {
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.COMPLETED.getSpec());

            }
            //提前完成
            else if ("100".equals(projectScheduleDto.getSchedule()) && ObjectUtil.isNotEmpty(projectScheduleDto.getActualEndDate())
                    && projectScheduleDto.getActualEndDate().compareTo(projectScheduleDto.getPlanEndDate()) < 0) {
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.ADVANCE_COMPLETED.getSpec());

            }
            //逾期完成
            else if ("100".equals(projectScheduleDto.getSchedule()) && ObjectUtil.isNotEmpty(projectScheduleDto.getActualEndDate())
                    && groups.size() > 0 && projectScheduleDto.getActualEndDate().compareTo(projectScheduleDto.getPlanEndDate()) > 0) {
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.OVERDUE_COMPLETED.getSpec());
            } else {
                //默认
                projectScheduleDto.setPhaseStatus(AtourSystemEnum.ScheduleStatus.UNOPENED.getSpec());
            }
        }
    }

    //    //当前二级节点作为不确定方，查询是否已经配置了联合任务、是否满足联合条件，满足的话，修改当前二级节点的状态为不确定状态、联合二级为未完成状态
//    private void upProjectInfoJointTaskByNodeCode(ProjectNodeInfoDto projectNodeInfoDto) {
//        //当前二级的三级数据按照nodeCode转成map数据，方便判断是否满足联合条件
//        Map<String, ProjectNodeInfoDto> projectNodeInfoDtoMap = null;
//        try {
//            projectNodeInfoDtoMap = projectNodeInfoDto.getList().stream().collect(Collectors.toMap(ProjectNodeInfoDto::getNodeCode, e -> e));
//        } catch (Exception e) {
//            throw new BadRequestException("当前二级任务中，存在相同的 三级nodeCode,请联系管理员进行处理");
//        }
//        //查询二级节点作为不确定方是否已经配置了联合任务和条件
//        List<ProjectJointTaskOnfigurationDto> projectNodeCode = projectJointTaskOnfigurationService.getByProjectNodeCode(projectNodeInfoDto);
//        if (ObjectUtils.isNotEmpty(projectNodeCode)) {
//            Boolean isIndeterminacy = false;
//            for (ProjectJointTaskOnfigurationDto taskOnfigurationDto : projectNodeCode) {
//                if (ObjectUtil.isNotEmpty(taskOnfigurationDto.getFieldRequirementNodeCode())) {
//                    ProjectNodeInfoDto infoDto = projectNodeInfoDtoMap.get(taskOnfigurationDto.getFieldRequirementNodeCode());
//                    if (ObjectUtil.isNotEmpty(infoDto) && ObjectUtil.isNotEmpty(infoDto.getNodeType()) && infoDto.getNodeType().equals(JhSystemEnum.NodeType.FORM_SELECT.getValue())) {
//                        if (taskOnfigurationDto.getConditionalDemand().contains(infoDto.getRemark())) {
//                            //满足联合条件，修改联合二级为未完成状态
//                            LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                                    .eq(ProjectGroup::getProjectId, Long.valueOf(projectNodeInfoDto.getProjectId()))
//                                    .eq(ProjectGroup::getNodeCode, taskOnfigurationDto.getRelevanceNodeCode())
//                                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                            projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
//                            isIndeterminacy = true;
//                        }
//                    } else if (ObjectUtil.isNotEmpty(infoDto) && ObjectUtil.isNotEmpty(infoDto.getNodeType()) && infoDto.getNodeType().equals(JhSystemEnum.NodeType.INPUT_SHOW_BLOCK.getValue())) {
//                        String remark = JhSystemEnum.inputBoxEnum.NULL.getKey();
//                        if (ObjectUtils.isNotEmpty(infoDto.getRemark())) {
//                            remark = JhSystemEnum.inputBoxEnum.NOTNULL.getKey();
//                        }
//                        if (taskOnfigurationDto.getConditionalDemand().contains(remark)) {
//                            //满足联合条件，修改联合二级为未完成状态
//                            LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                                    .eq(ProjectGroup::getProjectId, Long.valueOf(projectNodeInfoDto.getProjectId()))
//                                    .eq(ProjectGroup::getNodeCode, taskOnfigurationDto.getRelevanceNodeCode())
//                                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                            projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
//                            isIndeterminacy = true;
//                        }
//                    }
//
//                }
//            }
//            if (isIndeterminacy) {
//                //修改二级为不确认状态
//                LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                        .eq(ProjectGroup::getProjectId, Long.valueOf(projectNodeInfoDto.getProjectId()))
//                        .eq(ProjectGroup::getNodeCode, projectNodeInfoDto.getNodeCode())
//                        .set(ProjectGroup::getNodeIsfin, Boolean.FALSE)
//                        .set(ProjectGroup::getIsSubmit, Boolean.FALSE)
//                        .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS3.getKey())
//                        .set(ProjectGroup::getActualEndDate, null);
//                projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
//
//            }
//        }
//    }
    private void saveVisaFiling(ProjectInfo projectInfo, ProjectGroup projectGroup) {
        Long initiatUser = SecurityUtils.getCurrentUserId();
        List<String> nodeCodeEnum = JhSystemEnum.VisaDilingNodeCodeEnum.getVisaDilingNodeCodeEnum(projectGroup.getNodeCode());
        //查询对应二级任务的报备内容
        LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                .in(ProjectNodeInfo::getNodeCode, nodeCodeEnum)
                .eq(ProjectNodeInfo::getRemark, JhSystemEnum.confirmedRegionalLeaderEnum.REPORTING.getKey());
        List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(infos)) {
            for (ProjectNodeInfo info : infos) {
                //查询对应报备三级的内容
                String nodeCodesEnum = JhSystemEnum.VisaDilingNodeCodeEnum.getVisaDilingNodeCodesEnum(info.getNodeCode());
                LambdaQueryWrapper<ProjectNodeInfo> queryWrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectGroup.getProjectId())
                        .in(ProjectNodeInfo::getNodeCode, nodeCodesEnum.split(","));
                List<ProjectNodeInfo> infos1 = projectNodeInfoRepository.selectList(queryWrapper1);

                String presentationOndition = "";
                //发送消息通知
                for (ProjectNodeInfo projectNodeInfo : infos1) {
                    String byNodeCodeRemark = "";
                    if (projectNodeInfo.getNodeType().equals(JhSystemEnum.NodeType.FORM_SELECT.getValue())
                            && ObjectUtil.isNotEmpty(projectNodeInfo.getRemark())) {
                        byNodeCodeRemark = dictDetailRepository.getIdByNodeCodeRemark(projectNodeInfo.getRemark());
                    } else {
                        byNodeCodeRemark = projectNodeInfo.getRemark();
                    }
                    presentationOndition += (projectNodeInfo.getNodeName() + "：" + byNodeCodeRemark + "；");
                }

                projectGroup.setFieldNodeName(presentationOndition);
                projectNoticeService.generateNotice(projectGroup.getProjectGroupId(), projectGroup, JhSystemEnum.MessageTemplate.MB1000009, initiatUser);

            }
            // 发送短信和企微
            //提醒信息
            String messageText = "";
            StringBuffer messageTitle = new StringBuffer("");
            messageTitle.append("【营建新系统】待办提醒：");
            sendMyMessage(projectGroup.getProjectId(),projectGroup.getProjectGroupId().toString(),
                    projectGroup.getNodeCode(), messageTitle, AtourSystemEnum.engineeringRoleCodeEnum.GCJL.getKey(),
                    "02");
        }
    }

    private void upProjectGroupExpand(Long projectId, ProjectGroup projectGroup, Long brandId) {
        //根据当前二级任务的code，查询审批通过的审图数据
        LambdaQueryWrapper<ProjectGroupExpand> queryWrapper = Wrappers.lambdaQuery(ProjectGroupExpand.class);
        queryWrapper.eq(ProjectGroupExpand::getProjectId, projectId)
                .eq(ProjectGroupExpand::getGroupNodeCode, projectGroup.getNodeCode())
                .eq(ProjectGroupExpand::getDrawingStatus, AtourSystemEnum.ReviewDrawingStatus.APPROVED.getKey());
        List<ProjectGroupExpand> list = projectGroupExpandService.list(queryWrapper);
        if (ObjectUtils.isNotEmpty(list)) {
            List<String> relationCode = list.stream().map(ProjectGroupExpand::getExpandCode).collect(Collectors.toList());

            //轻居品牌    深化方案里，客房区装饰画（喷绘方案）- 不上传
            if (brandId.toString().equals(AtourSystemEnum.BrandCodeEnum.BRAND3.getKey())
                    && relationCode.contains(AtourSystemEnum.DeepeningSchemeEnum.DES0011502601.getKey())) {
                relationCode.remove(AtourSystemEnum.DeepeningSchemeEnum.DES0011502601.getKey());
            }

            if (ObjectUtil.isNotEmpty(relationCode)) {
                //根据审图通过的数据，查询当前二级审图通过的数据影响哪些深化审图数据，并修改状态
                for (String s : relationCode) {
                    LambdaQueryWrapper<ProjectGroupExpand> wrapper = Wrappers.lambdaQuery(ProjectGroupExpand.class);
                    wrapper.eq(ProjectGroupExpand::getProjectId, projectId)
                            .like(ProjectGroupExpand::getRelationCode, s);
                    List<ProjectGroupExpand> groupExpands = projectGroupExpandService.list(wrapper);
                    if (ObjectUtils.isNotEmpty(groupExpands)) {
                        groupExpands.forEach(a -> {
                            if (a != null) {
                                a.setDrawingStatus(AtourSystemEnum.ReviewDrawingStatus.STARTING_FROM_THE_END.getKey());
                            }
                        });
                        projectGroupExpandService.updateBatchById(groupExpands);

                        //根据被影响的深化数据来查询深化二级任务并修改状态为 【未完成】
                        List<String> collect = groupExpands.stream().map(ProjectGroupExpand::getGroupNodeCode).collect(Collectors.toList());
                        LambdaUpdateWrapper<ProjectGroup> updateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
                                .eq(ProjectGroup::getProjectId, projectId)
                                .in(ProjectGroup::getNodeCode, collect)
                                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                        projectGroupService.update(updateWrapper);
                    }
                }

            }
        }
    }

    @Override
    public void run() {
        System.out.println("已立项");
    }

    private void saveEngineeringRectificationIssues(ProjectGroup projectGroup, ProjectInfo projectInfo, String
            templateCode) {
        //根据用户选的conditions查询对应的模板
        ProjectInfoDto infoDto = new ProjectInfoDto();
        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
        infoDto.setProjectId(projectInfo.getProjectId());
        List<TemplateCollection> templateCollections = null;
        try {
            templateCollections = projectInfoService.findTemplateByConditionCode(infoDto, null);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        TemplateCollection templateCollection = templateCollections.stream().filter(s -> templateCode.equals(s.getTemplateCode())).findFirst().get();
        if (ObjectUtil.isEmpty(templateCollection)) {
            throw new BadRequestException("当前暂无模版信息，请联系超级管理员");
        }

        if (AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey().equals(templateCode)) {
            //查询质量管理需要立模板的数据
            this.addEngineeringRectificationIssues(projectGroup, projectInfo, templateCollection);
        } else if (AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getKey().equals(templateCode)) {
            //竣工系统自检模板需要立模板的数据     获取质量管理表中的数据；eng-00127134
            this.addNewTemplateBySSI(projectGroup, projectInfo, templateCollection);
        } else if (AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getKey().equals(templateCode)) {
            //竣工常规项目自检模板需要立模板的数据   获取质量管理表中的数据，加上【是否需要非标审批】即为常规项目自检；eng-00127213
            this.addNewTemplateByRSI(projectGroup, projectInfo, templateCollection);
        } else if (AtourSystemEnum.AdditionalTemplatesEnum.DESIGN_SAMPLE_ROOM.getKey().equals(templateCode)) {
            //样板间验收需要立模板的数据
            this.addNewTemplateByTable(projectGroup, projectInfo, templateCollection);
        }
    }

    private void addEngineeringRectificationIssues(ProjectGroup projectGroup, ProjectInfo projectInfo, TemplateCollection templateCollection) {
        //提交的时候，根据nodecode查询当前节点的质量管理数据，存在则发起模版
        QualityControlQueryCriteria criteria = new QualityControlQueryCriteria();
        criteria.setProjectId(projectGroup.getProjectId());
        criteria.setNodeCode(projectGroup.getNodeCode());
        criteria.setIsProjectGroupId(1);
        //查询质量管理需要立模板的数据
        List<QualityControlDto> controlDtos = qualityControlService.queryList(criteria);
        if (ObjectUtil.isNotEmpty(controlDtos)) {
            ExecutorService executor = Executors.newFixedThreadPool(controlDtos.size());
            if (ObjectUtil.isNotEmpty(templateCollection)) {
                //发起【工程问题整改模板】
                ProjectInfoExpansion developmentSystem = projectInfoServiceImpl.getDevelopmentSystem(projectInfo.getProjectHlmId());
                controlDtos.forEach(controlDto -> {
                    //查询当前质量问题是否存在模版
                    executor.execute(new Runnable() {
                        @Override
                        public void run() {
                            ProjectInfoDto infoDto = new ProjectInfoDto();
                            BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
                            infoDto.setQualityControlDto(controlDto);
                            try {
                                projectInfoService.saveProject(templateCollection, projectGroup.getProjectId(), "new", infoDto
                                        , null, projectInfo, projectInfo.getProjectType(), null, developmentSystem);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                });
            }
            executor.shutdown();
        }
    }

    private void addNewTemplateByTable(ProjectGroup projectGroup, ProjectInfo projectInfo, TemplateCollection templateCollection) {
        final LambdaQueryWrapper<ProjectTableNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectTableNodeInfo.class)
                .eq(ProjectTableNodeInfo::getProjectId, projectInfo.getProjectId())
                .eq(ProjectTableNodeInfo::getParentId, projectGroup.getParentId());
        final List<ProjectTableNodeInfo> tableNodeInfos = projectTableNodeInfoRepository.selectList(wrapper);
        final List<ProjectTableNodeInfoDto> projectTableNodeInfos = projectTableNodeInfoMapper.toDto(tableNodeInfos);
        final List<ProjectTableNodeInfo> collect = tableNodeInfos.stream()
                .filter(p -> "non_compliant".equals(getListRemark(p, 1, "$.tertiaryValue"))).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(collect)) {
            ExecutorService executor = Executors.newFixedThreadPool(collect.size());
            projectTableNodeInfos.stream().filter(p -> "non_compliant".equals(getListRemarkDto(p, 1, "$.tertiaryValue")))
                    .forEach(tableNodeInfo -> {
                        //        LambdaQueryWrapper<TemplateCollection> templateCollectionLambdaQueryWrapper = Wrappers
                        //                .lambdaQuery(TemplateCollection.class)
                        //                .eq(TemplateCollection::getTemplateCode, templateCode);
                        //        TemplateCollection templateCollection = templateConditionRepository.selectOne(templateCollectionLambdaQueryWrapper);
                        //查询当前质量问题是否存在模版
                        executor.execute(new Runnable() {
                            @Override
                            public void run() {
                                ProjectInfoDto infoDto = new ProjectInfoDto();
                                BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
                                infoDto.setProjectTableNodeInfo(tableNodeInfo);
                                try {
                                    projectInfoService.saveProject(templateCollection, projectGroup.getProjectId(), "new", infoDto
                                            , null, projectInfo, projectInfo.getProjectType(), null, null);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        });
                    });
            executor.shutdown();
        }

    }

    private void addNewTemplateByRSI(ProjectGroup projectGroup, ProjectInfo projectInfo, TemplateCollection templateCollection) {
        final LambdaQueryWrapper<QualityControl> wrapper = Wrappers.lambdaQuery(QualityControl.class)
                .eq(QualityControl::getProjectId, projectInfo.getProjectId())
                .eq(QualityControl::getNodeCode, AtourSystemEnum.CompletionSelfInspectionPro.ENG00127213.getKey());
        final List<QualityControlDto> qualityControls = qualityControlMapper.toDto(qualityControlRepository.selectList(wrapper));
        if (ObjectUtil.isNotEmpty(qualityControls)) {
            ExecutorService executor = Executors.newFixedThreadPool(qualityControls.size());
            qualityControls.stream().forEach(routineSelfInspection -> {
                //查询当前是否存在模版
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        ProjectInfoDto infoDto = new ProjectInfoDto();
                        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
                        infoDto.setRoutineSelfInspection(routineSelfInspection);
                        try {
                            projectInfoService.saveProject(templateCollection, projectGroup.getProjectId(), "new", infoDto
                                    , null, projectInfo, projectInfo.getProjectType(), null, null);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            });
            executor.shutdown();
        }

    }

    private void addNewTemplateBySSI(ProjectGroup projectGroup, ProjectInfo projectInfo, TemplateCollection templateCollection) {
        final LambdaQueryWrapper<ProjectSystemSelfInspection> wrapper = Wrappers.lambdaQuery(ProjectSystemSelfInspection.class)
                .eq(ProjectSystemSelfInspection::getProjectId, projectInfo.getProjectId())
                .eq(ProjectSystemSelfInspection::getNodeCode, AtourSystemEnum.CompletionSelfInspectionSys.ENG00127134.getKey())
                .eq(ProjectSystemSelfInspection::getConclusion, "false");
        final List<ProjectSystemSelfInspectionDto> projectSystemSelfInspections = projectSystemSelfInspectionMapper.toDto(projectSystemSelfInspectionRepository.selectList(wrapper));
        if (ObjectUtil.isNotEmpty(projectSystemSelfInspections)) {
            ExecutorService executor = Executors.newFixedThreadPool(projectSystemSelfInspections.size());
            projectSystemSelfInspections.stream().forEach(projectSystemSelfInspection -> {
                //        LambdaQueryWrapper<TemplateCollection> templateCollectionLambdaQueryWrapper = Wrappers
                //                .lambdaQuery(TemplateCollection.class)
                //                .eq(TemplateCollection::getTemplateCode, templateCode);
                //        TemplateCollection templateCollection = templateConditionRepository.selectOne(templateCollectionLambdaQueryWrapper);
                //查询当前质量问题是否存在模版
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        ProjectInfoDto infoDto = new ProjectInfoDto();
                        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
                        infoDto.setProjectSystemSelfInspection(projectSystemSelfInspection);
                        try {
                            projectInfoService.saveProject(templateCollection, projectGroup.getProjectId(), "new", infoDto
                                    , null, projectInfo, projectInfo.getProjectType(), null, null);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            });
            executor.shutdown();
        }

    }

//    //查询当前二级节点是否已经分了模版，二级节点分模版，均已提交或已完成的话，修改所有的二级节点分模版为已完成，否则状态给 已提交
//    private void upProjectGroupNodeStatus(ProjectGroup projectGroup) {
//        //根据nodecode模糊查询二级模版是否存在不符合 “已提交”、“已完成”的数据
//        String[] nodeStatus = new String[]{JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey(), JhSystemEnum.NodeStatusEnum.NODE_STATUS4.getKey()};
//        LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
//                .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
//                .like(ProjectGroup::getNodeCode, projectGroup.getNodeCode().split("_")[0] + "%")
//                .notIn(ProjectGroup::getNodeStatus, nodeStatus);
//        long count1 = projectGroupService.count(queryWrapper);
//        if (count1 == 0) {
//            //不存在“已提交”、“已完成”数据的话，修改所有二级为 “已完成”
//            LambdaUpdateWrapper<ProjectGroup> updateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                    .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
//                    .like(ProjectGroup::getNodeCode, projectGroup.getNodeCode().split("_")[0] + "%")
//                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
//            projectGroupService.update(updateWrapper);
//        } else {
//            //存在“已提交”、“已完成”数据的话，修改当前二级为 “已提交”
//            LambdaUpdateWrapper<ProjectGroup> updateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                    .eq(ProjectGroup::getProjectId, projectGroup.getProjectId())
//                    .eq(ProjectGroup::getNodeCode, projectGroup.getNodeCode())
//                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS4.getKey());
//            projectGroupService.update(updateWrapper);
//        }
//    }


//    //2.当前二级节点作为联合二级，查询是否已经配置了联合任务的不确定二级，配置了的话，查询不确定二级下的所有联合二级是否已经完成，全部完成则修改不确定二级为未完成的状态
//    private void upProjectInfoJointTaskByRelevanceNodeCode(ProjectNodeInfoDto projectNodeInfoDto) {
//        //二级节点作为联合二级,查询相关的所有不确定二级
//        List<ProjectJointTaskOnfigurationDto> projectNodeCode = projectJointTaskOnfigurationService.getByProjectRelevanceNodeCode(projectNodeInfoDto);
//        if (ObjectUtils.isNotEmpty(projectNodeCode)) {
//            Map<String, List<ProjectJointTaskOnfigurationDto>> stringListMap = projectNodeCode.stream().collect(Collectors.groupingBy(ProjectJointTaskOnfigurationDto::getNodeCode));
//            for (String s : stringListMap.keySet()) {
//                //当前不确定二级是否存在未提交的联合二级
//                String nodeCode = null;
//                Boolean isSubmit = true;
//                List<ProjectJointTaskOnfigurationDto> taskOnfigurationDtos = stringListMap.get(s);
//                for (ProjectJointTaskOnfigurationDto onfigurationDto : taskOnfigurationDtos) {
//                    if (ObjectUtils.isEmpty(nodeCode)) {
//                        nodeCode = onfigurationDto.getNodeCode();
//                    }
//                    if (isSubmit) {
//                        //查询联合二级是否存在没有提交的数据
//                        LambdaQueryWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
//                                .eq(ProjectGroup::getProjectId, projectNodeInfoDto.getProjectId())
//                                .eq(ProjectGroup::getNodeCode, onfigurationDto.getRelevanceNodeCode())
//                                .ne(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
//                        long count = projectGroupService.count(projectNodeInfoLambdaUpdateWrapper);
//                        if (count > 0) {
//                            isSubmit = false;
//                        }
//                    }
//                }
//                if (isSubmit && ObjectUtils.isNotEmpty(nodeCode)) {
//                    //当前不确定二级不存在未提交的联合二级，修改当前不确定二级的状态为未完成状态
//                    LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
//                            .eq(ProjectGroup::getProjectId, projectNodeInfoDto.getProjectId())
//                            .eq(ProjectGroup::getNodeCode, nodeCode)
//                            .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                    projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
//                }
//            }
//
//        }
//
//    }

    /**
     * 每走一个节点，根据节点状态 判断任务阶段和项目状态 并且更新到项目表中
     *
     * @param projectGroup
     */
    private void updateProjectPhaseAndStatus(ProjectGroup projectGroup) {
//        List<NodeStatusResult> statusList = projectGroupRepository.getNodeStatusByLevel1ByProjectId(projectGroup.getProjectId());
        ProjectStatusAndPhaseVo statusAndPhaseByProjectId = projectGroupRepository.getStatusAndPhaseByProjectId(projectGroup.getProjectId());
        if (ObjectUtil.isNotEmpty(statusAndPhaseByProjectId)) {
            String statusValue = statusAndPhaseByProjectId.getStatusValue();
            String phaseValue = statusAndPhaseByProjectId.getPhaseValue();
            if (ObjectUtil.isNotEmpty(statusValue)) {
                LambdaUpdateWrapper statusUpdate = Wrappers.lambdaUpdate(ProjectInfo.class)
                        .eq(ProjectInfo::getProjectId, projectGroup.getProjectId())
                        .set(ProjectInfo::getProjectStatus, statusValue);
                projectInfoService.update(statusUpdate);
            }
            if (ObjectUtil.isNotEmpty(phaseValue)) {
                LambdaUpdateWrapper phaseUpdate = Wrappers.lambdaUpdate(ProjectInfo.class)
                        .eq(ProjectInfo::getProjectId, projectGroup.getProjectId())
                        .set(ProjectInfo::getTaskPhase, phaseValue);
                projectInfoService.update(phaseUpdate);
            }
//            for (NodeStatusResult result : statusList) {
//                //0表示完成 1表示还未完成
//                if (ObjectUtil.isNotEmpty(result.getStatus2()) && result.getStatus2() == 0){
//                    String taskPhase = JhSystemEnum.TaskPhaseEnum.getKey(result.getCode1());
//                    String projectStatus = JhSystemEnum.ProjectStatusEnum.getKeyByCode(result.getCode1());
//                    break;
//                }
//            }
        }
    }

}
