package com.bassims.modules.atour.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 甲供材资产数量报表dto
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Data
public class FirstFinanceReportDto {

    @ExcelProperty("序号")
    private Integer indexNo;

    @ExcelProperty(value = "城市公司value")
    private String cityCompany;

    @ExcelProperty(value = "门店编码")
    private String storeNo;

    @ExcelProperty(value = "门店名称")
    private String storeName;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "项目类型")
    private String projectType;

    @ExcelProperty(value = "费用类型")
    private String costType;

    @ExcelProperty(value = "实际开业日期")
    private String actualOpenDate;

    @ExcelProperty(value = "订单类型")
    private String orderType;

    @ExcelProperty(value = "物料类型")
    private String secondClass;

    @ExcelProperty(value = "产品编号")
    private String productCode;

    @ExcelProperty(value = "产品名称")
    private String productName;

    @ExcelProperty(value = "决算数量")
    private BigDecimal subtotal;
}
