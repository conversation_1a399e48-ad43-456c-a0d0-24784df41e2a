package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.service.ProjectFacilityReportService;
import com.bassims.modules.atour.service.dto.ProjectFacilityReportQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 工程设备报表
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "工程设备报表")
@RequestMapping("/api/projectFacilityReport")
public class ProjectFacilityReportController {

    @Autowired
    private ProjectFacilityReportService projectFacilityReportService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectFacilityReportQueryCriteria criteria) throws IOException {
        projectFacilityReportService.download(response, criteria);
    }

    @Log("查询数据")
    @ApiOperation("查询数据")
    @GetMapping(value = "/queryTime")
    public ResponseEntity<Object> queryTime(ProjectFacilityReportQueryCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(projectFacilityReportService.queryTime(criteria, pageable));
    }

}
