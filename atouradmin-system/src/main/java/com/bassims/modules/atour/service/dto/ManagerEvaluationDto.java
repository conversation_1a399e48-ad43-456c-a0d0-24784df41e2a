package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

@Data
public class ManagerEvaluationDto {

    /** 主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /** 项目经理id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long supplierPmId;

    /** 项目经理名称 */
    private String pmName;

    /** 考评得分 */
    private BigDecimal evaluationScore;

    /** 考评开始时间 */
    private Date evaluationStartTime;

    /** 考评结束时间 */
    private Date evaluationEndTime;

    /** 是否删除 */
    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private Long createUser;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 修改人 */
    private Long updateUser;

    /**服务区域*/
    private Set<Long> pmServiceAreas;

}
