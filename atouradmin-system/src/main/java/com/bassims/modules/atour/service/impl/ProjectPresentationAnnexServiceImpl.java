/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.ProjectPresentationAnnex;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProjectPresentationAnnexRepository;
import com.bassims.modules.atour.service.ProjectPresentationAnnexService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProjectPresentationAnnexDto;
import com.bassims.modules.atour.service.dto.ProjectPresentationAnnexQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectPresentationAnnexMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-24
**/
@Service
public class ProjectPresentationAnnexServiceImpl extends BaseServiceImpl<ProjectPresentationAnnexRepository,ProjectPresentationAnnex> implements ProjectPresentationAnnexService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectPresentationAnnexServiceImpl.class);

    @Autowired
    private ProjectPresentationAnnexRepository projectPresentationAnnexRepository;
    @Autowired
    private ProjectPresentationAnnexMapper projectPresentationAnnexMapper;

    @Override
    public Map<String,Object> queryAll(ProjectPresentationAnnexQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProjectPresentationAnnex> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectPresentationAnnex.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectPresentationAnnexMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectPresentationAnnexDto> queryAll(ProjectPresentationAnnexQueryCriteria criteria){
        return projectPresentationAnnexMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectPresentationAnnex.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectPresentationAnnexDto findById(Long projectAnnexId) {
        ProjectPresentationAnnex projectPresentationAnnex = Optional.ofNullable(getById(projectAnnexId)).orElseGet(ProjectPresentationAnnex::new);
        ValidationUtil.isNull(projectPresentationAnnex.getProjectAnnexId(),getEntityClass().getSimpleName(),"projectAnnexId",projectAnnexId);
        return projectPresentationAnnexMapper.toDto(projectPresentationAnnex);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectPresentationAnnexDto create(ProjectPresentationAnnex resources) {
        save(resources);
        return findById(resources.getProjectAnnexId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectPresentationAnnex resources) {
        ProjectPresentationAnnex projectPresentationAnnex = Optional.ofNullable(getById(resources.getProjectAnnexId())).orElseGet(ProjectPresentationAnnex::new);
        ValidationUtil.isNull( projectPresentationAnnex.getProjectAnnexId(),"ProjectPresentationAnnex","id",resources.getProjectAnnexId());
        projectPresentationAnnex.copy(resources);
        updateById(projectPresentationAnnex);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectAnnexId : ids) {
            projectPresentationAnnexRepository.deleteById(projectAnnexId);
        }
    }

    @Override
    public void download(List<ProjectPresentationAnnexDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectPresentationAnnexDto projectPresentationAnnex : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目ID", projectPresentationAnnex.getProjectId());
            map.put("三级节点编码", projectPresentationAnnex.getNodeCode());
            map.put(" remark",  projectPresentationAnnex.getRemark());
            map.put(" isDelete",  projectPresentationAnnex.getIsDelete());
            map.put("创建时间", projectPresentationAnnex.getCreateTime());
            map.put(" createBy",  projectPresentationAnnex.getCreateBy());
            map.put("修改时间", projectPresentationAnnex.getUpdateTime());
            map.put("修改人", projectPresentationAnnex.getUpdateBy());
            map.put("是否可用", projectPresentationAnnex.getIsEnabled());
            map.put("是否可以编辑", projectPresentationAnnex.getIsEdit());
            map.put("用户添加的版本", projectPresentationAnnex.getAddedVersion());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}