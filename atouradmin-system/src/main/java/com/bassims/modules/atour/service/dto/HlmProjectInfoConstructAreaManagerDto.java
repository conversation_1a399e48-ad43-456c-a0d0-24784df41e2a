/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-11-16
**/
@Data
public class HlmProjectInfoConstructAreaManagerDto implements Serializable {

    /** hlm项目id */
    private Integer projectId;

    /** 酒店ID */
    private Integer hotelId;

    /** 员工编号 */
    private String employeeId;

    /** 员工花名
 */
    private String flowerName;

    /** email */
    private String email;

    /** 手机号
 */
    private String mobile;

    /** 部门ID
 */
    private String departmentId;

    /** 岗位名称 */
    private String jobName;

    /** 项目拓展表的ID */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long areaManagerId;
}