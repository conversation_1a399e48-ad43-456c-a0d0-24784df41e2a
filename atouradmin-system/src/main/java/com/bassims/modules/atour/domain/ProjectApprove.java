/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-28
**/
@Data
@TableName(value="t_project_approve")
public class ProjectApprove implements Serializable {

    @TableId(value = "approve_id")
    @ApiModelProperty(value = "审批主键")
    private Long approveId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @TableField(value = "approve_type")
    @ApiModelProperty(value = "审批类型")
    private String approveType;

    @TableField(value = "approve_status")
    @ApiModelProperty(value = "审批状态")
    private String approveStatus;

    @TableField(value = "approve_result")
    @ApiModelProperty(value = "审批结果")
    private String approveResult;

    @TableField(value = "approve_start")
    @ApiModelProperty(value = "审批开始时间")
    private Timestamp approveStart;

    @TableField(value = "approve_end")
    @ApiModelProperty(value = "审批结束时间")
    private Timestamp approveEnd;

    @TableField(value = "submit_user")
    @ApiModelProperty(value = "提交人")
    private Long submitUser;

    @TableField(value = "is_anew")
    @ApiModelProperty(value = "是否重报")
    private Boolean isAnew;

    @TableField(value = "anew_reason")
    @ApiModelProperty(value = "重报原因")
    private String anewReason;

    @TableField(value = "node_id")
    @ApiModelProperty(value = "审批对应的节点id")
    private Long nodeId;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ProjectApprove source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}