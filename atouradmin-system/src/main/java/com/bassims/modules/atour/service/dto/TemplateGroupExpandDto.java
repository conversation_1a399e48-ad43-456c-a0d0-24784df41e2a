/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-27
**/
@Data
public class TemplateGroupExpandDto implements Serializable {

    /** 模板组主键 */
    /** 防止精度丢失 */
    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private Long expandId;

    /** 审图分类（reclassify） */
    private String reclassify;

    /** 审图属性（深化、设计） */
    private String reviewAttribute;

    /** 文件标题 */
    private String fileHeader;

    /** 二级nodeCode */
    private String groupNodeCode;

    /** 三级nodeCode */
    private String nodeCode;

    private String templateCode;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新时间 */
    private Timestamp updateTime;

    private String createBy;

    private String updateBy;

    /**文件类型校验*/
    private String fileCheckFormat;

    /*当前审图的code*/
    private String expandCode;
    /*关联的审图code（设计通过后，起当前深化二级任务）*/
    private String relationCode;

    private Integer notCad;


    /**设计文本*/
    private String designText;


    /**图纸变更的二级nodeCode*/
    private String drawingChangeCode;


   /**PDF文件是否可选不适用非必填（0可选不适用PDF为非必传，1不可选不适用PDF为必传）*/
    private Integer pdfMandatory;

}