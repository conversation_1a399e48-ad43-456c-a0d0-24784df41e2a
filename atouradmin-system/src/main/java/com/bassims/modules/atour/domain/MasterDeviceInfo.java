/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-22
**/
@Data
@TableName(value="z_master_device_info")
public class MasterDeviceInfo implements Serializable {

    @TableId(value = "device_id")
    @ApiModelProperty(value = "主键id")
    private Long deviceId;

    @TableField(value = "device_type")
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @TableField(value = "device_name")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @TableField(value = "store_id")
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @TableField(value = "second_construct")
    @ApiModelProperty(value = "二次施工")
    private String secondConstruct;

    @TableField(value = "device_brand")
    @ApiModelProperty(value = "设备品牌")
    private String deviceBrand;

    @TableField(value = "device_maintenance")
    @ApiModelProperty(value = "设备维保方")
    private String deviceMaintenance;

    @TableField(value = "device_electricity")
    @ApiModelProperty(value = "设备电费承担")
    private String deviceElectricity;

    @TableField(value = "complete_accept_time")
    @ApiModelProperty(value = "竣工验收时间")
    private String completeAcceptTime;

    @TableField(value = "device_warranty")
    @ApiModelProperty(value = "设备质保期限")
    private String deviceWarranty;

    @TableField(value = "maintenance_period")
    @ApiModelProperty(value = "维保期间")
    private String maintenancePeriod;

    @TableField(value = "maintenance_amount")
    @ApiModelProperty(value = "维保金额")
    private BigDecimal maintenanceAmount;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(MasterDeviceInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}