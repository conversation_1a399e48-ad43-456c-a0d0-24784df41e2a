/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bassims.modules.atour.domain.TemplateCompletionReceipt;
import com.bassims.modules.atour.domain.TemplateCompletionReceiptRelation;
import com.bassims.modules.atour.domain.vo.TemplateCompletionReceiptRelationVo;
import com.bassims.modules.atour.service.TemplateCompletionReceiptService;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateCompletionReceiptRelationRepository;
import com.bassims.modules.atour.service.TemplateCompletionReceiptRelationService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptRelationDto;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateCompletionReceiptRelationMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-01-26
 **/
@Service
public class TemplateCompletionReceiptRelationServiceImpl extends BaseServiceImpl<TemplateCompletionReceiptRelationRepository, TemplateCompletionReceiptRelation> implements TemplateCompletionReceiptRelationService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateCompletionReceiptRelationServiceImpl.class);

    @Autowired
    private TemplateCompletionReceiptRelationRepository templateCompletionReceiptRelationRepository;
    @Autowired
    private TemplateCompletionReceiptRelationMapper templateCompletionReceiptRelationMapper;
    @Autowired
    private TemplateCompletionReceiptService templateCompletionReceiptService;

    @Override
    public Map<String, Object> queryAll(TemplateCompletionReceiptRelationQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<TemplateCompletionReceiptRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateCompletionReceiptRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateCompletionReceiptRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateCompletionReceiptRelationDto> queryAll(TemplateCompletionReceiptRelationQueryCriteria criteria) {
        return templateCompletionReceiptRelationMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateCompletionReceiptRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateCompletionReceiptRelationDto findById(Long relationId) {
        TemplateCompletionReceiptRelation templateCompletionReceiptRelation = Optional.ofNullable(getById(relationId)).orElseGet(TemplateCompletionReceiptRelation::new);
        ValidationUtil.isNull(templateCompletionReceiptRelation.getRelationId(), getEntityClass().getSimpleName(), "relationId", relationId);
        return templateCompletionReceiptRelationMapper.toDto(templateCompletionReceiptRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateCompletionReceiptRelationDto create(TemplateCompletionReceiptRelation resources) {
        save(resources);
        return findById(resources.getRelationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateCompletionReceiptRelation resources) {
        TemplateCompletionReceiptRelation templateCompletionReceiptRelation = Optional.ofNullable(getById(resources.getRelationId())).orElseGet(TemplateCompletionReceiptRelation::new);
        ValidationUtil.isNull(templateCompletionReceiptRelation.getRelationId(), "TemplateCompletionReceiptRelation", "id", resources.getRelationId());
        templateCompletionReceiptRelation.copy(resources);
        updateById(templateCompletionReceiptRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long relationId : ids) {
            templateCompletionReceiptRelationRepository.deleteById(relationId);
        }
    }

    @Override
    public void download(List<TemplateCompletionReceiptRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateCompletionReceiptRelationDto templateCompletionReceiptRelation : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("竣工验收单仓库ID", templateCompletionReceiptRelation.getReceiptId());
            map.put("竣工验收分组ID", templateCompletionReceiptRelation.getReceiptGroupId());
            map.put("创建时间", templateCompletionReceiptRelation.getCreateTime());
            map.put("创建人", templateCompletionReceiptRelation.getCreateBy());
            map.put("更新人", templateCompletionReceiptRelation.getUpdateBy());
            map.put("更新时间", templateCompletionReceiptRelation.getUpdateTime());
            map.put("是否可用", templateCompletionReceiptRelation.getIsEnabled());
            map.put("是否删除", templateCompletionReceiptRelation.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional
    public void saveRelation(Long receiptGroupId, List<Long> receiptIds, List<Long> receiptIdsCanInput) {
        if (receiptIds == null || receiptIds.size() == 0) {
            return;
        }
        //先删除receiptGroupId的
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("receipt_group_id", receiptGroupId);
        remove(queryWrapper);
        List<TemplateCompletionReceiptRelation> receiptRelations = new ArrayList<>();
        for (Long receiptId : receiptIds) {
            TemplateCompletionReceiptRelation receiptRelation = new TemplateCompletionReceiptRelation();
            receiptRelation.setReceiptId(receiptId);
            receiptRelation.setReceiptGroupId(receiptGroupId);
            receiptRelation.setIsDelete("0");
            if (receiptIdsCanInput != null && receiptIdsCanInput.contains(receiptId)) {
                receiptRelation.setCanInput(1);
            }
            receiptRelations.add(receiptRelation);
        }
        saveBatch(receiptRelations);
    }

    @Override
    public Object selectByReceiptGroupId(Long receiptGroupId, String totalItem, String project, String content, String subItemContent,String subItem) {
        if (receiptGroupId == null) {
            return templateCompletionReceiptService.selectByCondition(totalItem, project, content, subItemContent,subItem);
        } else {
            return templateCompletionReceiptRelationRepository.selectByReceiptGroupId(receiptGroupId, totalItem, project, content, subItemContent,subItem);
        }
    }

}