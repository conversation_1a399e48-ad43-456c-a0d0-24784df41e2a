/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.SupplierContacts;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.SupplierContactsRepository;
import com.bassims.modules.atour.service.SupplierContactsService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.SupplierContactsDto;
import com.bassims.modules.atour.service.dto.SupplierContactsQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SupplierContactsMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-09-14
**/
@Service
public class SupplierContactsServiceImpl extends BaseServiceImpl<SupplierContactsRepository,SupplierContacts> implements SupplierContactsService {

    private static final Logger logger = LoggerFactory.getLogger(SupplierContactsServiceImpl.class);

    @Autowired
    private SupplierContactsRepository supplierContactsRepository;
    @Autowired
    private SupplierContactsMapper supplierContactsMapper;

    @Override
    public Map<String,Object> queryAll(SupplierContactsQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SupplierContacts> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(SupplierContacts.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", supplierContactsMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SupplierContactsDto> queryAll(SupplierContactsQueryCriteria criteria){
        List<SupplierContacts> supplierContactsList = list(QueryHelpPlus.getPredicate(SupplierContacts.class, criteria));
        List<SupplierContactsDto> supplierContactsDtoList = supplierContactsMapper.toDto(supplierContactsList);
        supplierContactsDtoList.forEach(supplierContactsDto -> {
            // TODO 待封装联系人详细信息


        });
        return supplierContactsDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierContactsDto findById(Long id) {
        SupplierContacts supplierContacts = Optional.ofNullable(getById(id)).orElseGet(SupplierContacts::new);
        ValidationUtil.isNull(supplierContacts.getId(),getEntityClass().getSimpleName(),"id",id);
        return supplierContactsMapper.toDto(supplierContacts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierContactsDto create(SupplierContacts resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SupplierContacts resources) {
        SupplierContacts supplierContacts = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierContacts::new);
        ValidationUtil.isNull( supplierContacts.getId(),"SupplierContacts","id",resources.getId());
        supplierContacts.copy(resources);
        updateById(supplierContacts);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            supplierContactsRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SupplierContactsDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SupplierContactsDto supplierContacts : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("厂商ID", supplierContacts.getSupplierId());
            map.put("联系人", supplierContacts.getContact());
            map.put("联系电话", supplierContacts.getPhone());
            map.put("邮箱", supplierContacts.getEmail());
            map.put("传真", supplierContacts.getFax());
            map.put("联系人类型(厂商法人，客户联系人)", supplierContacts.getContactType());
            map.put("备注", supplierContacts.getRemark());
            map.put("创建人", supplierContacts.getCreateUser());
            map.put("创建时间", supplierContacts.getCreateTime());
            map.put("更新人", supplierContacts.getUpdateUser());
            map.put("更新时间", supplierContacts.getUpdateTime());
            map.put("是否删除", supplierContacts.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}