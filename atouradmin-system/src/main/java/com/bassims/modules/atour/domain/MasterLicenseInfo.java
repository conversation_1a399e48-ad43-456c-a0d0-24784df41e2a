/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-22
**/
@Data
@TableName(value="z_master_license_info")
public class MasterLicenseInfo implements Serializable {

    @TableId(value = "license_id")
    @ApiModelProperty(value = "主键id")
    private Long licenseId;

    @TableField(value = "license_name")
    @ApiModelProperty(value = "证照名称")
    private String licenseName;

    @TableField(value = "license_type")
    @ApiModelProperty(value = "证照类型")
    private String licenseType;

    @TableField(value = "store_id")
    @ApiModelProperty(value = "门店id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long storeId;

    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @TableField(value = "node_id")
    @ApiModelProperty(value = "门店编号")
    private String nodeId;

    @TableField(value = "actual_start")
    @ApiModelProperty(value = "实际开始时间")
    private Timestamp actualStart;

    @TableField(value = "actual_end")
    @ApiModelProperty(value = "实际结束时间")
    private Timestamp actualEnd;

    @TableField(value = "uploader")
    @ApiModelProperty(value = "上传人")
    private String uploader;

    @TableField(value = "upload_time")
    @ApiModelProperty(value = "上传时间")
    private Timestamp uploadTime;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(MasterLicenseInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}