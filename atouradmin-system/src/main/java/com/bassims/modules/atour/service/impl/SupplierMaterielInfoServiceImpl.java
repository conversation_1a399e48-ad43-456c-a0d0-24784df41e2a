/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.SupplierMaterielInfo;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.SupplierMaterielInfoRepository;
import com.bassims.modules.atour.service.SupplierMaterielInfoService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.SupplierMaterielInfoDto;
import com.bassims.modules.atour.service.dto.SupplierMaterielInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SupplierMaterielInfoMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-09-14
**/
@Service
public class SupplierMaterielInfoServiceImpl extends BaseServiceImpl<SupplierMaterielInfoRepository,SupplierMaterielInfo> implements SupplierMaterielInfoService {

    private static final Logger logger = LoggerFactory.getLogger(SupplierMaterielInfoServiceImpl.class);

    @Autowired
    private SupplierMaterielInfoRepository supplierMaterielInfoRepository;
    @Autowired
    private SupplierMaterielInfoMapper supplierMaterielInfoMapper;

    @Override
    public Map<String,Object> queryAll(SupplierMaterielInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SupplierMaterielInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(SupplierMaterielInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", supplierMaterielInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SupplierMaterielInfoDto> queryAll(SupplierMaterielInfoQueryCriteria criteria){
        return supplierMaterielInfoMapper.toDto(list(QueryHelpPlus.getPredicate(SupplierMaterielInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterielInfoDto findById(Long id) {
        SupplierMaterielInfo supplierMaterielInfo = Optional.ofNullable(getById(id)).orElseGet(SupplierMaterielInfo::new);
        ValidationUtil.isNull(supplierMaterielInfo.getId(),getEntityClass().getSimpleName(),"id",id);
        return supplierMaterielInfoMapper.toDto(supplierMaterielInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterielInfoDto create(SupplierMaterielInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SupplierMaterielInfo resources) {
        SupplierMaterielInfo supplierMaterielInfo = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierMaterielInfo::new);
        ValidationUtil.isNull( supplierMaterielInfo.getId(),"SupplierMaterielInfo","id",resources.getId());
        supplierMaterielInfo.copy(resources);
        updateById(supplierMaterielInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            supplierMaterielInfoRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SupplierMaterielInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SupplierMaterielInfoDto supplierMaterielInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" supContractId",  supplierMaterielInfo.getSupContractId());
            map.put(" supplierId",  supplierMaterielInfo.getSupplierId());
            map.put(" materielId",  supplierMaterielInfo.getMaterielId());
            map.put("物料名称", supplierMaterielInfo.getMaterielName());
            map.put("编号", supplierMaterielInfo.getMaterielCode());
            map.put("资产编号", supplierMaterielInfo.getFinanceCode());
            map.put("产品名称", supplierMaterielInfo.getProductName());
            map.put("产品编号", supplierMaterielInfo.getProductCode());
            map.put("物料种类", supplierMaterielInfo.getMaterielClass());
            map.put(" sku",  supplierMaterielInfo.getSku());
            map.put(" salePrice",  supplierMaterielInfo.getSalePrice());
            map.put(" buyPrice",  supplierMaterielInfo.getBuyPrice());
            map.put(" skuAttr",  supplierMaterielInfo.getSkuAttr());
            map.put(" supplyCycle",  supplierMaterielInfo.getSupplyCycle());
            map.put("创建人", supplierMaterielInfo.getCreateUser());
            map.put("创建时间", supplierMaterielInfo.getCreateTime());
            map.put("更新人", supplierMaterielInfo.getUpdateUser());
            map.put("更新时间", supplierMaterielInfo.getUpdateTime());
            map.put("是否删除", supplierMaterielInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}