/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.MaterialManagement;
import com.bassims.modules.atour.domain.ProjectRoom;
import com.bassims.modules.atour.repository.MaterialManagementRepository;
import com.bassims.modules.atour.service.MaterialManagementService;
import com.bassims.modules.atour.service.ProjectRoomService;
import com.bassims.modules.atour.service.dto.MaterialManagementDto;
import com.bassims.modules.atour.service.dto.MaterialManagementQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MaterialManagementMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-12-04
**/
@Service
public class MaterialManagementServiceImpl extends BaseServiceImpl<MaterialManagementRepository,MaterialManagement> implements MaterialManagementService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialManagementServiceImpl.class);

    @Autowired
    private MaterialManagementRepository materialManagementRepository;
    @Autowired
    private MaterialManagementMapper materialManagementMapper;
    @Autowired
    private ProjectRoomService projectRoomService;

    @Override
    public Map<String,Object> queryAll(MaterialManagementQueryCriteria criteria, Pageable pageable,Long projectId,String isUsed){
        getPage(pageable);
        PageInfo<MaterialManagement> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MaterialManagement.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", materialManagementMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        //查询【正式开工】提交的房间号
        final LambdaQueryWrapper<ProjectRoom> wrapper = Wrappers.lambdaQuery(ProjectRoom.class)
                .eq(ProjectRoom::getProjectId, projectId)
                .eq(ProjectRoom::getIsUsed,isUsed);
        final List<ProjectRoom> projectRooms = projectRoomService.list(wrapper);
        final List<String> collect = projectRooms.stream().map(ProjectRoom::getRoomNum).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(collect)) {
            map.put("roomNum", collect);
            map.put("roomSum", collect.size());
        }
        return map;
    }

    @Override
    public List<MaterialManagementDto> queryAll(MaterialManagementQueryCriteria criteria){
        return materialManagementMapper.toDto(list(QueryHelpPlus.getPredicate(MaterialManagement.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialManagementDto findById(Long managementId) {
        MaterialManagement materialManagement = Optional.ofNullable(getById(managementId)).orElseGet(MaterialManagement::new);
        ValidationUtil.isNull(materialManagement.getManagementId(),getEntityClass().getSimpleName(),"managementId",managementId);
        return materialManagementMapper.toDto(materialManagement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialManagementDto create(MaterialManagement resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setManagementId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getManagementId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MaterialManagement resources) {
        MaterialManagement materialManagement = Optional.ofNullable(getById(resources.getManagementId())).orElseGet(MaterialManagement::new);
        ValidationUtil.isNull( materialManagement.getManagementId(),"MaterialManagement","id",resources.getManagementId());
        materialManagement.copy(resources);
        updateById(materialManagement);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long managementId : ids) {
            materialManagementRepository.deleteById(managementId);
        }
    }

    @Override
    public void download(List<MaterialManagementDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MaterialManagementDto materialManagement : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("物资类别", materialManagement.getMaterialCategory());
            map.put("材料图样", materialManagement.getMaterialPattern());
            map.put("材料性能参数", materialManagement.getMaterialPerformanceParameter());
            map.put("合同约定必采", materialManagement.getAgreeTake());
            map.put("下单时间", materialManagement.getOrderTime());
            map.put("创建时间", materialManagement.getCreateTime());
            map.put("创建人", materialManagement.getCreateUser());
            map.put("创建部门", materialManagement.getCreateDept());
            map.put("更新时间", materialManagement.getUpdateTime());
            map.put("更新人", materialManagement.getUpdateUser());
            map.put("是否已删除：0 正常，1 已删除", materialManagement.getIsDelete());
            map.put("租户编号", materialManagement.getTenantId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}