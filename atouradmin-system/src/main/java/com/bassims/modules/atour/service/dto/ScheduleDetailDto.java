/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-04-26
**/
@Data
public class ScheduleDetailDto implements Serializable {

    /** 工期主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long scheduleDetailId;

    /** 模板ID */
    private Long templateId;

    /** 工期类型 */
    private String scheduleType;

    /** 计划需要完成天数 */
    private Integer planDay;

    /** 提醒天数 */
    private Integer noticeDay;

    /** 总工期 */
    private Integer totalDay;

    /** 前置任务配置[{"type":"FS","wbs":11},{"type":"SS","wbs":12}] */
    private String frontWbsConfig;

    /** 是否是关键节点 */
    private Boolean isKey;

    /** 关键节点前置任务 */
    private String keyFrontWbs;

    /** 模板code */
    private String templateCode;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新时间 */
    private Timestamp updateTime;

    private String createBy;

    private String updateBy;
}