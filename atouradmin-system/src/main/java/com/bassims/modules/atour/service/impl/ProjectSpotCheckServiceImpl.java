/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.ProjectNodeInfoRepository;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atour.repository.TemplateConditionRepository;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.utils.*;
import com.bassims.modules.atour.repository.ProjectSpotCheckRepository;
import com.bassims.modules.atour.service.ProjectSpotCheckService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.mapstruct.ProjectSpotCheckMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import io.reactivex.rxjava3.internal.operators.observable.ObservableJust;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-01-14
**/
@Service
public class ProjectSpotCheckServiceImpl extends BaseServiceImpl<ProjectSpotCheckRepository,ProjectSpotCheck> implements ProjectSpotCheckService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSpotCheckServiceImpl.class);

    @Autowired
    private ProjectSpotCheckRepository projectSpotCheckRepository;
    @Autowired
    private ProjectSpotCheckMapper projectSpotCheckMapper;
    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private TemplateConditionRepository templateConditionRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private NoteInfoMappingUtil util;

    @Override
    public String createSpotCheck(ProjectSpotCheck projectSpotCheck) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //根据当前用户，获取对应干系人的roleCode,有多个roleCode
        List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, projectNodeInfoService.getSubmeterProjectId(projectSpotCheck.getProjectId()));
        //插入工程抽查模板
        LambdaQueryWrapper<ProjectSpotCheck> query = Wrappers.lambdaQuery(ProjectSpotCheck.class);
        query.eq(ProjectSpotCheck::getProjectId, projectSpotCheck.getProjectId());
        query.eq(ProjectSpotCheck::getNodeCode, projectSpotCheck.getNodeCode());
        Long aLong = projectSpotCheckRepository.selectCount(query);
        if (ObjectUtil.isNotEmpty(aLong)) {
            projectSpotCheck.setGroupIndex(aLong.intValue() + 1);
        }else{
            projectSpotCheck.setGroupIndex(1);
        }

        this.save(projectSpotCheck);

        LambdaQueryWrapper<TemplateCollection> queryWrapper = Wrappers.lambdaQuery(TemplateCollection.class)
                .eq(TemplateCollection::getTemplateCode, projectSpotCheck.getTemplateCode())
                .last("limit 1");
        TemplateCollection templateCollection = templateConditionRepository.selectOne(queryWrapper);
        LambdaQueryWrapper<ProjectInfo> wrapper = Wrappers.lambdaQuery(ProjectInfo.class).eq(ProjectInfo::getProjectId, projectSpotCheck.getProjectId());
        ProjectInfo projectInfo = projectInfoService.getOne(wrapper);
        if (ObjectUtil.isNotEmpty(templateCollection) && ObjectUtil.isNotEmpty(projectInfo)) {
            //获取原任务数据
            util.setProjectTableName(projectInfo.getProjectId());
            LambdaQueryWrapper<ProjectNodeInfo> wrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectSpotCheck.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, projectSpotCheck.getNodeCode());
            List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(wrapper1);
            Map<String, ProjectNodeInfo> collect = nodeInfos.stream().collect(Collectors.toMap(i -> i.getNodeCode(), j -> j, (k1, k2) -> k1));
            projectSpotCheck.setNodeCodeInfo(collect);


            ProjectInfoDto infoDto = new ProjectInfoDto();
            BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
            if (ObjectUtil.isNotEmpty(roleCodes)) {
                projectSpotCheck.setRoleCode(roleCodes.get(0));
            }
            //查询项目在项干系人
//            List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectListByRoleCodes(projectSpotCheck.getProjectId(), JhSystemEnum.VFNodeCode.getVFNodeCode());
//            Map<String, String> stringStringMap = projectStakeholders.stream().filter(e -> StringUtils.isNotEmpty(e.getRoleCode())).collect(Collectors.toMap(ProjectStakeholders::getRoleCode, ProjectStakeholders::getNickName));
//            if (ObjectUtil.isNotEmpty(stringStringMap)) {
//                projectSpotCheck.setStringStringMap(stringStringMap);
//            }
            //当前签字报备信息
            infoDto.setProjectSpotCheck(projectSpotCheck);
            try {
                templateCollection.setParentId(projectSpotCheck.getParentId());
                templateCollection.setNodeCode(projectSpotCheck.getNodeCode());
                projectInfoService.saveProject(templateCollection, projectSpotCheck.getProjectId(), "new", infoDto, null
                        , projectInfo, projectInfo.getProjectType(), null, null);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return projectSpotCheck.getId().toString();
    }

    @Override
    public List<ProjectSpotCheckDto> getSpotCheckList(ProjectSpotCheckQueryCriteria criteria) {
        return projectSpotCheckRepository.getSpotCheckList(criteria);
    }

    @Override
    public ProjectSpotCheckDto getSpotCheckOne(ProjectSpotCheckQueryCriteria criteria) {
        return projectSpotCheckRepository.getSpotCheckOne(criteria.getId());
    }

    @Override
    public Map<String,Object> queryAll(ProjectSpotCheckQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProjectSpotCheck> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectSpotCheck.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectSpotCheckMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectSpotCheckDto> queryAll(ProjectSpotCheckQueryCriteria criteria){
        return projectSpotCheckMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectSpotCheck.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSpotCheckDto findById(Long id) {
        ProjectSpotCheck projectSpotCheck = Optional.ofNullable(getById(id)).orElseGet(ProjectSpotCheck::new);
        ValidationUtil.isNull(projectSpotCheck.getId(),getEntityClass().getSimpleName(),"id",id);
        return projectSpotCheckMapper.toDto(projectSpotCheck);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSpotCheckDto create(ProjectSpotCheck resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectSpotCheck resources) {
        ProjectSpotCheck projectSpotCheck = Optional.ofNullable(getById(resources.getId())).orElseGet(ProjectSpotCheck::new);
        ValidationUtil.isNull( projectSpotCheck.getId(),"ProjectSpotCheck","id",resources.getId());
        projectSpotCheck.copy(resources);
        updateById(projectSpotCheck);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            projectSpotCheckRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ProjectSpotCheckDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectSpotCheckDto projectSpotCheck : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目id", projectSpotCheck.getProjectId());
            map.put("抽查任务名称", projectSpotCheck.getGroupName());
            map.put("抽查任务轮次", projectSpotCheck.getGroupIndex());
            map.put("抽查任务二级code", projectSpotCheck.getNodeCode());
            map.put("备注", projectSpotCheck.getRemark());
            map.put("创建时间", projectSpotCheck.getCreateTime());
            map.put("创建人", projectSpotCheck.getCreateBy());
            map.put("更新时间", projectSpotCheck.getUpdateTime());
            map.put("更新人", projectSpotCheck.getUpdateBy());
            map.put("是否可用", projectSpotCheck.getIsEnabled());
            map.put("是否删除", projectSpotCheck.getIsDelete());
            map.put("情况说明", projectSpotCheck.getPresentationOndition());
            map.put("附件", projectSpotCheck.getAttachment());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}