/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.ProjectSpecialNeedsFranchisors;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProjectSpecialNeedsFranchisorsRepository;
import com.bassims.modules.atour.service.ProjectSpecialNeedsFranchisorsService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProjectSpecialNeedsFranchisorsDto;
import com.bassims.modules.atour.service.dto.ProjectSpecialNeedsFranchisorsQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectSpecialNeedsFranchisorsMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-12-16
**/
@Service
public class ProjectSpecialNeedsFranchisorsServiceImpl extends BaseServiceImpl<ProjectSpecialNeedsFranchisorsRepository,ProjectSpecialNeedsFranchisors> implements ProjectSpecialNeedsFranchisorsService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSpecialNeedsFranchisorsServiceImpl.class);

    @Autowired
    private ProjectSpecialNeedsFranchisorsRepository projectSpecialNeedsFranchisorsRepository;
    @Autowired
    private ProjectSpecialNeedsFranchisorsMapper projectSpecialNeedsFranchisorsMapper;

    @Override
    public Map<String,Object> queryAll(ProjectSpecialNeedsFranchisorsQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProjectSpecialNeedsFranchisors> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectSpecialNeedsFranchisors.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectSpecialNeedsFranchisorsMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectSpecialNeedsFranchisorsDto> queryAll(ProjectSpecialNeedsFranchisorsQueryCriteria criteria){
        return projectSpecialNeedsFranchisorsMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectSpecialNeedsFranchisors.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSpecialNeedsFranchisorsDto findById(Long franchisorsId) {
        ProjectSpecialNeedsFranchisors projectSpecialNeedsFranchisors = Optional.ofNullable(getById(franchisorsId)).orElseGet(ProjectSpecialNeedsFranchisors::new);
        ValidationUtil.isNull(projectSpecialNeedsFranchisors.getFranchisorsId(),getEntityClass().getSimpleName(),"franchisorsId",franchisorsId);
        return projectSpecialNeedsFranchisorsMapper.toDto(projectSpecialNeedsFranchisors);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSpecialNeedsFranchisorsDto create(ProjectSpecialNeedsFranchisors resources) {
        save(resources);
        return findById(resources.getFranchisorsId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectSpecialNeedsFranchisors resources) {
        ProjectSpecialNeedsFranchisors projectSpecialNeedsFranchisors = Optional.ofNullable(getById(resources.getFranchisorsId())).orElseGet(ProjectSpecialNeedsFranchisors::new);
        ValidationUtil.isNull( projectSpecialNeedsFranchisors.getFranchisorsId(),"ProjectSpecialNeedsFranchisors","id",resources.getFranchisorsId());
        projectSpecialNeedsFranchisors.copy(resources);
        updateById(projectSpecialNeedsFranchisors);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long franchisorsId : ids) {
            projectSpecialNeedsFranchisorsRepository.deleteById(franchisorsId);
        }
    }

    @Override
    public void download(List<ProjectSpecialNeedsFranchisorsDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectSpecialNeedsFranchisorsDto projectSpecialNeedsFranchisors : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目id", projectSpecialNeedsFranchisors.getProjectId());
            map.put("问题描述", projectSpecialNeedsFranchisors.getProblemDescription());
            map.put("是否需审批(0不需要、1需要)", projectSpecialNeedsFranchisors.getWhetherApprovalRequired());
            map.put("处理方案建议", projectSpecialNeedsFranchisors.getTreatmentProposal());
            map.put("约定完成时间", projectSpecialNeedsFranchisors.getAgreedCompletionTime());
            map.put("图片", projectSpecialNeedsFranchisors.getPicture());
            map.put("是否可用", projectSpecialNeedsFranchisors.getIsEnabled());
            map.put("是否删除", projectSpecialNeedsFranchisors.getIsDelete());
            map.put("创建时间", projectSpecialNeedsFranchisors.getCreateTime());
            map.put("更新时间", projectSpecialNeedsFranchisors.getUpdateTime());
            map.put(" createBy",  projectSpecialNeedsFranchisors.getCreateBy());
            map.put(" updateBy",  projectSpecialNeedsFranchisors.getUpdateBy());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void deleteByIds(List<Long> collect) {
        //删除
        projectSpecialNeedsFranchisorsRepository.deleteByIds(collect);
    }
}