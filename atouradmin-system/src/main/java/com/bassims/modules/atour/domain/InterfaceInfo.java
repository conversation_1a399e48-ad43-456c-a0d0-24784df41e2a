/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-10-20
**/
@Data
@TableName(value="t_interface_info")
public class InterfaceInfo implements Serializable {

    @TableId(value = "interface_info_id")
    @ApiModelProperty(value = "主键id")
    private Long interfaceInfoId;

    @TableField(value = "api_name")
    @ApiModelProperty(value = "接口名")
    private String apiName;

    @TableField(value = "impl_class_name")
    @ApiModelProperty(value = "实现类（注入的bean名称）")
    private String implClassName;

    @TableField(value = "api")
    @ApiModelProperty(value = "方法名")
    private String api;

    @TableField(value = "request_method")
    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    @TableField(value = "content_type")
    @ApiModelProperty(value = "content_type")
    private String contentType;

    @TableField(value = "request")
    @ApiModelProperty(value = "请求参数")
    private String request;

    @TableField(value = "response")
    @ApiModelProperty(value = "返回值")
    private String response;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否启用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(InterfaceInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}