package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName(value = "project_area_service")
public class ProjectAreaService {

    @TableId(value = "project_id")
    private Long projectId;
    private String cityCompany;
    private String cityName;
    private String projectName;
    private String storeName;
    private String storeNo;
    private String storeType;
    private String actualOpenDate;
    private BigDecimal serviceArea;
    private BigDecimal amusement;
    private BigDecimal growthTestStation;
    private BigDecimal otherAmusement;
    private BigDecimal interactiveExperience;
    private BigDecimal maternityExperienceEoom;
    private BigDecimal selectionShop;
    private BigDecimal local;
    private BigDecimal serviceStation;
    private BigDecimal momClass;
    private BigDecimal blackGoldZone;
    private BigDecimal serviceAreaReserveOne;
    private BigDecimal serviceAreaReserveTwo;
    private BigDecimal serviceAreaReserveThree;
}
