/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-22
**/
@Data
public class MasterDeviceInfoDto implements Serializable {

    /** 主键id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deviceId;

    /** 设备类型 */
    private String deviceType;

    /** 设备名称 */
    private String deviceName;

    /** 门店id */
    private Long storeId;

    /** 门店编号 */
    private String storeNo;

    /** 二次施工 */
    private String secondConstruct;

    /** 设备品牌 */
    private String deviceBrand;

    /** 设备维保方 */
    private String deviceMaintenance;

    /** 设备电费承担 */
    private String deviceElectricity;

    /** 竣工验收时间 */
    private String completeAcceptTime;

    /** 设备质保期限 */
    private String deviceWarranty;

    /** 维保期间 */
    private String maintenancePeriod;

    /** 维保金额 */
    private BigDecimal maintenanceAmount;

    private Timestamp createTime;

    private String createBy;

    private Timestamp updateTime;

    private String updateBy;

    /** 是否删除 */
    private Boolean isDelete;
}