/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-04-20
**/
@Data
@TableName(value="t_job_node_info")
public class JobNodeInfo implements Serializable {

    @TableId(value = "job_node_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long jobNodeId;

    @TableField(value = "job_id")
    @ApiModelProperty(value = "角色id")
    private Long jobId;

    @TableField(value = "node_id")
    @ApiModelProperty(value = "节点id")
    private Long nodeId;

    @TableField(value = "template_group_id")
    @ApiModelProperty(value = "模板id")
    private Long templateGroupId;

    @TableField(value = "is_read")
    @ApiModelProperty(value = "读权限")
    private Boolean isRead;

    @TableField(value = "is_write")
    @ApiModelProperty(value = "写权限")
    private Boolean isWrite;

    @TableField(value = "is_hidden")
    @ApiModelProperty(value = "隐藏权限")
    private Boolean isHidden;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    public void copy(JobNodeInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}