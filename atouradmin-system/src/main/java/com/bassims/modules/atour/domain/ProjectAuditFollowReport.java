/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-16
**/
@Data
@TableName(value="project_audit_follow_report")
public class ProjectAuditFollowReport implements Serializable {

    @ApiModelProperty(value = "送审状态")
    private String accessStatus;

    @ApiModelProperty(value = "年级店")
    private String grade;

    @ApiModelProperty(value = "城市公司value")
    private String cityCompany;

    @ApiModelProperty(value = "门店编码")
    private String storeNo;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "施工单位名称")
    private String supId;

    @ApiModelProperty(value = "进场时间")
    private String contractStartDate;

    @ApiModelProperty(value = "竣工时间")
    private String contractEndDate;

    @ApiModelProperty(value = "开业时间(仅新店)")
    private String openTime;

    @ApiModelProperty(value = "工程部接受材料时间(施工单位->工程部)(天)")
    private String submitTimeLast;

    @ApiModelProperty(value = "送审超期天数")
    private String approveLimitDay;

    @ApiModelProperty(value = "处罚金额")
    private Double penalty;

    @ApiModelProperty(value = "工程部送材料时间(工程部->工程财务)(天)")
    private String approveEndTimeLast;

    @ApiModelProperty(value = "收到盖章版审定单时间")
    private String submitTimeFile;

    @ApiModelProperty(value = "合同金额")
    private String contractTotal;

    @ApiModelProperty(value = "施工单位送审金额")
    private String approveTotal;

    @ApiModelProperty(value = "审定金额")
    private String approvedTotal;

    @ApiModelProperty(value = "核减额")
    private Double deduction;

    @ApiModelProperty(value = "核减率")
    private Double deducteRate;

    @ApiModelProperty(value = "处罚金额")
    private Double penaltyMoney;

    public void copy(ProjectAuditFollowReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}