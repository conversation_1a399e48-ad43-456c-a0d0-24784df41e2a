/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-27
**/
@Data
@TableName(value="project_person_report")
public class ProjectPersonReport implements Serializable {

    @TableField(value = "city_company_name")
    @ApiModelProperty(value = "分公司")
    private String cityCompanyName;

    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @TableField(value = "plumber")
    @ApiModelProperty(value = "水电工")
    private String plumber;

    @TableField(value = "phone")
    @ApiModelProperty(value = "联系方式")
    private String phone;

    @TableField(value = "area_name")
    @ApiModelProperty(value = "所属区域")
    private String areaName;

    public void copy(ProjectPersonReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}