/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.bassims.service.dto.LocalStorageDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-21
**/
@Data
public class FinalPaymentApplicationDto implements Serializable {

    /** 主键id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long finalPaymentApplicationId;

    /** 订单id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 订单类型 */
    private String contractType;

    /** 申请单号 */
    private String applySn;

    /** 创建人部门 */
    private String createDept;

    /** 费用核算部门 */
    private String expenseDept;

    /** 费用核算部门code */
    private String expenseDeptCode;

    /** 发票抬头 */
    private String invoiceHeader;

    /** 流程状态 */
    private String step;

    /** 费用归属期间 */
    private String expensePeriod;

    /** 结算付款方式 */
    private String settleMethod;

    /** 供应商名称 */
    private String supplierName;

    /** 供应商编码 */
    private String supplierCode;

    /** 供应商开户行名称 */
    private String bankNameCn;

    /** 供应商开户行账号 */
    private String bankAccountNumber;

    /** 合同金额 */
    private BigDecimal contractFee;

    /** 合同金额（去税） */
    private BigDecimal contractToTaxFee;

    /** 报销总金额 */
    private BigDecimal reimburseTotal;

    /** 尾款类型 */
    private String restType;

    /** 尾款金额 */
    private BigDecimal rest;

    /** 实际支付金额 */
    private BigDecimal actualAmount;

    /** 报销事由 */
    private String reason;

    /** KOA申请单号 */
    private String koaApplySn;

    /** 付款时间 */
    private String payTime;

    /** 附件 */
    private String filePath;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 创建人 */
    private String createBy;

    /** 更新人 */
    private String updateBy;

    /** 是否删除 */
    private Boolean isDelete;

    /** 附件 */
    private List<LocalStorageDto> files;

}