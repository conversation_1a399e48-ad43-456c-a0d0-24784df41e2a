package com.bassims.modules.atour.infra;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnClass(MeterRegistryCustomizer.class)
public class AtourMonitorAutoConfiguration {
    @Bean
    public MeterRegistryCustomizer<MeterRegistry>
        metricsCommonTags(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName)
            .meterFilter(new DefaultMeterFilter());
    }

    @Bean
    public FilterRegistrationBean<DefaultTraceFilter> traceFilter() {
        FilterRegistrationBean<DefaultTraceFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new DefaultTraceFilter());
        registrationBean.setOrder(TRACE_FILTER);
        return registrationBean;
    }
}
