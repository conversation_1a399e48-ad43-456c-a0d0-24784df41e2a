/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-06-10
**/
@Data
@TableName(value="t_project_approve_group")
public class ProjectApproveGroup implements Serializable {

    @TableId(value = "approve_group_id")
    @ApiModelProperty(value = "审批组主键")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long approveGroupId;

    @TableField(value = "parent_group_id")
    @ApiModelProperty(value = "父审批组id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentGroupId;

    @TableField(value = "approve_id")
    @ApiModelProperty(value = "审批主键")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long approveId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long orderId;

    @TableField(value = "node_id")
    @ApiModelProperty(value = "节点id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;

    @TableField(value = "approve_group")
    @ApiModelProperty(value = "审批组别")
    private String approveGroup;

    @TableField(value = "approve_level")
    @ApiModelProperty(value = "审批层级")
    private String approveLevel;

    @TableField(value = "approve_mode")
    @ApiModelProperty(value = "执行方式")
    private String approveMode;

    @TableField(value = "approve_begin")
    @ApiModelProperty(value = "发起方式")
    private String approveBegin;

    @TableField(value = "approve_status")
    @ApiModelProperty(value = "审批状态")
    private String approveStatus;

    @TableField(value = "approve_result")
    @ApiModelProperty(value = "审批结果")
    private String approveResult;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ProjectApproveGroup source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}