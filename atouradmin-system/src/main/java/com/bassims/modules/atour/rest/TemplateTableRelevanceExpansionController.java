/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateTableRelevanceExpansion;
import com.bassims.modules.atour.service.TemplateTableRelevanceExpansionService;
import com.bassims.modules.atour.service.dto.TemplateTableRelevanceExpansionDto;
import com.bassims.modules.atour.service.dto.TemplateTableRelevanceExpansionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "列表仓库模版关系拓展表管理")
@RequestMapping("/api/templateTableRelevanceExpansion")
public class TemplateTableRelevanceExpansionController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableRelevanceExpansionController.class);

    private final TemplateTableRelevanceExpansionService templateTableRelevanceExpansionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateTableRelevanceExpansionQueryCriteria criteria) throws IOException {
        templateTableRelevanceExpansionService.download(templateTableRelevanceExpansionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateTableRelevanceExpansionDto>>}
    */
    @GetMapping("/list")
    @Log("查询列表仓库模版关系拓展表")
    @ApiOperation("查询列表仓库模版关系拓展表")
    public ResponseEntity<Object> query(TemplateTableRelevanceExpansionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateTableRelevanceExpansionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateTableRelevanceExpansionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询列表仓库模版关系拓展表")
    @ApiOperation("查询列表仓库模版关系拓展表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateTableRelevanceExpansionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增列表仓库模版关系拓展表")
    @ApiOperation("新增列表仓库模版关系拓展表")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateTableRelevanceExpansion resources){
        return new ResponseEntity<>(templateTableRelevanceExpansionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改列表仓库模版关系拓展表")
    @ApiOperation("修改列表仓库模版关系拓展表")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateTableRelevanceExpansion resources){
        templateTableRelevanceExpansionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除列表仓库模版关系拓展表")
    @ApiOperation("删除列表仓库模版关系拓展表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateTableRelevanceExpansionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}