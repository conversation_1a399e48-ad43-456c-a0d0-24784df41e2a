/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-19
**/
@Data
public class ProjectDisclosureProblemDto implements Serializable {

    /** 设计交底问题汇总id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long disclosureProblemId;

    /** 项目id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 现场问题描述 */
    private String descriptionOfOnSiteIssues;

    /** 现场问题图片 */
    private String picturesOfOnSiteIssues;

    /** 处理方案 */
    private String processingPlan;

    /** 变更预计提交时间 */
    private Timestamp expectedSubmissionTimeForChanges;

    /** 责任人 */
    private String responsiblePerson;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否删除 */
    private Boolean isDelete;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 节点编码 */
    private String nodeCode;

}