/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-24
**/
@Data
public class ProjectSafeCivilizedConstructionDto implements Serializable {

    /** 项目证照管理id */
    private Long projectManagementId;

    /** 证照类型名称 */
    private String managementName;

    /** 证照类型code */
    private String managementCode;

    private String remark;

    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    private String createBy;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 修改人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否可以编辑 */
    private String isEdit;

    /** 用户添加的版本 */
    private Integer addedVersion;

    /** 业主上传时间 */
    private Timestamp ownerUploadTime;

    /** 证照审核时间 */
    private Timestamp licenseReviewTime;

    /** 证照审核状态 */
    private String certificateReviewStatus;

    /** 完成状态 */
    private String completionStatus;

    /** 证照完成时间 */
    private Timestamp licenseCompletionTime;

    /** 项目ID */
    private Long projectId;

    /** 关联三级节点的编码 */
    private String relevancyNodeCode;

    /** 关联三级节点的名称 */
    private String relevancyNodeName;
}