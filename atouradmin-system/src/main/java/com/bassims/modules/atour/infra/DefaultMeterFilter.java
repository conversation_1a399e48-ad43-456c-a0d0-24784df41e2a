package com.bassims.modules.atour.infra;

import java.time.Duration;
import io.micrometer.core.instrument.Meter.Id;
import io.micrometer.core.instrument.Meter.Type;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;

public class DefaultMeterFilter implements MeterFilter {

    @SuppressWarnings("deprecation")
    @Override
    public DistributionStatisticConfig configure(Id id, DistributionStatisticConfig config) {
        if (id.getType() == Type.TIMER) {
            return DistributionStatisticConfig.builder()
                    .percentilesHistogram(true)
                    .percentiles(0.5, 0.90, 0.95, 0.99)
                    .serviceLevelObjectives(Duration.ofMillis(50), Duration.ofMillis(100),
                    Duration.ofMillis(200), Duration.ofSeconds(1), Duration.ofSeconds(5))
                .minimumExpectedValue(Duration.ofMillis(1))
                .maximumExpectedValue(Duration.ofSeconds(5)).build().merge(config);
        }
        return config;
    }

}
