/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.bassims.modules.atour.domain.HlmProjectInfoStartWillResult;
import lombok.Data;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-11-06
 **/
@Data
public class HlmProjectInfoUpdateBrandChangeRecordDTO {
    //轮次
    private String round;
    //时间
    private String modificationTime;
    // 品牌
    private String brandName;
    // 经营地址【红线描述】
    private String redLineDescription;
    // 执行标准
    private String productName;
    //上会结论
    private HlmProjectInfoStartWillResult hlmProjectInfoStartWillResult;
    //法务约定工程内容
    private String legalAgreementContent;
    private Integer count;
    private String meetingState;
    private String description;
    private String ossFile;
    private String createTime;

}