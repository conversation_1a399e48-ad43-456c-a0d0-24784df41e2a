/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-01-14
**/
@Data
public class ProjectSpotCheckDto implements Serializable {

    /** 主键id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /** 项目id */

    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 抽查任务名称 */
    private String groupName;

    /** 抽查任务轮次 */
    private Integer groupIndex;

    /** 抽查任务二级code */
    private String nodeCode;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private Timestamp updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 情况说明 */
    private String presentationOndition;

    /** 附件 */
    private String attachment;

    /** 任务状态 */
    private String nodeStatusStr;


    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    @ApiModelProperty(value = "nodeId")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;


}