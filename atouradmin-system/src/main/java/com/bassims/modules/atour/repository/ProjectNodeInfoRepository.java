/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoDto;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoQueryCriteria;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-03-24
 **/
@Repository
public interface ProjectNodeInfoRepository extends BaseMapper<ProjectNodeInfo> {

    List<ProjectNodeInfoDto> selectListByQuery(ProjectNodeInfoQueryCriteria criteria);

    List<ProjectNodeInfoDto> getOverDueNode(Long projectId);

    void updateRoleCode(@Param("projectId") Long projectId, @Param("userid") Long userid, @Param("oldUserId") Long oldUserId);

    ProjectNodeInfoDto getLevelTwoNodeInfo(Long projectId, String nodeCode, List<Long> jobIds,String roundMarking);

    List<ProjectNodeInfoDto> getAllNodeInfo(Long projectId, String useCase, Boolean isMobile, List<Long> jobIds,String nodeCodes);

    /**
     * 根据不同的一级获取对应的二级节点
     */
    List<ProjectNodeInfoDto> getAllNodeInfo1(Long projectId, String useCase, Boolean isMobile, List<Long> jobIds, Long templateId, String roundMarking);

    List<ProjectNodeInfoDto> getAccountOverDueNode(Long projectId);

    List<ProjectNodeInfoDto> getIsNotDoneNode(Long projectId);

    /**
     * 根据项目id、父级id查询
     *
     * @param projectId
     * @param parentId
     * @return
     */
    List<ProjectNodeInfoDto> getMeasureAreaValue(@Param("projectId") Long projectId, @Param("parentId") Long parentId);

    Integer getMaxAddedVersion(@Param("projectId") Long projectId, @Param("parentId") Long parentId);

    List<Map<String, Object>> getAddedVersionNum(@Param("projectId") Long projectId, @Param("parentId") Long parentId);

    String getNodeIdByNodeCode(Long projectId,String nodeCode);
    List<ProjectNodeInfoDto> getNodeInfoByNodeCode(Long projectId,String nodeCode);

    List<ProjectNodeInfoDto> getConstructionLogList(Long projectId,String nodeCode);

    List<ProjectNodeInfoDto> getNodeInfoList(Long projectId,String templateCode);

    List<Long> getRectifyTheProblemByProjectId(Long projectId);

}