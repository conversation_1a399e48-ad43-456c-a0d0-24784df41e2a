/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2023-11-17
 **/
@Data
@TableName(value = "t_hlm_project_info_develope_manager")
public class HlmProjectInfoDevelopeManager implements Serializable {

    @TableId(value = "develope_manager_id")
    @ApiModelProperty(value = "项目拓展表的ID")
    private Long developeManagerId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "hlm项目id")
    private Integer projectId;

    @TableField(value = "hotel_id")
    @ApiModelProperty(value = "酒店ID")
    private Integer hotelId;

    @TableField(value = "employee_id")
    @ApiModelProperty(value = "员工编号")
    private String employeeId;

    @TableField(value = "flower_name")
    @ApiModelProperty(value = "员工花名")
    private String flowerName;

    @TableField(value = "email")
    @ApiModelProperty(value = "email")
    private String email;

    @TableField(value = "mobile")
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @TableField(value = "department_id")
    @ApiModelProperty(value = "部门ID ")
    private String departmentId;

    @TableField(value = "job_name")
    @ApiModelProperty(value = "岗位名称")
    private String jobName;

    public void copy(HlmProjectInfoDevelopeManager source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}