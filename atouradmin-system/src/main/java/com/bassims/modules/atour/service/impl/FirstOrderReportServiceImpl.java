package com.bassims.modules.atour.service.impl;

import com.alibaba.excel.EasyExcel;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.modules.atour.domain.FirstOrderReport;
import com.bassims.modules.atour.repository.FirstOrderReportRepository;
import com.bassims.modules.atour.service.CodeValueConversionService;
import com.bassims.modules.atour.service.FirstOrderReportService;
import com.bassims.modules.atour.service.dto.FirstOrderReportDto;
import com.bassims.modules.atour.service.dto.FirstOrderReportQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.FirstOrderReportMapper;
import com.bassims.utils.PageUtil;
import com.bassims.utils.QueryHelpPlus;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
public class FirstOrderReportServiceImpl extends BaseServiceImpl<FirstOrderReportRepository, FirstOrderReport> implements FirstOrderReportService {

    @Autowired
    private FirstOrderReportMapper firstOrderReportMapper;

    @Autowired
    private CodeValueConversionService codeValueConversionService;

    @Override
    public void download(HttpServletResponse response, FirstOrderReportQueryCriteria criteria) throws IOException {
        String fileName = "甲供材订单报表_" + System.currentTimeMillis() + ".xlsx";

        OutputStream outputStream = response.getOutputStream();
        String fileNameURL = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
        response.setContentType("application/msexcel;charset=UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String sheetName = "甲供材订单报表";
//        List<String> projectTypes = criteria.getProjectTypes();
//        if (ObjectUtils.isNotEmpty(projectTypes) && projectTypes.size() != 0){
//            int index = 1;
//            ExcelWriter excelWriter = null;
//            try {
//                excelWriter = EasyExcel.write(outputStream, ProjectTimeReportDto.class).build();
//                for (int i = 0; i < projectTypes.size(); i++) {
//                    String projectType = projectTypes.get(i);
//                    List<ProjectTimeReportDto> data = getProjectInfos(criteria.getProjectName(), projectType);
//                    if (JhSystemEnum.storeTypeEnum.STORE_NH.getKey().equals(projectType)) {
//                        sheetName = JhSystemEnum.storeTypeEnum.STORE_NH.getSpec();
//                        index = 1;
//                    } else if (JhSystemEnum.storeTypeEnum.STORE_NHS.getKey().equals(projectType)) {
//                        sheetName = JhSystemEnum.storeTypeEnum.STORE_NHS.getSpec();
//                        index = 4;
//                    } else if (JhSystemEnum.storeTypeEnum.STORE_NS.getKey().equals(projectType)) {
//                        sheetName = JhSystemEnum.storeTypeEnum.STORE_NS.getSpec();
//                        index = 3;
//                    } else if (JhSystemEnum.storeTypeEnum.STORE_NSC.getKey().equals(projectType)) {
//                        sheetName = JhSystemEnum.storeTypeEnum.STORE_NSC.getSpec();
//                        index = 2;
//                    }
////                EasyExcel.write(outputStream, ProjectTimeReportDto.class).sheet(i,sheetName).doWrite(data);
//                    WriteSheet writeSheet = EasyExcel.writerSheet(index, sheetName).build();
//                    excelWriter.write(data, writeSheet);
//                }
//            }finally {
//                if (excelWriter != null){
//                    excelWriter.close();
//                }
//            }
//        }else{
//
//        }
        List<FirstOrderReportDto> data = firstOrderReportMapper.toDto(list(QueryHelpPlus.getPredicate(FirstOrderReport.class,criteria)));
        int index = 1;
        for (FirstOrderReportDto datum : data) {
            if (KidsSystemEnum.OrderType.PURCHASE_ORDER.getValue().equals(datum.getOrderType())){
                datum.setOrderType(KidsSystemEnum.OrderType.PURCHASE_ORDER.getLabel());
            }
            String cityCompany = codeValueConversionService.getCityCompany(datum.getCityCompany());
            datum.setCityCompany(cityCompany);
            String projectType = codeValueConversionService.getProjectType(datum.getProjectType());
            datum.setProjectType(projectType);
            String orderStatus = codeValueConversionService.getOrderStatus(datum.getOrderStatus());
            datum.setOrderStatus(orderStatus);
            String costType = codeValueConversionService.getCostType(datum.getCostType());
            datum.setCostType(costType);

            datum.setIndexNo(index++);
        }
        EasyExcel.write(outputStream, FirstOrderReportDto.class).sheet(sheetName).doWrite(data);
    }

    @Override
    public Object queryTime(FirstOrderReportQueryCriteria criteria, Pageable pageable) {
        List<FirstOrderReport> finalList=list(QueryHelpPlus.getPredicate(FirstOrderReport.class,criteria));
        PageInfo<FirstOrderReport> page = new PageInfo<>();
        if(finalList.size()>0){
            List list1 = PageUtil.toPage(pageable.getPageNumber()==0?1:(pageable.getPageNumber()-1), pageable.getPageSize(), finalList);
            page = new PageInfo<FirstOrderReport>(list1);
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", page.getList());
        map.put("totalElements", finalList.size());
        return map;
    }
}
