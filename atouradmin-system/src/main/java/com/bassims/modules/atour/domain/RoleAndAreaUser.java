package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.codehaus.jackson.map.ser.ToStringSerializer;

import java.util.List;

@Data
public class RoleAndAreaUser {

    private Long areaCode;
    private String jobName;
    private List<RoleUser> user;
    /** 防止精度丢失 */
//    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;
    /** 防止精度丢失 */
//    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long   orderId;

    /**
     * 开发经理user_id
     */
    private String  parentId;

    /**
     * 供应商id
    private List<String>  supplierId;

    /**
     * 项目类型
     */
    private String templateCode;


}
