/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.ProjectMtoBaseMateriel;
import com.bassims.modules.atour.domain.UnauthorizedConstruction;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProjectMtoBaseMaterielRepository;
import com.bassims.modules.atour.service.ProjectMtoBaseMaterielService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProjectMtoBaseMaterielDto;
import com.bassims.modules.atour.service.dto.ProjectMtoBaseMaterielQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectMtoBaseMaterielMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-23
**/
@Service
public class ProjectMtoBaseMaterielServiceImpl extends BaseServiceImpl<ProjectMtoBaseMaterielRepository,ProjectMtoBaseMateriel> implements ProjectMtoBaseMaterielService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectMtoBaseMaterielServiceImpl.class);

    @Autowired
    private ProjectMtoBaseMaterielRepository projectMtoBaseMaterielRepository;
    @Autowired
    private ProjectMtoBaseMaterielMapper projectMtoBaseMaterielMapper;

    @Override
    public Map<String,Object> queryAll(ProjectMtoBaseMaterielQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProjectMtoBaseMateriel> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectMtoBaseMateriel.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectMtoBaseMaterielMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectMtoBaseMaterielDto> queryAll(ProjectMtoBaseMaterielQueryCriteria criteria){
        return projectMtoBaseMaterielMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectMtoBaseMateriel.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMtoBaseMaterielDto findById(Long projectMaterielId) {
        ProjectMtoBaseMateriel projectMtoBaseMateriel = Optional.ofNullable(getById(projectMaterielId)).orElseGet(ProjectMtoBaseMateriel::new);
        ValidationUtil.isNull(projectMtoBaseMateriel.getProjectMaterielId(),getEntityClass().getSimpleName(),"projectMaterielId",projectMaterielId);
        return projectMtoBaseMaterielMapper.toDto(projectMtoBaseMateriel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMtoBaseMaterielDto create(ProjectMtoBaseMateriel resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setProjectMaterielId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getProjectMaterielId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectMtoBaseMateriel resources) {
        ProjectMtoBaseMateriel projectMtoBaseMateriel = Optional.ofNullable(getById(resources.getProjectMaterielId())).orElseGet(ProjectMtoBaseMateriel::new);
        ValidationUtil.isNull( projectMtoBaseMateriel.getProjectMaterielId(),"ProjectMtoBaseMateriel","id",resources.getProjectMaterielId());
        projectMtoBaseMateriel.copy(resources);
        updateById(projectMtoBaseMateriel);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectMaterielId : ids) {
            projectMtoBaseMaterielRepository.deleteById(projectMaterielId);
        }
    }

    @Override
    public void download(List<ProjectMtoBaseMaterielDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectMtoBaseMaterielDto projectMtoBaseMateriel : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("物料分类ID", projectMtoBaseMateriel.getClassId());
            map.put("物料编码", projectMtoBaseMateriel.getMaterielNo());
            map.put("物料名称（品名）", projectMtoBaseMateriel.getMaterielName());
            map.put("创建时间", projectMtoBaseMateriel.getCreateTime());
            map.put("创建人", projectMtoBaseMateriel.getCreateUser());
            map.put("创建部门", projectMtoBaseMateriel.getCreateDept());
            map.put("更新时间", projectMtoBaseMateriel.getUpdateTime());
            map.put("更新人", projectMtoBaseMateriel.getUpdateUser());
            map.put("是否已删除：0 正常，1 已删除", projectMtoBaseMateriel.getIsDelete());
            map.put("租户编号", projectMtoBaseMateriel.getTenantId());
            map.put("材料图样", projectMtoBaseMateriel.getMaterialPattern());
            map.put("材料性能参数", projectMtoBaseMateriel.getMaterialPerformanceParameter());
            map.put("合同约定必采", projectMtoBaseMateriel.getAgreeTake());
            map.put("项目ID", projectMtoBaseMateriel.getProjectId());
            map.put("备注", projectMtoBaseMateriel.getRemark());
            map.put("是否平台采购（1-是，0-否）", projectMtoBaseMateriel.getIsPlatformProcurement());
            map.put("下单时间", projectMtoBaseMateriel.getOrderTime());
            map.put("基础物料Id", projectMtoBaseMateriel.getMaterielId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<ProjectMtoBaseMaterielDto> queryByProjectId(ProjectMtoBaseMaterielQueryCriteria criteria) {
        return projectMtoBaseMaterielMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectMtoBaseMateriel.class, criteria)));
    }
}