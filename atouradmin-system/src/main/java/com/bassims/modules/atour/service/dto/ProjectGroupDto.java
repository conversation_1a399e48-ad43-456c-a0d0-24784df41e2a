/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-08-29
 **/
@Data
public class ProjectGroupDto implements Serializable {

    /**
     * 项目一二级主键
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String projectGroupId;

    /**
     * 项目id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String projectId;

    /**
     * 订单id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String orderId;

    /**
     * 设计id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String designId;

    /**
     * 模板组主键
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String templateGroupId;

    /**
     * 二级id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String templateQueueId;

    /**
     * 模板主键
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String templateId;

    private String templateCode;

    /**
     * 父节点
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String parentId;

    /**
     * 名称
     */
    private String nodeName;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 子节点下标
     */
    private double nodeIndex;

    /**
     * 节点序号
     */
    private Integer nodeWbs;

    /**
     * 前置任务配置[{"type":"FS","wbs":11},{"type":"SS","wbs":12}]
     */
    private String frontWbsConfig;

    /**
     * 前置完成条件
     */
    private String frontFinishWbs;

    /**
     * 是否是关键节点
     */
    private Boolean isKey;

    /**
     * 关键节点前置任务
     */
    private String keyFrontWbs;

    /**
     * 计划需要完成天数
     */
    private Integer planDay;

    /**
     * 节点等级
     */
    private Integer nodeLevel;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 延期天数
     */
    private Integer delayDay;

    /**
     * 计划开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Timestamp planStartDate;

    /**
     * 计划结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Timestamp planEndDate;

    private String planEndDateCol;

    /**
     * 预估开始日期
     */
    private Timestamp predictStartDate;

    /**
     * 预估结束日期 (开工申请/设计勘测/设计启动 约定完成时间)
     */
    private Timestamp predictEndDate;

    /**
     * 实际完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp actualEndDate;

    /**
     * 提醒天数
     */
    private Integer noticeDay;

    /**
     * 使用场景
     */
    private String useCase;

    /**
     * 节点是否打开
     */
    private Boolean isOpen;

    /**
     * 节点状态
     */
    private String nodeStatus;

    /**
     * 已完成按钮
     */
    private Boolean nodeIsfin;

    /**
     * 是否可用
     */
    private Boolean isEnabled;

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    private String createBy;

    private String updateBy;

    /**
     * 总工期
     */
    private Integer totalDay;

    /**
     * 是否是手机端
     */
    private Boolean isMobile;

    /**
     * 责任人角色code
     */
    private String roleCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 小程序标志
     */
    private String icon;

    private String progress;

    private Boolean isDelay;
    /**
     * 阶段状态
     */
    private String phaseStatus;

    /**
     * 读权限
     */
    private Boolean isRead;

    /**
     * 写权限
     */
    private Boolean isWrite;

    /**
     * 隐藏权限
     */
    private Boolean isHidden;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private String nodeId;

    /**
     * 是否提交
     */
    private Boolean isSubmit;

    /**
     * 是否有第三方审批
     */
    private Boolean isThird;

    /**
     * 三方审批流程实例id
     */
    private String processInstanceId;

    /**
     * 签证工期
     */
    private Timestamp visaDuration;

    /**
     * 实际工期
     */
    private Timestamp actualDuration;

    /**
     * 标识是否已经配置了输出条件/联合条件
     */
    private String taskFlag;


    /*报备/签证的完成时间   预估完成时间*/
    private String ownerPlansEndDate;


   /*是否展示（0展示、1不展示）*/
    private Integer isShow;


    /**计划事件名称*/
    private String planEventName;

    /**计划事件对应模版节点的计划结束时间*/
    @JSONField(format = "yyyy-MM-dd")
    private Date planeventPlanEndDate;



    /*是否有计划事件(0没有计划事件1、有计划事件)*/
    private Integer isPlannedEvent;


    /*抄送角色*/
    private String carbonCopyRoleCode;

    /*轮次标识*/
    private String roundMarking;

    /*阶段名称*/
    private String phaseName;



    /*二级任务名称*/
    private String nodeNameTask;

    /*二级code*/
    private String nodeCodeTask;

    /*阶段节点名称（工程、设计）*/
    private String stageNodeName;

}