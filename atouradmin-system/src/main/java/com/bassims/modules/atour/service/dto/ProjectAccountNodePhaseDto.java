package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.sql.Date;

@Data
public class ProjectAccountNodePhaseDto {
    private Date createTime;
    /**
     * 结算资料提交
     */
    private String accountSubmitCode;
    private Integer accountSubmitNum;
    /**
     * 结算资料确认
     */
    private String accountConfirmCode;
    private Integer accountConfirmNum;
    /**
     * 结算审核完成
     */
    private String accountFinishCode;
    private Integer accountFinishNum;
    /**
     * 增补预算完成审批
     */
    private String budgetFinishCode;
    private Integer budgetFinishNum;
    /**
     * 增补预算充值完成
     */
    private String budgetRechargeCode;
    private Integer budgetRechargeNum;
    /**
     * 增补PR完成审批
     */
    private String prApprovalCode;
    private Integer prApprovalNum;
    /**
     * 结算合同完成
     */
    private String contractFinishCode;
    private Integer contractFinishNum;
    private Integer projectTotal;
}
