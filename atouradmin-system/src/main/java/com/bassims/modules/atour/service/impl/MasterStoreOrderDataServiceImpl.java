/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.MasterStoreOrderData;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.MasterStoreOrderDataRepository;
import com.bassims.modules.atour.service.MasterStoreOrderDataService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.MasterStoreOrderDataDto;
import com.bassims.modules.atour.service.dto.MasterStoreOrderDataQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MasterStoreOrderDataMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-22
**/
@Service
public class MasterStoreOrderDataServiceImpl extends BaseServiceImpl<MasterStoreOrderDataRepository,MasterStoreOrderData> implements MasterStoreOrderDataService {

    private static final Logger logger = LoggerFactory.getLogger(MasterStoreOrderDataServiceImpl.class);

    @Autowired
    private MasterStoreOrderDataRepository masterStoreOrderDataRepository;
    @Autowired
    private MasterStoreOrderDataMapper masterStoreOrderDataMapper;

    @Override
    public Map<String,Object> queryAll(MasterStoreOrderDataQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<MasterStoreOrderData> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MasterStoreOrderData.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", masterStoreOrderDataMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<MasterStoreOrderDataDto> queryAll(MasterStoreOrderDataQueryCriteria criteria){
        return masterStoreOrderDataMapper.toDto(list(QueryHelpPlus.getPredicate(MasterStoreOrderData.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterStoreOrderDataDto findById(Long storeOrderId) {
        MasterStoreOrderData masterStoreOrderData = Optional.ofNullable(getById(storeOrderId)).orElseGet(MasterStoreOrderData::new);
        ValidationUtil.isNull(masterStoreOrderData.getStoreOrderId(),getEntityClass().getSimpleName(),"storeOrderId",storeOrderId);
        return masterStoreOrderDataMapper.toDto(masterStoreOrderData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterStoreOrderDataDto create(MasterStoreOrderData resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setStoreOrderId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getStoreOrderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterStoreOrderData resources) {
        MasterStoreOrderData masterStoreOrderData = Optional.ofNullable(getById(resources.getStoreOrderId())).orElseGet(MasterStoreOrderData::new);
        ValidationUtil.isNull( masterStoreOrderData.getStoreOrderId(),"MasterStoreOrderData","id",resources.getStoreOrderId());
        masterStoreOrderData.copy(resources);
        updateById(masterStoreOrderData);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long storeOrderId : ids) {
            masterStoreOrderDataRepository.deleteById(storeOrderId);
        }
    }

    @Override
    public void download(List<MasterStoreOrderDataDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterStoreOrderDataDto masterStoreOrderData : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("操作", masterStoreOrderData.getOperate());
            map.put("订单编号", masterStoreOrderData.getOrderNo());
            map.put("订单id", masterStoreOrderData.getOrderId());
            map.put("门店id", masterStoreOrderData.getStoreId());
            map.put("门店编号", masterStoreOrderData.getStoreNo());
            map.put("订单类型", masterStoreOrderData.getOrderType());
            map.put("费用类型", masterStoreOrderData.getExpenseType());
            map.put("所属项目", masterStoreOrderData.getProject());
            map.put("订单金额(元)", masterStoreOrderData.getOrderAmount());
            map.put("订单结算金额(元)", masterStoreOrderData.getOrderSettlementAmount());
            map.put("订单状态", masterStoreOrderData.getOrderStatus());
            map.put("下单时间", masterStoreOrderData.getOrderTime());
            map.put("要求到货时间 ", masterStoreOrderData.getArrivalTime());
            map.put("供应商 ", masterStoreOrderData.getSupplier());
            map.put("收货人 ", masterStoreOrderData.getConsignee());
            map.put("特殊项目金额(元) ", masterStoreOrderData.getSpecialCost());
            map.put("原订单金额(元) ", masterStoreOrderData.getOriginalOrderAmount());
            map.put("送审金额(元) ", masterStoreOrderData.getApproveAmount());
            map.put("所属门店 ", masterStoreOrderData.getStoreOwner());
            map.put("门店类型 ", masterStoreOrderData.getStoreType());
            map.put("门店 ", masterStoreOrderData.getStore());
            map.put("采购专项说明 ", masterStoreOrderData.getProcurementDesc());
            map.put("订单创建人 ", masterStoreOrderData.getOrderCreator());
            map.put("订单创建时间 ", masterStoreOrderData.getOrderCreatorTime());
            map.put("订单创建人联系电话 ", masterStoreOrderData.getOrderCreatorPhone());
            map.put("工程经理 ", masterStoreOrderData.getEngineeringManager());
            map.put("工程经理电话 ", masterStoreOrderData.getEngineeringManagerPhone());
            map.put("要求开始安装时间 ", masterStoreOrderData.getStartInstallTime());
            map.put("要求结束安装时间 ", masterStoreOrderData.getStartEndTime());
            map.put("订单及制作文件 ", masterStoreOrderData.getOrderProDocu());
            map.put("物流单号 ", masterStoreOrderData.getLogisticsOderNo());
            map.put("发货人 ", masterStoreOrderData.getConsignor());
            map.put("物流信息联系电话 ", masterStoreOrderData.getLogisticsNum());
            map.put("发货时间 ", masterStoreOrderData.getDeliveryTime());
            map.put("订购单号", masterStoreOrderData.getPurchaseNo());
            map.put("采购单号 ", masterStoreOrderData.getPoNo());
            map.put("付款时间", masterStoreOrderData.getPayTime());
            map.put(" createTime",  masterStoreOrderData.getCreateTime());
            map.put(" createBy",  masterStoreOrderData.getCreateBy());
            map.put(" updateTime",  masterStoreOrderData.getUpdateTime());
            map.put(" updateBy",  masterStoreOrderData.getUpdateBy());
            map.put("是否删除", masterStoreOrderData.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}