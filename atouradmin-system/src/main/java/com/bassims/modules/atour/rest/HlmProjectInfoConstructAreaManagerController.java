/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.HlmProjectInfoConstructAreaManager;
import com.bassims.modules.atour.service.HlmProjectInfoConstructAreaManagerService;
import com.bassims.modules.atour.service.dto.HlmProjectInfoConstructAreaManagerDto;
import com.bassims.modules.atour.service.dto.HlmProjectInfoConstructAreaManagerQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-11-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "hlm项目-营建区域负责人管理")
@RequestMapping("/api/hlmProjectInfoConstructAreaManager")
public class HlmProjectInfoConstructAreaManagerController {

    private static final Logger logger = LoggerFactory.getLogger(HlmProjectInfoConstructAreaManagerController.class);

    private final HlmProjectInfoConstructAreaManagerService hlmProjectInfoConstructAreaManagerService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, HlmProjectInfoConstructAreaManagerQueryCriteria criteria) throws IOException {
        hlmProjectInfoConstructAreaManagerService.download(hlmProjectInfoConstructAreaManagerService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<HlmProjectInfoConstructAreaManagerDto>>}
    */
    @GetMapping("/list")
    @Log("查询hlm项目-营建区域负责人")
    @ApiOperation("查询hlm项目-营建区域负责人")
    public ResponseEntity<Object> query(HlmProjectInfoConstructAreaManagerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hlmProjectInfoConstructAreaManagerService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<HlmProjectInfoConstructAreaManagerDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询hlm项目-营建区域负责人")
    @ApiOperation("查询hlm项目-营建区域负责人")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(hlmProjectInfoConstructAreaManagerService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增hlm项目-营建区域负责人")
    @ApiOperation("新增hlm项目-营建区域负责人")
    public ResponseEntity<Object> create(@Validated @RequestBody HlmProjectInfoConstructAreaManager resources){
        return new ResponseEntity<>(hlmProjectInfoConstructAreaManagerService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改hlm项目-营建区域负责人")
    @ApiOperation("修改hlm项目-营建区域负责人")
    public ResponseEntity<Object> update(@Validated @RequestBody HlmProjectInfoConstructAreaManager resources){
        hlmProjectInfoConstructAreaManagerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除hlm项目-营建区域负责人")
    @ApiOperation("删除hlm项目-营建区域负责人")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        hlmProjectInfoConstructAreaManagerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}