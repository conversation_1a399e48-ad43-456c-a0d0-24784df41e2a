/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-21
**/
@Data
@TableName(value="s_reimburse_item")
public class ReimburseItem implements Serializable {

    @TableId(value = "reimburse_item_id")
    @ApiModelProperty(value = "主键id")
    private Long reimburseItemId;

    @TableField(value = "reimburse_id")
    @ApiModelProperty(value = "费用报销合计id")
    private Long reimburseId;

    @TableField(value = "cost_first_code")
    @ApiModelProperty(value = "费用一级")
    private String costFirstCode;

    @TableField(value = "cost_second_code")
    @ApiModelProperty(value = "费用二级")
    private String costSecondCode;

    @TableField(value = "cost_third_code")
    @ApiModelProperty(value = "费用三级")
    private String costThirdCode;

    @TableField(value = "expense_dept")
    @ApiModelProperty(value = "费用承担部门")
    private String expenseDept;

    @TableField(value = "expense_dept_code")
    @ApiModelProperty(value = "费用承担部门code")
    private String expenseDeptCode;

    @TableField(value = "expense_fee")
    @ApiModelProperty(value = "报销金额")
    private BigDecimal expenseFee;

    @TableField(value = "tax_ratio")
    @ApiModelProperty(value = "税率")
    private String taxRatio;

    @TableField(value = "tax_fee")
    @ApiModelProperty(value = "税额")
    private BigDecimal taxFee;

    @TableField(value = "expense_to_tax_fee")
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal expenseToTaxFee;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ReimburseItem source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}