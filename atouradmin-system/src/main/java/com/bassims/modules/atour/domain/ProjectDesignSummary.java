package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "project_design_summary")
public class ProjectDesignSummary implements Serializable {

    @TableId(value = "project_id")
    private Long projectId;
    private String projectName;
    private String storeName;
    private String storeNo;
    private String storeType;
    private String actualOpenDate;
    private String designer;
    private String rentOutTime;
    private String planeOutTime;
    private String buildOutTime;
    private String areaOutTime;
    private String jointOutTime;
    private String appreciationOutTime;
    private String lampsNum;
    private String woodenShelfNum;
    private String steelShelfNum;
}
