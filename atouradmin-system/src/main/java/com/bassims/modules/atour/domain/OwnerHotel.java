/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description / 业主和酒店的对应关系表
* <AUTHOR>
* @date 2023-12-22
**/
@Data
@TableName(value="t_owner_hotel")
public class OwnerHotel implements Serializable {

    @TableId(value = "owner_hotel_id")
    @ApiModelProperty(value = "id")
    private Long ownerHotelId;

    @TableField(value = "owner_user_id")
    @ApiModelProperty(value = "业主人员id")
    private Long ownerUserId;

    @TableField(value = "hotel_id")
    @ApiModelProperty(value = "酒店ID")
    private String hotelId;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否已删除：0 正常，1 已删除")
    private Boolean isDelete;

    public void copy(OwnerHotel source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}