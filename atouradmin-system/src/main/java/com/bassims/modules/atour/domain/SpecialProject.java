/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.math.BigDecimal;
import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-10-09
**/
@Data
@TableName(value="t_special_project")
public class SpecialProject implements Serializable {

    @TableId(value = "special_id")
    @ApiModelProperty(value = "主键")
    private Long specialId;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @TableField(value = "special_project_name")
    @ApiModelProperty(value = "特殊项目名称")
    private String specialProjectName;

    @TableField(value = "account_adjust_num")
    @ApiModelProperty(value = "核减列")
    private BigDecimal accountAdjustNum;

    @TableField(value = "approved_num")
    @ApiModelProperty(value = "审定数量")
    private BigDecimal approvedNum;

    @TableField(value = "num")
    @ApiModelProperty(value = "数量")
    private BigDecimal  num;

    @TableField(value = "price")
    @ApiModelProperty(value = "单价(元)")
    private BigDecimal price;

    @TableField(value = "total_price")
    @ApiModelProperty(value = "总价(元)")
    private BigDecimal totalPrice;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(exist = false)
    @ApiModelProperty(value = "资产编码")
    private String financeCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否配件")
    private Integer isMatch;

    public void copy(SpecialProject source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}