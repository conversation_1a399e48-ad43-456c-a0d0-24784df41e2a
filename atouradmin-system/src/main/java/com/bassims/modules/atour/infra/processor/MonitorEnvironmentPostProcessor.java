package com.bassims.modules.atour.infra.processor;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * 监控环境变量处理
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class MonitorEnvironmentPostProcessor implements EnvironmentPostProcessor {
    private static final String PROMETHEUS_ENABLE_CONFIG = "metrics.export.prometheus.enabled";
    private static final String MANAGEMENT_ENDPOINT_EXPOSURE_CONFIG = "management.endpoints.web.exposure.include";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // prometheus开启校验
        String promethusEnable = environment.getProperty(PROMETHEUS_ENABLE_CONFIG);
        if (StrUtil.isBlank(promethusEnable)) {
            environment.getSystemProperties().put(PROMETHEUS_ENABLE_CONFIG, true);
        }
        String managementEndPointIncludes = environment.getProperty(MANAGEMENT_ENDPOINT_EXPOSURE_CONFIG);
        if (StrUtil.isBlank(managementEndPointIncludes)) {
            environment.getSystemProperties().put(MANAGEMENT_ENDPOINT_EXPOSURE_CONFIG, "info,health,prometheus");
        } else {
            if (!StrUtil.contains(managementEndPointIncludes, "*")
                && !StrUtil.contains(managementEndPointIncludes, "prometheus")) {
                if (managementEndPointIncludes.endsWith(",")) {
                    managementEndPointIncludes = managementEndPointIncludes.concat("prometheus");
                } else {
                    managementEndPointIncludes = managementEndPointIncludes.concat(",prometheus");
                }
                environment.getSystemProperties().put(MANAGEMENT_ENDPOINT_EXPOSURE_CONFIG, managementEndPointIncludes);
            }
        }
        log.info("[monitor]初始化environment config成功");
    }

}
