/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-24
**/
@Data
public class TemplateSafeCivilizedConstructionDto implements Serializable {

    /** 安全文明施工模版id */
    private Long templateConstructionId;

    /** 安全文明施工模版名称 */
    private String templateConstructionName;

    /** 安全文明施工模版编码 */
    private String templateConstructionCode;

    /** 证照管理id */
    private Long constructionId;

    /** 证照管理code */
    private String constructionCode;

    /** 关联三级节点的编码 */
    private String relevancyNodeCode;

    /** 关联三级节点的名称 */
    private String relevancyNodeName;

    private Boolean isDelete;

    /** 项目版本号 */
    private String projectVersion;

    /** 创建时间 */
    private Timestamp createTime;

    private String createBy;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 修改人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否可以编辑 */
    private String isEdit;

    /** 项目类型（码值project_type） */
    private String projectType;

    /** 所属品牌 */
    private String brand;

    /** 门店类型 */
    private String storeType;
}