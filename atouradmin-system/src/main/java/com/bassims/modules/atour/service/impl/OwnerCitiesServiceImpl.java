/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.OwnerCities;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.OwnerCitiesRepository;
import com.bassims.modules.atour.service.OwnerCitiesService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.OwnerCitiesDto;
import com.bassims.modules.atour.service.dto.OwnerCitiesQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.OwnerCitiesMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-10
**/
@Service
public class OwnerCitiesServiceImpl extends BaseServiceImpl<OwnerCitiesRepository,OwnerCities> implements OwnerCitiesService {

    private static final Logger logger = LoggerFactory.getLogger(OwnerCitiesServiceImpl.class);

    @Autowired
    private OwnerCitiesRepository ownerCitiesRepository;
    @Autowired
    private OwnerCitiesMapper ownerCitiesMapper;

    @Override
    public Map<String,Object> queryAll(OwnerCitiesQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<OwnerCities> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(OwnerCities.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", ownerCitiesMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<OwnerCitiesDto> queryAll(OwnerCitiesQueryCriteria criteria){
        return ownerCitiesMapper.toDto(list(QueryHelpPlus.getPredicate(OwnerCities.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OwnerCitiesDto findById(Long ownerCitiesId) {
        OwnerCities ownerCities = Optional.ofNullable(getById(ownerCitiesId)).orElseGet(OwnerCities::new);
        ValidationUtil.isNull(ownerCities.getOwnerCitiesId(),getEntityClass().getSimpleName(),"ownerCitiesId",ownerCitiesId);
        return ownerCitiesMapper.toDto(ownerCities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OwnerCitiesDto create(OwnerCities resources) {
        save(resources);
        return findById(resources.getOwnerCitiesId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OwnerCities resources) {
        OwnerCities ownerCities = Optional.ofNullable(getById(resources.getOwnerCitiesId())).orElseGet(OwnerCities::new);
        ValidationUtil.isNull( ownerCities.getOwnerCitiesId(),"OwnerCities","id",resources.getOwnerCitiesId());
        ownerCities.copy(resources);
        updateById(ownerCities);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long ownerCitiesId : ids) {
            ownerCitiesRepository.deleteById(ownerCitiesId);
        }
    }

    @Override
    public void download(List<OwnerCitiesDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OwnerCitiesDto ownerCities : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("业主id", ownerCities.getOwnerId());
            map.put(" cityId",  ownerCities.getCityId());
            map.put("创建时间", ownerCities.getCreateTime());
            map.put("创建人", ownerCities.getCreateBy());
            map.put("更新时间", ownerCities.getUpdateTime());
            map.put("更新人", ownerCities.getUpdateBy());
            map.put("是否可用", ownerCities.getIsEnabled());
            map.put("是否删除", ownerCities.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}