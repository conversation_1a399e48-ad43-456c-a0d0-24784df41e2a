/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectSafeCivilizedConstruction;
import com.bassims.modules.atour.service.ProjectSafeCivilizedConstructionService;
import com.bassims.modules.atour.service.dto.ProjectSafeCivilizedConstructionDto;
import com.bassims.modules.atour.service.dto.ProjectSafeCivilizedConstructionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-24
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目的安全文明施工管理管理")
@RequestMapping("/api/projectSafeCivilizedConstruction")
public class ProjectSafeCivilizedConstructionController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSafeCivilizedConstructionController.class);

    private final ProjectSafeCivilizedConstructionService projectSafeCivilizedConstructionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectSafeCivilizedConstructionQueryCriteria criteria) throws IOException {
        projectSafeCivilizedConstructionService.download(projectSafeCivilizedConstructionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProjectSafeCivilizedConstructionDto>>}
    */
    @GetMapping("/list")
    @Log("查询项目的安全文明施工管理")
    @ApiOperation("查询项目的安全文明施工管理")
    public ResponseEntity<Object> query(ProjectSafeCivilizedConstructionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectSafeCivilizedConstructionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProjectSafeCivilizedConstructionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询项目的安全文明施工管理")
    @ApiOperation("查询项目的安全文明施工管理")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectSafeCivilizedConstructionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目的安全文明施工管理")
    @ApiOperation("新增项目的安全文明施工管理")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectSafeCivilizedConstruction resources){
        return new ResponseEntity<>(projectSafeCivilizedConstructionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目的安全文明施工管理")
    @ApiOperation("修改项目的安全文明施工管理")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectSafeCivilizedConstruction resources){
        projectSafeCivilizedConstructionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目的安全文明施工管理")
    @ApiOperation("删除项目的安全文明施工管理")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectSafeCivilizedConstructionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}