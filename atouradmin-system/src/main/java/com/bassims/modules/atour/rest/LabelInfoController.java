/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.LabelInfo;
import com.bassims.modules.atour.service.LabelInfoService;
import com.bassims.modules.atour.service.dto.LabelInfoDto;
import com.bassims.modules.atour.service.dto.LabelInfoQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-12-01
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "标签信息管理")
@RequestMapping("/api/labelInfo")
public class LabelInfoController {

    private static final Logger logger = LoggerFactory.getLogger(LabelInfoController.class);

    private final LabelInfoService labelInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, LabelInfoQueryCriteria criteria) throws IOException {
        labelInfoService.download(labelInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<LabelInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询标签信息")
    @ApiOperation("查询标签信息")
    public ResponseEntity<Object> query(LabelInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(labelInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<LabelInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询标签信息")
    @ApiOperation("查询标签信息")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(labelInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增标签信息")
    @ApiOperation("新增标签信息")
    public ResponseEntity<Object> create(@Validated @RequestBody LabelInfo resources){
        return new ResponseEntity<>(labelInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改标签信息")
    @ApiOperation("修改标签信息")
    public ResponseEntity<Object> update(@Validated @RequestBody LabelInfo resources){
        labelInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除标签信息")
    @ApiOperation("删除标签信息")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        labelInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}