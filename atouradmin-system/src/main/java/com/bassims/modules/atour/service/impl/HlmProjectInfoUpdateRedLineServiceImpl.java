/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.HlmProjectInfoUpdateRedLine;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.HlmProjectInfoUpdateRedLineRepository;
import com.bassims.modules.atour.service.HlmProjectInfoUpdateRedLineService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.HlmProjectInfoUpdateRedLineDto;
import com.bassims.modules.atour.service.dto.HlmProjectInfoUpdateRedLineQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.HlmProjectInfoUpdateRedLineMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-06
 **/
@Service
public class HlmProjectInfoUpdateRedLineServiceImpl extends BaseServiceImpl<HlmProjectInfoUpdateRedLineRepository, HlmProjectInfoUpdateRedLine> implements HlmProjectInfoUpdateRedLineService {

    private static final Logger logger = LoggerFactory.getLogger(HlmProjectInfoUpdateRedLineServiceImpl.class);

    @Autowired
    private HlmProjectInfoUpdateRedLineRepository hlmProjectInfoUpdateRedLineRepository;
    @Autowired
    private HlmProjectInfoUpdateRedLineMapper hlmProjectInfoUpdateRedLineMapper;

    @Override
    public Map<String, Object> queryAll(HlmProjectInfoUpdateRedLineQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<HlmProjectInfoUpdateRedLine> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(HlmProjectInfoUpdateRedLine.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", hlmProjectInfoUpdateRedLineMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<HlmProjectInfoUpdateRedLineDto> queryAll(HlmProjectInfoUpdateRedLineQueryCriteria criteria) {
        return hlmProjectInfoUpdateRedLineMapper.toDto(list(QueryHelpPlus.getPredicate(HlmProjectInfoUpdateRedLine.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HlmProjectInfoUpdateRedLineDto findById(Long updateRedLineId) {
        HlmProjectInfoUpdateRedLine hlmProjectInfoUpdateRedLine = Optional.ofNullable(getById(updateRedLineId)).orElseGet(HlmProjectInfoUpdateRedLine::new);
        ValidationUtil.isNull(hlmProjectInfoUpdateRedLine.getUpdateRedLineId(), getEntityClass().getSimpleName(), "updateRedLineId", updateRedLineId);
        return hlmProjectInfoUpdateRedLineMapper.toDto(hlmProjectInfoUpdateRedLine);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HlmProjectInfoUpdateRedLineDto create(HlmProjectInfoUpdateRedLine resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setUpdateRedLineId(snowflake.nextId());
        save(resources);
        return findById(resources.getUpdateRedLineId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(HlmProjectInfoUpdateRedLine resources) {
        HlmProjectInfoUpdateRedLine hlmProjectInfoUpdateRedLine = Optional.ofNullable(getById(resources.getUpdateRedLineId())).orElseGet(HlmProjectInfoUpdateRedLine::new);
        ValidationUtil.isNull(hlmProjectInfoUpdateRedLine.getUpdateRedLineId(), "HlmProjectInfoUpdateRedLine", "id", resources.getUpdateRedLineId());
        hlmProjectInfoUpdateRedLine.copy(resources);
        updateById(hlmProjectInfoUpdateRedLine);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long updateRedLineId : ids) {
            hlmProjectInfoUpdateRedLineRepository.deleteById(updateRedLineId);
        }
    }

    @Override
    public void download(List<HlmProjectInfoUpdateRedLineDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (HlmProjectInfoUpdateRedLineDto hlmProjectInfoUpdateRedLine : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("酒店ID", hlmProjectInfoUpdateRedLine.getHotelId());
            map.put("变更记录ID", hlmProjectInfoUpdateRedLine.getChangeNo());
            map.put("变更后地址", hlmProjectInfoUpdateRedLine.getAfterAddress());
            map.put("变更后房间数量", hlmProjectInfoUpdateRedLine.getAfterRoomQuantity());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}