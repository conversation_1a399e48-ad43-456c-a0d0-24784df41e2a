/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-04-18
**/
@Data
public class ProjectTaskInfoDto implements Serializable {

    /** 任务表主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectTaskId;

    /** 任务名称 */
    private String taskName;

    /** 任务类型 */
    private String taskType;

    /** 项目id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 节点id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;

    /** 节点id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long storeId;

    /** 使用场景 */
    private String useCase;

    /** 节点code */
    private String nodeCode;

    /** 项目名称 */
    private String projectName;

    /** 门店类型 */
    private String storeType;

    /** 项目类型 */
    private String projectType;

    /** 节点名称 */
    private String nodeName;

    /** 角色id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long jobId;

    /** 用户名 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /** 角色名称 */
    private String jobName;
    /**
     * 城市公司
     */
    private String cityCompany;

    /** 计划结束时间 */
    @JSONField(format = "yyyy-MM-dd")
    private Date planEndDate;

    /** 任务状态 */
    private String taskStatus;

    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    private String createBy;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 修改人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /**模板id*/
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;



    /** 父级节点id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentNodeId;

    /** 父级节点code */
    private String parentNodeCode;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long orderId;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long city;

    private String projectNo;


    /**跳转地址*/
    private String address;


    /**节点状态（待审批、待提交）*/
    private String nodeStatus;
    /**轮次标识*/
    private String roundMarking;





    /**项目节点*/
    private String projectNode;
    /** 项目阶段 */
    private String projectStage;


   /*二级pid*/
    private String parentId;

}