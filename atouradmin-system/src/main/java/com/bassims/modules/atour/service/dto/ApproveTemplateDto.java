/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-30
**/
@Data
public class ApproveTemplateDto implements Serializable {

    /** 审批模板表主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long approveTemplateId;

    /** 审批编码 */
    private String appTempleteCode;

    /** 审批名称 */
    private String appTemplateName;

    /** 备注说明 */
    private String appTemplateRemark;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;
    /** 是否选择*/
    private Boolean isSelected;
}