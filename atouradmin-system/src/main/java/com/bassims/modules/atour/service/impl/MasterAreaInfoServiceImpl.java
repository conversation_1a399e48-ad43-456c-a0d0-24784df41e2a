/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.MasterAreaInfo;
import com.bassims.modules.atour.repository.MasterAreaInfoRepository;
import com.bassims.modules.atour.service.MasterAreaInfoService;
import com.bassims.modules.atour.service.dto.MasterAreaInfoDto;
import com.bassims.modules.atour.service.dto.MasterAreaInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MasterAreaInfoMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-11-23
 **/
@Service
public class MasterAreaInfoServiceImpl extends BaseServiceImpl<MasterAreaInfoRepository, MasterAreaInfo> implements MasterAreaInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MasterAreaInfoServiceImpl.class);

    @Autowired
    private MasterAreaInfoRepository masterAreaInfoRepository;
    @Autowired
    private MasterAreaInfoMapper masterAreaInfoMapper;

    @Override
    public Map<String, Object> queryAll(MasterAreaInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<MasterAreaInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MasterAreaInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", masterAreaInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<MasterAreaInfoDto> queryAll(MasterAreaInfoQueryCriteria criteria) {
        return masterAreaInfoMapper.toDto(list(QueryHelpPlus.getPredicate(MasterAreaInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterAreaInfoDto findById(Long areaId) {
        MasterAreaInfo masterAreaInfo = Optional.ofNullable(getById(areaId)).orElseGet(MasterAreaInfo::new);
        ValidationUtil.isNull(masterAreaInfo.getAreaId(), getEntityClass().getSimpleName(), "areaId", areaId);
        return masterAreaInfoMapper.toDto(masterAreaInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterAreaInfoDto create(MasterAreaInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setAreaId(snowflake.nextId());
        save(resources);
        return findById(resources.getAreaId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterAreaInfo resources) {
        MasterAreaInfo masterAreaInfo = Optional.ofNullable(getById(resources.getAreaId())).orElseGet(MasterAreaInfo::new);
        ValidationUtil.isNull(masterAreaInfo.getAreaId(), "MasterAreaInfo", "id", resources.getAreaId());
        masterAreaInfo.copy(resources);
        updateById(masterAreaInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long areaId : ids) {
            masterAreaInfoRepository.deleteById(areaId);
        }
    }

    @Override
    public void download(List<MasterAreaInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterAreaInfoDto masterAreaInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("门店id", masterAreaInfo.getStoreId());
            map.put("门店编号", masterAreaInfo.getStoreNo());
            map.put("外服童鞋(m²)", masterAreaInfo.getKidShoes());
            map.put("内衣家纺(m²)", masterAreaInfo.getLingerieHomeTextile());
            map.put("玩具(m²)", masterAreaInfo.getToys());
            map.put("文教智能(m²)", masterAreaInfo.getCulturalEdu());
            map.put("用品(m²)", masterAreaInfo.getArticles());
            map.put("车床椅(m²)", masterAreaInfo.getLatheChair());
            map.put("奶粉(m²)", masterAreaInfo.getPowderedMilk());
            map.put("纸尿裤(m²)", masterAreaInfo.getDiapers());
            map.put("零食辅食(m²)", masterAreaInfo.getSnackFood());
            map.put("营养保健(m²)", masterAreaInfo.getNutritionHealth());
            map.put(" createTime", masterAreaInfo.getCreateTime());
            map.put(" createBy", masterAreaInfo.getCreateBy());
            map.put(" updateTime", masterAreaInfo.getUpdateTime());
            map.put(" updateBy", masterAreaInfo.getUpdateBy());
            map.put("自有品牌(m²)", masterAreaInfo.getPrivateBrand());
            map.put("孕妈专区(m²)", masterAreaInfo.getPregnantMothers());
            map.put("门店商品面积备用一(m²)", masterAreaInfo.getStoreAreaReserveOne());
            map.put("门店商品面积备二(m²)", masterAreaInfo.getStoreAreaReserveTwo());
            map.put("游乐（童乐园）(m²)", masterAreaInfo.getAmusement());
            map.put("成长试验站(m²)", masterAreaInfo.getGrowthTestStation());
            map.put("其它游乐自营项目(m²)", masterAreaInfo.getOtherAmusement());
            map.put("互动体验(m²)", masterAreaInfo.getInteractiveExperience());
            map.put("孕产体验室（含洽谈室）(m²)", masterAreaInfo.getMaternityExperienceEoom());
            map.put("甄选店-产康(m²)", masterAreaInfo.getSelectionShop());
            map.put("同城(m²)", masterAreaInfo.getLocal());
            map.put("服务站(m²)", masterAreaInfo.getServiceStation());
            map.put("妈妈课堂(m²)", masterAreaInfo.getMomClass());
            map.put("黑金专区(m²)", masterAreaInfo.getBlackGoldZone());
            map.put("门店服务面积备用一(m²)", masterAreaInfo.getServiceAreaReserveOne());
            map.put("门店服务面积备用二(m²)", masterAreaInfo.getServiceAreaReserveTwo());
            map.put("门店服务面积备用三(m²)", masterAreaInfo.getServiceAreaReserveThree());
            map.put("增值服务一(m²)", masterAreaInfo.getValueAddOne());
            map.put("增值服务二(m²)", masterAreaInfo.getValueAddTwo());
            map.put("增值服务三(m²)", masterAreaInfo.getValueAddThree());
            map.put("增值服务四(m²)", masterAreaInfo.getValueAddFour());
            map.put("增值服务五(m²)", masterAreaInfo.getValueAddFix());
            map.put("客服心中+收银区童(m²)", masterAreaInfo.getCustomerService());
            map.put("橱窗(m²)", masterAreaInfo.getDisplayShow());
            map.put("母婴室(m²)", masterAreaInfo.getMomBabyRoom());
            map.put("主通道(m²)", masterAreaInfo.getMainChannel());
            map.put("商品仓库(m²)", masterAreaInfo.getCommodityWarehouse());
            map.put("纺织仓库(m²)", masterAreaInfo.getTextileWarehouse());
            map.put("赠品仓库(m²)", masterAreaInfo.getGiftWarehouse());
            map.put("市场库(m²)", masterAreaInfo.getMarketLibrary());
            map.put("行政库房(m²)", masterAreaInfo.getAdministrativeWarehouse());
            map.put("办公室(m²)", masterAreaInfo.getOffice());
            map.put("财务室(m²)", masterAreaInfo.getFinanceOffice());
            map.put("洽谈室会议室(m²)", masterAreaInfo.getNegotiationRoom());
            map.put("员工休息室(m²)", masterAreaInfo.getStaffLounge());
            map.put("员工更衣室(m²)", masterAreaInfo.getStaffLockerRoom());
            map.put("强电间(m²)", masterAreaInfo.getStrongElectricityRoom());
            map.put("信息机房(m²)", masterAreaInfo.getInformationRoom());
            map.put("保洁间(m²)", masterAreaInfo.getCleaningRoom());
            map.put("纸箱回收区(m²)", masterAreaInfo.getCartonRecyclingArea());
            map.put("办公区通道(m²)", masterAreaInfo.getAccessOfficeArea());
            map.put("门店辅助面积备用一(m²)", masterAreaInfo.getAuxiliaryAreaReserveOne());
            map.put("机房（红线内）(m²)", masterAreaInfo.getComputerRoom());
            map.put("墙体/疏散（红线内）(m²)", masterAreaInfo.getWallRedLine());
            map.put("电梯间（红线内）(m²)", masterAreaInfo.getElevatorRedLine());
            map.put("公共走廊（红线内）(m²)", masterAreaInfo.getPublicCorridorRedLine());
            map.put("楼梯（红线外）(m²)", masterAreaInfo.getStairsRedOut());
            map.put("电梯间（红线外）(m²)", masterAreaInfo.getElevatorRedOut());
            map.put("物业公摊(m²)", masterAreaInfo.getPropertySharing());
            map.put("总经理办公室(m²)", masterAreaInfo.getGeneralManagersOffice());
            map.put("分公司财务办公室(m²)", masterAreaInfo.getFinanceBranchOffice());
            map.put("敞开办公区(m²)", masterAreaInfo.getOpenOfficeArea());
            map.put("分公司信息房(m²)", masterAreaInfo.getInformationBranchRoom());
            map.put("打印室(m²)", masterAreaInfo.getPhotocopyRoom());
            map.put("茶水间(m²)", masterAreaInfo.getTeaRoom());
            map.put("前台接待区(m²)", masterAreaInfo.getReceptionArea());
            map.put("行政仓库(m²)", masterAreaInfo.getAdministrativeCangku());
            map.put("大会议室(m²)", masterAreaInfo.getConventionHall());
            map.put("小会议室1(m²)", masterAreaInfo.getSmallMeetingOne());
            map.put("小会议室2(m²)", masterAreaInfo.getSmallMeetingTwo());
            map.put("洽谈室1(m²)", masterAreaInfo.getNegotiationRoomOne());
            map.put("洽谈室2(m²)", masterAreaInfo.getNegotiationRoomTwo());
            map.put("物业公摊面积(m²)", masterAreaInfo.getSharedAreaProperty());
            map.put("合同租赁面积(m²)", masterAreaInfo.getContractLeaseArea());
            map.put("净使用面积(m²)", masterAreaInfo.getNetUsableArea());
            map.put("营业面积(m²)", masterAreaInfo.getBusinessArea());
            map.put("商品面积(m²)", masterAreaInfo.getCommodityArea());
            map.put("服务面积(m²)", masterAreaInfo.getServiceArea());
            map.put("招商面积(m²)", masterAreaInfo.getInvestmentArea());
            map.put("辅助面积(m²)", masterAreaInfo.getAuxiliaryArea());
            map.put("仓库区(m²)", masterAreaInfo.getWarehouseArea());
            map.put("后场办公室（门店）(m²)", masterAreaInfo.getBackcourtOffice());
            map.put("门店公用面积(m²)", masterAreaInfo.getStorePublicArea());
            map.put("分公司办公室面积(m²)", masterAreaInfo.getBranchOfficeArea());
            map.put("是否删除", masterAreaInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void statisticalArea() {
        LambdaQueryWrapper<MasterAreaInfo> areaInfo = Wrappers.lambdaQuery(MasterAreaInfo.class)
                .eq(MasterAreaInfo::getIsDelete, false);
        List<MasterAreaInfo> masterAreaInfos = masterAreaInfoRepository.selectList(areaInfo);
        System.out.println(masterAreaInfos.size());
        for (MasterAreaInfo masterAreaInfo : masterAreaInfos) {
            BigDecimal commodityArea = masterAreaInfo.getCommodityArea();
            //外服童鞋
            BigDecimal KidShoes = masterAreaInfo.getKidShoes() == null ? BigDecimal.ZERO : masterAreaInfo.getKidShoes();
            String kidShoes = KidShoes.toString();
            //内衣家纺
            BigDecimal LingerieHomeTextile = masterAreaInfo.getLingerieHomeTextile() == null ? BigDecimal.ZERO : masterAreaInfo.getLingerieHomeTextile();
            String lingerieHomeTextile = LingerieHomeTextile.toString();
            //玩具
            BigDecimal Toys = masterAreaInfo.getToys() == null ? BigDecimal.ZERO : masterAreaInfo.getToys();
            String toys = Toys.toString();
            //文教智能
            BigDecimal CulturalEdu = masterAreaInfo.getCulturalEdu() == null ? BigDecimal.ZERO : masterAreaInfo.getCulturalEdu();
            String culturalEdu = CulturalEdu.toString();
            //用品
            BigDecimal Articles = masterAreaInfo.getArticles() == null ? BigDecimal.ZERO : masterAreaInfo.getArticles();
            String articles = Articles.toString();
            //车床椅
            BigDecimal LatheChair = masterAreaInfo.getLatheChair() == null ? BigDecimal.ZERO : masterAreaInfo.getLatheChair();
            String latheChair = LatheChair.toString();
            //奶粉
            BigDecimal PowderedMilk = masterAreaInfo.getPowderedMilk() == null ? BigDecimal.ZERO : masterAreaInfo.getPowderedMilk();
            String powderedMilk = PowderedMilk.toString();
            //纸尿裤
            BigDecimal Diapers = masterAreaInfo.getDiapers() == null ? BigDecimal.ZERO : masterAreaInfo.getDiapers();
            String diapers = Diapers.toString();
            //零食辅食
            BigDecimal SnackFood = masterAreaInfo.getSnackFood() == null ? BigDecimal.ZERO : masterAreaInfo.getSnackFood();
            String snackFood = SnackFood.toString();
            //营养保健
            BigDecimal NutritionHealth = masterAreaInfo.getNutritionHealth() == null ? BigDecimal.ZERO : masterAreaInfo.getNutritionHealth();
            String nutritionHealth = NutritionHealth.toString();
            //自有品牌
            BigDecimal PrivateBrand = masterAreaInfo.getPrivateBrand() == null ? BigDecimal.ZERO : masterAreaInfo.getPrivateBrand();
            String privateBrand = PrivateBrand.toString();
            //孕妈专区
            BigDecimal PregnantMothers = masterAreaInfo.getPregnantMothers() == null ? BigDecimal.ZERO : masterAreaInfo.getPregnantMothers();
            String pregnantMothers = PregnantMothers.toString();
            //备用一
            BigDecimal StoreAreaReserveOne = masterAreaInfo.getStoreAreaReserveOne() == null ? BigDecimal.ZERO : masterAreaInfo.getStoreAreaReserveOne();
            String storeAreaReserveOne = StoreAreaReserveOne.toString();
            //备用二
            BigDecimal StoreAreaReserveTwo = masterAreaInfo.getStoreAreaReserveTwo() == null ? BigDecimal.ZERO : masterAreaInfo.getStoreAreaReserveTwo();
            String storeAreaReserveTwo = StoreAreaReserveTwo.toString();

            BigDecimal commodityAreaSum = BigDecimal.ZERO;
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(kidShoes));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(lingerieHomeTextile));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(toys));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(culturalEdu));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(articles));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(latheChair));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(powderedMilk));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(diapers));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(snackFood));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(nutritionHealth));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(privateBrand));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(pregnantMothers));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(storeAreaReserveOne));
            commodityAreaSum = commodityAreaSum.add(new BigDecimal(storeAreaReserveTwo));
            if (!(commodityArea.compareTo(commodityAreaSum) == 0)) {
                masterAreaInfo.setCommodityArea(commodityAreaSum);
            }

            BigDecimal serviceArea = masterAreaInfo.getServiceArea();
            //游乐（童乐园）
            BigDecimal Amusement = masterAreaInfo.getAmusement() == null ? BigDecimal.ZERO : masterAreaInfo.getAmusement();
            String amusement = Amusement.toString();
            //成长实验站
            BigDecimal GrowthTestStation = masterAreaInfo.getGrowthTestStation() == null ? BigDecimal.ZERO : masterAreaInfo.getGrowthTestStation();
            String growthTestStation = GrowthTestStation.toString();
            //其他游乐自营项目
            BigDecimal OtherAmusement = masterAreaInfo.getOtherAmusement() == null ? BigDecimal.ZERO : masterAreaInfo.getOtherAmusement();
            String otherAmusement = OtherAmusement.toString();
            //互动体验
            BigDecimal InteractiveExperience = masterAreaInfo.getInteractiveExperience() == null ? BigDecimal.ZERO : masterAreaInfo.getInteractiveExperience();
            String interactiveExperience = InteractiveExperience.toString();
            //孕产体验室
            BigDecimal MaternityExperienceEoom = masterAreaInfo.getMaternityExperienceEoom() == null ? BigDecimal.ZERO : masterAreaInfo.getMaternityExperienceEoom();
            String maternityExperienceEoom = MaternityExperienceEoom.toString();
            //甄选店-产康
            BigDecimal SelectionShop = masterAreaInfo.getSelectionShop() == null ? BigDecimal.ZERO : masterAreaInfo.getSelectionShop();
            String selectionShop = SelectionShop.toString();
            //同城
            BigDecimal Local = masterAreaInfo.getLocal() == null ? BigDecimal.ZERO : masterAreaInfo.getLocal();
            String local = Local.toString();
            //服务站
            BigDecimal ServiceStation = masterAreaInfo.getServiceStation() == null ? BigDecimal.ZERO : masterAreaInfo.getServiceStation();
            String serviceStation = ServiceStation.toString();
            //妈妈课堂
            BigDecimal MomClass = masterAreaInfo.getMomClass() == null ? BigDecimal.ZERO : masterAreaInfo.getMomClass();
            String momClass = MomClass.toString();
            //黑金专区
            BigDecimal BlackGoldZone = masterAreaInfo.getBlackGoldZone() == null ? BigDecimal.ZERO : masterAreaInfo.getBlackGoldZone();
            String blackGoldZone = BlackGoldZone.toString();
            //服务面积备用一
            BigDecimal ServiceAreaReserveOne = masterAreaInfo.getServiceAreaReserveOne() == null ? BigDecimal.ZERO : masterAreaInfo.getServiceAreaReserveOne();
            String serviceAreaReserveOne = ServiceAreaReserveOne.toString();
            //服务面积备用二
            BigDecimal ServiceAreaReserveTwo = masterAreaInfo.getServiceAreaReserveTwo() == null ? BigDecimal.ZERO : masterAreaInfo.getServiceAreaReserveTwo();
            String serviceAreaReserveTwo = ServiceAreaReserveTwo.toString();
            //服务面积备用三
            BigDecimal ServiceAreaReserveThree = masterAreaInfo.getServiceAreaReserveThree() == null ? BigDecimal.ZERO : masterAreaInfo.getServiceAreaReserveThree();
            String serviceAreaReserveThree = ServiceAreaReserveThree.toString();

            BigDecimal serviceAreaSum = BigDecimal.ZERO;
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(amusement));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(growthTestStation));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(otherAmusement));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(interactiveExperience));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(maternityExperienceEoom));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(selectionShop));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(local));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(serviceStation));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(momClass));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(blackGoldZone));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(serviceAreaReserveOne));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(serviceAreaReserveTwo));
            serviceAreaSum = serviceAreaSum.add(new BigDecimal(serviceAreaReserveThree));
            if (!(serviceArea.compareTo(serviceAreaSum) == 0)) {
                masterAreaInfo.setServiceArea(serviceAreaSum);
            }

            //营业面积
            BigDecimal businessArea = BigDecimal.ZERO;
            businessArea = businessArea.add(new BigDecimal(serviceAreaSum.toString()));
            businessArea = businessArea.add(new BigDecimal(commodityAreaSum.toString()));
            masterAreaInfo.setBusinessArea(businessArea);

            //净使用面积
            BigDecimal netUsableArea = BigDecimal.ZERO;
            BigDecimal investmentArea = masterAreaInfo.getInvestmentArea();
            BigDecimal auxiliaryArea = masterAreaInfo.getAuxiliaryArea();
            BigDecimal branchOfficeArea = masterAreaInfo.getBranchOfficeArea();
            netUsableArea = netUsableArea.add(new BigDecimal(businessArea.toString()));
            netUsableArea = netUsableArea.add(new BigDecimal(investmentArea.toString()));
            netUsableArea = netUsableArea.add(new BigDecimal(auxiliaryArea.toString()));
            netUsableArea = netUsableArea.add(new BigDecimal(branchOfficeArea.toString()));
            masterAreaInfo.setNetUsableArea(netUsableArea);

            //合同租赁面积
            BigDecimal contractLeaseArea = BigDecimal.ZERO;
            contractLeaseArea = contractLeaseArea.add(new BigDecimal(netUsableArea.toString()));
            BigDecimal storePublicArea = masterAreaInfo.getStorePublicArea();
            contractLeaseArea = contractLeaseArea.add(new BigDecimal(storePublicArea.toString()));
            masterAreaInfo.setContractLeaseArea(contractLeaseArea);

            update(masterAreaInfo);
        }
    }

    @Override
    public void newStatisticalArea() {
        LambdaQueryWrapper<MasterAreaInfo> areaInfo = Wrappers.lambdaQuery(MasterAreaInfo.class).eq(MasterAreaInfo::getIsDelete, false);
        List<MasterAreaInfo> masterAreaInfos = masterAreaInfoRepository.selectList(areaInfo);
        for (MasterAreaInfo masterAreaInfo : masterAreaInfos) {
            BigDecimal valueAddOne = masterAreaInfo.getValueAddOne() != null ? masterAreaInfo.getValueAddOne() : BigDecimal.ZERO;
            BigDecimal valueAddTwo = masterAreaInfo.getValueAddTwo() != null ? masterAreaInfo.getValueAddTwo() : BigDecimal.ZERO;
            BigDecimal valueAddThree = masterAreaInfo.getValueAddThree() != null ? masterAreaInfo.getValueAddThree() : BigDecimal.ZERO;
            BigDecimal valueAddFour = masterAreaInfo.getValueAddFour() != null ? masterAreaInfo.getValueAddFour() : BigDecimal.ZERO;
            BigDecimal valueAddFix = masterAreaInfo.getValueAddFix() != null ? masterAreaInfo.getValueAddFix() : BigDecimal.ZERO;

            BigDecimal investmentArea = masterAreaInfo.getInvestmentArea();
            //判断这五个加起来是否等于投资面积
            // 将五个值相加
            BigDecimal sumOfValues = valueAddOne.add(valueAddTwo).add(valueAddThree).add(valueAddFour).add(valueAddFix);

            // 使用compareTo方法比较两个BigDecimal对象是否相等
            if (sumOfValues.compareTo(investmentArea) == 0) {
                System.out.println("五个值之和等于投资面积。");
            } else {
                masterAreaInfo.setInvestmentArea(sumOfValues);
                update(masterAreaInfo);
            }
        }
    }
}