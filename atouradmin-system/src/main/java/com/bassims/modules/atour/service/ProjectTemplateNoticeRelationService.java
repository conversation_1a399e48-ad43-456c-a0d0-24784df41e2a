/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectTemplateNoticeRelation;
import com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-04-26
**/
public interface ProjectTemplateNoticeRelationService extends BaseService<ProjectTemplateNoticeRelation> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ProjectTemplateNoticeRelationQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ProjectTemplateNoticeRelationDto>
    */
    List<ProjectTemplateNoticeRelationDto> queryAll(ProjectTemplateNoticeRelationQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param templateRelationId ID
     * @return ProjectTemplateNoticeRelationDto
     */
    ProjectTemplateNoticeRelationDto findById(Long templateRelationId);

    /**
    * 创建
    * @param resources /
    * @return ProjectTemplateNoticeRelationDto
    */
    ProjectTemplateNoticeRelationDto create(ProjectTemplateNoticeRelation resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ProjectTemplateNoticeRelation resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ProjectTemplateNoticeRelationDto> all, HttpServletResponse response) throws IOException;

    /**
     * 获取
     * @param templateId
     * @return
     */
    List<ProjectTemplateNoticeRelationDto> getNoticeTemplate(Long templateId);

    Boolean saveNotiRelation(List<ProjectTemplateNoticeRelationDto> resources);

    Boolean deleteNotiRelation(ProjectTemplateNoticeRelationDto resource);

    List<ProjectTemplateNoticeRelationDto> getAppNoticeTemplate(Long appTemplateDetailId);

    Boolean saveAppNotiRelation(List<ProjectTemplateNoticeRelationDto> resources);

    Boolean deleteAppNotiRelation(ProjectTemplateNoticeRelationDto resource);

}