/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.domain.ProjectTableNodeInfo;
import com.bassims.modules.atour.domain.TemplateTableGroup;
import com.bassims.modules.atour.repository.ProjectTableNodeInfoRepository;
import com.bassims.modules.atour.repository.TemplateTableGroupRepository;
import com.bassims.modules.atour.repository.TemplateTableRepository;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.ProjectTableNodeInfoService;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectTableNodeInfoMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-12
 **/
@Service
public class ProjectTableNodeInfoServiceImpl extends BaseServiceImpl<ProjectTableNodeInfoRepository, ProjectTableNodeInfo> implements ProjectTableNodeInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTableNodeInfoServiceImpl.class);

    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private ProjectInfoServiceImpl projectInfoService;
    @Autowired
    private ProjectTableNodeInfoRepository projectTableNodeInfoRepository;
    @Autowired
    private ProjectTableNodeInfoMapper projectTableNodeInfoMapper;

    @Autowired
    private TemplateTableGroupRepository templateTableGroupRepository;

    @Autowired
    private TableConfiguringTertiaryNodesServiceImpl tableConfiguringTertiaryNodesService;

    @Autowired
    private TemplateTableRepository templateTableRepository;

    @Override
    public Map<String, Object> queryAll(ProjectTableNodeInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectTableNodeInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectTableNodeInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectTableNodeInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectTableNodeInfoDto> queryAll(ProjectTableNodeInfoQueryCriteria criteria) {
        return projectTableNodeInfoMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectTableNodeInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTableNodeInfoDto findById(Long nodeId) {
        ProjectTableNodeInfo projectTableNodeInfo = Optional.ofNullable(getById(nodeId)).orElseGet(ProjectTableNodeInfo::new);
        ValidationUtil.isNull(projectTableNodeInfo.getNodeId(), getEntityClass().getSimpleName(), "nodeId", nodeId);
        return projectTableNodeInfoMapper.toDto(projectTableNodeInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTableNodeInfoDto create(ProjectTableNodeInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setNodeId(snowflake.nextId());
        save(resources);
        return findById(resources.getNodeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectTableNodeInfo resources) {
        ProjectTableNodeInfo projectTableNodeInfo = Optional.ofNullable(getById(resources.getNodeId())).orElseGet(ProjectTableNodeInfo::new);
        ValidationUtil.isNull(projectTableNodeInfo.getNodeId(), "ProjectTableNodeInfo", "id", resources.getNodeId());
        projectTableNodeInfo.copy(resources);
        updateById(projectTableNodeInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long nodeId : ids) {
            projectTableNodeInfoRepository.deleteById(nodeId);
        }
    }

    @Override
    public void download(List<ProjectTableNodeInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectTableNodeInfoDto projectTableNodeInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectTableNodeInfo.getProjectId());
            map.put("队列id", projectTableNodeInfo.getTemplateQueueId());
            map.put("模板主键", projectTableNodeInfo.getTemplateId());
            map.put("父节点", projectTableNodeInfo.getParentId());
            map.put(" moduleRelationId", projectTableNodeInfo.getModuleRelationId());
            map.put("项目版本号", projectTableNodeInfo.getProjectVersion());
            map.put("节点编码", projectTableNodeInfo.getNodeCode());
            map.put("节点名称", projectTableNodeInfo.getNodeName());
            map.put("计划开始时间", projectTableNodeInfo.getPlanStartDate());
            map.put("计划结束时间", projectTableNodeInfo.getPlanEndDate());
            map.put("预估开始日期", projectTableNodeInfo.getPredictStartDate());
            map.put("预估结束日期", projectTableNodeInfo.getPredictEndDate());
            map.put("实际完成时间", projectTableNodeInfo.getActualEndDate());
            map.put("计划需要完成天数", projectTableNodeInfo.getPlanDay());
            map.put("提醒天数", projectTableNodeInfo.getNoticeDay());
            map.put("延期天数", projectTableNodeInfo.getDelayDay());
            map.put("节点序号", projectTableNodeInfo.getNodeWbs());
            map.put("子节点排序", projectTableNodeInfo.getNodeIndex());
            map.put("节点等级", projectTableNodeInfo.getNodeLevel());
            map.put("节点类型", projectTableNodeInfo.getNodeType());
            map.put("节点状态", projectTableNodeInfo.getNodeStatus());
            map.put("已完成按钮", projectTableNodeInfo.getNodeIsfin());
            map.put("前置任务配置[]", projectTableNodeInfo.getFrontWbsConfig());
            map.put("是否是关键节点", projectTableNodeInfo.getIsKey());
            map.put("关键节点前置任务", projectTableNodeInfo.getKeyFrontWbs());
            map.put("节点备注", projectTableNodeInfo.getRemark());
            map.put("关联nodecode", projectTableNodeInfo.getRelationCode());
            map.put("关联的类型", projectTableNodeInfo.getRelationType());
            map.put("下拉列表角色名称", projectTableNodeInfo.getDownCode());
            map.put("干系人角色名称", projectTableNodeInfo.getJobCode());
            map.put("使用场景", projectTableNodeInfo.getUseCase());
            map.put("节点是否打开", projectTableNodeInfo.getIsOpen());
            map.put(" isDelete", projectTableNodeInfo.getIsDelete());
            map.put("创建时间", projectTableNodeInfo.getCreateTime());
            map.put(" createBy", projectTableNodeInfo.getCreateBy());
            map.put("修改时间", projectTableNodeInfo.getUpdateTime());
            map.put("修改人", projectTableNodeInfo.getUpdateBy());
            map.put("是否可用", projectTableNodeInfo.getIsEnabled());
            map.put("dict转码用", projectTableNodeInfo.getStartSign());
            map.put("结束标志（甘特图）", projectTableNodeInfo.getEndSign());
            map.put("总工期", projectTableNodeInfo.getTotalDay());
            map.put("是否是手机端", projectTableNodeInfo.getIsMobile());
            map.put("责任人角色code", projectTableNodeInfo.getRoleCode());
            map.put("是否可以编辑", projectTableNodeInfo.getIsEdit());
            map.put("占位", projectTableNodeInfo.getSeat());
            map.put("是否换行", projectTableNodeInfo.getIsWrap());
            map.put("是否显示nodeName(1 不显示)", projectTableNodeInfo.getNotLabel());
            map.put("小程序标志", projectTableNodeInfo.getIcon());
            map.put("公式", projectTableNodeInfo.getFormula());
            map.put("影响的code", projectTableNodeInfo.getFormulaCode());
            map.put("用户添加的版本", projectTableNodeInfo.getAddedVersion());
            map.put("同组最后的code", projectTableNodeInfo.getLastCode());
            map.put(" projectNo", projectTableNodeInfo.getProjectNo());
            map.put(" brandId", projectTableNodeInfo.getBrandId());
            map.put(" isNotTask", projectTableNodeInfo.getIsNotTask());
            map.put("项目和动态模版的关系id", projectTableNodeInfo.getProjectTableRelationId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<ProjectTableNodeInfo> copyProjectTableNodeInfo(Long projectId, TemplateTableRelationDto tableRelation) {

        //通过复制动态列表节点的数据，赋值到项目动态列表中
        List<ProjectTableNodeInfo> infoList = new ArrayList<>();
        //根据条件查询到对应的模版信息
        LambdaQueryWrapper<TemplateTableGroup> queryWrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getTemplateTableGroupId, tableRelation.getTemplateTableGroupId());
        TemplateTableGroup projectInfos = templateTableGroupRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(projectInfos)) {
            //查询表头数据
            String byStartSign = tableConfiguringTertiaryNodesService.getNodeByStartSign(projectInfos.getDynamicTableType());
            if (ObjectUtils.isNotEmpty(byStartSign)) {
                //根据动态模版ID,查找模版
                Integer count = 0;

                //查到对应的模版后，去查询模版的列表数据
                List<TemplateTableDto> getNodeList = templateTableRepository.getListByGroup(projectInfos.getTemplateTableGroupId());

                Map<String, List<TemplateTableDto>> collect = new HashMap<>();
                if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.COMPLETION_ACCEPTANCE_SCORE.getKey())) {
                    //竣工验收总项
                    collect = getNodeList.stream().collect(Collectors.groupingBy(a -> ObjectUtil.isEmpty(a.getTotalItem()) ? "-1" : a.getTotalItem()));
                } else {
                    collect = getNodeList.stream().collect(Collectors.groupingBy(a -> ObjectUtil.isEmpty(a.getNodeLevel()) ? "-1" : a.getNodeLevel().toString()));
                }
                for (String type : collect.keySet()) {
                    Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                    TemplateTableDto tableDto = new TemplateTableDto();
                    TemplateTableDto tableDtoByStartSign = new TemplateTableDto();
                    if (ObjectUtils.isNotEmpty(type) && !type.equals("-1")) {
                        /**
                         * 竣工验收总项
                         * 公共区域「外立面、大堂区域」	public_areas
                         * 功能区「厨房、消毒间、布草间、仓库」	functional_area
                         * 客房区域	                       guest_room_area
                         * 风险管理「消防、安防、监控、电梯」	risk_management
                         * 机电设施「强电、弱电、给排水、暖通」	electromechanical_facilities
                         */
                        if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.COMPLETION_ACCEPTANCE_SCORE.getKey())) {
                            tableDto.setRemark(JhSystemEnum.completionTotalItems.PUBLIC_AREAS.getSpec());
                            tableDto.setNodeCode(tableRelation.getThreeNodeCode() + "P-" + "table-tab");

                            tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + "P-" + "table-head");
                            if (type.equals(JhSystemEnum.completionTotalItems.FUNCTIONAL_AREA.getKey())) {
                                tableDto.setRemark(JhSystemEnum.completionTotalItems.FUNCTIONAL_AREA.getSpec());
                                tableDto.setNodeCode(tableRelation.getThreeNodeCode() + "F-" + "table-tab");

                                tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + "F-" + "table-head");
                            } else if (type.equals(JhSystemEnum.completionTotalItems.GUEST_ROOM_AREA.getKey())) {
                                tableDto.setRemark(JhSystemEnum.completionTotalItems.GUEST_ROOM_AREA.getSpec());
                                tableDto.setNodeCode(tableRelation.getThreeNodeCode() + "G-" + "table-tab");

                                tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + "G-" + "table-head");
                            } else if (type.equals(JhSystemEnum.completionTotalItems.RISK_MANAGEMENT.getKey())) {
                                tableDto.setRemark(JhSystemEnum.completionTotalItems.RISK_MANAGEMENT.getSpec());
                                tableDto.setNodeCode(tableRelation.getThreeNodeCode() + "R-" + "table-tab");

                                tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + "R-" + "table-head");
                            } else if (type.equals(JhSystemEnum.completionTotalItems.ELECTROMECHANICAL_FACILITIES.getKey())) {
                                tableDto.setRemark(JhSystemEnum.completionTotalItems.ELECTROMECHANICAL_FACILITIES.getSpec());
                                tableDto.setNodeCode(tableRelation.getThreeNodeCode() + "E-" + "table-tab");

                                tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + "E-" + "table-head");
                            }
                        }
                        tableDto.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                        tableDto.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_TAB.getValue());
                        count = this.insertProjectNodeInfoTable(projectId, count, tableDto, infoList, tableRelation);

                        tableDtoByStartSign.setRemark(byStartSign);
                        tableDtoByStartSign.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                        tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
                        count = this.insertProjectNodeInfoTable(projectId, count, tableDtoByStartSign, infoList, tableRelation);
                    } else {
                        tableDtoByStartSign.setRemark(byStartSign);
                        tableDtoByStartSign.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                        tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
                        tableDtoByStartSign.setNodeCode(tableRelation.getThreeNodeCode() + count + "-" + "table-head");
                        count = this.insertProjectNodeInfoTable(projectId, count, tableDtoByStartSign, infoList, tableRelation);
                    }

                    for (TemplateTableDto dto : collect.get(type)) {
                        dto.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue());
                        dto.setNodeCode(tableRelation.getThreeNodeCode() + "-" + dto.getNodeCode());
                        count = this.insertProjectNodeInfoTable(projectId, count, dto, infoList, tableRelation);
                    }
                }
                this.saveBatch(infoList);
            }
        }

        return infoList;
    }

    @Override
    public List<ProjectInfoTableCheckReceiptSumUpDateDTO> getScoreCalculationWeakCurrentAcceptance(List<ProjectTableNodeInfo> projectTableNodeInfos) {

        Long projectId = projectTableNodeInfos.get(0).getProjectId();
        List<ProjectInfoTableCheckReceiptSumUpDateDTO> dateDTOS = new ArrayList<>();
        //去除表头的信息
        projectTableNodeInfos.remove(0);
        if (CollectionUtils.isNotEmpty(projectTableNodeInfos)) {
            for (ProjectTableNodeInfo p : projectTableNodeInfos) {
                ProjectInfoTableCheckReceiptSumUpDateDTO acceptance = new ProjectInfoTableCheckReceiptSumUpDateDTO();//验收
                ProjectInfoTableCheckReceiptSumUpDateDTO getScore = new ProjectInfoTableCheckReceiptSumUpDateDTO(); //得分
                ProjectInfoTableCheckReceiptSumUpDateDTO score = new ProjectInfoTableCheckReceiptSumUpDateDTO(); //期初分值
                //json转实体
                String json = p.getRemark();
                if (StringUtils.isNotEmpty(json)) {

                    List<ProjectInfoTableCheckReceiptSumUpDateDTO> sumUpDateDTOList = JSONObject.parseArray(json, ProjectInfoTableCheckReceiptSumUpDateDTO.class);
                        //计算json数据并返回
//                    dateDTOS.addAll(sumUpDateDTOList);
                        for (ProjectInfoTableCheckReceiptSumUpDateDTO sumUpDTO : sumUpDateDTOList) {
                            if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.ACCEPTANCE.getKey())) {
                                acceptance = sumUpDTO;
                            } else if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.SCORE.getKey())) {
                                score = sumUpDTO;
                            } else if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.GET_SCORE.getKey())) {
                                getScore = sumUpDTO;
//                                dateDTOS.remove(sumUpDTO);
                            }
                    }
                    //计算得分
                    dateDTOS.add(this.getScoreWeak(getScore, acceptance.getTertiaryValue(), score.getTertiaryValue()));
                }
            }

            Double num=0d;
            for(ProjectInfoTableCheckReceiptSumUpDateDTO dto:dateDTOS){
                if(StringUtils.isNotEmpty(dto.getTertiaryValue()) && !dto.getTertiaryValue().equals("分值")){
                    num=num+Double.parseDouble(dto.getTertiaryValue());
                }
            }
            //把得分写入project
            LambdaQueryWrapper<ProjectNodeInfo> queryNodeWrapper = Wrappers.lambdaQuery();
            //查询出自检里的 3个项
        queryNodeWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                       .eq(ProjectNodeInfo::getNodeCode, "eng-00139027");
        util.setProjectTableName(projectNodeInfoService.getSubmeterProjectId(projectId));

        ProjectNodeInfo projectNodeInfos = projectNodeInfoService.getOne(queryNodeWrapper);
        projectNodeInfos.setRemark(String.valueOf(num));
        projectNodeInfoService.updateById(projectNodeInfos);
      }
        return dateDTOS;
    }

    @Override
    public Map<String, Object> designSampleRoomList(ProjectTableNodeInfoQueryCriteria criteria) {
        final Map<String, Object> map = new LinkedHashMap<>();
        final ArrayList<ProjectTableNodeInfo> list1 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list2 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list3 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list4 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list5 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list6 = new ArrayList<>();
        final ArrayList<ProjectTableNodeInfo> list7 = new ArrayList<>();
        final LambdaQueryWrapper<ProjectTableNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectTableNodeInfo.class)
                .eq(ProjectTableNodeInfo::getProjectId, criteria.getProjectId())
                .like(ProjectTableNodeInfo::getNodeCode, AtourSystemEnum.DesignNodeTow.DES00125.getKey());
        final List<ProjectTableNodeInfo> tableNodeInfos = projectTableNodeInfoRepository.selectList(wrapper);
        final List<ProjectTableNodeInfo> list = tableNodeInfos.stream()
                .filter(t -> !t.getNodeCode().contains("head") && "non_compliant".equals(getListRemark(t, 1, "$.tertiaryValue")))
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(list)){
            getTableTypeList(map, list1, list, "des-00125009", "室内空间及功能性检查");
            getTableTypeList(map, list2, list, "des-00125011", "机电");
            getTableTypeList(map, list3, list, "des-00125013", "门/窗");
            getTableTypeList(map, list4, list, "des-00125015", "卫生间/小走道");
            getTableTypeList(map, list5, list, "des-00125017", "卧室/休闲区");
            getTableTypeList(map, list6, list, "des-00125019", "装饰画");
            getTableTypeList(map, list7, list, "des-00125021", "其他");
        }
        return map;
    }

    private void getTableTypeList(Map<String, Object> map, ArrayList<ProjectTableNodeInfo> list1, List<ProjectTableNodeInfo> list, String s, String name) {
        list.stream().filter(l -> l.getNodeCode().contains(s)).forEach(table -> {
            final ProjectTableNodeInfo info = new ProjectTableNodeInfo();
            info.setCheckEntryDescription(getListRemark(table, 0, "$.tertiaryValue"));
            info.setCheckStatus(getListRemark(table, 1, "$.tertiaryValue"));
            info.setCheckDescription(getListRemark(table, 2, "$.tertiaryValue"));
            info.setReformPlan(getListRemark(table, 3, "$.tertiaryValue"));
            info.setFollowPersonnel(getListRemark(table, 4, "$.tertiaryValue"));
            info.setSitePhoto(getListRemark(table, 5, "$.tertiaryValue"));
            info.setCheckSubItems(getListRemark(table, 6, "$.tertiaryValue"));
            list1.add(info);
        });
        map.put(name, list1);
    }

    private String getListRemark(ProjectTableNodeInfo nodeInfo, int key, String path) {
        if (ObjectUtil.isNotEmpty(nodeInfo)){
            String substring = nodeInfo.getRemark();
            if (!nodeInfo.getRemark().startsWith("[")) {
                substring = nodeInfo.getRemark().substring(1);
            }
            JSONArray jsonArray = new JSONArray(substring);
            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(key);
            final String read = JSONPath.read(jsonObject.toString(), path).toString();
            return read;
        }
        return null;
    }

    private ProjectInfoTableCheckReceiptSumUpDateDTO getScoreWeak(ProjectInfoTableCheckReceiptSumUpDateDTO dto, String acceptanceVal, String scoreVal) {
        /*if (  验收 =='不合格' ) {  得分 = -分值} else {  得分 = 分值} */
        if (acceptanceVal!=null&&acceptanceVal.equals(JhSystemEnum.acceptance.UNQUALIFIED.getValue())) {
            dto.setTertiaryValue("-" + scoreVal);
        } else {
            dto.setTertiaryValue(scoreVal);
        }
        return dto;
    }

    private Integer insertProjectNodeInfoTable(Long projectId, Integer count, TemplateTableDto templateTable, List<ProjectTableNodeInfo> infoList, TemplateTableRelationDto tableRelation) {

        ProjectTableNodeInfo info = new ProjectTableNodeInfo();
        info.setProjectId(projectId);
        info.setNodeType(templateTable.getNodeType());
        info.setNodeCode(templateTable.getNodeCode());
        info.setParentId(tableRelation.getParentId());
        info.setProjectTableRelationId(tableRelation.getTemplateTableGroupId());
        info.setIsDelete(true);
        count++;
        info.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        info.setIsEnabled(true);
        info.setIsMobile(true);
        info.setSeat("3");
        info.setRemark(templateTable.getRemark());
        info.setNodeLevel(3);
        info.setTemplateId(templateTable.getRelevanceId());
        info.setNodeWbs(2);
        info.setNodeIndex(count);
        infoList.add(info);
        return count;
    }

    @Override
    public Boolean getAllCalculationScore(String[] array){

        Long projectId = Long.parseLong(array[0]);
        String hege = array[11];
        String score = array[9];
        if("".equals(hege)){
            hege = "0%";
        }

        LambdaQueryWrapper<ProjectNodeInfo> queryNodeWrapper = Wrappers.lambdaQuery();
        //查询出自检里的 3个项
        queryNodeWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, "eng-00133020");
        util.setProjectTableName(projectId);

        ProjectNodeInfo projectNodeInfos = projectNodeInfoService.getOne(queryNodeWrapper);
        projectNodeInfos.setRemark(hege);
        projectNodeInfoService.updateById(projectNodeInfos);

        LambdaQueryWrapper<ProjectNodeInfo> scoreWrapper = Wrappers.lambdaQuery();
        //查询出自检里的 3个项
        scoreWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, "eng-00133049");
        util.setProjectTableName(projectId);
        ProjectNodeInfo scoreNodeInfos = projectNodeInfoService.getOne(scoreWrapper);
        scoreNodeInfos.setRemark(score);
        projectNodeInfoService.updateById(scoreNodeInfos);

        return true;
    }

    @Override
    public List<ProjectInfoTableCheckReceiptSumUpDateDTO> getOneScore(List<ProjectInfoTableCheckReceiptSumUpDateDTO> sumUpDTOS) {

        //计算json数据并返回
        List<ProjectInfoTableCheckReceiptSumUpDateDTO> dateDTOS = new ArrayList<>();
        dateDTOS.addAll(sumUpDTOS);
        ProjectInfoTableCheckReceiptSumUpDateDTO acceptance = new ProjectInfoTableCheckReceiptSumUpDateDTO();//验收
        ProjectInfoTableCheckReceiptSumUpDateDTO getScore = new ProjectInfoTableCheckReceiptSumUpDateDTO(); //得分
        ProjectInfoTableCheckReceiptSumUpDateDTO score = new ProjectInfoTableCheckReceiptSumUpDateDTO(); //期初分值
        for (ProjectInfoTableCheckReceiptSumUpDateDTO sumUpDTO : sumUpDTOS) {
            if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.ACCEPTANCE.getKey())) {
                acceptance = sumUpDTO;
            } else if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.SCORE.getKey())) {
                score = sumUpDTO;
            } else if (sumUpDTO.getTertiaryKey().equals(JhSystemEnum.elementHeaderEnum.GET_SCORE.getKey())) {
                getScore = sumUpDTO;
                dateDTOS.remove(sumUpDTO);
            }
        }

        //计算得分
        dateDTOS.add(this.getScoreWeak(getScore, acceptance.getTertiaryValue(),score.getTertiaryValue()));
        return dateDTOS;
    }
}
