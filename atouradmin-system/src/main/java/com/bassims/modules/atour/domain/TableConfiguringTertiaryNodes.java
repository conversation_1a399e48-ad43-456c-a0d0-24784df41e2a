/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-25
**/
@Data
@TableName(value="t_table_configuring_tertiary_nodes")
public class TableConfiguringTertiaryNodes implements Serializable {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    @TableField(value = "tertiary_key")
    @ApiModelProperty(value = "key")
    private String tertiaryKey;

    @TableField(value = "tertiary_value")
    @ApiModelProperty(value = "名称")
    private String tertiaryValue;

    @TableField(value = "tertiary_type")
    @ApiModelProperty(value = "类型")
    private String tertiaryType;

    @TableField(value = "start_sign")
    @ApiModelProperty(value = "dict转码用")
    private String startSign;

    @TableField(value = "relation_code")
    @ApiModelProperty(value = "关联三级的nodecode")
    private String relationCode;

    @TableField(value = "relation_name")
    @ApiModelProperty(value = "关联三级的name")
    private String relationName;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号")
    private String projectVersion;

    @TableField(value = "is_hide")
    @ApiModelProperty(value = "是否隐藏(0否1是)")
    private int isHide;


    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;


    @TableField(value = "dynamic_table_type")
    @ApiModelProperty(value = "动态表格的类型")
    private String dynamicTableType;

    @TableField(value = "table_type")
    @ApiModelProperty(value = "小类型")
    private String tableType;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;


    @TableField(value = "is_compile")
    @ApiModelProperty(value = "是否编辑（0不可编辑1可编辑）")
    private Integer isCompile;


    @TableField(value = "is_audit_display")
    @ApiModelProperty(value = "是否审核节点展示（0审核节点展示、1审核节点不展示）")
    private Integer isAuditDisplay;


    @TableField(value = "is_merge_cells")
    @ApiModelProperty(value = "是否合并单元格（0不合并、1合并）")
    private Integer isMergeCells;


    @TableField(value = "is_must")
    @ApiModelProperty(value = "是否必填（0否1是）")
    private Integer isMust;

    @TableField(value = "is_modifiable")
    @ApiModelProperty(value = "是否存在有条件必填（0不存在、1存在）")
    private Integer isModifiable;

    @TableField(value = "modifiable_code")
    @ApiModelProperty(value = "（条件值,条件key）")
    private String modifiableCode;


    @TableField(value = "width_and_height")
    @ApiModelProperty(value = "当前列的宽高")
    private String widthAndHeight;


    public void copy(TableConfiguringTertiaryNodes source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}