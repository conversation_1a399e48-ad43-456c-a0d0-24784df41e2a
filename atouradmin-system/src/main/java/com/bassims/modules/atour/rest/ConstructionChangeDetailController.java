/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ConstructionChangeDetail;
import com.bassims.modules.atour.service.ConstructionChangeDetailService;
import com.bassims.modules.atour.service.dto.ConstructionChangeDetailQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-28
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_construction_change_detail管理")
@RequestMapping("/api/constructionChangeDetail")
public class ConstructionChangeDetailController {

    private static final Logger logger = LoggerFactory.getLogger(ConstructionChangeDetailController.class);

    private final ConstructionChangeDetailService constructionChangeDetailService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ConstructionChangeDetailQueryCriteria criteria) throws IOException {
        constructionChangeDetailService.download(constructionChangeDetailService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ConstructionChangeDetailDto>>}
    */
    @GetMapping("/list")
//    @Log("查询t_construction_change_detail")
    @ApiOperation("查询t_construction_change_detail")
    public ResponseEntity<Object> query(ConstructionChangeDetailQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(constructionChangeDetailService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ConstructionChangeDetailDto>}
    */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询t_construction_change_detail")
    @ApiOperation("查询t_construction_change_detail")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(constructionChangeDetailService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_construction_change_detail")
    @ApiOperation("新增t_construction_change_detail")
    public ResponseEntity<Object> create(@Validated @RequestBody ConstructionChangeDetail resources){
        return new ResponseEntity<>(constructionChangeDetailService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_construction_change_detail")
    @ApiOperation("修改t_construction_change_detail")
    public ResponseEntity<Object> update(@Validated @RequestBody ConstructionChangeDetail resources){
        constructionChangeDetailService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_construction_change_detail")
    @ApiOperation("删除t_construction_change_detail")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        constructionChangeDetailService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}