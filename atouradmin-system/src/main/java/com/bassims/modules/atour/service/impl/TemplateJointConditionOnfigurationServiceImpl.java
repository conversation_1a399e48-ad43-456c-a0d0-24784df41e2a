/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.TemplateJointConditionOnfiguration;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateJointConditionOnfigurationRepository;
import com.bassims.modules.atour.service.TemplateJointConditionOnfigurationService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateJointConditionOnfigurationDto;
import com.bassims.modules.atour.service.dto.TemplateJointConditionOnfigurationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateJointConditionOnfigurationMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-10-08
 **/
@Service
public class TemplateJointConditionOnfigurationServiceImpl extends BaseServiceImpl<TemplateJointConditionOnfigurationRepository, TemplateJointConditionOnfiguration> implements TemplateJointConditionOnfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateJointConditionOnfigurationServiceImpl.class);

    @Autowired
    private TemplateJointConditionOnfigurationRepository templateJointConditionOnfigurationRepository;
    @Autowired
    private TemplateJointConditionOnfigurationMapper templateJointConditionOnfigurationMapper;

    @Override
    public Map<String, Object> queryAll(TemplateJointConditionOnfigurationQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<TemplateJointConditionOnfiguration> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateJointConditionOnfiguration.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateJointConditionOnfigurationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateJointConditionOnfigurationDto> queryAll(TemplateJointConditionOnfigurationQueryCriteria criteria) {
        return templateJointConditionOnfigurationMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateJointConditionOnfiguration.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateJointConditionOnfigurationDto findById(Long templateJointConditionId) {
        TemplateJointConditionOnfiguration templateJointConditionOnfiguration = Optional.ofNullable(getById(templateJointConditionId)).orElseGet(TemplateJointConditionOnfiguration::new);
        ValidationUtil.isNull(templateJointConditionOnfiguration.getTemplateJointConditionId(), getEntityClass().getSimpleName(), "templateJointConditionId", templateJointConditionId);
        return templateJointConditionOnfigurationMapper.toDto(templateJointConditionOnfiguration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateJointConditionOnfigurationDto create(TemplateJointConditionOnfiguration resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTemplateJointConditionId(snowflake.nextId());
        save(resources);
        return findById(resources.getTemplateJointConditionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateJointConditionOnfiguration resources) {
        TemplateJointConditionOnfiguration templateJointConditionOnfiguration = Optional.ofNullable(getById(resources.getTemplateJointConditionId())).orElseGet(TemplateJointConditionOnfiguration::new);
        ValidationUtil.isNull(templateJointConditionOnfiguration.getTemplateJointConditionId(), "TemplateJointConditionOnfiguration", "id", resources.getTemplateJointConditionId());
        templateJointConditionOnfiguration.copy(resources);
        updateById(templateJointConditionOnfiguration);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateJointConditionId : ids) {
            templateJointConditionOnfigurationRepository.deleteById(templateJointConditionId);
        }
    }

    @Override
    public void download(List<TemplateJointConditionOnfigurationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateJointConditionOnfigurationDto templateJointConditionOnfiguration : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("二级联合节点关系id", templateJointConditionOnfiguration.getTemplateJointTaskId());
            map.put("二级联合编码", templateJointConditionOnfiguration.getJointCode());
            map.put("三级需求字段编码判断", templateJointConditionOnfiguration.getFieldRequirementNodeCode());
            map.put("三级需求字段判断name", templateJointConditionOnfiguration.getFieldRequirement());
            map.put("需求条件判断", templateJointConditionOnfiguration.getConditionalDemand());
            map.put(" createTime", templateJointConditionOnfiguration.getCreateTime());
            map.put(" updateTime", templateJointConditionOnfiguration.getUpdateTime());
            map.put(" createBy", templateJointConditionOnfiguration.getCreateBy());
            map.put(" updateBy", templateJointConditionOnfiguration.getUpdateBy());
            map.put("是否可用", templateJointConditionOnfiguration.getIsEnabled());
            map.put("是否删除", templateJointConditionOnfiguration.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}