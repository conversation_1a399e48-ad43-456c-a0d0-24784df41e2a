/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.TemplateGroupCompletionReceipt;
import com.bassims.modules.atour.domain.vo.TemplateGroupCompletionReceiptSave;
import com.bassims.modules.atour.service.dto.TemplateGroupCompletionReceiptDto;
import com.bassims.modules.atour.service.dto.TemplateGroupCompletionReceiptQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-02-22
**/
public interface TemplateGroupCompletionReceiptService extends BaseService<TemplateGroupCompletionReceipt> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TemplateGroupCompletionReceiptQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TemplateGroupCompletionReceiptDto>
    */
    List<TemplateGroupCompletionReceiptDto> queryAll(TemplateGroupCompletionReceiptQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param receiptGroupId ID
     * @return TemplateGroupCompletionReceiptDto
     */
    TemplateGroupCompletionReceiptDto findById(Long receiptGroupId);

    /**
    * 创建
    * @param resources /
    * @return TemplateGroupCompletionReceiptDto
    */
    TemplateGroupCompletionReceiptDto create(TemplateGroupCompletionReceipt resources);

    TemplateGroupCompletionReceiptDto createAndSetReceiptIds(TemplateGroupCompletionReceiptSave receiptSave);

    /**
    * 编辑
    * @param resources /
    */
    void update(TemplateGroupCompletionReceipt resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TemplateGroupCompletionReceiptDto> all, HttpServletResponse response) throws IOException;

    List<TemplateGroupCompletionReceipt> getCompleteVersion(Long projectId);

    void deleteById(Long id);
}