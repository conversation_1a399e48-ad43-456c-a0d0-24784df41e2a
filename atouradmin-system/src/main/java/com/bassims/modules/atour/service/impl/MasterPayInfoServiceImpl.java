/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.MasterPayInfo;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.MasterPayInfoRepository;
import com.bassims.modules.atour.service.MasterPayInfoService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.MasterPayInfoDto;
import com.bassims.modules.atour.service.dto.MasterPayInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MasterPayInfoMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-22
**/
@Service
public class MasterPayInfoServiceImpl extends BaseServiceImpl<MasterPayInfoRepository,MasterPayInfo> implements MasterPayInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MasterPayInfoServiceImpl.class);

    @Autowired
    private MasterPayInfoRepository masterPayInfoRepository;
    @Autowired
    private MasterPayInfoMapper masterPayInfoMapper;

    @Override
    public Map<String,Object> queryAll(MasterPayInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<MasterPayInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MasterPayInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", masterPayInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<MasterPayInfoDto> queryAll(MasterPayInfoQueryCriteria criteria){
        return masterPayInfoMapper.toDto(list(QueryHelpPlus.getPredicate(MasterPayInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterPayInfoDto findById(Long payId) {
        MasterPayInfo masterPayInfo = Optional.ofNullable(getById(payId)).orElseGet(MasterPayInfo::new);
        ValidationUtil.isNull(masterPayInfo.getPayId(),getEntityClass().getSimpleName(),"payId",payId);
        return masterPayInfoMapper.toDto(masterPayInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterPayInfoDto create(MasterPayInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setPayId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getPayId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterPayInfo resources) {
        MasterPayInfo masterPayInfo = Optional.ofNullable(getById(resources.getPayId())).orElseGet(MasterPayInfo::new);
        ValidationUtil.isNull( masterPayInfo.getPayId(),"MasterPayInfo","id",resources.getPayId());
        masterPayInfo.copy(resources);
        updateById(masterPayInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long payId : ids) {
            masterPayInfoRepository.deleteById(payId);
        }
    }

    @Override
    public void download(List<MasterPayInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterPayInfoDto masterPayInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目类型", masterPayInfo.getProjectType());
            map.put("费用类型", masterPayInfo.getExpenseType());
            map.put("门店id", masterPayInfo.getStoreId());
            map.put("门店编号", masterPayInfo.getStoreNo());
            map.put("费用小类", masterPayInfo.getExpenseCategory());
            map.put("总合同金额", masterPayInfo.getTotalContractAmount());
            map.put("总审定金额", masterPayInfo.getTotalApproveAmount());
            map.put("税率", masterPayInfo.getTaxRate());
            map.put("供应商", masterPayInfo.getSupplier());
            map.put("结算进度", masterPayInfo.getSettlementProgress());
            map.put("已付金额", masterPayInfo.getAmountPaid());
            map.put("未付金额", masterPayInfo.getAmountUnpaid());
            map.put("订单号&合同编号", masterPayInfo.getOrderConNo());
            map.put("订购单号/采购单号", masterPayInfo.getPurchaseOrderNo());
            map.put("付款单号", masterPayInfo.getPayOrderNo());
            map.put(" createTime",  masterPayInfo.getCreateTime());
            map.put(" createBy",  masterPayInfo.getCreateBy());
            map.put(" updateTime",  masterPayInfo.getUpdateTime());
            map.put(" updateBy",  masterPayInfo.getUpdateBy());
            map.put("付款金额", masterPayInfo.getPayAmount());
            map.put("付款时间", masterPayInfo.getPayTime());
            map.put("是否删除", masterPayInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}