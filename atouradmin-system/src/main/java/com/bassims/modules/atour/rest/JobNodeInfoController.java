/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.JobNodeInfo;
import com.bassims.modules.atour.service.JobNodeInfoService;
import com.bassims.modules.atour.service.dto.JobNodeInfoDto;
import com.bassims.modules.atour.service.dto.JobNodeInfoQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-04-20
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_job_node_info管理")
@RequestMapping("/api/jobNodeInfo")
public class JobNodeInfoController {

    private static final Logger logger = LoggerFactory.getLogger(JobNodeInfoController.class);

    private final JobNodeInfoService jobNodeInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, JobNodeInfoQueryCriteria criteria) throws IOException {
        jobNodeInfoService.download(jobNodeInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<JobNodeInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_job_node_info")
    @ApiOperation("查询t_job_node_info")
    public ResponseEntity<Object> query(JobNodeInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(jobNodeInfoService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <JobNodeInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_job_node_info")
    @ApiOperation("查询t_job_node_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(jobNodeInfoService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_job_node_info")
    @ApiOperation("新增t_job_node_info")
    public ResponseEntity<Object> create(@Validated @RequestBody JobNodeInfo resources){
        return new ResponseEntity<>(jobNodeInfoService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_job_node_info")
    @ApiOperation("修改t_job_node_info")
    public ResponseEntity<Object> update(@Validated @RequestBody JobNodeInfo resources){
        jobNodeInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_job_node_info")
    @ApiOperation("删除t_job_node_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        jobNodeInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/saveList")
    @Log("批量保存角色与节点关系")
    @ApiOperation("批量保存角色与节点关系")
    public ResponseEntity<Object> saveJobNodeList(@Validated @RequestBody JobNodeInfoDto resources){
        return new ResponseEntity<>(jobNodeInfoService.batchSave(resources), HttpStatus.CREATED);
    }
}