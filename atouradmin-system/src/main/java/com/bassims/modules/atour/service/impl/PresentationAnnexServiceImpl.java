/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.PresentationAnnex;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.PresentationAnnexRepository;
import com.bassims.modules.atour.service.PresentationAnnexService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.PresentationAnnexDto;
import com.bassims.modules.atour.service.dto.PresentationAnnexQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.PresentationAnnexMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-24
**/
@Service
public class PresentationAnnexServiceImpl extends BaseServiceImpl<PresentationAnnexRepository,PresentationAnnex> implements PresentationAnnexService {

    private static final Logger logger = LoggerFactory.getLogger(PresentationAnnexServiceImpl.class);

    @Autowired
    private PresentationAnnexRepository presentationAnnexRepository;
    @Autowired
    private PresentationAnnexMapper presentationAnnexMapper;

    @Override
    public Map<String,Object> queryAll(PresentationAnnexQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<PresentationAnnex> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(PresentationAnnex.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", presentationAnnexMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<PresentationAnnexDto> queryAll(PresentationAnnexQueryCriteria criteria){
        return presentationAnnexMapper.toDto(list(QueryHelpPlus.getPredicate(PresentationAnnex.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PresentationAnnexDto findById(Long annexId) {
        PresentationAnnex presentationAnnex = Optional.ofNullable(getById(annexId)).orElseGet(PresentationAnnex::new);
        ValidationUtil.isNull(presentationAnnex.getAnnexId(),getEntityClass().getSimpleName(),"annexId",annexId);
        return presentationAnnexMapper.toDto(presentationAnnex);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PresentationAnnexDto create(PresentationAnnex resources) {
        save(resources);
        return findById(resources.getAnnexId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PresentationAnnex resources) {
        PresentationAnnex presentationAnnex = Optional.ofNullable(getById(resources.getAnnexId())).orElseGet(PresentationAnnex::new);
        ValidationUtil.isNull( presentationAnnex.getAnnexId(),"PresentationAnnex","id",resources.getAnnexId());
        presentationAnnex.copy(resources);
        updateById(presentationAnnex);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long annexId : ids) {
            presentationAnnexRepository.deleteById(annexId);
        }
    }

    @Override
    public void download(List<PresentationAnnexDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (PresentationAnnexDto presentationAnnex : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("宣讲附件名称", presentationAnnex.getAnnexName());
            map.put("宣讲附件编码", presentationAnnex.getAnnexCode());
            map.put(" remark",  presentationAnnex.getRemark());
            map.put("子节点排序", presentationAnnex.getNodeIndex());
            map.put("项目版本号", presentationAnnex.getProjectVersion());
            map.put("节点序号", presentationAnnex.getNodeWbs());
            map.put("节点等级", presentationAnnex.getNodeLevel());
            map.put("节点类型", presentationAnnex.getNodeType());
            map.put("节点状态", presentationAnnex.getNodeStatus());
            map.put(" isDelete",  presentationAnnex.getIsDelete());
            map.put("创建时间", presentationAnnex.getCreateTime());
            map.put(" createBy",  presentationAnnex.getCreateBy());
            map.put("修改时间", presentationAnnex.getUpdateTime());
            map.put("修改人", presentationAnnex.getUpdateBy());
            map.put("是否可用", presentationAnnex.getIsEnabled());
            map.put("是否可以编辑", presentationAnnex.getIsEdit());
            map.put("用户添加的版本", presentationAnnex.getAddedVersion());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}