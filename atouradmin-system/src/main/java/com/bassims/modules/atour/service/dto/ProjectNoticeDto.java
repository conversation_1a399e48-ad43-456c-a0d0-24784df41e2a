/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-05-17
 **/
@Data
public class ProjectNoticeDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectNoticeId;

    /**
     * 项目id
     */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /**
     * 节点id
     */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 角色id
     */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long roleId;

    /**
     * 用户名
     */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 消息状态
     */
    private String noticeStatus;

    /**
     * 消息id
     */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long noticeId;

    /**
     * 消息名称
     */
    private String noticeName;

    /**
     * 消息类型
     */
    private String noticeType;

    /**
     * 消息内容
     */
    private String noticeContent;

    /**
     * 消息编码
     */
    private String noticeCode;

    /**
     * 计划结束时间
     */
    private Date planEndDate;

    /**
     * 消息返回
     */
    private String msg;

    /**
     * 是否发送
     */
    private Boolean isSend;

    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    private String createBy;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 是否可用
     */
    private Boolean isEnabled;

    private String taskName;
    private String userName;
    private String submitDate;
    private String operationalEvents;


    /*二级pid*/
    private String parentId;

    /*酒店ID*/
    private String projectNo;
}