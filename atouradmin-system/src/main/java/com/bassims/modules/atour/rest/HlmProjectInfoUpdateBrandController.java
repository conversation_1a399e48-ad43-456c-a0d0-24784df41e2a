/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.HlmProjectInfoUpdateBrand;
import com.bassims.modules.atour.service.HlmProjectInfoUpdateBrandService;
import com.bassims.modules.atour.service.dto.HlmProjectInfoUpdateBrandDto;
import com.bassims.modules.atour.service.dto.HlmProjectInfoUpdateBrandQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-11-06
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "hlm项目更新项目品牌变更信息管理")
@RequestMapping("/api/hlmProjectInfoUpdateBrand")
public class HlmProjectInfoUpdateBrandController {

    private static final Logger logger = LoggerFactory.getLogger(HlmProjectInfoUpdateBrandController.class);

    private final HlmProjectInfoUpdateBrandService hlmProjectInfoUpdateBrandService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, HlmProjectInfoUpdateBrandQueryCriteria criteria) throws IOException {
        hlmProjectInfoUpdateBrandService.download(hlmProjectInfoUpdateBrandService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<HlmProjectInfoUpdateBrandDto>>}
    */
    @GetMapping("/list")
    @Log("查询hlm项目更新项目品牌变更信息")
    @ApiOperation("查询hlm项目更新项目品牌变更信息")
    public ResponseEntity<Object> query(HlmProjectInfoUpdateBrandQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hlmProjectInfoUpdateBrandService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<HlmProjectInfoUpdateBrandDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询hlm项目更新项目品牌变更信息")
    @ApiOperation("查询hlm项目更新项目品牌变更信息")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(hlmProjectInfoUpdateBrandService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增hlm项目更新项目品牌变更信息")
    @ApiOperation("新增hlm项目更新项目品牌变更信息")
    public ResponseEntity<Object> create(@Validated @RequestBody HlmProjectInfoUpdateBrand resources){
        return new ResponseEntity<>(hlmProjectInfoUpdateBrandService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改hlm项目更新项目品牌变更信息")
    @ApiOperation("修改hlm项目更新项目品牌变更信息")
    public ResponseEntity<Object> update(@Validated @RequestBody HlmProjectInfoUpdateBrand resources){
        hlmProjectInfoUpdateBrandService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除hlm项目更新项目品牌变更信息")
    @ApiOperation("删除hlm项目更新项目品牌变更信息")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        hlmProjectInfoUpdateBrandService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}