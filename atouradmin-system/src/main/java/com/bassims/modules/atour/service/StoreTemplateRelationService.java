/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.StoreTemplateRelation;
import com.bassims.modules.atour.service.dto.StoreTemplateRelationDto;
import com.bassims.modules.atour.service.dto.StoreTemplateRelationQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-03-25
**/
public interface StoreTemplateRelationService extends BaseService<StoreTemplateRelation> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(StoreTemplateRelationQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<StoreTemplateRelationDto>
    */
    List<StoreTemplateRelationDto> queryAll(StoreTemplateRelationQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param relationId ID
     * @return StoreTemplateRelationDto
     */
    StoreTemplateRelationDto findById(Long relationId);

    /**
    * 创建
    * @param resources /
    * @return StoreTemplateRelationDto
    */
    StoreTemplateRelationDto create(StoreTemplateRelation resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(StoreTemplateRelation resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<StoreTemplateRelationDto> all, HttpServletResponse response) throws IOException;

    StoreTemplateRelation getByTemplateId(Long templateId);
}