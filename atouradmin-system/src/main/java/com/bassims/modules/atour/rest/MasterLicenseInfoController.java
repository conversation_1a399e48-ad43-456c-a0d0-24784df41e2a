/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.MasterLicenseInfo;
import com.bassims.modules.atour.service.MasterLicenseInfoService;
import com.bassims.modules.atour.service.dto.MasterLicenseInfoDto;
import com.bassims.modules.atour.service.dto.MasterLicenseInfoQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-11-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "z_master_license_info管理")
@RequestMapping("/api/masterLicenseInfo")
public class MasterLicenseInfoController {

    private static final Logger logger = LoggerFactory.getLogger(MasterLicenseInfoController.class);

    private final MasterLicenseInfoService masterLicenseInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, MasterLicenseInfoQueryCriteria criteria) throws IOException {
        masterLicenseInfoService.download(masterLicenseInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<MasterLicenseInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询z_master_license_info")
    @ApiOperation("查询z_master_license_info")
    public ResponseEntity<Object> query(MasterLicenseInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(masterLicenseInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<MasterLicenseInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询z_master_license_info")
    @ApiOperation("查询z_master_license_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(masterLicenseInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增z_master_license_info")
    @ApiOperation("新增z_master_license_info")
    public ResponseEntity<Object> create(@Validated @RequestBody MasterLicenseInfo resources){
        return new ResponseEntity<>(masterLicenseInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改z_master_license_info")
    @ApiOperation("修改z_master_license_info")
    public ResponseEntity<Object> update(@Validated @RequestBody MasterLicenseInfo resources){
        masterLicenseInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除z_master_license_info")
    @ApiOperation("删除z_master_license_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        masterLicenseInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}