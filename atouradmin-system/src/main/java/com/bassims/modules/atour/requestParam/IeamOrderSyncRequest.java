package com.bassims.modules.atour.requestParam;

import lombok.Data;

import java.util.List;

/**
 * ieam订单同步请求
 *
 * <AUTHOR>
 */
@Data
public class IeamOrderSyncRequest {
    private Long orderId;

    private int opType = 2;

    private IeamOrderSyncMainRequest main;
    
    private List<IeamOrderSyncDetail> itemList;


//    /**
//     * 供应商编码
//     */
//    private String supplierCode;
//    /**
//     * 合同号
//     */
//    private String contractCode;
//    /**
//     * 收货人工号
//     */
//    private String receivingLaborCode;
//    /**
//     * 收货仓库编码
//     */
//    private String receivingWarehouseCode;
//    /**
//     * 收货省：江苏
//     */
//    private String receiptProvince;
//    /**
//     * 收货市：南京
//     */
//    private String receiptCity;
//    /**
//     * 收货区：玄武
//     */
//    private String receiptRegion;
//    /**
//     * 地址（业务录入）
//     */
//    private String address;
//    /**
//     * 收货人电话（业务录入）
//     */
//    private String consigneeTelephone;
//    /**
//     * 备注说明
//     */
//    private String remarks;
//    /**
//     * 外部来源单号（营建系统订单号）
//     */
//    private String externalSourceDocumentCode;
//    /**
//     * 附件List（URL）
//     */
//    private List<String> enclosureList;
//    /**
//     * 采购物资明细List
//     */
//    private List<MaterialDetails> purchasedMaterialDetails;


}
