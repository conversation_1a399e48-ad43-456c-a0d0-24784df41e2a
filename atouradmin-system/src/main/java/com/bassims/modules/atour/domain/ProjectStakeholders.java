/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-29
**/
@Data
@TableName(value="t_project_stakeholders")
public class ProjectStakeholders implements Serializable {

    @TableId(value = "stakeholder_id")
    @ApiModelProperty(value = "干系人主键")
    private Long stakeholderId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @TableField(value = "role_id")
    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @TableField(value = "role_name")
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @TableField(value = "join_time")
    @ApiModelProperty(value = "加入时间")
    private Timestamp joinTime;

    @TableField(value = "shakeholder_status")
    @ApiModelProperty(value = "状态")
    private String shakeholderStatus;

    @TableField(value = "leave_time")
    @ApiModelProperty(value = "离开时间")
    private Timestamp leaveTime;

    @TableField(value = "reason")
    @ApiModelProperty(value = "原因")
    private String reason;

    @TableField(value = "is_approve")
    @ApiModelProperty(value = "是否是审批角色")
    private Boolean isApprove;

    @TableField(value = "is_notshow")
    @ApiModelProperty(value = "是否不展示")
    private Boolean isNotshow;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "role_code")
    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @TableField(value = "down_code")
    @ApiModelProperty(value = "下拉列表角色名称")
    private String downCode;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @TableField(value = "change_authorizatio_file")
    @ApiModelProperty(value = "变更授权文件")
    private String changeAuthorizatioFile;

    @TableField(value = "change_start_time")
    @ApiModelProperty(value = "变更干系人开始时间")
    private Timestamp changeStartTime;

    @TableField(value = "change_end_time")
    @ApiModelProperty(value = "变更干系人结束时间")
    private Timestamp changeEndTime;

    @TableField(value = "change_id")
    @ApiModelProperty(value = "变更干系人ID")
    private Long changeId;

    /** 用户名 */
    @TableField(exist = false)
    private String username;

    /** 用户昵称 */
    @TableField(exist = false)
    private String nickName;

    /*项目ID*/
    @TableField(exist = false)
    private Long supplierId;

    public void copy(ProjectStakeholders source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}