/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.TemplateCustomSignage;
import com.bassims.modules.atour.service.TemplateCustomSignageService;
import com.bassims.modules.atour.service.dto.TemplateCustomSignageDto;
import com.bassims.modules.atour.service.dto.TemplateCustomSignageQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-04-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "九宫格信息管理")
@RequestMapping("/api/templateCustomSignage")
public class TemplateCustomSignageController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateCustomSignageController.class);

    private final TemplateCustomSignageService templateCustomSignageService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateCustomSignageQueryCriteria criteria) throws IOException {
        templateCustomSignageService.download(templateCustomSignageService.queryAll(criteria), response);
    }

    /**
     * @real_return {@link ResponseEntity<List<TemplateCustomSignageDto>>}
     */
    @GetMapping("/getList")
//    @Log("查询九宫格信息列表")
    @ApiOperation("查询九宫格信息列表")
    @MyDataPermission(title = "营建对接启动,项目推进,数据处理")
    public ResponseEntity<Object> getList(TemplateCustomSignageQueryCriteria criteria){
        return new ResponseEntity<>(templateCustomSignageService.queryAll(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateCustomSignageDto>>}
    */
    @GetMapping("/list")
//    @Log("查询九宫格信息")
    @ApiOperation("查询九宫格信息")
    public ResponseEntity<Object> query(TemplateCustomSignageQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateCustomSignageService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateCustomSignageDto>}
    */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询九宫格信息")
    @ApiOperation("查询九宫格信息")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateCustomSignageService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增九宫格信息")
    @ApiOperation("新增九宫格信息")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateCustomSignage resources){
        return new ResponseEntity<>(templateCustomSignageService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改九宫格信息")
    @ApiOperation("修改九宫格信息")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateCustomSignage resources){
        templateCustomSignageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除九宫格信息")
    @ApiOperation("删除九宫格信息")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateCustomSignageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}