/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.ProjectNodeStatusVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.ProjectGroupService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.StoreMasterInfoService;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectGroupMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-08-29
 **/
@Service
public class ProjectGroupServiceImpl extends BaseServiceImpl<ProjectGroupRepository, ProjectGroup> implements ProjectGroupService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectGroupServiceImpl.class);

    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private StoreMasterInfoService storeMasterInfoService;
    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;

    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private TemplateConditionRepository templateConditionRepository;
    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private RoleRepository roleRepository;

    @Override
    public Map<String, Object> queryAll(ProjectGroupQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectGroup> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectGroup.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectGroupMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectGroupDto> queryAll(ProjectGroupQueryCriteria criteria) {
        return projectGroupMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectGroup.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectGroupDto findById(Long projectGroupId) {
        ProjectGroup projectGroup = Optional.ofNullable(getById(projectGroupId)).orElseGet(ProjectGroup::new);
        ValidationUtil.isNull(projectGroup.getProjectGroupId(), getEntityClass().getSimpleName(), "projectGroupId", projectGroupId);
        return projectGroupMapper.toDto(projectGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectGroupDto create(ProjectGroup resources) {
        save(resources);
        return findById(resources.getProjectGroupId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectGroup resources) {
        ProjectGroup projectGroup = Optional.ofNullable(getById(resources.getProjectGroupId())).orElseGet(ProjectGroup::new);
        ValidationUtil.isNull(projectGroup.getProjectGroupId(), "ProjectGroup", "id", resources.getProjectGroupId());
        projectGroup.copy(resources);
        updateById(projectGroup);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectGroupId : ids) {
            projectGroupRepository.deleteById(projectGroupId);
        }
    }

    @Override
    public void download(List<ProjectGroupDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectGroupDto projectGroup : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectGroup.getProjectId());
            map.put("订单id", projectGroup.getOrderId());
            map.put("设计id", projectGroup.getDesignId());
            map.put("模板组主键", projectGroup.getTemplateGroupId());
            map.put("二级id", projectGroup.getTemplateQueueId());
            map.put("模板主键", projectGroup.getTemplateId());
            map.put(" templateCode", projectGroup.getTemplateCode());
            map.put("父节点", projectGroup.getParentId());
            map.put("名称", projectGroup.getNodeName());
            map.put("节点编码", projectGroup.getNodeCode());
            map.put("子节点下标", projectGroup.getNodeIndex());
            map.put("节点序号", projectGroup.getNodeWbs());
            map.put("前置任务配置", projectGroup.getFrontWbsConfig());
            map.put("前置完成条件", projectGroup.getFrontFinishWbs());
            map.put("是否是关键节点", projectGroup.getIsKey());
            map.put("关键节点前置任务", projectGroup.getKeyFrontWbs());
            map.put("计划需要完成天数", projectGroup.getPlanDay());
            map.put("节点等级", projectGroup.getNodeLevel());
            map.put("节点类型", projectGroup.getNodeType());
            map.put("延期天数", projectGroup.getDelayDay());
            map.put("计划开始时间", projectGroup.getPlanStartDate());
            map.put("计划结束时间", projectGroup.getPlanEndDate());
            map.put("预估开始日期", projectGroup.getPredictStartDate());
            map.put("预估结束日期", projectGroup.getPredictEndDate());
            map.put("实际完成时间", projectGroup.getActualEndDate());
            map.put("提醒天数", projectGroup.getNoticeDay());
            map.put("使用场景", projectGroup.getUseCase());
            map.put("节点是否打开", projectGroup.getIsOpen());
            map.put("节点状态", projectGroup.getNodeStatus());
            map.put("已完成按钮", projectGroup.getNodeIsfin());
            map.put("是否可用", projectGroup.getIsEnabled());
            map.put("是否删除", projectGroup.getIsDelete());
            map.put("创建时间", projectGroup.getCreateTime());
            map.put("更新时间", projectGroup.getUpdateTime());
            map.put(" createBy", projectGroup.getCreateBy());
            map.put(" updateBy", projectGroup.getUpdateBy());
            map.put("总工期", projectGroup.getTotalDay());
            map.put("是否是手机端", projectGroup.getIsMobile());
            map.put("责任人角色code", projectGroup.getRoleCode());
            map.put("remark", projectGroup.getRemark());
            map.put("小程序标志", projectGroup.getIcon());
            map.put("是否有第三方审批", projectGroup.getIsThird());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean isThirdGroup(ProjectGroupDto projectGroupDto) {
        Boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class);
        groupLambdaQueryWrapper.eq(ProjectGroup::getProjectGroupId, projectGroupDto.getProjectGroupId());
        ProjectGroup one = this.getOne(groupLambdaQueryWrapper);
        if (one.getIsThird() != null && one.getIsThird()) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    @Override
    public void updateGroupSubmit(ProjectGroup projectGroup) {
        LambdaUpdateWrapper groupUpdate = Wrappers.lambdaUpdate(ProjectGroup.class)
                .eq(ProjectGroup::getProjectGroupId, projectGroup.getProjectGroupId())
                .set(ProjectGroup::getIsSubmit, Boolean.TRUE)
                .set(ProjectGroup::getActualEndDate, null)
                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
        this.update(groupUpdate);
    }

    @Override
    public void updateGroupNext(ProjectGroup projectGroup) {
        Timestamp d = new Timestamp(System.currentTimeMillis());
        LambdaUpdateWrapper groupUpdate = Wrappers.lambdaUpdate(ProjectGroup.class)
                .eq(ProjectGroup::getProjectGroupId, projectGroup.getProjectGroupId())
                .set(ProjectGroup::getNodeIsfin, Boolean.TRUE)
                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())
                .set(ProjectGroup::getActualEndDate, d);
        this.update(groupUpdate);
    }

    @Override
    public Long getIdByNodeApplyNo(String applyNo) {
        return projectGroupRepository.getIdByNodeApplyNo(applyNo);
    }

    @Override
    public Long getIdByReportApplyNo(String applyNo) {
        return projectGroupRepository.getIdByReportApplyNo(applyNo);
    }

    @Override
    public List<Long> getAccessByRole(List<Long> roles) {

        return null;
    }

    @Override
    public void updateStoreStatus(ProjectGroup projectGroup) {
        ProjectNodeStatusVo statusNode = Optional.ofNullable(projectGroupRepository.getProjectStatusByProjectId(projectGroup.getProjectId())).orElseGet(ProjectNodeStatusVo::new);
        //0表示已完成 1表示未完成
        if (ObjectUtil.isNotEmpty(statusNode.getStatus2()) && statusNode.getStatus2() == 0) {
            String templateCode = projectGroup.getTemplateCode();
            List<String> reforms = Arrays.asList("store_new", "warehouse_new", "office_new");
            if (reforms.contains(templateCode)) {
                ProjectInfo projectInfo = Optional.ofNullable(projectInfoRepository.selectById(projectGroup.getProjectId())).orElseGet(ProjectInfo::new);
                if (ObjectUtil.isNotEmpty(projectInfo.getStoreId())) {
                    LambdaUpdateWrapper storeStatusUpdate = Wrappers.lambdaUpdate(StoreMasterInfo.class)
                            .eq(StoreMasterInfo::getStoreMasterId, projectInfo.getStoreId())
                            .set(StoreMasterInfo::getStoreStatus, KidsSystemEnum.StoreStatusEnum.BUSINESS.getValue());
                    storeMasterInfoService.update(storeStatusUpdate);
                }
            }
            List<String> closes = Arrays.asList("store_close", "warehouse_close", "office_close");
            if (closes.contains(templateCode)) {
                ProjectInfo projectInfo = Optional.ofNullable(projectInfoRepository.selectById(projectGroup.getProjectId())).orElseGet(ProjectInfo::new);
                if (ObjectUtil.isNotEmpty(projectInfo.getStoreId())) {
                    LambdaUpdateWrapper storeStatusUpdate = Wrappers.lambdaUpdate(StoreMasterInfo.class)
                            .eq(StoreMasterInfo::getStoreMasterId, projectInfo.getStoreId())
                            .set(StoreMasterInfo::getStoreStatus, KidsSystemEnum.StoreStatusEnum.CLOSE.getValue());
                    storeMasterInfoService.update(storeStatusUpdate);
                }
            }

            List<String> projectStatuses = Arrays.asList("store_new", "store_major", "store_minor", "store_close", "warehouse_new", "warehouse_major", "warehouse_minor", "warehouse_close", "office_new", "office_reform", "office_close", "store_design_adjust", "warehouse_design_adjust", "office_design_adjust");
            //修改项目状态为已完成
            if (projectStatuses.contains(templateCode)) {
                if (ObjectUtil.isNotEmpty(projectGroup.getProjectId())) {
                    LambdaUpdateWrapper projectStatusUpdate = Wrappers.lambdaUpdate(ProjectInfo.class)
                            .eq(ProjectInfo::getProjectId, projectGroup.getProjectId())
                            .set(ProjectInfo::getProjectStatus, JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey());
                    projectInfoService.update(projectStatusUpdate);
                }
            }
        }
//        List<ProjectGroup> lastCodeGroup = projectGroupRepository.getLastCodeByTemplateCode();
//        if (ObjectUtil.isNotEmpty(lastCodeGroup) && lastCodeGroup.size() > 0){
//            Map<Long, ProjectGroup> lastMap = lastCodeGroup.stream().collect(Collectors.toMap(ProjectGroup::getTemplateGroupId,g -> g));
//            ProjectGroup group = lastMap.get(projectGroup.getTemplateGroupId());

//            if (ObjectUtil.isNotEmpty(group)){
//
//            }
//        }
    }


    @Override
    public List<ProjectGroupDto> getFactoryStageNodeList(ProjectGroupQueryCriteria criteria) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //查询工厂阶段的一级数据
        Long submeterProjectId = projectNodeInfoService.getSubmeterProjectId(Long.parseLong(criteria.getProjectId()));
        criteria.setProjectId(submeterProjectId.toString());

        List<ProjectGroupDto> nodeInfoDtos = projectGroupRepository.getFactoryStageNodeList(criteria);
        List<ProjectGroupDto> list = nodeInfoDtos.stream().filter(n -> "eng-010".equals(n.getNodeCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            //派驻现长 时间 =竣工验收-60天
            ProjectGroupDto dto = nodeInfoDtos.stream().filter(n -> "eng-010".equals(n.getNodeCode())).findFirst().get();
            Calendar c = Calendar.getInstance();
            c.setTime(nodeInfoDtos.get(0).getPlanStartDate());
            c.add(Calendar.DATE, dto.getTotalDay());
            Date yanTime = c.getTime();
            c.setTime(yanTime);
            c.add(Calendar.DAY_OF_MONTH, -60);
            //减完60天后的日期
            Date d = c.getTime();

            LocalDate date1 = d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(); // 第一个日期
            LocalDate date2 = nodeInfoDtos.get(0).getPlanStartDate().toLocalDateTime().toLocalDate(); // 当前日期（或者指定其他日期）
            nodeInfoDtos.stream().forEach(n -> {
                if ("eng-012".equals(n.getNodeCode())) {
                    n.setTotalDay(Math.toIntExact(ChronoUnit.DAYS.between(date1, date2)));
                }
            });
        }
        return nodeInfoDtos;
    }

    @Override
    public List<ProjectGroupDto> nodeCompletionList(ProjectGroupQueryCriteria criteria) {
        final ArrayList<ProjectGroup> list = new ArrayList<>();
        List<ProjectGroup> projectGroups =  projectGroupRepository.getEngineeringCompletionList(criteria.getProjectId());
        Optional.ofNullable(projectGroups).ifPresent(p -> {
            p.stream().forEach(n -> {
                final ProjectGroup projectGroup = new ProjectGroup();
                projectGroup.setNodeName(Optional.ofNullable(n.getNodeName()).filter(ObjectUtil::isNotEmpty).orElse(null));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String date = sdf.format(Optional.ofNullable(n.getPlanEndDate()).filter(ObjectUtil::isNotEmpty).filter(ObjectUtil::isNotEmpty).orElse(null));
                projectGroup.setPlanEndDateCol(date);
                //完成情况：任务完成为已完成task_fin；未完成是计划完成时间PlanEndDate
                final boolean present = Optional.ofNullable(n.getNodeStatus()).filter(status -> JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(status)).isPresent();
                projectGroup.setNodeStatus(present?JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getSpec():Optional.ofNullable(date).orElse(null));
                list.add(projectGroup);
            });
        });
        return projectGroupMapper.toDto(list);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveConstructionLog(ProjectConstructionLogDto resources) throws IllegalAccessException {
        //插入施工日志模板    获取项目主档数据
        LambdaQueryWrapper<ProjectInfo> wrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, resources.getProjectId());
        ProjectInfo projectInfo = projectInfoService.getOne(wrapper);

        //获取当前项目的施工日志
        ProjectInfoDto infoDto = new ProjectInfoDto();
        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
        infoDto.setProjectId(projectInfo.getProjectId());
        List<TemplateCollection> templateCollections = projectInfoService.findTemplateByConditionCode(infoDto, null);
        TemplateCollection templateCollection = templateCollections.stream().filter(s -> AtourSystemEnum.AdditionalTemplatesEnum.CONSTRUCTION_LOG.getKey()
                .equals(s.getTemplateCode())).findFirst().get();
        if (ObjectUtil.isNotEmpty(templateCollection) && ObjectUtil.isNotEmpty(projectInfo)) {
            infoDto.setProjectConstructionLog(resources);
            try {
                projectInfoService.saveProject(templateCollection, resources.getProjectId(), projectInfo.getProjectType(), infoDto, null
                        , projectInfo, projectInfo.getProjectType(), null, null);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            throw new BadRequestException("当前条件未配置模板");
        }
        return Boolean.TRUE;
    }

    @Override
    public List<ProjectConstructionLogDto> getConstructionLogList(ProjectConstructionLogQueryCriteria criteria) {
        //查询施工日志列表
        List<ProjectConstructionLogDto> logDtos= new ArrayList<>();
        ProjectInfo projectInfo = projectInfoService.getById(criteria.getProjectId());
        util.initialize(projectInfo.getProjectId());
        List<ProjectNodeInfoDto> infos = projectNodeInfoRepository.getConstructionLogList(projectInfo.getProjectId(),AtourSystemEnum.nodeCodeIdentifying.COL.getKey());
//        Map<String, List<ProjectNodeInfoDto>> collect = infos.stream().collect(Collectors.groupingBy(ProjectNodeInfoDto::getRoundMarkingStr));
//        for (String key : collect.keySet()) {
//            List<ProjectNodeInfoDto> nodeInfos = collect.get(key);
            ProjectConstructionLogDto logDto=new ProjectConstructionLogDto();
            for (ProjectNodeInfoDto nodeInfo : infos) {
                logDto.setCreateBy(nodeInfo.getCreateByNickName());
                logDto.setCreateTime(nodeInfo.getCreateTime());
                logDto.setUpdateTime(nodeInfo.getUpdateTime());
                logDto.setTemplateId(nodeInfo.getOneTemplateId());
                logDto.setNodeId(nodeInfo.getTwoNodeId());
                logDto.setRoundMarkingStr(nodeInfo.getRoundMarkingStr());
            }
            logDtos.add(logDto);
//        }
        return logDtos;
    }


    @Override
    public List<ProjectVisaFilingContentDto> getVisaApplicationContent(ProjectVisaFilingQueryCriteria criteria) {
        //获取签证报备大类和内容   查询当前登录人在项目里所属角色   1、设计的决策会意见无法落地，设计师和区域负责人提交发起； 其他类型都是设计师发起；  2、工程：由项目经理发起；
        //查询当前人的角色
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<ProjectVisaFilingContentDto> visaApplicationContent = null;
        if (currentUserId == 1 || currentUserId == 488) {
            visaApplicationContent = templateGroupRepository.getVisaApplicationContent(criteria);
        }else{
            //根据当前用户，获取对应干系人的roleCode,有多个roleCode
            List<String> roleCodes = roleRepository.findRoleCodesByUserId(currentUserId, criteria.getProjectId());
            String nodeCode = JhSystemEnum.VisaDilingEnum.getVisaDilingEnum(roleCodes);
            criteria.setNodeCode(nodeCode);
            visaApplicationContent = templateGroupRepository.getVisaApplicationContent(criteria);
        }

        return visaApplicationContent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createVisaFiling(ProjectVisaFilingDto projectVisaFilingDto) {
        //插入签证报备模板
//        this.save(projectVisaFiling);

        ProjectInfo projectInfo = projectInfoService.getById(projectVisaFilingDto.getProjectId());
        LambdaQueryWrapper<TemplateCollection> queryWrapper = Wrappers.lambdaQuery(TemplateCollection.class)
                .eq(TemplateCollection::getTemplateCode, projectVisaFilingDto.getTemplateCode()).last("limit 1");
        TemplateCollection templateCollection = templateConditionRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(templateCollection) && ObjectUtil.isNotEmpty(projectInfo)) {
            ProjectInfoDto infoDto = new ProjectInfoDto();
            BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
            //当前签字报备信息
//            infoDto.setProjectVisaFiling(projectVisaFilingDto);
            try {
                projectInfoService.saveProject(templateCollection, projectVisaFilingDto.getProjectId(),projectInfo.getProjectType(), infoDto, null
                        , projectInfo, projectInfo.getProjectType(), null, null);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    @Override
    public List<ProjectVisaFilingDto> getVisaFilingList(ProjectVisaFilingQueryCriteria criteria) {
        //获取签证报备列表数据
        List<ProjectVisaFilingDto> dtoList= new ArrayList<>();
        ProjectInfo projectInfo = projectInfoService.getById(criteria.getProjectId());
        util.initialize(projectInfo.getProjectId());
        List<ProjectNodeInfoDto> infos = projectNodeInfoRepository.getNodeInfoList(projectInfo.getProjectId(),AtourSystemEnum.nodeCodeIdentifying.VSF.getKey());
        Map<String, List<ProjectNodeInfoDto>> collect = infos.stream().collect(Collectors.groupingBy(ProjectNodeInfoDto::getRoundMarkingStr));
        for (String key : collect.keySet()) {
            List<ProjectNodeInfoDto> nodeInfos = collect.get(key);
            ProjectVisaFilingDto dto=new ProjectVisaFilingDto();
            for (ProjectNodeInfoDto nodeInfo : nodeInfos) {
                if (ObjectUtil.isEmpty(nodeInfo.getNodeLevel())) {
                    dto.setCreateBy(nodeInfo.getCreateByNickName());
                    dto.setCreateTime(nodeInfo.getCreateTime());
                    dto.setUpdateTime(nodeInfo.getUpdateTime());
                    dto.setTemplateId(nodeInfo.getOneTemplateId());
                    dto.setNodeId(nodeInfo.getTwoNodeId());
                    /*报备内容*/
                    dto.setReportContent(nodeInfo.getNodeName());
                    /*报备内容*/
                    dto.setTemplateCode(nodeInfo.getTemplateCode());
                }else{
                    if (nodeInfo.getNodeCode().equals(dto.getTemplateCode()+"03")) {
                        /*情况说明*/
                        dto.setPresentationOndition(nodeInfo.getRemark());
                    }else if(nodeInfo.getNodeCode().equals(dto.getTemplateCode()+"04")){
                        /*附件*/
                        dto.setAttachment(nodeInfo.getRemark());
                    }
                }
            }
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    public Boolean getConfidentialItem(Long projectId, String nodeCode) {

        //获取当前登录用户
        Long currentUserId = SecurityUtils.getCurrentUserId();
        ProjectInfo projectInfo = projectInfoRepository.selectById(projectId);
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Boolean.FALSE;
        }
        //获取当前登录用户的项目干系角色
        List<String> roleCodes =  roleRepository.findRoleCodesByUserId(currentUserId, projectId);
        List<Long> roleIdsByUserId = roleRepository.findRoleIdsByUserId(currentUserId, projectId);

        if (ObjectUtil.isNotEmpty(roleCodes) || ObjectUtil.isNotEmpty(roleIdsByUserId)) {
            //查看当前登录用户，是否存在该任务的负责、审批权限，是的话，返回true
            int confidentialItem = projectGroupRepository.getConfidentialItem(projectId, nodeCode, roleCodes, roleIdsByUserId);
            if (ObjectUtil.isNotEmpty(confidentialItem) && confidentialItem !=0) {
                return  Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
