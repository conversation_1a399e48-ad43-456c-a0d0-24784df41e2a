/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.OrderStock;
import com.bassims.modules.atour.service.OrderStockService;
import com.bassims.modules.atour.service.dto.OrderStockDto;
import com.bassims.modules.atour.service.dto.OrderStockQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_order_stock管理")
@RequestMapping("/api/orderStock")
public class OrderStockController {

    private static final Logger logger = LoggerFactory.getLogger(OrderStockController.class);

    private final OrderStockService orderStockService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, OrderStockQueryCriteria criteria) throws IOException {
        orderStockService.download(orderStockService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<OrderStockDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_order_stock")
    @ApiOperation("查询t_order_stock")
    public ResponseEntity<Object> query(OrderStockQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(orderStockService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<OrderStockDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_order_stock")
    @ApiOperation("查询t_order_stock")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(orderStockService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_order_stock")
    @ApiOperation("新增t_order_stock")
    public ResponseEntity<Object> create(@Validated @RequestBody OrderStock resources){
        return new ResponseEntity<>(orderStockService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_order_stock")
    @ApiOperation("修改t_order_stock")
    public ResponseEntity<Object> update(@Validated @RequestBody OrderStock resources){
        orderStockService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_order_stock")
    @ApiOperation("删除t_order_stock")
    public ResponseEntity<Object> delete(@RequestBody String[] ids) {
        orderStockService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/incrStock")
    @Log("预约单付款中")
    @ApiOperation("预约单付款中")
    public ResponseEntity<Object> incrStock(String materielNo,String supplierNum,BigDecimal orderNum) {
        orderStockService.incrStock(materielNo,supplierNum,orderNum);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/decrStock")
    @Log("实时单订单完结")
    @ApiOperation("实时单订单完结")
    public ResponseEntity<Object> decrStock(String materielNo,String supplierNum,BigDecimal orderNum) {
        orderStockService.decrStock(materielNo,supplierNum,orderNum);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}