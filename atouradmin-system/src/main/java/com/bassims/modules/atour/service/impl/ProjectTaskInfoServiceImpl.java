/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.NodeCodeCountVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterSmsVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterTargetsVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectTaskInfoMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.feishu.service.PortalService;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-04-18
 **/
@Service("projectTaskInfoService")
public class ProjectTaskInfoServiceImpl extends BaseServiceImpl<ProjectTaskInfoRepository, ProjectTaskInfo> implements ProjectTaskInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTaskInfoServiceImpl.class);

    @Autowired
    private ProjectTaskInfoRepository projectTaskInfoRepository;
    @Autowired
    private ProjectTaskInfoMapper projectTaskInfoMapper;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private ProjectNoticeService projectNoticeService;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private PortalService portalService;
    @Autowired
    private UserRepository userRepository;//findUserByGroupRoleCode
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;

    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private SupplierInfoService supplierInfoService;
    @Autowired
    private SupplierPmService supplierPmService;

    @Override
    public Map<String, Object> queryAll(ProjectTaskInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        List<ProjectTaskInfo> taskInfos  = projectTaskInfoRepository.getProjectTaskInfoList(criteria);
//        taskInfos.forEach(t->{
//            if (StringUtils.isEmpty(t.getProjectType())) {
//                t.setProjectType("暂无");
//            }
//        });
        PageInfo<ProjectTaskInfo> page = new PageInfo<>(taskInfos);
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectTaskInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    private void sendCarbonCopyRoleCode(Long nodeId, Long userId, String carbonCopyRoleCode) {
        if (ObjectUtil.isNotEmpty(carbonCopyRoleCode)) {
            ProjectGroup group = projectGroupRepository.selectById(nodeId);
            group.setIsCarbonCopy(1);
            //给抄送人发起消息通知
            //发送消息通知
            projectNoticeService.generateNotice(nodeId, group, JhSystemEnum.MessageTemplate.MB1000008, userId);
        }
    }

    @Override
    public void updateTaskType() {
        // 定时任务调用此方法
        // 获取当前应该逾期的代办任务（不区分是否审批）    审批-创建时间延后两天算逾期       代办-创建时间根据工期延后算逾期
        List<ProjectTaskInfo> taskInfos = projectTaskInfoRepository.selectListByOverdueTask();
        if (taskInfos != null && taskInfos.size() > 0) {
            //改为逾期任务
            taskInfos.forEach(taskinfo -> {
                taskinfo.setTaskType(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey());
                taskinfo.setTaskName(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getSpec());
                if (taskinfo.getNodeName().contains("审批")) {
                    taskinfo.setNodeName(taskinfo.getNodeName().replaceAll("(审批)", "(逾期审批)"));
                } else {
                    taskinfo.setNodeName(taskinfo.getNodeName() + "(逾期)");
                    //当前逾期的任务存在抄送人的话，给抄送人发送逾期消息提醒
                    this.sendCarbonCopyRoleCode(taskinfo.getNodeId(), taskinfo.getUserId(), taskinfo.getCarbonCopyRoleCode());

                    if(taskinfo.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00117.getKey()) || taskinfo.getNodeCode().equals(AtourSystemEnum.EngineeringNodeTow.ENG00125.getKey())){
                        sendMessage(taskinfo.getNodeId());
                    }
                }
            });
            boolean taskResult = this.updateBatchById(taskInfos);
            logger.info("代办任务是否逾期执行结果:{}", taskResult);
        }

//        //获取任务
//        LambdaQueryWrapper<ProjectTaskInfo> eq = Wrappers.lambdaQuery(ProjectTaskInfo.class)
//                .eq(ProjectTaskInfo::getTaskStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey())
//                .eq(ProjectTaskInfo::getTaskName, JhSystemEnum.TaskNameEnum.TODO_TASK.getSpec())
//                .eq(ProjectTaskInfo::getTaskType, JhSystemEnum.TaskNameEnum.TODO_TASK.getKey())
//                .like(ProjectTaskInfo::getNodeName, "(审批)")
//                //两天逾期
//                .le(ProjectTaskInfo::getCreateTime, new Timestamp(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L));
//        List<ProjectTaskInfo> taskInfos = projectTaskInfoRepository.selectList(eq);
//        if (taskInfos != null && taskInfos.size() > 0) {
//            //改为逾期任务
//            taskInfos.forEach(taskinfo -> {
//                taskinfo.setTaskType(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey());
//                taskinfo.setTaskName(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getSpec());
//                taskinfo.setNodeName(taskinfo.getNodeName().replaceAll("(审批)", "(逾期审批)"));
//            });
//            boolean taskResult = this.updateBatchById(taskInfos);
//            logger.info("审核任务是否逾期执行结果:{}", taskResult);
//        }
    }

    private void  sendMessage(Long nodeId){
        ProjectGroup projectGroup = projectGroupRepository.selectById(nodeId);
        ProjectInfo projectInfo = projectInfoRepository.selectById(projectGroup.getProjectId());
        List<ProjectStakeholders> projectStakeholdersList = projectStakeholdersRepository.selectListByRoleCodes(projectGroup.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.GCJL.getKey());
        if (projectStakeholdersList != null && projectStakeholdersList.size() > 0) {
            List<String> mobileList = new ArrayList<>();
            List<Long> userIds = new ArrayList<>();
            for (ProjectStakeholders user : projectStakeholdersList) {
                userIds.add(user.getUserId());
            }
            List<SendNotifyCenterTargetsVo> centerTargetsVos = new ArrayList<>();
            List<User> userList = userRepository.findByUserIds(userIds);
            if (userList != null && userList.size() > 0) {
                for (User user : userList) {
                    if (StringUtils.isNotEmpty(user.getPhone())) {
                        mobileList.add(user.getPhone());
                        SendNotifyCenterTargetsVo targetsVo = new SendNotifyCenterTargetsVo();
                        targetsVo.setMobile(user.getPhone());
                        centerTargetsVos.add(targetsVo);
                    }
                }
            }
            if (mobileList.size() > 0) {
                StringBuffer messageTitle = new StringBuffer("");
                messageTitle.append("【营建新系统】待办提醒：");
                String description = projectTaskService.getDescription(projectInfo, null, "03");
                if (mobileList.size() > 0) {
                    portalService.sendMessage(mobileList, messageTitle.toString(), description);
                }
                if (ObjectUtil.isNotEmpty(centerTargetsVos)) {
                    projectTaskService.sendAsynchronous(centerTargetsVos, messageTitle.append(description).toString());
                }

            }
        }
    }
    private void compareTaskDate(String dateString, Integer[] noticeDay, ProjectTaskInfo projectTaskInfo, int i, String key, String taskType) {
        final java.sql.Date planEndDate = projectTaskInfo.getPlanEndDate();
        final String endDate = planEndDate.toString();
        //获取提醒天数
        final ProjectGroup projectGroup = projectGroupRepository.selectById(projectTaskInfo.getNodeId());
        if (ObjectUtil.isNotEmpty(projectGroup.getNoticeDay())) {
            noticeDay[0] = Math.negateExact(projectGroup.getNoticeDay());
        }
        //判断比较时间
        if (DateUtil.compareTodate(dateString, endDate) > i
                && !projectTaskInfo.getTaskType().equals(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey())) {
            //逾期
            projectTaskInfo.setTaskType(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey());
            projectTaskInfo.setTaskName(JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getSpec());
            this.update(projectTaskInfo);
        } else if (DateUtil.compareTodate(dateString, endDate) >= noticeDay[0]
                && DateUtil.compareTodate(dateString, endDate) <= i
                && !key.equals(taskType)) {
            //提醒
            projectTaskInfo.setTaskType(JhSystemEnum.TaskNameEnum.REMIND_TASK.getKey());
            projectTaskInfo.setTaskName(JhSystemEnum.TaskNameEnum.REMIND_TASK.getSpec());
            this.update(projectTaskInfo);
        }
    }

    @Override
    public List<ProjectTaskInfoDto> queryAll(ProjectTaskInfoQueryCriteria criteria) {
        //获取该项目的 工程问题整改的id
        List<Long> byProjectId = projectNodeInfoRepository.getRectifyTheProblemByProjectId(criteria.getProjectId());
        if (ObjectUtil.isNotEmpty(byProjectId)) {
            criteria.setProjectIds(byProjectId);
        }
        List<ProjectTaskInfoDto> taskInfoByProjectId = projectTaskInfoRepository.getProjectTaskInfoByProjectId(criteria);
        return taskInfoByProjectId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTaskInfoDto findById(Long projectTaskId) {
        ProjectTaskInfo projectTaskInfo = Optional.ofNullable(getById(projectTaskId)).orElseGet(ProjectTaskInfo::new);
        ValidationUtil.isNull(projectTaskInfo.getProjectTaskId(), getEntityClass().getSimpleName(), "projectTaskId", projectTaskId);
        return projectTaskInfoMapper.toDto(projectTaskInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTaskInfoDto create(ProjectTaskInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setProjectTaskId(snowflake.nextId());
        //final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
        //        .eq(ProjectGroup::getTemplateId, resources.getTemplateId())
        //        .eq(ProjectGroup::getProjectId, resources.getProjectId());
        //final ProjectGroup group = projectGroupRepository.selectOne(eq);
        //if (ObjectUtil.isNotEmpty(group)&&ObjectUtil.isNotEmpty(group.getParentId())) {
        //    final LambdaQueryWrapper<ProjectGroup> eqOne = Wrappers.lambdaQuery(ProjectGroup.class)
        //            .eq(ProjectGroup::getTemplateId, group.getParentId())
        //            .eq(ProjectGroup::getProjectId, resources.getProjectId());
        //    final ProjectGroup groupOne = projectGroupRepository.selectOne(eqOne);
        //    if (ObjectUtil.isNotEmpty(groupOne)&&ObjectUtil.isNotEmpty(groupOne.getTemplateId())){
        //        resources.setParentId(groupOne.getTemplateId());
        //    }
        //}
        save(resources);
        return findById(resources.getProjectTaskId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectTaskInfo resources) {
        ProjectTaskInfo projectTaskInfo = Optional.ofNullable(getById(resources.getProjectTaskId())).orElseGet(ProjectTaskInfo::new);
        ValidationUtil.isNull(projectTaskInfo.getProjectTaskId(), "ProjectTaskInfo", "id", resources.getProjectTaskId());
        projectTaskInfo.copy(resources);
        updateById(projectTaskInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectTaskId : ids) {
            projectTaskInfoRepository.deleteById(projectTaskId);
        }
    }

    @Override
    public void download(List<ProjectTaskInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectTaskInfoDto projectTaskInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("任务名称", projectTaskInfo.getTaskName());
            map.put("任务类型", projectTaskInfo.getTaskType());
            map.put("项目id", projectTaskInfo.getProjectId());
            map.put("节点id", projectTaskInfo.getNodeId());
            map.put("使用场景", projectTaskInfo.getUseCase());
            map.put("项目名称", projectTaskInfo.getProjectName());
            map.put("门店类型", projectTaskInfo.getStoreType());
            map.put("节点名称", projectTaskInfo.getNodeName());
            map.put("角色id", projectTaskInfo.getJobId());
            map.put("用户名", projectTaskInfo.getUserId());
            map.put("角色名称", projectTaskInfo.getJobName());
            map.put("计划结束时间", projectTaskInfo.getPlanEndDate());
            map.put("任务状态", projectTaskInfo.getTaskStatus());
            map.put(" isDelete", projectTaskInfo.getIsDelete());
            map.put("创建时间", projectTaskInfo.getCreateTime());
            map.put(" createBy", projectTaskInfo.getCreateBy());
            map.put("修改时间", projectTaskInfo.getUpdateTime());
            map.put("修改人", projectTaskInfo.getUpdateBy());
            map.put("是否可用", projectTaskInfo.getIsEnabled());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<ProjectTaskInfo> getProjectTaskInfo(Long projectId, Long nodeId, String jobCode) {
        return projectTaskInfoRepository.getProjectTaskInfo(projectId, jobCode);
    }

    @Override
    public List<ProjectTaskInfoDto> getTaskInfoByUser(String taskType, String storeType, String projectName, String templateCode, Boolean isMobile, String nodeCode) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<ProjectTaskInfoDto> taskInfoByUser = new LinkedList<>();
        String isCity = "";
        Boolean roleFlag = Boolean.FALSE;
        //判断用户角色
        Role role = projectInfoService.getRolePermission(currentUserId);
        taskInfoByUser = getProjectTaskInfoDtos(taskType, storeType, projectName, templateCode, isMobile, nodeCode, currentUserId, taskInfoByUser, role, null);
        //营建对接启动
        if (ObjectUtil.isNotEmpty(nodeCode) && "eng-00101".equals(nodeCode)) {
            taskInfoByUser = getProjectTaskInfoDtos(taskType, storeType, projectName, templateCode, isMobile, null, currentUserId, taskInfoByUser, role, "营建对接启动");
        }
        for (ProjectTaskInfoDto projectTaskInfoDto : taskInfoByUser) {
            String nodeName = projectTaskInfoDto.getNodeName();
            if (ObjectUtil.isNotEmpty(projectTaskInfoDto.getRoundMarking())) {
                projectTaskInfoDto.setNodeName(projectTaskInfoDto.getNodeName()+"(轮次"+projectTaskInfoDto.getRoundMarking()+")");
            }
            if ("todo_task".equals(projectTaskInfoDto.getTaskType()) && StringUtils.isNotEmpty(nodeName) && nodeName.contains("(审批)")) {
                projectTaskInfoDto.setProjectName(StringUtils.isNotEmpty(projectTaskInfoDto.getProjectName()) ? (projectTaskInfoDto.getProjectName() + "-" + projectTaskInfoDto.getUserName()) : projectTaskInfoDto.getUserName());
            }
        }

        return taskInfoByUser;
    }

    private List<ProjectTaskInfoDto> getProjectTaskInfoDtos(String taskType, String storeType, String projectName, String templateCode, Boolean isMobile, String nodeCode, Long currentUserId, List<ProjectTaskInfoDto> taskInfoByUser, Role role, String nodeName) {
        if (isMobile) {
            if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
                if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByStaForMobile(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
                } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByCityForMobile(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
                }

            } else {
                taskInfoByUser = projectTaskInfoRepository.getTaskInfoByUserForMobile(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
            }
        } else {
            if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
                if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByStaNew(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
                } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByCityNew(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
                }

            } else {
                taskInfoByUser = projectTaskInfoRepository.getTaskInfoByUserNew(currentUserId, taskType, storeType, projectName, nodeCode, nodeName, templateCode);
            }
        }
        return taskInfoByUser;
    }
//    @Override
//    public List<ProjectTaskInfoDto> getTaskInfoByUser(String taskType, String storeType, String projectName, Boolean isMobile) {
//        Long currentUserId = SecurityUtils.getCurrentUserId();
//        //修改任务类型
//        //updateTaskType();
//        List<ProjectTaskInfoDto> taskInfoByUser = new LinkedList<>();
//        String isCity = "";
//        Boolean roleFlag = Boolean.FALSE;
//        //判断用户角色
//        Role role = projectInfoService.getRolePermission(currentUserId);
//        if (isMobile) {
//            if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
//                if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
//                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByStaForMobile(currentUserId, taskType, storeType, projectName);
//                } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
//                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByCityForMobile(currentUserId, taskType, storeType, projectName);
//                }
//
//            } else {
//                taskInfoByUser = projectTaskInfoRepository.getTaskInfoByUserForMobile(currentUserId, taskType, storeType, projectName);
//            }
//        } else {
//            if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
//                if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
//                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoBySta(currentUserId, taskType, storeType, projectName);
//                } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
//                    taskInfoByUser = projectTaskInfoRepository.getTaskInfoByCity(currentUserId, taskType, storeType, projectName);
//                }
//
//            } else {
//                taskInfoByUser = projectTaskInfoRepository.getTaskInfoByUser(currentUserId, taskType, storeType, projectName);
//            }
//        }
//
//        //将列表进行按时间降序排序
////        Collections.sort(taskInfoByUser, new Comparator<ProjectTaskInfoDto>() {
////            @Override
////            public int compare(ProjectTaskInfoDto o1, ProjectTaskInfoDto o2) {
////                long time = o2.getCreateTime().getTime() - o1.getCreateTime().getTime();
////                if(time>0){
////                    return -1;
////                }else if(time==0){
////                    return 0;
////                }else if (time<0){
////                    return 1;
////                }
////                return (int) time;
////            }
////        });
//        Map<Long, ProjectInfo> projectCollect = new HashMap<>();
//        List<Long> projectIds = taskInfoByUser.stream().map(ProjectTaskInfoDto::getProjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(projectIds)) {
//            List<ProjectInfo> projectInfoList = projectInfoService.listByIds(projectIds);
//            projectCollect = projectInfoList.stream().collect(Collectors.toMap(ProjectInfo::getProjectId, e -> e));
//        }
//        Map<Long, OrderInfo> orderCollect = new HashMap<>();
//        List<Long> orderIds = taskInfoByUser.stream().map(ProjectTaskInfoDto::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(orderIds)) {
//            List<OrderInfo> orderInfoList = orderInfoService.listByIds(orderIds);
//            orderCollect = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getOrderId, e -> e));
//        }
//        for (ProjectTaskInfoDto projectTaskInfoDto : taskInfoByUser) {
//            String nodeName = projectTaskInfoDto.getNodeName();
//            if (projectCollect.get(projectTaskInfoDto.getProjectId()) != null) {
//                projectTaskInfoDto.setStoreId(projectCollect.get(projectTaskInfoDto.getProjectId()).getStoreId());
//                LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
//                        .eq(ProjectGroup::getNodeCode, projectTaskInfoDto.getNodeCode())
//                        .eq(ProjectGroup::getProjectId, projectTaskInfoDto.getProjectId());
//                ProjectGroup projectGroup = projectGroupRepository.selectOne(eq);
//                if (ObjectUtil.isNotEmpty(projectGroup)) {
//                    LambdaQueryWrapper<ProjectGroup> eqNode = Wrappers.lambdaQuery(ProjectGroup.class)
//                            .eq(ProjectGroup::getTemplateId, projectGroup.getParentId())
//                            .eq(ProjectGroup::getProjectId, projectGroup.getProjectId());
//                    ProjectGroup projectGroupNode = projectGroupRepository.selectOne(eqNode);
//                    if (projectTaskInfoDto.getTemplateId() == null && ObjectUtil.isNotEmpty(projectGroupNode)
//                            && ObjectUtil.isNotEmpty(projectGroupNode.getTemplateId())) {
//                        //final LambdaQueryWrapper<ProjectTaskInfo> taskInfoLambdaQueryWrapper = Wrappers
//                        //        .lambdaQuery(ProjectTaskInfo.class)
//                        //        .eq(ProjectTaskInfo::getProjectTaskId, projectTaskInfoDto.getProjectTaskId());
//                        //ProjectTaskInfo projectTaskInfo = new ProjectTaskInfo();
//                        //projectTaskInfo.setTemplateId(projectGroupNode.getTemplateId());
//                        projectTaskInfoDto.setTemplateId(projectGroupNode.getTemplateId());
//                        //projectTaskInfoRepository.update(projectTaskInfo,taskInfoLambdaQueryWrapper);
//                    }
//                }
//                if (projectTaskInfoDto.getStoreId() == null) {
//                    if (orderCollect.get(projectTaskInfoDto.getOrderId()) != null) {
//                        projectTaskInfoDto.setStoreId(orderCollect.get(projectTaskInfoDto.getOrderId()).getStoreId());
//                    }
//                }
//            } else {
//                if (orderCollect.get(projectTaskInfoDto.getOrderId()) != null) {
//                    projectTaskInfoDto.setStoreId(orderCollect.get(projectTaskInfoDto.getOrderId()).getStoreId());
//                }
//            }
//            if ("todo_task".equals(projectTaskInfoDto.getTaskType()) && StringUtils.isNotEmpty(nodeName) && nodeName.contains("(审批)")) {
//                projectTaskInfoDto.setProjectName(StringUtils.isNotEmpty(projectTaskInfoDto.getProjectName()) ? (projectTaskInfoDto.getProjectName() + "-" + projectTaskInfoDto.getUserName()) : projectTaskInfoDto.getUserName());
//            }
//
//            final ProjectInfo projectInfo = projectInfoRepository.selectById(projectTaskInfoDto.getProjectId());
//            if (ObjectUtil.isNotEmpty(projectInfo) && ObjectUtil.isNotEmpty(projectInfo.getCity())) {
//                projectTaskInfoDto.setCity(projectInfo.getCity());
//            }
//        }
//
//        return taskInfoByUser;
//    }

    @Override
    public ProjectTaskInfoHomePageDto getTodoTaskByUser(String taskType, String storeType, String projectName, String templateCode, Boolean isMobile, String nodeCode) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        ProjectTaskInfoHomePageDto pageDto = new ProjectTaskInfoHomePageDto();
        List<ProjectTaskInfoDto> taskInfo = this.getTaskInfoByUser(KidsSystemEnum.TaskTypeEnum.TODO_OVERDUE.getValue(), storeType, projectName, templateCode, Boolean.FALSE, nodeCode);

        List<ProjectTaskInfoDto> toTaskList = taskInfo.stream().filter(c -> !c.getNodeName().contains("审批")).collect(Collectors.toList());
        for (ProjectTaskInfoDto dto : toTaskList) {
            if(StringUtils.isEmpty(dto.getProjectName())) {
                SupplierInfo s = supplierInfoService.getById(dto.getProjectId());
                if (s != null) dto.setProjectName(s.getSupNameCn());
                SupplierPm p = supplierPmService.getById(dto.getProjectId());
                if (p != null) dto.setProjectName(p.getPmName());
            }
        }
        pageDto.setToTaskList(toTaskList);

        List<ProjectTaskInfoDto> toTaskApproveList = taskInfo.stream().filter(c -> c.getNodeName().contains("审批")).collect(Collectors.toList());
        pageDto.setToTaskApproveList(toTaskApproveList);

        ProjectNoticeQueryCriteria criteria = new ProjectNoticeQueryCriteria();
        criteria.setNoticeStatus(JhSystemEnum.NoticeStatus.SENT.getStatus());
        criteria.setUserId(currentUserId);
        List<ProjectNoticeDto> noticeDtos = projectNoticeService.queryAll(criteria);
        pageDto.setRemindTaskInfoList(noticeDtos);
        return pageDto;
    }

    @Override
    public List<NodeCodeCountVo> getTaskInfoByUserCountNew(String templateCode) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<NodeCodeCountVo> countVos = new LinkedList<>();
        //判断用户角色
        Role role = projectInfoService.getRolePermission(currentUserId);
        if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
            if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                List<ProjectGroupHomePageDto> secNodeCodes = projectTaskInfoRepository.getSECByNodeCodeAndStaCount(currentUserId, templateCode);
                if (ObjectUtil.isEmpty(templateCode)) {
                    NodeCodeCountVo nodeCodeCountVo = new NodeCodeCountVo("营建对接启动", "eng-00101",secNodeCodes.size() > 0 ? secNodeCodes.get(0).getYingjiecount() : 0, 1.0, "engineering", "email");
                    countVos.add(nodeCodeCountVo);
                }

                for (ProjectGroupHomePageDto secNodeCode : secNodeCodes) {
                    String nodeCode = secNodeCode.getNodeCode();
                    String nodeName = secNodeCode.getNodeName();
                    NodeCodeCountVo nodeCodeCountVoA = new NodeCodeCountVo(nodeName, nodeCode, secNodeCode.getCount(), secNodeCode.getNodeIndex(), secNodeCode.getTemplateCode(), secNodeCode.getSecondaryIcon());
                    countVos.add(nodeCodeCountVoA);
                }
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                List<ProjectGroupHomePageDto> secNodeCodes = projectTaskInfoRepository.getSECByNodeCodeAndCityCount(currentUserId, templateCode);
                if (ObjectUtil.isEmpty(templateCode)) {
                    NodeCodeCountVo nodeCodeCountVo = new NodeCodeCountVo("营建对接启动", "eng-00101", secNodeCodes.size() > 0 ? secNodeCodes.get(0).getYingjiecount() : 0, 1.0, "engineering", "email");
                    countVos.add(nodeCodeCountVo);
                }
                for (ProjectGroupHomePageDto secNodeCode : secNodeCodes) {
                    String nodeCode = secNodeCode.getNodeCode();
                    String nodeName = secNodeCode.getNodeName();
                    NodeCodeCountVo nodeCodeCountVoA = new NodeCodeCountVo(nodeName, nodeCode, secNodeCode.getCount(), secNodeCode.getNodeIndex(), secNodeCode.getTemplateCode(), secNodeCode.getSecondaryIcon());
                    countVos.add(nodeCodeCountVoA);
                }
            }
        } else {
            List<ProjectGroupHomePageDto> secNodeCodes = projectTaskInfoRepository.getSECByNodeCodeAndUserCount(currentUserId,  templateCode);
            if (ObjectUtil.isEmpty(templateCode)) {
                NodeCodeCountVo nodeCodeCountVo = new NodeCodeCountVo("营建对接启动", "eng-00101", secNodeCodes.size() > 0 ? secNodeCodes.get(0).getYingjiecount() : 0, 1.0, "engineering", "email");
                countVos.add(nodeCodeCountVo);
            }
            for (ProjectGroupHomePageDto secNodeCode : secNodeCodes) {
                String nodeCode = secNodeCode.getNodeCode();
                String nodeName = secNodeCode.getNodeName();
                NodeCodeCountVo nodeCodeCountVoA = new NodeCodeCountVo(nodeName, nodeCode, secNodeCode.getCount(), secNodeCode.getNodeIndex(), secNodeCode.getTemplateCode(), secNodeCode.getSecondaryIcon());
                countVos.add(nodeCodeCountVoA);
            }
        }
        return countVos;
    }

    @Override
    public boolean projectApproval(Long projectId, Long nodeId, Map<Long, Long> roles, KidsSystemEnum.TaskTypeEnum taskType) {
        LambdaQueryWrapper<ProjectInfo> projectInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, projectId);
        ProjectInfo projectInfo = projectInfoRepository.selectOne(projectInfoLambdaQueryWrapper);
        ProjectNodeInfo nodeInfo = null;
        if (nodeId != null) {
            util.initialize(projectNodeInfoService.getSubmeterProjectId(projectId));
            nodeInfo = projectNodeInfoRepository.selectById(nodeId);
        }
        List<ProjectTaskInfo> infoList = new ArrayList<>();
        ProjectNodeInfo finalNodeInfo = nodeInfo;
        List<Long> userId = new ArrayList<>();

        Date date = new Date(System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000);
        roles.forEach((k, v) -> {
            Role role = roleRepository.findRoleByRoleId(v);
            ProjectTaskInfo taskInfo = new ProjectTaskInfo();
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            Long projectNoticeId = snowflake.nextId();
            taskInfo.setProjectTaskId(projectNoticeId);
            taskInfo.setTaskName(taskType.getLabel());
            taskInfo.setTaskType(taskType.getValue());
            taskInfo.setProjectId(projectInfo.getProjectId());
            taskInfo.setUseCase("1");
            taskInfo.setProjectName(projectInfo.getProjectName());
            taskInfo.setProjectType("new");
            if (finalNodeInfo != null) {
                taskInfo.setNodeId(nodeId);
                taskInfo.setNodeName(finalNodeInfo.getNodeName());
                taskInfo.setAddress(AtourSystemEnum.jumpAddressEnum.SECOND.getKey());
            } else {
                taskInfo.setNodeName("营建对接启动");
                taskInfo.setAddress(AtourSystemEnum.jumpAddressEnum.BUILDINGKICK.getKey());
            }
            taskInfo.setJobId(v);
            taskInfo.setUserId(k);
            taskInfo.setJobName(role.getName());
            //两天逾期
            taskInfo.setPlanEndDate(date);
            taskInfo.setTaskStatus(KidsSystemEnum.TaskStatusEnum.UNFINISH.getValue());
            taskInfo.setIsDelete(Boolean.FALSE);
            taskInfo.setIsEnabled(Boolean.TRUE);
            infoList.add(taskInfo);
            userId.add(k);
        });
        if (ObjectUtil.isEmpty(finalNodeInfo)) {
            //发送信息通知，出错不影响程序运行,发送企微消息
            sendMyMessage(projectInfo, new StringBuffer("【营建新系统】待办提醒："), new StringBuffer(""), ObjectUtil.isNotEmpty(date) ? date.toString() : "", userId);
        }
        return this.saveBatch(infoList);
    }


    /**
     * @param
     * @param messageTitle
     * @param messageText
     */
    private void sendMyMessage(ProjectInfo myInfo, StringBuffer messageTitle, StringBuffer messageText, String endDate, List<Long> userId) {
        //发送信息对应负责任信息

        try {
            if (myInfo != null) {
                List<User> userList = userRepository.findByUserIds(userId);
                if (userList != null && userList.size() > 0) {
                    List<String> mobileList = new ArrayList<>();
                    for (User user : userList) {
                        if (StringUtils.isNotEmpty(user.getPhone()) && "atour".equals(user.getTk())) {
                            mobileList.add(user.getPhone());
                        }
                    }
                    if (mobileList.size() > 0) {
                        portalService.sendMessage(mobileList, messageTitle.toString(),
                                messageText.append("您有新的「营建对接启动」任务。项目名：")
                                        .append(myInfo.getProjectName()).append("，任务结束时间是：").append(endDate).append(",请您尽快登录系统进行处理。\n系统链接：https://acms.yaduo.com/").toString());

                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean projectApprovalOperation(Long projectTaskId) {
        LambdaUpdateWrapper<ProjectTaskInfo> projectTaskInfoWrapper = Wrappers.lambdaUpdate(ProjectTaskInfo.class)
                .eq(ProjectTaskInfo::getProjectTaskId, projectTaskId)
                .set(ProjectTaskInfo::getTaskStatus, KidsSystemEnum.TaskStatusEnum.FINISH.getValue());
        return update(projectTaskInfoWrapper);
    }

    @Override
    public List<ProjectTaskInfo> getProjectTaskInfoNodeName(Long projectId, Long userId, String nodeName) {
        //查询对应的代办数据
        LambdaQueryWrapper<ProjectTaskInfo> queryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class)
                .eq(ProjectTaskInfo::getProjectId, projectId)
                .eq(ProjectTaskInfo::getUserId, userId)
                .eq(ProjectTaskInfo::getNodeName, nodeName);
        return list(queryWrapper);
    }

    @Override
    public void stakeholderUpdateInTime() {
        projectStakeholdersService.stakeholderInTerm();
        projectStakeholdersService.stakeholderNoTerm();
    }
}