/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.Reimburse;
import com.bassims.modules.atour.repository.ReimburseRepository;
import com.bassims.modules.atour.service.ReimburseItemService;
import com.bassims.modules.atour.service.ReimburseService;
import com.bassims.modules.atour.service.dto.ReimburseDto;
import com.bassims.modules.atour.service.dto.ReimburseItemQueryCriteria;
import com.bassims.modules.atour.service.dto.ReimburseQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ReimburseMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-12-21
**/
@Service
public class ReimburseServiceImpl extends BaseServiceImpl<ReimburseRepository,Reimburse> implements ReimburseService {

    private static final Logger logger = LoggerFactory.getLogger(ReimburseServiceImpl.class);

    @Autowired
    private ReimburseRepository reimburseRepository;
    @Autowired
    private ReimburseMapper reimburseMapper;
    @Autowired
    private ReimburseItemService reimburseItemService;

    @Override
    public Map<String,Object> queryAll(ReimburseQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<Reimburse> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(Reimburse.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", reimburseMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ReimburseDto> queryAll(ReimburseQueryCriteria criteria){
        return reimburseMapper.toDto(list(QueryHelpPlus.getPredicate(Reimburse.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReimburseDto findById(Long reimburseId) {
        Reimburse reimburse = Optional.ofNullable(getById(reimburseId)).orElseGet(Reimburse::new);
        ValidationUtil.isNull(reimburse.getReimburseId(),getEntityClass().getSimpleName(),"reimburseId",reimburseId);
        return reimburseMapper.toDto(reimburse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReimburseDto findByPaymentApplicationId(Long paymentApplicationId) {
        LambdaQueryWrapper<Reimburse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Reimburse::getPaymentApplicationId,paymentApplicationId);
        queryWrapper.eq(Reimburse::getIsDelete,false);
        ReimburseDto reimburseDto = reimburseMapper.toDto(getOne(queryWrapper));
        if (reimburseDto!=null){
            ReimburseItemQueryCriteria reimburseItemQueryCriteria = new ReimburseItemQueryCriteria();
            reimburseItemQueryCriteria.setReimburseId(reimburseDto.getReimburseId());
            reimburseDto.setReimburseItemDtoList(reimburseItemService.queryAll(reimburseItemQueryCriteria));
        }
        return reimburseDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReimburseDto create(Reimburse resources) {
        save(resources);
        return findById(resources.getReimburseId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Reimburse resources) {
        Reimburse reimburse = Optional.ofNullable(getById(resources.getReimburseId())).orElseGet(Reimburse::new);
        ValidationUtil.isNull( reimburse.getReimburseId(),"Reimburse","id",resources.getReimburseId());
        reimburse.copy(resources);
        updateById(reimburse);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long reimburseId : ids) {
            reimburseRepository.deleteById(reimburseId);
        }
    }

    @Override
    public void download(List<ReimburseDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ReimburseDto reimburse : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("付款申请id", reimburse.getPaymentApplicationId());
            map.put("报销合计", reimburse.getReimburseTotal());
            map.put("税额合计", reimburse.getTaxTotal());
            map.put("不含税金额合计", reimburse.getReimburseToTaxTotal());
            map.put("创建时间", reimburse.getCreateTime());
            map.put("更新时间", reimburse.getUpdateTime());
            map.put("创建人", reimburse.getCreateBy());
            map.put("更新人", reimburse.getUpdateBy());
            map.put("是否删除", reimburse.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}