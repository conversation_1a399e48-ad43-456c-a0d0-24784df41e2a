/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import com.bassims.utils.QueryHelpPlus;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-10
**/
@Data
public class UnauthorizedConstructionQueryCriteria{


    /*项目id*/
    @JSONField(serializeUsing = ToStringSerializer.class)
    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "projectId")
    private Long projectId;

    /** 节点编码 */
    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "nodeCode")
    private String nodeCode;


}