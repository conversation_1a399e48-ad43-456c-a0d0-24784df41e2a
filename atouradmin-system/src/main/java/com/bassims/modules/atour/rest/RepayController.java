/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.Repay;
import com.bassims.modules.atour.service.RepayService;
import com.bassims.modules.atour.service.dto.RepayDto;
import com.bassims.modules.atour.service.dto.RepayQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-29
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "s_repay管理")
@RequestMapping("/api/repay")
public class RepayController {

    private static final Logger logger = LoggerFactory.getLogger(RepayController.class);

    private final RepayService repayService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, RepayQueryCriteria criteria) throws IOException {
        repayService.download(repayService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<RepayDto>>}
    */
    @GetMapping("/list")
    @Log("查询s_repay")
    @ApiOperation("查询s_repay")
    public ResponseEntity<Object> query(RepayQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(repayService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<RepayDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询s_repay")
    @ApiOperation("查询s_repay")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(repayService.findById(id),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<RepayDto>}
     */
    @GetMapping(value = "/queryRepay")
    @Log("通过Id查询s_repay")
    @ApiOperation("查询s_repay")
    public ResponseEntity<Object> queryRepay(Long projectId,String nodeCode){
        return new ResponseEntity<>(repayService.findByProjectIdAndCode(projectId,nodeCode),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增s_repay")
    @ApiOperation("新增s_repay")
    public ResponseEntity<Object> create(@Validated @RequestBody Repay resources){
        return new ResponseEntity<>(repayService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改s_repay")
    @ApiOperation("修改s_repay")
    public ResponseEntity<Object> update(@Validated @RequestBody Repay resources){
        repayService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除s_repay")
    @ApiOperation("删除s_repay")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        repayService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}