/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.JobUser;
import com.bassims.modules.atour.repository.JobUserRepository;
import com.bassims.modules.atour.service.JobUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-04-08
**/
@Service
public class JobUserServiceImpl   implements JobUserService {

    private static final Logger logger = LoggerFactory.getLogger(JobUserServiceImpl.class);

    @Autowired
    private JobUserRepository jobUserRepository;


    @Override
    public List<JobUser> findByJobName(String jobName, Long areaCode) {
        List<JobUser> userList = jobUserRepository.findByJobName(jobName, areaCode);
        return userList;
    }
}