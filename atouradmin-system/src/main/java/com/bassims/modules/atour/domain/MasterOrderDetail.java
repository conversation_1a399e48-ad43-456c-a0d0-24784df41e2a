/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-22
**/
@Data
@TableName(value="z_master_order_detail")
public class MasterOrderDetail implements Serializable {

    @TableId(value = "detail_id")
    @ApiModelProperty(value = "订单详情主键")
    private Long detailId;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @TableField(value = "materiel_id")
    @ApiModelProperty(value = "物料表主键")
    private Long materielId;

    @TableField(value = "finance_code")
    @ApiModelProperty(value = "资产编号")
    private String financeCode;

    @TableField(value = "product_name")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @TableField(value = "product_code")
    @ApiModelProperty(value = "产品编号")
    private String productCode;

    @TableField(value = "materiel_code")
    @ApiModelProperty(value = "物料编号")
    private String materielCode;

    @TableField(value = "senior_class")
    @ApiModelProperty(value = "一级分类")
    private String seniorClass;

    @TableField(value = "second_class")
    @ApiModelProperty(value = "二级分类")
    private String secondClass;

    @TableField(value = "brand")
    @ApiModelProperty(value = "所属品牌")
    private String brand;

    @TableField(value = "spec")
    @ApiModelProperty(value = "规格")
    private String spec;

    @TableField(value = "model")
    @ApiModelProperty(value = "型号")
    private String model;

    @TableField(value = "unit")
    @ApiModelProperty(value = "单位")
    private String unit;

    @TableField(value = "suggest_unit_price")
    @ApiModelProperty(value = "建议单价")
    private BigDecimal suggestUnitPrice;

    @TableField(value = "order_stock")
    @ApiModelProperty(value = "预约单库存")
    private BigDecimal orderStock;

    @TableField(value = "supplier_id")
    @ApiModelProperty(value = "厂商id")
    private Long supplierId;

    @TableField(value = "sup_name_cn")
    @ApiModelProperty(value = "厂商中文名称")
    private String supNameCn;

    @TableField(value = "service_num")
    @ApiModelProperty(value = "服务区数量")
    private BigDecimal serviceNum;

    @TableField(value = "toy_num")
    @ApiModelProperty(value = "玩具区数量")
    private BigDecimal toyNum;

    @TableField(value = "daily_num")
    @ApiModelProperty(value = "用品区数量")
    private BigDecimal dailyNum;

    @TableField(value = "fast_num")
    @ApiModelProperty(value = "快消区数量")
    private BigDecimal fastNum;

    @TableField(value = "spin_num")
    @ApiModelProperty(value = "纺织区数量")
    private BigDecimal spinNum;

    @TableField(value = "is_match")
    @ApiModelProperty(value = "是否配件:1 配件 0不配件")
    private Integer isMatch;

    @TableField(value = "unit_price")
    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @TableField(value = "rate")
    @ApiModelProperty(value = "税率")
    private BigDecimal rate;

    @TableField(value = "contract_no")
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @TableField(value = "end_time")
    @ApiModelProperty(value = "到期时间")
    private Timestamp endTime;

    @TableField(value = "account_adjust_num")
    @ApiModelProperty(value = "核算调整数量")
    private BigDecimal accountAdjustNum;

    @TableField(value = "subtotal")
    @ApiModelProperty(value = "小计")
    private BigDecimal subtotal;

    @TableField(value = "total_price")
    @ApiModelProperty(value = "总计")
    private BigDecimal totalPrice;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    public void copy(MasterOrderDetail source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}