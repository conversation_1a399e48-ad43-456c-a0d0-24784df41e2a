/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.RoleUser;
import com.bassims.modules.atour.domain.StakeholdersReq;
import com.bassims.modules.atour.domain.StakeholdersRoleReq;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-04-08
**/
@Repository
public interface RoleUserRepository extends BaseMapper<RoleUser> {


    /**
     * 根据职位名查询
     * @param roleName 职位名称
     * @return /
     */

    List<RoleUser> findByRoleName(String roleName, Long areaCode);


    List<RoleUser> findByRoleNames(List<String> roleNames, Long areaCode);

    /**
     * 根据角色code获取用户
     * @param roleCode
     * @return
     */
    List<RoleUser> queryUserByRole(String roleCode);

    List<Long> getAccessUser(Long userId);

    List<StakeholdersRoleReq> selectRoleByCode(List<String> roleCode);
}