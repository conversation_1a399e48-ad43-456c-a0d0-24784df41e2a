/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.FeeInfo;
import com.bassims.modules.atour.service.FeeInfoService;
import com.bassims.modules.atour.service.dto.FeeInfoDto;
import com.bassims.modules.atour.service.dto.FeeInfoQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-04-18
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_fee_info管理")
@RequestMapping("/api/feeInfo")
public class FeeInfoController {

    private static final Logger logger = LoggerFactory.getLogger(FeeInfoController.class);

    private final FeeInfoService feeInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, FeeInfoQueryCriteria criteria) throws IOException {
        feeInfoService.download(feeInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<FeeInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_fee_info")
    @ApiOperation("查询t_fee_info")
    public ResponseEntity<Object> query(FeeInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(feeInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<FeeInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_fee_info")
    @ApiOperation("查询t_fee_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(feeInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_fee_info")
    @ApiOperation("新增t_fee_info")
    public ResponseEntity<Object> create(@Validated @RequestBody FeeInfo resources){
        return new ResponseEntity<>(feeInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_fee_info")
    @ApiOperation("修改t_fee_info")
    public ResponseEntity<Object> update(@Validated @RequestBody FeeInfo resources){
        feeInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_fee_info")
    @ApiOperation("删除t_fee_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        feeInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/importFeeInfo")
    @Log("读取Excel数据插入t_fee_info")
    @ApiOperation("读取Excel数据插入t_fee_info")
    public ResponseEntity<Object> importFeeInfo(MultipartFile file, Long projectId){
        return new ResponseEntity<>(feeInfoService.importFeeInfo(file,projectId),HttpStatus.CREATED);
    }
}