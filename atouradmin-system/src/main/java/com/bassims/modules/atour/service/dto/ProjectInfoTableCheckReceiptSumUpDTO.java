/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO 验收单各项
 * @date 2023/11/26
 */
@Data
public class ProjectInfoTableCheckReceiptSumUpDTO implements Serializable {

    /*key*/
    private String tertiaryKey;

    /*字典*/
    private String startSign;

    /*类型*/
    private String tertiaryType;

    /*值*/
    private String tertiaryValue;

}