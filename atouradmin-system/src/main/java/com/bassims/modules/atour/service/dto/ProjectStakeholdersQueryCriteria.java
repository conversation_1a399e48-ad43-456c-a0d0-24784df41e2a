/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-29
**/
@Data
public class ProjectStakeholdersQueryCriteria{
    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "projectId" )
    private Long projectId;

    @QueryPlus(type = QueryPlus.Type.IS_NULL,propName = "orderId" )
    private String orderIdNull;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "roleCode" )
    private String roleCode;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "projectNo" )
    private String projectNo;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "projectName" )
    private String projectName;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "userId" )
    private Long userId;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "roleName" )
    private String roleName;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "userName" )
    private String userName;


    @QueryPlus(type = QueryPlus.Type.IN,propName = "roleCode" )
    private String[] roleCodes;

}