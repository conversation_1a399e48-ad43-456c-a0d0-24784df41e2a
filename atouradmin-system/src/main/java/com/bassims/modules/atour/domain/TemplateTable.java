/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-15
**/
@Data
@TableName(value="t_template_table")
public class TemplateTable implements Serializable {

    @TableId(value = "template_table_id")
    @ApiModelProperty(value = "模版表格id")
    private Long templateTableId;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "表格节点编码")
    private String nodeCode;

    @TableField(value = "remark")
    @ApiModelProperty(value = "表格节点备注（json）")
    private String remark;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号")
    private String projectVersion;


    @TableField(value = "node_wbs")
    @ApiModelProperty(value = "节点序号")
    private Integer nodeWbs;

    @TableField(value = "node_index")
    @ApiModelProperty(value = "子节点排序")
    private double nodeIndex;

    @TableField(value = "node_level")
    @ApiModelProperty(value = "节点等级")
    private Integer nodeLevel;

    @TableField(value = "node_type")
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @TableField(value = "node_status")
    @ApiModelProperty(value = "节点状态")
    private String nodeStatus;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "isDelete")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_edit")
    @ApiModelProperty(value = "是否可以编辑")
    private String isEdit;

    @TableField(value = "added_version")
    @ApiModelProperty(value = "用户添加的版本")
    private Integer addedVersion;

    @TableField(value = "dynamic_table_type")
    @ApiModelProperty(value = "动态表格的类型")
    private String dynamicTableType;


    @TableField(value = "relation_name")
    @ApiModelProperty(value = "关联三级的name")
    private String relationName;

    @TableField(value = "relation_code")
    @ApiModelProperty(value = "关联三级的nodecode")
    private String relationCode;

    @TableField(value = "table_type")
    @ApiModelProperty(value = "小类型")
    private String tableType;

    @TableField(value = "role_code")
    @ApiModelProperty(value = "责任人角色code")
    private String roleCode;



    @TableField(exist = false)
    @ApiModelProperty(value = "是否必采（必采-must_pay 非必采-febitane）")
    private String isMustMined;

    @TableField(exist = false)
    @ApiModelProperty(value = "安全文明施工类型（办公室管理-office_administrator 临时水电 -temporary_hydropower 工人住宿-accommodation_workers）")
    private String constructionType;


    public void copy(TemplateTable source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}