/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplatePresentationAnnex;
import com.bassims.modules.atour.service.TemplatePresentationAnnexService;
import com.bassims.modules.atour.service.dto.TemplatePresentationAnnexDto;
import com.bassims.modules.atour.service.dto.TemplatePresentationAnnexQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-24
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "系统宣讲部分附件模版管理")
@RequestMapping("/api/templatePresentationAnnex")
public class TemplatePresentationAnnexController {

    private static final Logger logger = LoggerFactory.getLogger(TemplatePresentationAnnexController.class);

    private final TemplatePresentationAnnexService templatePresentationAnnexService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplatePresentationAnnexQueryCriteria criteria) throws IOException {
        templatePresentationAnnexService.download(templatePresentationAnnexService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplatePresentationAnnexDto>>}
    */
    @GetMapping("/list")
    @Log("查询系统宣讲部分附件模版")
    @ApiOperation("查询系统宣讲部分附件模版")
    public ResponseEntity<Object> query(TemplatePresentationAnnexQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templatePresentationAnnexService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplatePresentationAnnexDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询系统宣讲部分附件模版")
    @ApiOperation("查询系统宣讲部分附件模版")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templatePresentationAnnexService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增系统宣讲部分附件模版")
    @ApiOperation("新增系统宣讲部分附件模版")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplatePresentationAnnex resources){
        return new ResponseEntity<>(templatePresentationAnnexService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改系统宣讲部分附件模版")
    @ApiOperation("修改系统宣讲部分附件模版")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplatePresentationAnnex resources){
        templatePresentationAnnexService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除系统宣讲部分附件模版")
    @ApiOperation("删除系统宣讲部分附件模版")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templatePresentationAnnexService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}