/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.TemplateOutputRelation;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateOutputRelationRepository;
import com.bassims.modules.atour.service.TemplateOutputRelationService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateOutputRelationDto;
import com.bassims.modules.atour.service.dto.TemplateOutputRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateOutputRelationMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-23
**/
@Service
public class TemplateOutputRelationServiceImpl extends BaseServiceImpl<TemplateOutputRelationRepository,TemplateOutputRelation> implements TemplateOutputRelationService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateOutputRelationServiceImpl.class);

    @Autowired
    private TemplateOutputRelationRepository templateOutputRelationRepository;
    @Autowired
    private TemplateOutputRelationMapper templateOutputRelationMapper;

    @Override
    public Map<String,Object> queryAll(TemplateOutputRelationQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<TemplateOutputRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateOutputRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateOutputRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateOutputRelationDto> queryAll(TemplateOutputRelationQueryCriteria criteria){
        return templateOutputRelationMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateOutputRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateOutputRelationDto findById(Long templateOutputRelationId) {
        TemplateOutputRelation templateOutputRelation = Optional.ofNullable(getById(templateOutputRelationId)).orElseGet(TemplateOutputRelation::new);
        ValidationUtil.isNull(templateOutputRelation.getTemplateOutputRelationId(),getEntityClass().getSimpleName(),"templateOutputRelationId",templateOutputRelationId);
        return templateOutputRelationMapper.toDto(templateOutputRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateOutputRelationDto create(TemplateOutputRelation resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTemplateOutputRelationId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getTemplateOutputRelationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateOutputRelation resources) {
        TemplateOutputRelation templateOutputRelation = Optional.ofNullable(getById(resources.getTemplateOutputRelationId())).orElseGet(TemplateOutputRelation::new);
        ValidationUtil.isNull( templateOutputRelation.getTemplateOutputRelationId(),"TemplateOutputRelation","id",resources.getTemplateOutputRelationId());
        templateOutputRelation.copy(resources);
        updateById(templateOutputRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateOutputRelationId : ids) {
            templateOutputRelationRepository.deleteById(templateOutputRelationId);
        }
    }

    @Override
    public void download(List<TemplateOutputRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateOutputRelationDto templateOutputRelation : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("联合关系名称", templateOutputRelation.getAssociationName());
            map.put("联合编码", templateOutputRelation.getJointCode());
            map.put("二级节点ID", templateOutputRelation.getTemplateGroupId());
            map.put("二级节点编码", templateOutputRelation.getNodeCode());
            map.put("二级节点名称", templateOutputRelation.getNodeName());
            map.put("关联二级节点ID", templateOutputRelation.getRelevanceTemplateGroupId());
            map.put("关联二级节点编码", templateOutputRelation.getRelevanceNodeCode());
            map.put("关联二级节点name", templateOutputRelation.getRelevanceNodeName());
            map.put(" createTime",  templateOutputRelation.getCreateTime());
            map.put(" updateTime",  templateOutputRelation.getUpdateTime());
            map.put(" createBy",  templateOutputRelation.getCreateBy());
            map.put(" updateBy",  templateOutputRelation.getUpdateBy());
            map.put("是否可用", templateOutputRelation.getIsEnabled());
            map.put("是否删除", templateOutputRelation.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}