package com.bassims.modules.atour.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SummaryOrder {
    private Long projectId;

    /** 项目类型 */
    private String projectType;

    /** 费用类型 */
    private String feeType;

    /** 费用小类 */
    private String feeSub;

    /** 总合同金额 */
    private String orderConMoney;

    /** 总审定金额 */
    private String approvedMoney;

    /** 税率 */
    private String rate;

    /** 供应商 */
    private String sup;

    /** 结算金额 */
    private String settleProcess;

    /** 已付金额 */
    private BigDecimal payMoney;

    /** 未付金额 */
    private BigDecimal unpayMoney;

    /** 订单号&合同编号 */
    private String orderConNo;

    /** 订购单号/采购单号 */
    private String purchaseNo;

    /** 付款单号 */
    private String payOrder;

    /** 付款金额 */
    private BigDecimal predictMoney;

    /** 付款时间 */
    private String payTime;

    private String secOrder;

    private BigDecimal secMoney;

    private String secPayTime;

    private String thirOrder;

    private BigDecimal thirMoney;

    private String thirPayTime;
}
