/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.OrderNodeInfo;
import com.bassims.modules.atour.service.OrderNodeInfoService;
import com.bassims.modules.atour.service.dto.OrderNodeInfoDto;
import com.bassims.modules.atour.service.dto.OrderNodeInfoQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-23
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_order_node_info管理")
@RequestMapping("/api/orderNodeInfo")
public class OrderNodeInfoController {

    private static final Logger logger = LoggerFactory.getLogger(OrderNodeInfoController.class);

    private final OrderNodeInfoService orderNodeInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, OrderNodeInfoQueryCriteria criteria) throws IOException {
        orderNodeInfoService.download(orderNodeInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<OrderNodeInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_order_node_info")
    @ApiOperation("查询t_order_node_info")
    public ResponseEntity<Object> query(OrderNodeInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(orderNodeInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<OrderNodeInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_order_node_info")
    @ApiOperation("查询t_order_node_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(orderNodeInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_order_node_info")
    @ApiOperation("新增t_order_node_info")
    public ResponseEntity<Object> create(@Validated @RequestBody OrderNodeInfo resources){
        return new ResponseEntity<>(orderNodeInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_order_node_info")
    @ApiOperation("修改t_order_node_info")
    public ResponseEntity<Object> update(@Validated @RequestBody OrderNodeInfo resources){
        orderNodeInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_order_node_info")
    @ApiOperation("删除t_order_node_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        orderNodeInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/updateData")
    @Log("修改项目节点")
    @ApiOperation("修改项目节点")
    public ResponseEntity<Object> updateData(@Validated @RequestBody List<OrderNodeInfoDto> list) {
        return new ResponseEntity<>(orderNodeInfoService.updateData(list,Boolean.FALSE ), HttpStatus.OK);
    }

    @PostMapping("/submit")
    @Log("提交节点")
    @ApiOperation("提交节点 : 此传递是二级节点")
    public ResponseEntity<Object> submit(@Validated @RequestBody OrderNodeInfoDto orderNodeInfoDto) {
        return new ResponseEntity<>(orderNodeInfoService.submit(orderNodeInfoDto), HttpStatus.OK);
    }

    @GetMapping("/orderTree")
    @Log("获取项目模板树")
    @ApiOperation("获取项目模板树")
    public ResponseEntity<Object> orderTree(Long orderId, String useCase, Boolean isMobile) {
        return new ResponseEntity<>(orderNodeInfoService.orderTree(orderId,useCase,isMobile,4 ), HttpStatus.OK);
    }

    @GetMapping("/orderTreeByNodeCode")
    @Log("通过nodeCode获取项目模板树")
    @ApiOperation("通过nodeCode获取项目模板树")
    public ResponseEntity<Object> orderTreeByNodeCode(Long orderId, String nodeCode, String useCase, Boolean isMobile) {
        return new ResponseEntity<>(orderNodeInfoService.orderTreeByNodeCode(orderId, nodeCode,useCase,isMobile), HttpStatus.OK);
    }

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/downloadSettleNode")
    public void downloadSettleNode(HttpServletResponse response, OrderNodeInfoQueryCriteria criteria) throws IOException {
        orderNodeInfoService.downloadSettleData(response,criteria);
    }

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/downloadOrderInfo")
    public void downloadOrderInfo(HttpServletResponse response, OrderNodeInfoQueryCriteria criteria) throws IOException {
        orderNodeInfoService.downloadOrderData(response,criteria);
    }
}