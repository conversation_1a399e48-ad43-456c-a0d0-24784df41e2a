/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description / 项目验收状态表
* @date 2023-12-04
**/
@Data
@TableName(value="t_project_accept_status")
public class ProjectAcceptStatus implements Serializable {

    @TableId(value = "project_accept_status_id")
    @ApiModelProperty(value = "主键id")
    private Long projectAcceptStatusId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "post")
    @ApiModelProperty(value = "post")
    private String post;

    @TableField(value = "status")
    @ApiModelProperty(value = "post：1：true成功；0：false失败")
    private String status;

    public void copy(ProjectAcceptStatus source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}