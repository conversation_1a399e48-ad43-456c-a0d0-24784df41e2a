/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectApproveDetail;
import com.bassims.modules.atour.service.ProjectApproveDetailService;
import com.bassims.modules.atour.service.dto.ProjectApproveDetailQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-28
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目审批详情管理")
@RequestMapping("/api/projectApproveDetail")
public class ProjectApproveDetailController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApproveDetailController.class);

    private final ProjectApproveDetailService projectApproveDetailService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectApproveDetailQueryCriteria criteria) throws IOException {
        projectApproveDetailService.download(projectApproveDetailService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ProjectApproveDetailDto>>}
    */
    @GetMapping("/page")
//    @Log("查询项目审批详情")
    @ApiOperation("查询项目审批详情")
    public ResponseEntity<Object> page(ProjectApproveDetailQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectApproveDetailService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectApproveDetailDto>>}
     */
    @GetMapping("/list")
//    @Log("查询项目审批详情")
    @ApiOperation("查询项目审批详情")
    public ResponseEntity<Object> list(ProjectApproveDetailQueryCriteria criteria){
        return new ResponseEntity<>(projectApproveDetailService.queryAll(criteria), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ProjectApproveDetailDto>}
    */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询项目审批详情")
    @ApiOperation("查询项目审批详情")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectApproveDetailService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目审批详情")
    @ApiOperation("新增项目审批详情")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectApproveDetail resources){
        return new ResponseEntity<>(projectApproveDetailService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目审批详情")
    @ApiOperation("修改项目审批详情")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectApproveDetail resources){
        projectApproveDetailService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目审批详情")
    @ApiOperation("删除项目审批详情")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectApproveDetailService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}