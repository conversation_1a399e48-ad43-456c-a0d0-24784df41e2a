package com.bassims.modules.atour.domain.vo;

import com.bassims.modules.atour.service.dto.OrderDetailDto;
import com.bassims.modules.atour.service.dto.SpecialProjectDto;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Data
public class TempOrder {
    /** 订单主键 */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 项目主表 */
    private Long projectId;

    /** 项目编码 */
    private String projectNo;

    /** 门店id */
    private Long storeId;

    /** 门店名称 */
    private String storeName;

    /** 门店编码 */
    private String storeNo;

    /** 项目名称 */
    private String projectName;

    /** 项目类型 */
    private String projectType;

    /** 门店类型 */
    private String storeType;

    /** 订单类型 */
    private String orderType;

    /** 费用类型 */
    private String costType;

    /** 订单状态 */
    private String orderStatus;

    /** 专项采购说明 */
    private String purchaseDesc;

    /** 订单创建人 */
    private String addUser;

    /** 订单创建时间 */
    private String addTime;

    /** 创建人电话 */
    private String addPhone;

    /** 工程经理 */
    private String receiver;

    /** 工程经理电话 */
    private String receivePhone;

    /** 要求到货时间 */
    private String arrivalTime;

    /** 要求开始安装时间 */
    private String startInstallTime;

    /** 要求结束安装时间 */
    private String endInstallTime;

    /** 人员编码 */
    private String empCode;

    /** 人员名称 */
    private String empName;

    /** 人事组织编码 */
    private String hrOrgCode;

    /** 人事组织名称 */
    private String hrOrgName;

    /** 仓库编码 */
    private String whCode;

    /** 仓库名称 */
    private String whName;

    /** 仓库类型 */
    private String whType;

    /** REM备注 */
    private String remark;

    /** 订单金额 */
    private BigDecimal orderAmount;

    /** 厂商id */
    private Long supplierId;

    /** 厂商名称 */
    private String supNameCn;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 二级字段合集 */
    private String secondClass;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 物流单号 */
    private String waybillNo;

    /** 发货人 */
    private String senderName;

    /** 发货人联系电话 */
    private String senderPhone;

    /** 发货时间 */
    private String actualSendTime;

    /** 收货人 */
    private String receiveName;

    private List<OrderDetailDto> orderDetail;
    private List<SpecialProjectDto> specialProjectDtos;
}
