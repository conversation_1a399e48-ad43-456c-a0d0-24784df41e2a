package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName(value = "project_area_summary")
public class ProjectAreaSummary implements Serializable {

    @TableId(value = "project_id")
    private Long projectId;
    private String cityCompany;
    private String cityName;
    private String projectName;
    private String storeName;
    private String storeNo;
    private String storeType;
    private String actualOpenDate;
    private String floor;
    private String designPosition;
    private String decorateGrade;
    private String storeVersion;
    private String projectType;
    private BigDecimal contractLeaseArea;
    private BigDecimal netUsableArea;
    private BigDecimal businessArea;
    private BigDecimal commodityArea;
    private BigDecimal serviceArea;
    private BigDecimal investmentArea;
    private BigDecimal auxiliaryArea;
    private BigDecimal storePublicArea;
    private BigDecimal branchOfficeArea;
}
