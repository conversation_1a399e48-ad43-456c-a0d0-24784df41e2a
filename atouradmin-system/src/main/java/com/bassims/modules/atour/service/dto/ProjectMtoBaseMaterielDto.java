/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-23
**/
@Data
public class ProjectMtoBaseMaterielDto implements Serializable {

    /** 物料分类ID */
    private Long classId;

    /** 物料编码 */
    private String materielNo;

    /** 物料名称（品名） */
    private String materielName;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private Long createUser;

    /** 创建部门 */
    private Long createDept;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private Long updateUser;

    /** 是否已删除：0 正常，1 已删除 */
    private Integer isDelete;

    /** 租户编号 */
    private Long tenantId;

    /** 材料图样 */
    private String materialPattern;

    /** 材料性能参数 */
    private String materialPerformanceParameter;

    /** 合同约定必采 */
    private String agreeTake;

    /** 项目ID */
    private Integer projectId;

    /** 备注 */
    private String remark;

    /** 是否平台采购（1-是，0-否） */
    private Integer isPlatformProcurement;

    /** 下单时间 */
    private Timestamp orderTime;

    /** 项目物料ID */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectMaterielId;

    /** 基础物料Id */
    private Long materielId;

    /** 是否必采（0必采，1非必采） */
    private Integer isMustMined;

}