/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.bassims.annotation.QueryPlus;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-03-24
 **/
@Data
public class ProjectInfoQueryCriteria {

    /*是否是筹建对接启动的菜单 是的话为1*/
    private Integer preparationDocking;

    @QueryPlus(type = QueryPlus.Type.IN, propName = "projectId")
    private List<Long> projectIds;
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "projectName")
    private String projectName;
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "projectNo")
    private String projectNo;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "cityCompany")
    private String cityCompany;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "region")
    private String region;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isOverdue")
    private Boolean isOverdue;
    @QueryPlus(type = QueryPlus.Type.IN, propName = "projectStatus")
    private Set<String> projectStatus;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "storeType")
    private String storeType;
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "decorateArea")
    private BigDecimal decorateArea;
    @QueryPlus(type = QueryPlus.Type.BETWEEN, propName = "planOpenDate")
    private List<String> planOpenDate;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isDelete")
    private Boolean isDelete = false;
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String useCase;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isOpen")
    private Boolean isOpen;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isCreate")
    private Boolean isCreate;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "accountOverdue")
    private Boolean accountOverdue;
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "accountPhase")
    private String accountPhase;

    @QueryPlus(type = QueryPlus.Type.IN, propName = "projectStatus")
    private Set<String> accountStatus;

    @QueryPlus(type = QueryPlus.Type.NOT_EQUAL, propName = "accountPhase")
    private String accountPhaseForQuery;

    @QueryPlus(type = QueryPlus.Type.BETWEEN)
    private List<String> createTime;

    @QueryPlus(type = QueryPlus.Type.BETWEEN)
    private List<String> planApproachDate;



    @QueryPlus(blurry = "storeName,storeNo", type = QueryPlus.Type.INNER_LIKE)
    private String blurry;

    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "storeName")
    private String storeName;

    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "storeNo")
    private String storeNo;
    @QueryPlus(type = QueryPlus.Type.IN, propName = "projectType")
    private List<String> projectType;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "decorateGrade")
    private String decorateGrade;


    /**
     * 分配状态
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "allocationStatus")
    private String allocationStatus;

    private String nodeCode;

    private Long projectId;

    private String taskType;

    private Long storeId;

    private Long id;
    private String deleteFlag;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "brandCode")
    private String brandCode;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "brandName")
    private String brandName;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "productCode")
    private String productCode;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "productName")
    private String productName;



    private String taskPhase;

    /*任务阶段*/
    private String kanbanTaskPhase;

    /*任务节点*/
    private String projectTaskPhase;

    /**
     * 项目备注
     */
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "remark")
    private String remark;

    //离职用户
    private Long leavingUser;

}