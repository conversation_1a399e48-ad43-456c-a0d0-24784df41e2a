/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-24
**/
@Data
@TableName(value="t_org_interface_info")
public class OrgInterfaceInfo implements Serializable {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "费用承担部门id")
    private Long id;

    @TableField(value = "orgtype")
    @ApiModelProperty(value = "部门类型")
    private Integer orgtype;

    @TableField(value = "parentname")
    @ApiModelProperty(value = "父级部门名称")
    private String parentname;

    @TableField(value = "orgname")
    @ApiModelProperty(value = "部门名称")
    private String orgname;

    @TableField(value = "parentcode")
    @ApiModelProperty(value = "父级部门code")
    private String parentcode;

    @TableField(value = "orgcode")
    @ApiModelProperty(value = "部门code")
    private String orgcode;

    @TableField(value = "orgproperty")
    @ApiModelProperty(value = "部门类型")
    private String orgproperty;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(OrgInterfaceInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}