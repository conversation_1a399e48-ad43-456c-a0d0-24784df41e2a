/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.RoleControlElement;
import com.bassims.modules.atour.service.RoleControlElementService;
import com.bassims.modules.atour.service.dto.RoleControlElementDto;
import com.bassims.modules.atour.service.dto.RoleControlElementQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "筹建对接启动的角色对应可控制的元素管理")
@RequestMapping("/api/roleControlElement")
public class RoleControlElementController {

    private static final Logger logger = LoggerFactory.getLogger(RoleControlElementController.class);

    private final RoleControlElementService roleControlElementService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, RoleControlElementQueryCriteria criteria) throws IOException {
        roleControlElementService.download(roleControlElementService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<RoleControlElementDto>>}
    */
    @GetMapping("/list")
    @Log("查询筹建对接启动的角色对应可控制的元素")
    @ApiOperation("查询筹建对接启动的角色对应可控制的元素")
    public ResponseEntity<Object> query(RoleControlElementQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(roleControlElementService.queryAll(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<RoleControlElementDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询筹建对接启动的角色对应可控制的元素")
    @ApiOperation("查询筹建对接启动的角色对应可控制的元素")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(roleControlElementService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增筹建对接启动的角色对应可控制的元素")
    @ApiOperation("新增筹建对接启动的角色对应可控制的元素")
    public ResponseEntity<Object> create(@Validated @RequestBody RoleControlElement resources){
        return new ResponseEntity<>(roleControlElementService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改筹建对接启动的角色对应可控制的元素")
    @ApiOperation("修改筹建对接启动的角色对应可控制的元素")
    public ResponseEntity<Object> update(@Validated @RequestBody RoleControlElement resources){
        roleControlElementService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除筹建对接启动的角色对应可控制的元素")
    @ApiOperation("删除筹建对接启动的角色对应可控制的元素")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        roleControlElementService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}