package com.bassims.modules.atour.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProjectAreaWareHouseDto {

    @ExcelProperty({"基础信息", "分公司"})
    private String cityCompany;
    @ExcelProperty({"基础信息", "城市"})
    private String cityName;
    @ExcelProperty({"基础信息", "项目名称"})
    private String projectName;
    @ExcelProperty({"基础信息", "门店名称"})
    private String storeName;
    @ExcelProperty({"基础信息", "门店编码"})
    private String storeNo;
    @ExcelProperty({"基础信息", "门店类型"})
    private String storeType;
    @ExcelProperty({"基础信息", "实际开业时间"})
    private String actualOpenDate;
    @ExcelProperty({"门店仓库面积", "仓库面积"})
    private BigDecimal warehouseArea;
    @ExcelProperty({"门店仓库面积", "商品仓库"})
    private BigDecimal commodityWarehouse;
    @ExcelProperty({"门店仓库面积", "纺织仓库"})
    private BigDecimal textileWarehouse;
    @ExcelProperty({"门店仓库面积", "赠品仓库"})
    private BigDecimal giftWarehouse;
    @ExcelProperty({"门店仓库面积", "市场库"})
    private BigDecimal marketLibrary;
    @ExcelProperty({"门店仓库面积", "行政库房"})
    private BigDecimal administrativeWarehouse;
}
