/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-24
**/
@Data
public class ProjectInfoImportDto implements Serializable {

    /** 项目id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 品牌id */
    private Long brandId;

    /** 模板id */
    private Long templateId;

    /** 项目编码 */
    private String projectNo;

    /** 项目版本号  */
    private String projectVersion;

    /** 项目名称 */
    private String projectName;

    /** 经营性质 */
    private String businessNature;

    /** 门店类型 */
    private String storeType;

    /** 设计形象 */
    private String designImage;

    /** 大区value */
    private String region;

    /** 城市公司value */
    private String cityCompany;

    /** 省份id */
    private Long province;

    /** 省份名称 */
    private String provinceName;

    /** 城市id */
    private Long city;

    /** 城市名称 */
    private String cityName;

    /** 区县id */
    private Long county;

    /** 区县名称 */
    private String countyName;

    /** 详细地址（不包括省市县） */
    private String projectAddress;

    /** 商圈id */
    private Long tradeId;

    /** 立项时间 */
    private Date  establishTime;

    /** 城市公司网发经理 */
    private Long regionNetManager;

    /** SE营建经理 */
    private Long constructionManager;

    /** 工程总监 */
    private Long engineerDirector;

    /** 总部网发主管 */
    private Long generalNetDirector;

    /** 总部网发 */
    private Long generalNetwork;

    /** 城市公司财务 */
    private Long regionaFinanceManager;

    /** 项目计划开始时间 */
    private Date projectPlanStart;

    /** 项目计划结束时间 */
    private Date projectPlanEnd;

    /** 项目实际结束时间 */
    private Date projectActualEnd;

    /** 计划开业日期 */
    private Date planOpenDate;

    /** 计划进场日期 */
    private Date planApproachDate;

    /** 项目创建日期 */
    private Date projectCreateDate;

    /** 实际开业日期 */
    private String actualOpenDate;

    /** 是否开业 */
    private Boolean isOpen;

    /** 是否立项 */
    private Boolean isCreate;

    /** 总计面积 */
    private BigDecimal totalArea;

    /** 使用面积 */
    private BigDecimal usedArea;

    /** 装修面积 */
    private BigDecimal decorateArea;

    /** 项目类型 */
    private String projectType;

    /** 项目状态 */
    private String projectStatus;

    /** 当前任务阶段 */
    private String taskPhase;

    /** 是否逾期 */
    private Boolean isOverdue;

    /** 结算任务阶段 */
    private String accountPhase;

    /** 结算是否逾期 */
    private Boolean accountOverdue;

    /** 项目备注 */
    private String remark;

    /** 是否生效 */
    private Boolean isActive;

    /** 是否删除 */
    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 创建人 */
    private String createBy;

    /** 更新人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;
    /**
     * mobile端的进度条
     */
    List<Tree<String>> nodeList;
    /**
     * 项目创建时
     */
    List<Long> uploadList;

    private String createDate;



}