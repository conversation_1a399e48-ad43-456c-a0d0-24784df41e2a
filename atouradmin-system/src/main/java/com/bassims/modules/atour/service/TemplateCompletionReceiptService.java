/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.constant.bsEnum.TemplateEnum;
import com.bassims.modules.atour.domain.TemplateCompletionReceipt;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptDto;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptQueryCriteria;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2024-01-26
 **/
public interface TemplateCompletionReceiptService extends BaseService<TemplateCompletionReceipt> {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(TemplateCompletionReceiptQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<TemplateCompletionReceiptDto>
     */
    List<TemplateCompletionReceiptDto> queryAll(TemplateCompletionReceiptQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param receiptId ID
     * @return TemplateCompletionReceiptDto
     */
    TemplateCompletionReceiptDto findById(Long receiptId);

    /**
     * 创建
     *
     * @param resources /
     * @return TemplateCompletionReceiptDto
     */
    TemplateCompletionReceiptDto create(TemplateCompletionReceipt resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(TemplateCompletionReceipt resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<TemplateCompletionReceiptDto> all, HttpServletResponse response) throws IOException;

    List<TemplateCompletionReceipt> selectByCondition(String totalItem, String project, String content, String subItemContent,String subItem);



    /**
     * 模板下载
     */
    void downloadTemplate(String templateCode, HttpServletResponse response);
    Map<String, Object> importTemplateCompletionReceipt(MultipartFile file);

    /**
     * 给模板填充下拉数据
     *
     * @param templateEnum 模板枚举
     * @param inputStream 输入流
     */
    Workbook fillDropDownData(TemplateEnum templateEnum, InputStream inputStream);
}