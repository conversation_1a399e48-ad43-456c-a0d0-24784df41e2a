package com.bassims.modules.atour.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ProjectDesignSummaryDto {

    @ExcelProperty({"基础信息", "项目名称"})
    private String projectName;
    @ExcelProperty({"基础信息", "门店名称"})
    private String storeName;
    @ExcelProperty({"基础信息", "门店编码"})
    private String storeNo;
    @ExcelProperty({"基础信息", "门店类型"})
    private String storeType;
    @ExcelProperty({"基础信息", "实际开业时间"})
    private String actualOpenDate;
    @ExcelProperty({"基础信息", "设计师"})
    private String designer;
    @ExcelProperty({"设计进度", "租赁红线图超期"})
    private String rentOutTime;
    @ExcelProperty({"设计进度", "平面图超期"})
    private String planeOutTime;
    @ExcelProperty({"设计进度", "施工图超期"})
    private String buildOutTime;
    @ExcelProperty({"设计进度", "面积录入超期"})
    private String areaOutTime;
    @ExcelProperty({"设计进度", "联营图超期"})
    private String jointOutTime;
    @ExcelProperty({"设计进度", "增值商户设计图超期"})
    private String appreciationOutTime;
    @ExcelProperty({"设计进度", "灯具下单次数"})
    private String lampsNum;
    @ExcelProperty({"设计进度", "木制货架下单次数"})
    private String woodenShelfNum;
    @ExcelProperty({"设计进度", "钢制货架下单次数"})
    private String steelShelfNum;
}
