/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.StoreInterfaceInfo;
import com.bassims.modules.atour.service.dto.StoreInterfaceInfoDto;
import com.bassims.modules.atour.service.dto.StoreInterfaceInfoQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-11-23
**/
public interface StoreInterfaceInfoService extends BaseService<StoreInterfaceInfo> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(StoreInterfaceInfoQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<StoreInterfaceInfoDto>
    */
    List<StoreInterfaceInfoDto> queryAll(StoreInterfaceInfoQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param storeId ID
     * @return StoreInterfaceInfoDto
     */
    StoreInterfaceInfoDto findById(Long storeId);

    /**
    * 创建
    * @param resources /
    * @return StoreInterfaceInfoDto
    */
    StoreInterfaceInfoDto create(StoreInterfaceInfo resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(StoreInterfaceInfo resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<StoreInterfaceInfoDto> all, HttpServletResponse response) throws IOException;

    /**
     * 获取门店所有数据存库
     * @return
     */
    Boolean getStoreMasterInfo();

    /**
     *根据门店名称获取门店数据
     * @param storeName
     * @return
     */
    List<StoreInterfaceInfoDto> getStoreListByName(String storeName);
}