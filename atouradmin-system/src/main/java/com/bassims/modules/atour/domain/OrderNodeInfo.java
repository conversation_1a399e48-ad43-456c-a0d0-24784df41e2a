/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-09-23
**/
@Data
@TableName(value="t_order_node_info")
public class OrderNodeInfo implements Serializable {

    @TableId(value = "node_id")
    @ApiModelProperty(value = "节点id")
    private Long nodeId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "template_queue_id")
    @ApiModelProperty(value = "队列id")
    private Long templateQueueId;

    @TableField(value = "template_id")
    @ApiModelProperty(value = "模板主键")
    private Long templateId;

    @TableField(value = "order_id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点")
    private Long parentId;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号")
    private String projectVersion;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    @TableField(value = "node_name")
    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @TableField(value = "plan_start_date")
    @ApiModelProperty(value = "计划开始时间")
    private Timestamp planStartDate;

    @TableField(value = "plan_end_date")
    @ApiModelProperty(value = "计划结束时间")
    private Timestamp planEndDate;

    @TableField(value = "predict_start_date")
    @ApiModelProperty(value = "预估开始日期")
    private Timestamp predictStartDate;

    @TableField(value = "predict_end_date")
    @ApiModelProperty(value = "预估结束日期")
    private Timestamp predictEndDate;

    @TableField(value = "actual_end_date")
    @ApiModelProperty(value = "实际完成时间")
    private Timestamp actualEndDate;

    @TableField(value = "plan_day")
    @ApiModelProperty(value = "计划需要完成天数")
    private Integer planDay;

    @TableField(value = "notice_day")
    @ApiModelProperty(value = "提醒天数")
    private Integer noticeDay;

    @TableField(value = "delay_day")
    @ApiModelProperty(value = "延期天数")
    private Integer delayDay;

    @TableField(value = "node_wbs")
    @ApiModelProperty(value = "节点序号")
    private Integer nodeWbs;

    @TableField(value = "node_index")
    @ApiModelProperty(value = "子节点排序")
    private double nodeIndex;

    @TableField(value = "node_level")
    @ApiModelProperty(value = "节点等级")
    private Integer nodeLevel;

    @TableField(value = "node_type")
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @TableField(value = "node_status")
    @ApiModelProperty(value = "节点状态")
    private String nodeStatus;

    @TableField(value = "node_isfin")
    @ApiModelProperty(value = "已完成按钮")
    private Boolean nodeIsfin;

    @TableField(value = "front_wbs_config")
    @ApiModelProperty(value = "前置任务配置")
    private String frontWbsConfig;

    @TableField(value = "is_key")
    @ApiModelProperty(value = "是否是关键节点")
    private Boolean isKey;

    @TableField(value = "key_front_wbs")
    @ApiModelProperty(value = "关键节点前置任务")
    private String keyFrontWbs;

    @TableField(value = "remark")
    @ApiModelProperty(value = "节点备注")
    private String remark;

    @TableField(value = "relation_code")
    @ApiModelProperty(value = "关联nodecode")
    private String relationCode;

    @TableField(value = "relation_type")
    @ApiModelProperty(value = "关联的类型")
    private String relationType;

    @TableField(value = "down_code")
    @ApiModelProperty(value = "下拉列表角色名称")
    private String downCode;

    @TableField(value = "job_code")
    @ApiModelProperty(value = "干系人角色名称")
    private String jobCode;

    @TableField(value = "use_case")
    @ApiModelProperty(value = "使用场景")
    private String useCase;

    @TableField(value = "is_open")
    @ApiModelProperty(value = "节点是否打开")
    private String isOpen;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "isDelete")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "start_sign")
    @ApiModelProperty(value = "dict转码用")
    private String startSign;

    @TableField(value = "end_sign")
    @ApiModelProperty(value = "结束标志（甘特图）")
    private Integer endSign;

    @TableField(value = "total_day")
    @ApiModelProperty(value = "总工期")
    private Integer totalDay;

    @TableField(value = "is_mobile")
    @ApiModelProperty(value = "是否是手机端")
    private Boolean isMobile;

    @TableField(value = "role_code")
    @ApiModelProperty(value = "责任人角色code")
    private String roleCode;

    @TableField(value = "is_edit")
    @ApiModelProperty(value = "是否可以编辑")
    private String isEdit;

    @TableField(value = "seat")
    @ApiModelProperty(value = "占位")
    private String seat;

    @TableField(value = "icon")
    @ApiModelProperty(value = "小程序标志")
    private String icon;

    @TableField(value = "formula")
    @ApiModelProperty(value = "公式")
    private String formula;

    @TableField(value = "formula_code")
    @ApiModelProperty(value = "影响的code")
    private String formulaCode;

    @TableField(exist = false)
    private List<OrderDetail> orderDetails;

    @TableField(exist = false)
    private List<SpecialProject> specialProjects;

    public void copy(OrderNodeInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}