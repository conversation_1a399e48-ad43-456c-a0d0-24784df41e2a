package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectTimeReport;
import com.bassims.modules.atour.service.dto.ProjectTimeReportQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 项目时间报告服务
 *
 * <AUTHOR>
 * @date 2022/07/09
 */
public interface ProjectTimeReportService extends BaseService<ProjectTimeReport> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectTimeReportQueryCriteria criteria) throws IOException;

    /**
     * 查询时间
     *
     * @param criteria 标准
     * @param pageable
     * @return {@link Object}
     */
    Object queryTime(ProjectTimeReportQueryCriteria criteria, Pageable pageable);
}
