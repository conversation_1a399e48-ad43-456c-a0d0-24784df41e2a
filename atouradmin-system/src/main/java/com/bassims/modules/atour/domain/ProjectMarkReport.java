/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-27
**/
@Data
@TableName(value="project_mark_report")
public class ProjectMarkReport implements Serializable {

    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @TableField(value = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @TableField(value = "engineer_mark")
    @ApiModelProperty(value = "现场工程经理")
    private String engineerMark;

    @TableField(value = "project_manager_mark")
    @ApiModelProperty(value = "项目经理")
    private String projectManagerMark;

    @TableField(value = "project_manager")
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @TableField(value = "sup")
    @ApiModelProperty(value = "供应商单位")
    private String sup;

    @TableField(value = "avg_mark")
    @ApiModelProperty(value = "总评分")
    private String avgMark;

    public void copy(ProjectMarkReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}