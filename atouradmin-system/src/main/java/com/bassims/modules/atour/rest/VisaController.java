/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.Visa;
import com.bassims.modules.atour.service.VisaService;
import com.bassims.modules.atour.service.dto.VisaDto;
import com.bassims.modules.atour.service.dto.VisaQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_visa管理")
@RequestMapping("/api/visa")
public class VisaController {

    private static final Logger logger = LoggerFactory.getLogger(VisaController.class);

    private final VisaService visaService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, VisaQueryCriteria criteria) throws IOException {
        visaService.download(visaService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<VisaDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_visa")
    @ApiOperation("查询t_visa")
    public ResponseEntity<Object> query(VisaQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(visaService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<VisaDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_visa")
    @ApiOperation("查询t_visa")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(visaService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_visa")
    @ApiOperation("新增t_visa")
    public ResponseEntity<Object> create(@Validated @RequestBody Visa resources){
        return new ResponseEntity<>(visaService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_visa")
    @ApiOperation("修改t_visa")
    public ResponseEntity<Object> update(@Validated @RequestBody Visa resources){
        visaService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_visa")
    @ApiOperation("删除t_visa")
    public ResponseEntity<Object> delete(@RequestBody String[] ids) {
        visaService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<com.bassims.modules.atour.service.dto.VisaDto>>}
     */
    @GetMapping("/visaList")
    @Log("查询审批通过的签证")
    @ApiOperation("查询审批通过的签证")
    public ResponseEntity<Object> queryVisa(VisaDto visaDto,Pageable pageable){
        return new ResponseEntity<>(visaService.queryApprovePassInfo(visaDto,pageable),HttpStatus.OK);
    }

    @PostMapping("/submit")
    @Log("提交节点")
    @ApiOperation("提交节点 : 此传递是二级节点")
    public ResponseEntity<Object> submit(@Validated @RequestBody VisaDto visaDto) {
        return new ResponseEntity<>(visaService.submit(visaDto), HttpStatus.OK);
    }

    @GetMapping("/downloadExcel")
    @Log("根据模板导出Excel")
    @ApiOperation("根据模板导出Excel")
    public void downloadExcel(@Validated Long visaId, HttpServletResponse response) throws IOException {
        visaService.downloadExcel(visaId,response);
    }
}