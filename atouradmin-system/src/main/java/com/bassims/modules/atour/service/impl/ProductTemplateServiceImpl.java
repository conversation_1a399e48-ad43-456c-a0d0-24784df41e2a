/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.ProductTemplate;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProductTemplateRepository;
import com.bassims.modules.atour.service.ProductTemplateService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProductTemplateDto;
import com.bassims.modules.atour.service.dto.ProductTemplateQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProductTemplateMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-11-08
**/
@Service
public class ProductTemplateServiceImpl extends BaseServiceImpl<ProductTemplateRepository,ProductTemplate> implements ProductTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ProductTemplateServiceImpl.class);

    @Autowired
    private ProductTemplateRepository productTemplateRepository;
    @Autowired
    private ProductTemplateMapper productTemplateMapper;

    @Override
    public Map<String,Object> queryAll(ProductTemplateQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProductTemplate> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProductTemplate.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", productTemplateMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProductTemplateDto> queryAll(ProductTemplateQueryCriteria criteria){
        return productTemplateMapper.toDto(list(QueryHelpPlus.getPredicate(ProductTemplate.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductTemplateDto findById(Long id) {
        ProductTemplate productTemplate = Optional.ofNullable(getById(id)).orElseGet(ProductTemplate::new);
        ValidationUtil.isNull(productTemplate.getId(),getEntityClass().getSimpleName(),"id",id);
        return productTemplateMapper.toDto(productTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductTemplateDto create(ProductTemplate resources) {
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductTemplate resources) {
        ProductTemplate productTemplate = Optional.ofNullable(getById(resources.getId())).orElseGet(ProductTemplate::new);
        ValidationUtil.isNull( productTemplate.getId(),"ProductTemplate","id",resources.getId());
        productTemplate.copy(resources);
        updateById(productTemplate);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            productTemplateRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ProductTemplateDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProductTemplateDto productTemplate : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("产品名称", productTemplate.getProductName());
            map.put("产品code", productTemplate.getProductCode());
            map.put("品牌code", productTemplate.getBrandCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}