/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectVisaFiling;
import com.bassims.modules.atour.service.dto.ProjectVisaFilingContentDto;
import com.bassims.modules.atour.service.dto.ProjectVisaFilingDto;
import com.bassims.modules.atour.service.dto.ProjectVisaFilingQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-04-23
**/
public interface ProjectVisaFilingService extends BaseService<ProjectVisaFiling> {

    List<ProjectVisaFilingContentDto> getVisaApplicationContent(ProjectVisaFilingQueryCriteria criteria);

    String createVisaFiling(ProjectVisaFiling filing);

    List<ProjectVisaFilingDto> getVisaFilingList(ProjectVisaFilingQueryCriteria criteria);

    ProjectVisaFilingDto getVisaFilingOne(ProjectVisaFilingQueryCriteria criteria);

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ProjectVisaFilingQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ProjectVisaFilingDto>
    */
    List<ProjectVisaFilingDto> queryAll(ProjectVisaFilingQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return ProjectVisaFilingDto
     */
    ProjectVisaFilingDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return ProjectVisaFilingDto
    */
    ProjectVisaFilingDto create(ProjectVisaFiling resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ProjectVisaFiling resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ProjectVisaFilingDto> all, HttpServletResponse response) throws IOException;
}