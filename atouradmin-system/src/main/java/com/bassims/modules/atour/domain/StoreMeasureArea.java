/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-02
**/
@Data
@TableName(value="t_store_measure_area")
public class StoreMeasureArea implements Serializable {

    @TableId(value = "store_measure_area_id")
    @ApiModelProperty(value = "面积计算id")
    private Long storeMeasureAreaId;

    @TableField(value = "store_id")
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "label_info_area_id")
    @ApiModelProperty(value = "标签区域id")
    private Long labelInfoAreaId;

    @TableField(value = "label_info_code")
    @ApiModelProperty(value = "标签code")
    private String labelInfoCode;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点")
    private String nodeCode;

    @TableField(value = "is_project")
    @ApiModelProperty(value = "是否项目")
    private Boolean isProject;

    @TableField(value = "measure_area")
    @ApiModelProperty(value = "面积")
    private BigDecimal measureArea;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(StoreMeasureArea source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}