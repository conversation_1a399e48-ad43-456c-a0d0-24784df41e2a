/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ConstructionPhotograph;
import com.bassims.modules.atour.domain.ProjectRoom;
import com.bassims.modules.atour.repository.ConstructionPhotographRepository;
import com.bassims.modules.atour.repository.ProjectRoomRepository;
import com.bassims.modules.atour.service.ConstructionPhotographService;
import com.bassims.modules.atour.service.dto.ConstructionPhotographDto;
import com.bassims.modules.atour.service.dto.ConstructionPhotographQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ConstructionPhotographMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-10-13
**/
@Service
public class ConstructionPhotographServiceImpl extends BaseServiceImpl<ConstructionPhotographRepository,ConstructionPhotograph> implements ConstructionPhotographService {

    private static final Logger logger = LoggerFactory.getLogger(ConstructionPhotographServiceImpl.class);

    @Autowired
    private ConstructionPhotographRepository constructionPhotographRepository;
    @Autowired
    private ConstructionPhotographMapper constructionPhotographMapper;
    @Autowired
    private ProjectRoomRepository projectRoomRepository;

    @Override
    public Map<String,Object> queryAll(ConstructionPhotographQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ConstructionPhotograph> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ConstructionPhotograph.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", constructionPhotographMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ConstructionPhotographDto> queryAll(ConstructionPhotographQueryCriteria criteria){
        final LambdaQueryWrapper<ConstructionPhotograph> eq = Wrappers.lambdaQuery(ConstructionPhotograph.class)
                .eq(ConstructionPhotograph::getProjectId, criteria.getProjectId());
        final List<ConstructionPhotograph> list = this.list(eq);
        final LambdaQueryWrapper<ProjectRoom> wrapper = Wrappers.lambdaQuery(ProjectRoom.class)
                .eq(ProjectRoom::getProjectId, criteria.getProjectId())
                .eq(ProjectRoom::getIsUsed, "1");
        final List<ProjectRoom> projectRooms = projectRoomRepository.selectList(wrapper);
        final List<String> collect = projectRooms.stream().map(ProjectRoom::getRoomNum).collect(Collectors.toList());
        final List<ConstructionPhotograph> photographs = list.stream().filter(p -> collect.contains(p.getBuildingNumber())).collect(Collectors.toList());
        return constructionPhotographMapper.toDto(photographs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConstructionPhotographDto findById(Long constructionId) {
        ConstructionPhotograph constructionPhotograph = Optional.ofNullable(getById(constructionId)).orElseGet(ConstructionPhotograph::new);
        ValidationUtil.isNull(constructionPhotograph.getConstructionId(),getEntityClass().getSimpleName(),"constructionId",constructionId);
        return constructionPhotographMapper.toDto(constructionPhotograph);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConstructionPhotographDto create(ConstructionPhotograph resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setConstructionId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getConstructionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ConstructionPhotograph resources) {
        ConstructionPhotograph constructionPhotograph = Optional.ofNullable(getById(resources.getConstructionId())).orElseGet(ConstructionPhotograph::new);
        ValidationUtil.isNull( constructionPhotograph.getConstructionId(),"ConstructionPhotograph","id",resources.getConstructionId());
        constructionPhotograph.copy(resources);
        updateById(constructionPhotograph);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long constructionId : ids) {
            constructionPhotographRepository.deleteById(constructionId);
        }
    }

    @Override
    public void download(List<ConstructionPhotographDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ConstructionPhotographDto constructionPhotograph : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目id", constructionPhotograph.getProjectId());
            map.put("样板间名称", constructionPhotograph.getShowHouseName());
            map.put("客房小过道", constructionPhotograph.getGuestRoomAisle());
            map.put("卧室区", constructionPhotograph.getBedroomArea());
            map.put("客房卫生间", constructionPhotograph.getGuestBathroom());
            map.put("墙面", constructionPhotograph.getWallSpace());
            map.put("客房淋浴间", constructionPhotograph.getGuestShowerRoom());
            map.put("吊顶", constructionPhotograph.getSuspendedCeiling());
            map.put("备注", constructionPhotograph.getRemark());
            map.put("创建时间", constructionPhotograph.getCreateTime());
            map.put("创建人", constructionPhotograph.getCreateBy());
            map.put("更新时间", constructionPhotograph.getUpdateTime());
            map.put("更新人", constructionPhotograph.getUpdateBy());
            map.put("是否可用", constructionPhotograph.getIsEnabled());
            map.put("是否删除", constructionPhotograph.getIsDelete());
            map.put("节点编码", constructionPhotograph.getNodeCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void deleteByIds(List<Long> collect) {
        //根据id删除数据
        constructionPhotographRepository.deleteByIds(collect);
    }


}