/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.SupplierPm;
import com.bassims.modules.atour.service.dto.SupplierPmDto;
import com.bassims.modules.atour.service.dto.SupplierPmQueryCriteria;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.system.domain.User;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-09-15
**/
public interface SupplierPmService extends BaseService<SupplierPm> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SupplierPmQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SupplierPmDto>
    */
    List<SupplierPmDto> queryAllNoPage(SupplierPmQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SupplierPmDto
     */
    SupplierPmDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return SupplierPmDto
    */
    SupplierPmDto create(SupplierPmDto resources,Long supplierId);
    SupplierPmDto createPm(SupplierPmDto resources,Long supplierId);

    SupplierPmDto createAndGiveUserSupplier(SupplierPmDto resources);


    /**
    * 编辑
    * @param resources /
    */
    void update(SupplierPmDto resources);

    User createUser(SupplierPm resources, Long supplierId, Long currentUserId);
    /**
     * 编辑状态
     * @param resources /
     */
    void updateStatus(SupplierPm resources);
    /**
     * 编辑状态
     * @param resources /
     */
    void updateSupplierPmStatus(List<SupplierPm> resources);

    void deleteSupplierPmStatus(List<SupplierPm> resources);
    void deleteSupplierPm(SupplierPm resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SupplierPmDto> all, HttpServletResponse response) throws IOException;

    /**
     * 根据名称查询项目经理信息
     * @param pmName 项目经理名称
     * @return SupplierPmDto
     */
    SupplierPmDto supPmInfoByName(String pmName);


    /**
     * 根据供应商ID和供应商角色查询供应商人员
     * @param criteria 条件参数
     * @return List<SupplierPmDto>
     */
    List<SupplierPmDto> getSupplierPmList(SupplierPmQueryCriteria criteria);

    void savOrUpdatePmFromAtos(AtosUserRequest atosUserRequest);

    void deletePmFromAtos(AtosUserRequest atosUserRequest);

    Boolean supplierPmImport(MultipartFile file) throws IOException;

    void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;
}