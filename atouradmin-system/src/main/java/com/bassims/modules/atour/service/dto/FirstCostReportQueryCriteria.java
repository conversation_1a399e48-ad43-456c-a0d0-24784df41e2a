/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.bassims.annotation.QueryPlus;
import lombok.Data;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-13
**/
@Data
public class FirstCostReportQueryCriteria {

//    @QueryPlus(type = QueryPlus.Type.IN,propName = "projectTypes")
//    private List<String> projectTypes;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "cityCompany")
    private String cityCompany;

    @QueryPlus(type = QueryPlus.Type.INNER_LIKE,propName = "storeName")
    private String storeName;

    @QueryPlus(type = QueryPlus.Type.IN,propName = "projectType")
    private List<String> projectTypes;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "costType")
    private String costType;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "designPosition")
    private String designPosition;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "decorateGrade")
    private String decorateGrade;

    @QueryPlus(type = QueryPlus.Type.EQUAL,propName = "secondClass")
    private String secondClass;

    @QueryPlus(type = QueryPlus.Type.BETWEEN_DATE,propName = "actualOpenDate")
    private List<String> actualOpenDate;

    @QueryPlus(type = QueryPlus.Type.BETWEEN_DATE,propName = "addTime")
    private List<String> addTime;
}