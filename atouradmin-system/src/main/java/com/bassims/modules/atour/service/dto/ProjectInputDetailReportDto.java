package com.bassims.modules.atour.service.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 工程投入明细报表dto
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Data
public class ProjectInputDetailReportDto {

    @ExcelProperty("序号")
    private Integer indexNo;

    @ExcelProperty(value = "城市公司value")
    private String cityCompany;

    @ExcelProperty(value = "门店编码")
    private String storeNo;

    @ExcelProperty(value = "门店名称")
    private String storeName;

    @ExcelProperty(value = "项目类型")
    @ExcelIgnore
    private String projectType;

    @ExcelProperty(value = "开工时间")
    private String enterTime;

    @ExcelProperty(value = "开业时间")
    private String openTime;

    @ExcelProperty(value = "年级店")
    private String openYear;

    @ExcelProperty(value = "设计师")
    private String designer;

    @ExcelProperty(value = "现场工程")
    private String engineer;

    @ExcelProperty(value = "装修设计等级")
    private String decorateGrade;

    @ExcelProperty(value = "交付标准")
    private String standard;

    @ExcelProperty(value = "物业")
    private String property;

    @ExcelProperty(value = "租赁面积")
    private String rentArea;

    @ExcelProperty(value = "游乐面积")
    private String amuse;

    @ExcelProperty(value = "招商面积")
    private String businessArea;

    @ExcelProperty(value = "施工面积")
    private Double buildArea;

    @ExcelProperty(value = "装饰合同额")
    private String decorateTotal;

    @ExcelProperty(value = "装饰审定额")
    private String approvedTotal;

    @ExcelProperty(value = "装饰单坪效")
    private Double decorateSingle;

    @ExcelProperty(value = "消防")
    private String fireTotal;

    @ExcelProperty(value = "消防单坪效")
    private Double fireSingle;

    @ExcelProperty(value = "空调")
    private String airTotal;

    @ExcelProperty(value = "空调单坪效")
    private Double airSingle;

    @ExcelProperty(value = "店招")
    private Double shopTotal;

    @ExcelProperty(value = "装潢投入")
    private Double decorateCost;

    @ExcelProperty(value = "灯具")
    private Double lampTotal;

    @ExcelProperty(value = "钢制")
    private Double steelTotal;

    @ExcelProperty(value = "木制")
    private Double woodenTotal;

    @ExcelProperty(value = "精灵")
    private Double spiritTotal;

    @ExcelProperty(value = "单点总投入")
    private Double shopCostTotal;

    @ExcelProperty(value = "竣工时间")
    private String endAcceptTime;
}
