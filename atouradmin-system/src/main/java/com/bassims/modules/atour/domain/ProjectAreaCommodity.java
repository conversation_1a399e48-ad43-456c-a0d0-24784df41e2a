package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName(value = "project_area_commodity")
public class ProjectAreaCommodity {

    @TableId(value = "project_id")
    private Long projectId;
    private String cityCompany;
    private String cityName;
    private String projectName;
    private String storeName;
    private String storeNo;
    private String storeType;
    private String actualOpenDate;
    private BigDecimal commodityArea;
    private BigDecimal lingerieHomeTextile;
    private BigDecimal kidShoes;
    private BigDecimal toys;
    private BigDecimal culturalEdu;
    private BigDecimal articles;
    private BigDecimal latheChair;
    private BigDecimal powderedMilk;
    private BigDecimal diapers;
    private BigDecimal snackFood;
    private BigDecimal nutritionHealth;
    private BigDecimal pregnantMothers;
    private BigDecimal privateBrand;
    private BigDecimal storeAreaReserveOne;
    private BigDecimal storeAreaReserveTwo;
}
