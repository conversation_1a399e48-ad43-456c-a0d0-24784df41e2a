/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.SupplierFineRecord;
import com.bassims.modules.atour.service.SupplierFineRecordService;
import com.bassims.modules.atour.service.dto.SupplierFineRecordDto;
import com.bassims.modules.atour.service.dto.SupplierFineRecordQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-04-01
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_supplier_fine_record管理")
@RequestMapping("/api/supplierFineRecord")
public class SupplierFineRecordController {

    private static final Logger logger = LoggerFactory.getLogger(SupplierFineRecordController.class);

    private final SupplierFineRecordService supplierFineRecordService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, SupplierFineRecordQueryCriteria criteria) throws IOException {
        supplierFineRecordService.download(supplierFineRecordService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<SupplierFineRecordDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_supplier_fine_record")
    @ApiOperation("查询t_supplier_fine_record")
    public ResponseEntity<Object> query(SupplierFineRecordQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(supplierFineRecordService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<SupplierFineRecordDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_supplier_fine_record")
    @ApiOperation("查询t_supplier_fine_record")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(supplierFineRecordService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_supplier_fine_record")
    @ApiOperation("新增t_supplier_fine_record")
    public ResponseEntity<Object> create(@Validated @RequestBody SupplierFineRecord resources){
        return new ResponseEntity<>(supplierFineRecordService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_supplier_fine_record")
    @ApiOperation("修改t_supplier_fine_record")
    public ResponseEntity<Object> update(@Validated @RequestBody SupplierFineRecord resources){
        supplierFineRecordService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_supplier_fine_record")
    @ApiOperation("删除t_supplier_fine_record")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        supplierFineRecordService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/updateData")
    @Log("修改t_supplier_fine_record")
    @ApiOperation("修改t_supplier_fine_record")
    public ResponseEntity<Object> updateData(@RequestBody List<SupplierFineRecordDto> supplierFineRecords){
        supplierFineRecordService.updateData(supplierFineRecords);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/queryFineRecord")
    @Log("查询t_supplier_fine_record")
    @ApiOperation("查询t_supplier_fine_record")
    public ResponseEntity<Object> queryFineRecord(@Validated Long supplierId){
        return new ResponseEntity<>(supplierFineRecordService.queryBySupplierId(supplierId), HttpStatus.OK);
    }
}