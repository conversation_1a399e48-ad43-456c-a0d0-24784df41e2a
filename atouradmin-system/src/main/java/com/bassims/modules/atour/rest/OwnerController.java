/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.Owner;
import com.bassims.modules.atour.service.OwnerService;
import com.bassims.modules.atour.service.dto.OwnerDto;
import com.bassims.modules.atour.service.dto.OwnerQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-10
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "业主表管理")
@RequestMapping("/api/owner")
public class OwnerController {

    private static final Logger logger = LoggerFactory.getLogger(OwnerController.class);

    private final OwnerService ownerService;

    @AnonymousAccess
    @PostMapping("/saveOwner")
    @Log("新增业主表")
    @ApiOperation("新增业主表")
    public ResponseEntity<Object> saveOwner(@Validated @RequestBody Owner resources){
        return new ResponseEntity<>(ownerService.saveOwner(resources),HttpStatus.CREATED);
    }

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, OwnerQueryCriteria criteria) throws IOException {
        ownerService.download(ownerService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<OwnerDto>>}
    */
    @GetMapping("/list")
    @Log("查询业主表")
    @ApiOperation("查询业主表")
    public ResponseEntity<Object> query(OwnerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(ownerService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<OwnerDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询业主表")
    @ApiOperation("查询业主表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(ownerService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增业主表")
    @ApiOperation("新增业主表")
    public ResponseEntity<Object> create(@Validated @RequestBody Owner resources){
        return new ResponseEntity<>(ownerService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改业主表")
    @ApiOperation("修改业主表")
    public ResponseEntity<Object> update(@Validated @RequestBody Owner resources){
        ownerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除业主表")
    @ApiOperation("删除业主表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        ownerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * @real_return {@link ResponseEntity<List<OwnerDto>>}
     */
    @GetMapping("/getList")
    @Log("查询业主列表")
    @ApiOperation("查询业主列表")
    public ResponseEntity<Object> getList(OwnerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(ownerService.queryAll(criteria),HttpStatus.OK);
    }
}