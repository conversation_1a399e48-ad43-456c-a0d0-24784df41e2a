/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.domain.vo.EmailVo;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.ProjectCompletionReceiptQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoDto;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoQueryCriteria;
import com.bassims.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-03-24
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目节点管理")
@RequestMapping("/api/projectNodeInfo")
public class ProjectNodeInfoController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectNodeInfoController.class);

    private final ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private EmailService emailService;
    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectNodeInfoQueryCriteria criteria) throws IOException {
        projectNodeInfoService.download(projectNodeInfoService.queryAll(criteria), response);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectNodeInfoDto>>}
     */
    @GetMapping("/list")
//    @Log("查询项目节点")
    @ApiOperation("查询项目节点")
    public ResponseEntity<Object> query(ProjectNodeInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectNodeInfoService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <ProjectNodeInfoDto>}
     */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询项目节点")
    @ApiOperation("查询项目节点")
    public ResponseEntity<Object> query(@PathVariable Long id) {
        return new ResponseEntity<>(projectNodeInfoService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目节点")
    @ApiOperation("新增项目节点")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectNodeInfo resources) {
        return new ResponseEntity<>(projectNodeInfoService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目节点")
    @ApiOperation("修改项目节点")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectNodeInfo resources) {
        projectNodeInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/updateRemark")
    @Log("修改项目节点数据")
    @ApiOperation("修改项目节点数据")
    public ResponseEntity<Object> updateRemark(@Validated @RequestBody ProjectNodeInfo resources) {
        projectNodeInfoService.updateRemark(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目节点")
    @ApiOperation("删除项目节点")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectNodeInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/projectTree")
//    @Log("获取项目模板树")
    @ApiOperation("获取项目模板树")
    public ResponseEntity<Object> projectTree(Long projectId, String useCase, Boolean isMobile, Long templateId, String nodeCode, Long nodeId, String roundMarking) {
        return new ResponseEntity<>(projectNodeInfoService.projectTree2(projectId, useCase, isMobile, 4,
                templateId, nodeCode, nodeId, roundMarking), HttpStatus.OK);
    }

    //@AnonymousAccess
    @GetMapping("/projectTreeForBoard")
//    @Log("获取项目看板")
    @ApiOperation("获取项目看板")
    @MyDataPermission
    public ResponseEntity<Object> projectTreeForBoard(Long projectId, String useCase, Boolean isMobile) {
        return new ResponseEntity<>(projectNodeInfoService.projectTreeForBoardNew(projectId, useCase, isMobile, 3),
                HttpStatus.OK);
    }

    @GetMapping("/projectTreeByNodeCode")
//    @Log("通过nodeCode获取项目模板树")
    @ApiOperation("通过nodeCode获取项目模板树")
    public ResponseEntity<Object> projectTreeByNodeCode(Long projectId, String nodeCode, String useCase,  Boolean isMobile, String roundMarking) {
        return new ResponseEntity<>(projectNodeInfoService.projectTreeByNodeCode(projectId, nodeCode, useCase, isMobile,roundMarking),
                HttpStatus.OK);
    }

    @GetMapping("/projectTreeProgress")
//    @Log("获取项目模板进度条")
    @ApiOperation("获取项目模板进度条")
    public ResponseEntity<Object> projectTreeProgress(Long projectId, String useCase, Boolean isMobile) {
        return new ResponseEntity<>(projectNodeInfoService.projectTreeProgress(projectId, useCase, isMobile),
                HttpStatus.OK);
    }

    @PostMapping("/updateData")
    @Log("修改项目节点")
    @ApiOperation("修改项目节点")
    public ResponseEntity<Object> updateData(@Validated @RequestBody List<ProjectNodeInfo> list) {
        return new ResponseEntity<>(projectNodeInfoService.updateData(list, Boolean.FALSE), HttpStatus.OK);
    }

    @PostMapping("/addUserVersion")
    @Log("添加用户添加的数据")
    @ApiOperation("添加用户添加的数据")
    public ResponseEntity<Object> createUserAddData(@Validated @RequestBody List<ProjectNodeInfoDto> list) {
        return new ResponseEntity<>(projectNodeInfoService.createUserAddData(list), HttpStatus.OK);
    }

    @PostMapping("/submit")
    @Log("提交节点")
    @ApiOperation("提交节点 : 此传递是二级节点")
    public ResponseEntity<Object> submit(@Validated @RequestBody ProjectNodeInfoDto projectNodeInfoDto) {
        return new ResponseEntity<>(projectNodeInfoService.submit(projectNodeInfoDto), HttpStatus.OK);
    }

    @PostMapping("/submitNoApprove")
    @Log("提交节点")
    @ApiOperation("提交节点:不生成审批，直接关闭节点")
    public ResponseEntity<Object> submitNoApprove(@Validated @RequestBody ProjectNodeInfoDto projectNodeInfoDto) {
        return new ResponseEntity<>(projectNodeInfoService.submitNoApprove(projectNodeInfoDto), HttpStatus.OK);
    }

    @GetMapping("/projectTreeForSchedule")
//    @Log("获取项目工期模板树")
    @ApiOperation("获取项目工期模板树")
    @ResponseBody
    public ResponseEntity<Object> projectTreeForSchedule(Long projectId) {
        return new ResponseEntity<>(projectNodeInfoService.projectTreeForSchedule(projectId), HttpStatus.OK);
    }

    @PostMapping("/updateDataForAdmin")
    @Log("修改项目节点")
    @ApiOperation("修改项目节点")
    public ResponseEntity<Object> updateDataForAdmin(@RequestParam("processCode") String processCode,
                                                     @Validated @RequestBody List<ProjectNodeInfo> list) {
        return new ResponseEntity<>(projectNodeInfoService.updateData(list, Boolean.TRUE), HttpStatus.OK);
    }


    @GetMapping("/getQueryTimeNode")
//    @Log("查询项目模块的时间节点")
    @ApiOperation("查询项目模块的时间节点")
    @MyDataPermission
    public ResponseEntity<Object> getQueryTimeNode(String projectId) {
        return new ResponseEntity<>(projectNodeInfoService.getQueryTimeNode(projectId), HttpStatus.OK);
    }

    @GetMapping("/getThreeRoleCode")
//    @Log("根据三级的roleCode查询当前角色可以操作哪些三级节点")
    @ApiOperation("根据三级的roleCode查询当前角色可以操作哪些三级节点")
    public ResponseEntity<Object> getThreeRoleCode(Long projectId, String parentId, String roleCode) {
        return new ResponseEntity<>(projectNodeInfoService.getThreeRoleCode(projectId, parentId, roleCode), HttpStatus.OK);
    }

    @GetMapping("/getConventionalInformation")
//    @Log("查询项目模块的常规信息")
    @ApiOperation("查询项目模块的常规信息")
    public ResponseEntity<Object> getConventionalInformation(Long projectId) {
        List<ProjectNodeInfo> result = projectNodeInfoService.getConventionalInformation(projectId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/getDeepeningList")
//    @Log("查询当前项目深化列表的信息")
    @ApiOperation("查询当前项目深化列表的信息")
    public ResponseEntity<Object> getDeepeningList(Long projectId) {
        List<ProjectNodeInfo> result = projectNodeInfoService.getDeepeningList(projectId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/documentManage")
//    @Log("查询当前项目资料管理")
    @ApiOperation("查询当前项目资料管理")
    public ResponseEntity<Object> documentManage(Long projectId) {
        return new ResponseEntity<>(projectNodeInfoService.documentManage(projectId), HttpStatus.OK);
    }

    @GetMapping("/getEngineeringRectificationIssues")
//    @Log("查询工程整改问题列表")
    @ApiOperation("查询工程整改问题列表")
    public ResponseEntity<Object> getEngineeringRectificationIssues(Long projectId) {
        return new ResponseEntity<>(projectNodeInfoService.getEngineeringRectificationIssues(projectId), HttpStatus.OK);
    }



    @GetMapping("/getGenerateDynamicListTemplates")
    @Log("生成动态列表模版")
    @ApiOperation("生成动态列表模版")
    public ResponseEntity<Object> getGenerateDynamicListTemplates(Long projectId,String nodeCode) {
        return new ResponseEntity<>(projectNodeInfoService.getEngineeringRectificationIssues(projectId), HttpStatus.OK);
    }

    @GetMapping("/getNodeIdByPdf")
    @Log("二级任务的PDF生成带出的节点编码")
    @ApiOperation("二级任务的PDF生成带出的节点编码")
    public ResponseEntity<Object> getNodeIdByPdf(Long projectId) {
        return new ResponseEntity<>(projectNodeInfoService.getNodeIdByPdf(projectId), HttpStatus.OK);
    }


    //@AnonymousAccess
    @GetMapping("/projectTreeForBoardNew")
//    @Log("获取项目看板")
    @ApiOperation("获取项目看板(存在同一个模版重复的情况)")
    public ResponseEntity<Object> projectTreeForBoardNew(Long projectId, String useCase, Boolean isMobile) {
        return new ResponseEntity<>(projectNodeInfoService.projectTreeForBoardNew(projectId, useCase, isMobile, 3),
                HttpStatus.OK);
    }


    @GetMapping("/sendEmail")
    @ApiOperation("发送邮件")
    public ResponseEntity<Object> sendEmail() {
        EmailVo emailVo = new EmailVo();
        emailVo.setContent("ACMS营建管理系统通知： 【酒店ID：10086】 的质量问题有 >=1 未整改情况。");
        emailVo.setSubject("法务函件");
        List<String> tos=new ArrayList<>();
        //tos.add("<EMAIL>");
        //tos.add("<EMAIL>");
        tos.add("<EMAIL>");
        tos.add("<EMAIL>");
        tos.add("<EMAIL>");
        emailVo.setTos(tos);
        emailService.sendEmail(emailVo, emailService.find());
        return new ResponseEntity<>(HttpStatus.OK);
    }



}