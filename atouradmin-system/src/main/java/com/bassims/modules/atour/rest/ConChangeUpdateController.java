package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ConChangeUpdate;
import com.bassims.modules.atour.service.ConChangeUpdateService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/conChangeUpdate")
public class ConChangeUpdateController {
    private static final Logger logger = LoggerFactory.getLogger(ConChangeUpdateController.class);
    private final ConChangeUpdateService conChangeUpdateService;

    @PostMapping("/create")
    @Log("新增工期变更")
    @ApiOperation("新增工期变更")
    public ResponseEntity<Object> create(@Validated @RequestBody ConChangeUpdate resources){
        return new ResponseEntity<>(conChangeUpdateService.conChangeUpdate(resources), HttpStatus.CREATED);
    }


    @GetMapping(value = "/getChangeList")
    @Log("查询所有工单变更数据")
    @ApiOperation("查询所有工单变更数据")
    public ResponseEntity<Object> getChangeList(Long conChangeId){
        return new ResponseEntity<>(conChangeUpdateService.getConChangeUpdate(conChangeId), HttpStatus.OK);
    }
}
