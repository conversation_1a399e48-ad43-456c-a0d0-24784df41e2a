/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2023-10-25
 **/
@Data
public class TableConfiguringTertiaryNodesVO implements Serializable {

    /**
     * key
     */
    private String tertiaryKey;

    /**
     * 名称
     */
    private String tertiaryValue;

    /**
     * 类型
     */
    private String tertiaryType;

    /**
     * dict转码用
     */
    private String startSign;


    /**
     * 是否编辑（0不可编辑1可编辑）
     */
    private Integer isCompile;


    /**
     * 是否审核节点展示（0审核节点展示、1审核节点不展示）
     */
    private Integer isAuditDisplay;


    /**
     * 是否合并单元格（0不合并、1合并）
     */
    private Integer isMergeCells;


    /**
     * 是否必填（0否1是）
     */
    private Integer isMust;

    /**
     * 是否存在有条件必填（0不存在、1存在）
     */
    private Integer isModifiable;

    /**
     * （条件值,条件key）
     */
    private String modifiableCode;

    /**
     * 当前列的宽高
     */
    private String widthAndHeight;

}