/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.BudgetVersionRepository;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.BudgetVersionDto;
import com.bassims.modules.atour.service.dto.BudgetVersionQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.BudgetVersionDetailMapper;
import com.bassims.modules.atour.service.mapstruct.BudgetVersionMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-30
**/
@Service
public class BudgetVersionServiceImpl extends BaseServiceImpl<BudgetVersionRepository,BudgetVersion> implements BudgetVersionService {

    private static final Logger logger = LoggerFactory.getLogger(BudgetVersionServiceImpl.class);

    @Autowired
    private BudgetVersionRepository budgetVersionRepository;
    @Autowired
    private BudgetVersionMapper budgetVersionMapper;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private BudgetVersionDetailService budgetVersionDetailService;
    @Autowired
    private BudgetVersionDetailMapper budgetVersionDetailMapper;

    @Override
    public Map<String,Object> queryAll(BudgetVersionQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<BudgetVersion> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(BudgetVersion.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", budgetVersionMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<BudgetVersionDto> queryAll(BudgetVersionQueryCriteria criteria){
        return budgetVersionMapper.toDto(list(QueryHelpPlus.getPredicate(BudgetVersion.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BudgetVersionDto findById(Long budgetVersionId) {
        BudgetVersion budgetVersion = Optional.ofNullable(getById(budgetVersionId)).orElseGet(BudgetVersion::new);
        ValidationUtil.isNull(budgetVersion.getBudgetVersionId(),getEntityClass().getSimpleName(),"budgetVersionId",budgetVersionId);
        return budgetVersionMapper.toDto(budgetVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BudgetVersionDto create(BudgetVersion resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setBudgetVersionId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getBudgetVersionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BudgetVersion resources) {
        BudgetVersion budgetVersion = Optional.ofNullable(getById(resources.getBudgetVersionId())).orElseGet(BudgetVersion::new);
        ValidationUtil.isNull( budgetVersion.getBudgetVersionId(),"BudgetVersion","id",resources.getBudgetVersionId());
        budgetVersion.copy(resources);
        updateById(budgetVersion);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long budgetVersionId : ids) {
            budgetVersionRepository.deleteById(budgetVersionId);
        }
    }

    @Override
    public void download(List<BudgetVersionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (BudgetVersionDto budgetVersion : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("审批id", budgetVersion.getApproveId());
            map.put("节点id", budgetVersion.getNodeId());
            map.put("提交人", budgetVersion.getSubmitUser());
            map.put("项目id", budgetVersion.getProjectId());
            map.put("审批次数", budgetVersion.getSubmitNumber());
            map.put("是否是最后一次审批", budgetVersion.getIsLast());
            map.put("创建时间", budgetVersion.getCreateTime());
            map.put("创建人", budgetVersion.getCreateBy());
            map.put("更新时间", budgetVersion.getUpdateTime());
            map.put("更新人", budgetVersion.getUpdateBy());
            map.put("是否可用", budgetVersion.getIsEnabled());
            map.put("是否删除", budgetVersion.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean createBudgetVersion(ProjectApprove projectApprove) {
        Long projectId = projectApprove.getProjectId();
        Long nodeId = projectApprove.getNodeId();
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectGroup.class);
        projectGroupLambdaQueryWrapper.eq(ProjectGroup::getProjectId,projectId).eq(ProjectGroup::getProjectGroupId,nodeId);
        ProjectGroup one = projectGroupService.getOne(projectGroupLambdaQueryWrapper);
        LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId,projectId).eq(ProjectNodeInfo::getParentId,one.getTemplateId());
        List<ProjectNodeInfo> list = projectNodeInfoService.list(projectNodeInfoLambdaQueryWrapper);
        //查询审批次数
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectApprove.class)
                .eq(ProjectApprove::getProjectId,projectId)
                .eq(ProjectApprove::getNodeId,nodeId);
        int count = (int) projectApproveService.count(approveLambdaQueryWrapper);
        List<BudgetVersionDetail> budgetVersionDetails=new ArrayList<>();
        BudgetVersion budgetVersion=new BudgetVersion();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long budgetVersionId = snowflake.nextId();
        budgetVersion.setBudgetVersionId(budgetVersionId);
        budgetVersion.setApproveId(projectApprove.getApproveId());
        budgetVersion.setSubmitUser(projectApprove.getSubmitUser());
        budgetVersion.setProjectId(projectId);
        budgetVersion.setNodeId(nodeId);
        budgetVersion.setSubmitNumber(count);
        for(ProjectNodeInfo nodeInfo:list){
            Snowflake detail = IdUtil.getSnowflake(1, 1);
            Long detailVersionId = detail.nextId();
            BudgetVersionDetail budgetVersionDetail=new BudgetVersionDetail();
            BeanUtils.copyProperties(nodeInfo,budgetVersionDetail);
            budgetVersionDetail.setBudgetVersionId(budgetVersionId);
            budgetVersionDetail.setBudgetVersionDetailId(detailVersionId);
            budgetVersionDetails.add(budgetVersionDetail);
        }
        save(budgetVersion);
        budgetVersionDetailService.saveBatch(budgetVersionDetails);

        return null;
    }

    @Override
    public Boolean createLastVersion(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        Long nodeId = projectGroup.getProjectGroupId();
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectGroup.class);
        projectGroupLambdaQueryWrapper.eq(ProjectGroup::getProjectId,projectId).eq(ProjectGroup::getProjectGroupId,nodeId);
        ProjectGroup one = projectGroupService.getOne(projectGroupLambdaQueryWrapper);
        LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId,projectId).eq(ProjectNodeInfo::getParentId,one.getTemplateId());
        List<ProjectNodeInfo> list = projectNodeInfoService.list(projectNodeInfoLambdaQueryWrapper);
        //查询审批次数
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectApprove.class)
                .eq(ProjectApprove::getProjectId,projectId)
                .eq(ProjectApprove::getNodeId,nodeId);
        int count = (int) projectApproveService.count(approveLambdaQueryWrapper);
        //获取审批通过的审批数据
        LambdaQueryWrapper<ProjectApprove> passLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectApprove.class)
                .eq(ProjectApprove::getProjectId,projectId).eq(ProjectApprove::getNodeId,nodeId)
                .eq(ProjectApprove::getApproveResult, JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey())
                .eq(ProjectApprove::getApproveStatus,JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
        ProjectApprove passApprove = projectApproveService.getOne(passLambdaQueryWrapper);
        List<BudgetVersionDetail> budgetVersionDetails=new ArrayList<>();
        BudgetVersion budgetVersion=new BudgetVersion();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long budgetVersionId = snowflake.nextId();
        budgetVersion.setBudgetVersionId(budgetVersionId);
        budgetVersion.setApproveId(passApprove.getApproveId());
        budgetVersion.setSubmitUser(passApprove.getSubmitUser());
        budgetVersion.setProjectId(projectId);
        budgetVersion.setNodeId(nodeId);
        budgetVersion.setSubmitNumber(count);
        budgetVersion.setIsLast(Boolean.TRUE);
        for(ProjectNodeInfo nodeInfo:list){
            Snowflake detail = IdUtil.getSnowflake(1, 1);
            Long detailVersionId = detail.nextId();
            BudgetVersionDetail budgetVersionDetail=new BudgetVersionDetail();
            BeanUtils.copyProperties(nodeInfo,budgetVersionDetail);
            budgetVersionDetail.setBudgetVersionId(budgetVersionId);
            budgetVersionDetail.setBudgetVersionDetailId(detailVersionId);
            budgetVersionDetails.add(budgetVersionDetail);
        }
        save(budgetVersion);
        budgetVersionDetailService.saveBatch(budgetVersionDetails);

        return null;
    }

    @Override
    public List<BudgetVersionDto> getBudgetVersionList(Long projectId, String nodeCode) {
        LambdaQueryWrapper<BudgetVersion> versionLambdaQueryWrapper=Wrappers.lambdaQuery(BudgetVersion.class)
                .eq(BudgetVersion::getProjectId,projectId);
        List<BudgetVersion> list = this.list(versionLambdaQueryWrapper);
        List<BudgetVersionDto> budgetVersionDtos = budgetVersionMapper.toDto(list);
        for(BudgetVersionDto dto:budgetVersionDtos){
            Long budgetVersionId = dto.getBudgetVersionId();
            LambdaQueryWrapper<BudgetVersionDetail> detailLambdaQueryWrapper=Wrappers.lambdaQuery(BudgetVersionDetail.class)
                    .eq(BudgetVersionDetail::getBudgetVersionId,budgetVersionId)
                    .orderByAsc(BudgetVersionDetail::getNodeIndex);
            List<BudgetVersionDetail> detailList = budgetVersionDetailService.list(detailLambdaQueryWrapper);
            dto.setBudgetVersionDetailDtoList(budgetVersionDetailMapper.toDto(detailList));
        }

        return budgetVersionDtos;
    }
}