/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-19
**/
@Data
@TableName(value="t_project_disclosure_problem")
public class ProjectDisclosureProblem implements Serializable {

    @TableId(value = "disclosure_problem_id")
    @ApiModelProperty(value = "设计交底问题汇总id")
    private Long disclosureProblemId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "description_of_on_site_issues")
    @ApiModelProperty(value = "现场问题描述")
    private String descriptionOfOnSiteIssues;

    @TableField(value = "pictures_of_on_site_issues")
    @ApiModelProperty(value = "现场问题图片")
    private String picturesOfOnSiteIssues;

    @TableField(value = "processing_plan")
    @ApiModelProperty(value = "处理方案")
    private String processingPlan;

    @TableField(value = "expected_submission_time_for_changes")
    @ApiModelProperty(value = "变更预计提交时间")
    private Timestamp expectedSubmissionTimeForChanges;

    @TableField(value = "responsible_person")
    @ApiModelProperty(value = "责任人")
    private String responsiblePerson;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;


    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    public void copy(ProjectDisclosureProblem source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}