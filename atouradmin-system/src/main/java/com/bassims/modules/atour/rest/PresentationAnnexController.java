/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.PresentationAnnex;
import com.bassims.modules.atour.service.PresentationAnnexService;
import com.bassims.modules.atour.service.dto.PresentationAnnexDto;
import com.bassims.modules.atour.service.dto.PresentationAnnexQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-24
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "宣讲附件基础信息管理")
@RequestMapping("/api/presentationAnnex")
public class PresentationAnnexController {

    private static final Logger logger = LoggerFactory.getLogger(PresentationAnnexController.class);

    private final PresentationAnnexService presentationAnnexService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, PresentationAnnexQueryCriteria criteria) throws IOException {
        presentationAnnexService.download(presentationAnnexService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<PresentationAnnexDto>>}
    */
    @GetMapping("/list")
    @Log("查询宣讲附件基础信息")
    @ApiOperation("查询宣讲附件基础信息")
    public ResponseEntity<Object> query(PresentationAnnexQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(presentationAnnexService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<PresentationAnnexDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询宣讲附件基础信息")
    @ApiOperation("查询宣讲附件基础信息")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(presentationAnnexService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增宣讲附件基础信息")
    @ApiOperation("新增宣讲附件基础信息")
    public ResponseEntity<Object> create(@Validated @RequestBody PresentationAnnex resources){
        return new ResponseEntity<>(presentationAnnexService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改宣讲附件基础信息")
    @ApiOperation("修改宣讲附件基础信息")
    public ResponseEntity<Object> update(@Validated @RequestBody PresentationAnnex resources){
        presentationAnnexService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除宣讲附件基础信息")
    @ApiOperation("删除宣讲附件基础信息")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        presentationAnnexService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}