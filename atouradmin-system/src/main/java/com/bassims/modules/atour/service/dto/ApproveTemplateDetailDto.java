/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-24
**/
@Data
public class ApproveTemplateDetailDto implements Serializable {

    /** 审批子模板主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long approveTemplateDetailId;

    /** 审批人角色 */
    private String  approveRoleName;

    /** 审批主模板主键 */
    private Long approveTemplateId;

    /** 父节点id */
    private Long parentId;

    /** 审批编号 */
    private String approveNum;

    /** 审批角色 */
    private String approveRole;

    /** 执行方式 */
    private String approveMode;

    /** 是否有子集 */
    private Boolean hasChild;

    /** 审批组别 */
    private String approveGroup;

    /** 审批层级 */
    private String approveLevel;

    /** 发起方式 */
    private String approveBegin;

    /** 该审批角色是否可以修改 */
    private Integer isModifiable;

    /** 可修改的字段code */
    private String modifiableCode;


    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 审批排序 */
    private Integer approveIndex;

    private String roleName;

    private String modeName;


    /*可修改的字段code*/
    private String modifiableCodeTextarea;


    /*是否显示（0.显示、1.隐藏）*/
    private Boolean isShow;

    /*是否存在审批开启条件（0.不存在、1.存在）*/
    private Boolean isExistOpenCondition;

    /**审批开启条件的三级code*/
    private String approvalOpeningConditionNodeCode;


    /**审批开启条件的三级code值*/
    private String approvalOpeningConditionValue;

}