/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-04-18
**/
@Data
public class FeeInfoDto implements Serializable {

    /** 主键 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long feeId;

    /** 项目id */
    @J<PERSON><PERSON>ield(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 单位名称 */
    private String unitName;

    /** 建筑面积(m²) */
    private BigDecimal floorArea;

    /** 土建工程造价(元) */
    private BigDecimal civilEngineerCost;

    /** 机电工程造价(元) */
    private BigDecimal mechatronicsCost;

    /** 给排水工程造价(元) */
    private BigDecimal waterCost;

    /** 小计 */
    private BigDecimal subtotal;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;
}