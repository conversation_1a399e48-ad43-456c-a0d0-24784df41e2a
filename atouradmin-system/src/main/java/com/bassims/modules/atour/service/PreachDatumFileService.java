/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.domain.LocalStorage;
import com.bassims.modules.atour.domain.PreachDatumFile;
import com.bassims.modules.atour.service.dto.PreachDatumFileDto;
import com.bassims.modules.atour.service.dto.PreachDatumFileQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2023-10-10
 **/
public interface PreachDatumFileService extends BaseService<PreachDatumFile> {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(PreachDatumFileQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<PreachDatumFileDto>
     */
    List<PreachDatumFileDto> queryAll(PreachDatumFileQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param datumFileId ID
     * @return PreachDatumFileDto
     */
    PreachDatumFileDto findById(Long datumFileId);

    List<PreachDatumFileDto> queryByNodeCode(String nodeCode);

    /**
     * 创建
     *
     * @param resources /
     * @return PreachDatumFileDto
     */
    PreachDatumFileDto create(PreachDatumFile resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(PreachDatumFile resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<PreachDatumFileDto> all, HttpServletResponse response) throws IOException;


    /**
     * 上传
     * @param name 文件名称
     * @param file 文件
     * @return
     */
    PreachDatumFile createOss(String nodeCode, String name, MultipartFile file, Long userId) throws IOException;

    PreachDatumFile updateOss(Long datumFileId, String name, MultipartFile file) throws IOException;
    /**
     * 下载
     *
     * @param localId  当地id
     * @param response 响应
     * @param request  请求
     */
    void downloadOss(Long localId, HttpServletResponse response, HttpServletRequest request) throws IOException;
}