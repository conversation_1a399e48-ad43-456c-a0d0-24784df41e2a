/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.WorkbookUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.TemplateEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.domain.TemplateCompletionReceipt;
import com.bassims.modules.atour.service.dto.DropDownDataFillDTO;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateCompletionReceiptRepository;
import com.bassims.modules.atour.service.TemplateCompletionReceiptService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptDto;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateCompletionReceiptMapper;

import com.bassims.utils.excelutil.ExcelUtils;
import com.bassims.utils.reflex.NodeFiledValue;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;
import org.lionsoul.ip2region.DbConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-01-26
 **/
@Service
public class TemplateCompletionReceiptServiceImpl extends BaseServiceImpl<TemplateCompletionReceiptRepository, TemplateCompletionReceipt> implements TemplateCompletionReceiptService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateCompletionReceiptServiceImpl.class);

    @Autowired
    private TemplateCompletionReceiptRepository templateCompletionReceiptRepository;
    @Autowired
    private TemplateCompletionReceiptMapper templateCompletionReceiptMapper;
    @Autowired
    private DictDetailRepository dictDetailRepository;


    @Override
    public Map<String, Object> queryAll(TemplateCompletionReceiptQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        List<TemplateCompletionReceipt> receipts = templateCompletionReceiptRepository.queryCondition(criteria);
        PageInfo<TemplateCompletionReceipt> page = new PageInfo<>(receipts);
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateCompletionReceiptMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateCompletionReceiptDto> queryAll(TemplateCompletionReceiptQueryCriteria criteria) {
        return templateCompletionReceiptMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateCompletionReceipt.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateCompletionReceiptDto findById(Long receiptId) {
        TemplateCompletionReceipt templateCompletionReceipt = Optional.ofNullable(getById(receiptId)).orElseGet(TemplateCompletionReceipt::new);
        ValidationUtil.isNull(templateCompletionReceipt.getReceiptId(), getEntityClass().getSimpleName(), "receiptId", receiptId);
        return templateCompletionReceiptMapper.toDto(templateCompletionReceipt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateCompletionReceiptDto create(TemplateCompletionReceipt resources) {
        resources.setIsDelete("0");
        save(resources);
        return findById(resources.getReceiptId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateCompletionReceipt resources) {
        TemplateCompletionReceipt templateCompletionReceipt = Optional.ofNullable(getById(resources.getReceiptId())).orElseGet(TemplateCompletionReceipt::new);
        ValidationUtil.isNull(templateCompletionReceipt.getReceiptId(), "TemplateCompletionReceipt", "id", resources.getReceiptId());
        templateCompletionReceipt.copy(resources);
        updateById(templateCompletionReceipt);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long receiptId : ids) {
            templateCompletionReceiptRepository.deleteById(receiptId);
        }
    }

    @Override
    public void download(List<TemplateCompletionReceiptDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateCompletionReceiptDto templateCompletionReceipt : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("总项(码值completion_total_items)", templateCompletionReceipt.getTotalItem());
            map.put("序号", templateCompletionReceipt.getSerialNumber());
            map.put("分项", templateCompletionReceipt.getSubItem());
            map.put("项目", templateCompletionReceipt.getProject());
            map.put("内容（码值completion_content）", templateCompletionReceipt.getContent());
            map.put("分项", templateCompletionReceipt.getSubItemContent());
            map.put("分值", templateCompletionReceipt.getScore());
            map.put("标准分值", templateCompletionReceipt.getStandardScore());
            map.put("验收（码值acceptance）", templateCompletionReceipt.getAcceptance());
            map.put("得分", templateCompletionReceipt.getGetScore());
            map.put("分类（码值classification）", templateCompletionReceipt.getClassification());
            map.put("合格率", templateCompletionReceipt.getQualificationRate());
            map.put("是否总包（码值general_contracting_or_not）", templateCompletionReceipt.getGeneralContractingOrNot());
            map.put("总包标准分值", templateCompletionReceipt.getGeneralContractingStandardScore());
            map.put("总包得分", templateCompletionReceipt.getOverallContractingScore());
            map.put("总包得分率", templateCompletionReceipt.getOverallContractingScoreRate());
            map.put("现场照片", templateCompletionReceipt.getCheckAttachments());
            map.put("标准照片", templateCompletionReceipt.getStandardPhotos());
            map.put("备注", templateCompletionReceipt.getRemarks());
            map.put("整改说明", templateCompletionReceipt.getRectificationInstructions());
            map.put("整改照片", templateCompletionReceipt.getRectificationAttachments());
            map.put("整改日期", templateCompletionReceipt.getRectificationDate());
            map.put("创建时间", templateCompletionReceipt.getCreateTime());
            map.put("创建人", templateCompletionReceipt.getCreateBy());
            map.put("更新时间", templateCompletionReceipt.getUpdateTime());
            map.put("更新人", templateCompletionReceipt.getUpdateBy());
            map.put("是否可用", templateCompletionReceipt.getIsEnabled());
            map.put("是否删除", templateCompletionReceipt.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<TemplateCompletionReceipt> selectByCondition(String totalItem, String project, String content, String subItemContent, String subItem) {
        return this.baseMapper.selectByCondition(totalItem, project, content, subItemContent, subItem);
    }

    @Override
    public void downloadTemplate(String templateCode, HttpServletResponse response) {
        TemplateEnum templateEnum = TemplateEnum.getByCode(templateCode);
        // 这里能读到这个流，但是是找不到这个文件的
        ClassPathResource classPathResource = new ClassPathResource(templateEnum.getPath());
        try (
                InputStream inputStream = classPathResource.getInputStream();
//                InputStream inputStream = ResourceUtil.getResource(templateEnum.getPath()).openStream();
                OutputStream outputStream = response.getOutputStream();
        ) {
            // 根据枚举类型，设置下拉选项
            Workbook workbook = fillDropDownData(templateEnum, inputStream);
            response.setContentType("application/x-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(templateEnum.getName().getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            // 如果 workbook 有值，则代表设置了下拉选项，需要写回 workbook
            if (workbook == null) {
                IOUtils.copy(inputStream, outputStream);
                outputStream.flush();
            } else {
                workbook.write(outputStream);
                outputStream.flush();
                workbook.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Override
    public Workbook fillDropDownData(TemplateEnum templateEnum, InputStream inputStream) {
        // 如果模板没有填充数据的 json 描述文件，则无需填充数据直接返回
        if (templateEnum.getDropDownDataFillJsonPath() == null) {
            return null;
        }

        // 创建工作簿
        Workbook workbook = WorkbookUtil.createBook(inputStream);

        // 读取 json 描述文件
        String dropDownDataJsonStr = ResourceUtil.readUtf8Str(templateEnum.getDropDownDataFillJsonPath());
        JSONArray jsonArray = JSONUtil.parseArray(dropDownDataJsonStr);
        List<DropDownDataFillDTO> dropDownDataFillList = JSONUtil.toList(jsonArray, DropDownDataFillDTO.class);

        // 循环遍历设置下拉数据
        for (DropDownDataFillDTO dropDownDataFill : dropDownDataFillList) {
            for (DropDownDataFillDTO.DataDescriptions dataDescriptions : dropDownDataFill.getDataDescriptions()) {
                // 数据字典填充
                if (dataDescriptions.getDataType() == DropDownDataFillDTO.DataType.dict) {
                    List<String> dictLabel = dictDetailRepository.findDictDetailByDictNames(dataDescriptions.getDataKey());
                    dataDescriptions.setDataList(dictLabel);
                }
            }
        }

        // 将下拉数据存储到隐藏的 sheet 中
        for (DropDownDataFillDTO dropDownDataFillDTO : dropDownDataFillList) {
            String hiddenSheetName = "hidden_" + dropDownDataFillDTO.getSheetIndex();
            Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);
            workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
            List<DropDownDataFillDTO.DataDescriptions> dataDescriptionsList = dropDownDataFillDTO.getDataDescriptions();
            for (int column = 0; column < dataDescriptionsList.size(); column++) {
                List<String> dataList = dataDescriptionsList.get(column).getDataList();
                for (int row = 0; row < dataList.size(); row++) {
                    Row hiddenSheetRow = hiddenSheet.getRow(row);
                    if (hiddenSheetRow == null) {
                        hiddenSheetRow = hiddenSheet.createRow(row);
                    }
                    Cell cell = hiddenSheetRow.createCell(column);
                    cell.setCellValue(dataList.get(row));
                }
                // 没有数据时，不填充
                if (dataList.size() == 0) {
                    continue;
                }
                // 存储 Name 用于设置下拉数据
                Name namedCell = workbook.createName();
                namedCell.setNameName(hiddenSheetName + "_" + column);
                String columnLetter = CellReference.convertNumToColString(column);
                namedCell.setRefersToFormula(String.format("%s!$%s$1:$%s$%s", hiddenSheetName, columnLetter, columnLetter, dataList.size()));
                dataDescriptionsList.get(column).setNamedCell(namedCell);
            }
        }

        // 填充下拉选项
        for (DropDownDataFillDTO dropDownDataFill : dropDownDataFillList) {
            int sheetIndex = 0;
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            for (DropDownDataFillDTO.DataDescriptions dataDescription : dropDownDataFill.getDataDescriptions()) {
                DataValidationHelper validationHelper = sheet.getDataValidationHelper();
                if (dataDescription.getNamedCell() == null) {
                    continue;
                }
                // 数据有效性对象
                DataValidationConstraint constraint = validationHelper.createFormulaListConstraint(dataDescription.getNamedCell().getNameName());
                // 四个参数：要添加下拉框的首行、末行、首列、末列
                CellRangeAddressList addressList = new CellRangeAddressList(dataDescription.getStartRowIndex(), dataDescription.getEndRowIndex(),
                        dataDescription.getStartColumnIndex(), dataDescription.getEndColumnIndex());
                DataValidation validation = validationHelper.createValidation(constraint, addressList);
                sheet.addValidationData(validation);
            }
        }
        return workbook;
    }


    @Override
    public Map<String, Object> importTemplateCompletionReceipt(MultipartFile file) {
        Map<String, Object> resultMap = new HashMap<>();
        List<TemplateCompletionReceipt> receiptList = new ArrayList<>();
        //获取房型
        List<DictDetail> roomType = dictDetailRepository.findDictDetailByDictName("room_type");
        List<String> type = roomType.stream().map(DictDetail::getLabel).collect(Collectors.toList());

        try {
            //获取数据
            List<List<Object>> list = ExcelUtils.getListByExcelSheet(file.getInputStream(), file.getOriginalFilename());
            //校验文件格式是否正确
            this.fileFormat(file, resultMap, type, list);

//            Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity(),(oldData, newData)->newData));
//            System.out.println(new ArrayList<>(userMap.values()));
//
            Map<String, DictDetail> completionTotalItems = dictDetailRepository.findDictDetailByDictName("completion_total_items").stream().collect(Collectors.toMap(DictDetail::getLabel,Function.identity(),(oldData,newData)->newData));
            Map<String, DictDetail> content = dictDetailRepository.findDictDetailByDictName("completion_content").stream().distinct().collect(Collectors.toMap(DictDetail::getLabel,Function.identity(),(oldData,newData)->newData));
            Map<String, DictDetail> acceptance = dictDetailRepository.findDictDetailByDictName("acceptance").stream().distinct().collect(Collectors.toMap(DictDetail::getLabel,Function.identity(),(oldData,newData)->newData));
            Map<String, DictDetail> classifications = dictDetailRepository.findDictDetailByDictName("classification").stream().collect(Collectors.toMap(DictDetail::getLabel,Function.identity(),(oldData,newData)->newData));
            Map<String, DictDetail> contractingOrNots = dictDetailRepository.findDictDetailByDictName("general_contracting_or_not").stream().collect(Collectors.toMap(DictDetail::getLabel,Function.identity(),(oldData,newData)->newData));

            //封装数据
            for (int i = 2; i < list.size(); i++) {
                List<Object> objects = list.get(i);
                if (objects.get(0) == "") {
                    continue;
                }
                TemplateCompletionReceipt receipt = new TemplateCompletionReceipt();
//                receipt.setTotalItem(IdUtil.getSnowflake(1, 1).nextId());
                //总项 码值 completion_total_items
                DictDetail total = completionTotalItems.get(objects.get(1)+"");
                receipt.setTotalItem(ObjectUtil.isNotEmpty(total)?total.getValue():"");

                //分项
                receipt.setSubItem(objects.get(2)+"");
                //项目
                receipt.setProject(objects.get(3)+"");

                //内容 （码值 completion_content）
                DictDetail dictDetail = content.get(objects.get(4)+"");
                receipt.setContent(ObjectUtil.isNotEmpty(dictDetail)?dictDetail.getValue():"");

                //分项内容
                receipt.setSubItemContent(objects.get(5)+"");
                //期初分值
                receipt.setScore(objects.get(6)+"");
                //标准分值
                receipt.setStandardScore(objects.get(7)+"");
                //验收
                DictDetail dictDetail1 = acceptance.get(objects.get(8) + "");
                receipt.setAcceptance(ObjectUtil.isNotEmpty(dictDetail1)?dictDetail1.getValue():"");
                //得分
                receipt.setGetScore(objects.get(9)+"");

                //分类 码值 classification
                DictDetail classification = classifications.get(objects.get(10)+"");
                receipt.setClassification(ObjectUtil.isNotEmpty(classification)?classification.getValue():"");

                //合格率
                Object o = objects.get(11);
                receipt.setQualificationRate(o+"");
                //是否总包 码值general_contracting_or_not
                DictDetail contractingOrNot = contractingOrNots.get( objects.get(12)+"");
                receipt.setGeneralContractingOrNot(ObjectUtil.isNotEmpty(contractingOrNot)?contractingOrNot.getValue():"");

                //总包标准分值
                receipt.setGeneralContractingStandardScore(objects.get(13)+"");
                //总包得分
                receipt.setOverallContractingScore(objects.get(14)+"");
                //总包得分率
                Object o1 = objects.get(15);
                receipt.setOverallContractingScore(o1 +"");

                //是否实拍照片
                receipt.setIsRealPhotosTaken("0");
                receipt.setIsDelete("0");
                receiptList.add(receipt);
            }
            if (this.saveBatch(receiptList)) {
                resultMap.put("state", 200);
            } else {
                throw new BadRequestException("文档内无数据，请重新导入");
            }
        } catch (NullPointerException e) {
            throw new BadRequestException("当前上传的验收单文件格式错误或者文档内无数据");
        } catch (Exception e) {
            throw new BadRequestException(String.valueOf(resultMap.get("mete")));
        }

        return resultMap;
    }

    private void fileFormat(MultipartFile file, Map<String, Object> resultMap, List<String> type, List<List<Object>> list) {
        //验证文件类型
        if (!Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".")).equals(".xls") && !file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")).equals(".xlsx")) {
            resultMap.put("mete", "文件类型有误！请上传excel文件");
            throw new BadRequestException("文件类型有误！请上传excel文件");
        }
        //校验excel
        ExcelReader reader;
        try {
            reader = ExcelUtil.getReader(file.getInputStream());
        } catch (Exception e) {
            throw new NullPointerException();
        }
        Sheet sheet = reader.getSheet();
        String a2 = ExcelUtils.getCellValue(sheet.getRow(2).getCell(0)).toString();
        String b2 = ExcelUtils.getCellValue(sheet.getRow(2).getCell(1)).toString();
        String c2 = ExcelUtils.getCellValue(sheet.getRow(2).getCell(2)).toString();
        String d2 = ExcelUtils.getCellValue(sheet.getRow(2).getCell(3)).toString();
        if (!"序号".equals(a2) || !"总项".equals(b2) || !"分项".equals(c2) || !"项目".equals(d2)) {
            resultMap.put("mete", "当前上传的验收单文件格式错误，请先下载【验收单模版】，填写正确数据格式并上传");
            throw new BadRequestException("当前上传的验收单文件格式错误，请先下载【验收单模版】，填写正确数据格式并上传");
        }

        if (list.size() <= 4) {
            resultMap.put("mete", "文档内无数据，请重新导入");
            throw new BadRequestException("文档内无数据，请重新导入");
        }
        list.remove(0);
//        list.remove(0);
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).size() < 4) {
                resultMap.put("mete", "第" + (i + 2) + "行的数据不能为空");
                resultMap.put("state", 400);
                throw new BadRequestException("第" + (i + 2) + "行的数据不能为空");
            }
            Object num = list.get(i).get(0);
            Object roomNum = list.get(i).get(1);
            Object roomArea = list.get(i).get(2);
            Object roomTypeE = list.get(i).get(3);
            if (num == null || roomNum == null || roomArea == null || roomTypeE == null) {
                resultMap.put("mete", "第" + (i + 2) + "行的数据不能为空");
                resultMap.put("state", 400);
                throw new BadRequestException("第" + (i + 2) + "行的数据不能为空");
            }
            if (StrUtil.isBlank(num.toString()) || StrUtil.isBlank(roomNum.toString())
                    || StrUtil.isBlank(roomArea.toString()) || StrUtil.isBlank(roomTypeE.toString())) {
                resultMap.put("mete", "第" + (i + 2) + "行的数据不能为空");
                resultMap.put("state", 400);
                throw new BadRequestException("第" + (i + 2) + "行的数据不能为空");
            }

        }

    }


}