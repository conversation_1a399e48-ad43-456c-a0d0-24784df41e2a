/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.BaseStoreInfo;
import com.bassims.modules.atour.repository.BaseStoreInfoRepository;
import com.bassims.modules.atour.service.BaseStoreInfoService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.dto.BaseStoreInfoDto;
import com.bassims.modules.atour.service.dto.BaseStoreInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.BaseStoreInfoMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-10
**/
@Service
public class BaseStoreInfoServiceImpl extends BaseServiceImpl<BaseStoreInfoRepository,BaseStoreInfo> implements BaseStoreInfoService {

    private static final Logger logger = LoggerFactory.getLogger(BaseStoreInfoServiceImpl.class);

    @Autowired
    private BaseStoreInfoRepository baseStoreInfoRepository;
    @Autowired
    private BaseStoreInfoMapper baseStoreInfoMapper;
    @Autowired
    private ProjectInfoService projectInfoService;

    @Override
    public Map<String,Object> queryAll(BaseStoreInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<BaseStoreInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(BaseStoreInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", baseStoreInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<BaseStoreInfoDto> queryAll(BaseStoreInfoQueryCriteria criteria){
        return baseStoreInfoMapper.toDto(list(QueryHelpPlus.getPredicate(BaseStoreInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseStoreInfoDto findById(Long storeId) {
        BaseStoreInfo baseStoreInfo = Optional.ofNullable(getById(storeId)).orElseGet(BaseStoreInfo::new);
        ValidationUtil.isNull(baseStoreInfo.getStoreId(),getEntityClass().getSimpleName(),"storeId",storeId);
        return baseStoreInfoMapper.toDto(baseStoreInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseStoreInfoDto create(BaseStoreInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setStoreId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getStoreId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BaseStoreInfo resources) {
        BaseStoreInfo baseStoreInfo = Optional.ofNullable(getById(resources.getStoreId())).orElseGet(BaseStoreInfo::new);
        ValidationUtil.isNull( baseStoreInfo.getStoreId(),"BaseStoreInfo","id",resources.getStoreId());
        baseStoreInfo.copy(resources);
        updateById(baseStoreInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long storeId : ids) {
            baseStoreInfoRepository.deleteById(storeId);
        }
    }

    @Override
    public void download(List<BaseStoreInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (BaseStoreInfoDto baseStoreInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("门店no", baseStoreInfo.getStoreNo());
            map.put("门店类型", baseStoreInfo.getStoreType());
            map.put("门店名称", baseStoreInfo.getStoreName());
            map.put("大区value", baseStoreInfo.getRegion());
            map.put("省份id", baseStoreInfo.getProvince());
            map.put("省份名称", baseStoreInfo.getProvinceName());
            map.put("城市id", baseStoreInfo.getCity());
            map.put("城市名称", baseStoreInfo.getCityName());
            map.put("区县id", baseStoreInfo.getCounty());
            map.put("区县名称", baseStoreInfo.getCountyName());
            map.put("详细地址（不包括省市县）", baseStoreInfo.getProjectAddress());
            map.put("城市公司value", baseStoreInfo.getCityCompany());
            map.put("是否生效", baseStoreInfo.getIsActive());
            map.put("创建时间", baseStoreInfo.getCreateTime());
            map.put("创建人", baseStoreInfo.getCreateBy());
            map.put("更新时间", baseStoreInfo.getUpdateTime());
            map.put("更新人", baseStoreInfo.getUpdateBy());
            map.put("是否可用", baseStoreInfo.getIsEnabled());
            map.put("是否删除", baseStoreInfo.getIsDelete());
            map.put("门店全称", baseStoreInfo.getStoreFullName());
            map.put("门店主档id", baseStoreInfo.getStoreMasterInfo());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<BaseStoreInfoDto> getStoreList(BaseStoreInfoQueryCriteria criteria) {
        List<BaseStoreInfoDto> list =  baseStoreInfoMapper.toDto(list(QueryHelpPlus.getPredicate(BaseStoreInfo.class, criteria)));
        return list;
    }
}