package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;

/**
 * 项目时间报告
 *
 * <AUTHOR>
 * @date 2022/07/09
 */
@Data
@TableName(value = "project_time_report")
public class ProjectTimeReport implements Serializable {

    @TableId(value = "project_id")
    private Long projectId;
    private Long city;
    private String projectName;
    private String projectTypeName;
    private String projectType;
    private String projectArea;
    private String projectBudget;
    private String cityCompanyName;
    private String cityCompany;
    private String regionName;
    private String region;
    private String projectNo;
    private String nickName;
    private String projectTime;
    private String taskPhaseName;
    private String taskPhase;
    private String checkTime;
    private String rechargeTime;
    private String prTime;
    private String supTime;
    private String totalTime;
    private String softTime;
    private String totalUnit;
    private String pmcUnit;
    private String designTime;
    private String conTime;
    private String softPlanTime;
    private String totalPointTime;
    private String totalEntryTime;
    private String concealWorkTime;
    private String softEntryTime;
    private String completeTime;
    private String settleSubTime;
    private String settleConTime;
    private String settleAppTime;
    private String supAppTime;
    private String supRechargeTime;
    private String rpAppTime;
    private String settleContractTime;
    private Boolean isDelete;
    private Date createTime;

}
