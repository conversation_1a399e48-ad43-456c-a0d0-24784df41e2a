/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.Reimburse;
import com.bassims.modules.atour.service.ReimburseService;
import com.bassims.modules.atour.service.dto.ReimburseDto;
import com.bassims.modules.atour.service.dto.ReimburseQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-12-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "费用报销合计管理")
@RequestMapping("/api/reimburse")
public class ReimburseController {

    private static final Logger logger = LoggerFactory.getLogger(ReimburseController.class);

    private final ReimburseService reimburseService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ReimburseQueryCriteria criteria) throws IOException {
        reimburseService.download(reimburseService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ReimburseDto>>}
    */
    @GetMapping("/list")
    @Log("查询费用报销合计")
    @ApiOperation("查询费用报销合计")
    public ResponseEntity<Object> query(ReimburseQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(reimburseService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ReimburseDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询费用报销合计")
    @ApiOperation("查询费用报销合计")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(reimburseService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增费用报销合计")
    @ApiOperation("新增费用报销合计")
    public ResponseEntity<Object> create(@Validated @RequestBody Reimburse resources){
        return new ResponseEntity<>(reimburseService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改费用报销合计")
    @ApiOperation("修改费用报销合计")
    public ResponseEntity<Object> update(@Validated @RequestBody Reimburse resources){
        reimburseService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除费用报销合计")
    @ApiOperation("删除费用报销合计")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        reimburseService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}