/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /项目竣工验收照片表
* <AUTHOR>
* @date 2024-02-22
**/
@Data
@TableName(value="t_project_completion_photo")
public class ProjectCompletionPhoto implements Serializable {

    @TableId(value = "project_receipt_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectReceiptId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目ID")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "total_item")
    @ApiModelProperty(value = "总项(码值completion_total_items)")
    private String totalItem;

    @TableField(value = "sub_item")
    @ApiModelProperty(value = "分项")
    private String subItem;

    @TableField(value = "standard_photos")
    @ApiModelProperty(value = "标准照片")
    private String standardPhotos;

    @TableField(value = "standard_videos")
    @ApiModelProperty(value = "标准视频")
    private String standardVideos;

    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private String isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private String isDelete;

    public void copy(ProjectCompletionPhoto source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}