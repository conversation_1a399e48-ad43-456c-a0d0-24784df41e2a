/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-29
**/
@Data
public class ProjectStakeholdersUpdateDto implements Serializable {


    /** 需要变更的项目id */
    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private List<Long> projectId;

    /** 离职用户id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long leavingUser;


    /** 变更的用户id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
}