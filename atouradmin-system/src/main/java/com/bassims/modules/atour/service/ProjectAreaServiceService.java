package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectAreaService;
import com.bassims.modules.atour.service.dto.ProjectAreaServiceQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 服务面积
 */
public interface ProjectAreaServiceService extends BaseService<ProjectAreaService> {

    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectAreaServiceQueryCriteria criteria) throws IOException;

    /**
     * 查询明细
     *
     * @param criteria 标准
     * @param pageable
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(ProjectAreaServiceQueryCriteria criteria, Pageable pageable);
}
