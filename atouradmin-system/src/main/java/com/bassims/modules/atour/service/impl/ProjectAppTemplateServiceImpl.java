/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.ProjectAppTemplateRepository;
import com.bassims.modules.atour.repository.ProjectNodeInfoRepository;
import com.bassims.modules.atour.service.ApproveTemplateDetailService;
import com.bassims.modules.atour.service.ProjectAppTemplateService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectAppTemplateMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-06-09
 **/
@Service
public class ProjectAppTemplateServiceImpl extends BaseServiceImpl<ProjectAppTemplateRepository, ProjectAppTemplate> implements ProjectAppTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAppTemplateServiceImpl.class);

    @Autowired
    private ProjectAppTemplateRepository projectAppTemplateRepository;
    @Autowired
    private ProjectAppTemplateMapper projectAppTemplateMapper;
    @Autowired
    private ApproveTemplateDetailService approveTemplateDetailService;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;

    @Autowired
    private NoteInfoMappingUtil util;

    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;

    @Override
    public Map<String, Object> queryAll(ProjectAppTemplateQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectAppTemplate> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectAppTemplate.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectAppTemplateMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectAppTemplateDto> queryAll(ProjectAppTemplateQueryCriteria criteria) {
        return projectAppTemplateMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectAppTemplate.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectAppTemplateDto findById(Long approveNodeId) {
        ProjectAppTemplate projectAppTemplate = Optional.ofNullable(getById(approveNodeId)).orElseGet(ProjectAppTemplate::new);
        ValidationUtil.isNull(projectAppTemplate.getApproveNodeId(), getEntityClass().getSimpleName(), "approveNodeId", approveNodeId);
        return projectAppTemplateMapper.toDto(projectAppTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectAppTemplateDto create(ProjectAppTemplate resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setApproveNodeId(snowflake.nextId());
        save(resources);
        return findById(resources.getApproveNodeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectAppTemplate resources) {
        ProjectAppTemplate projectAppTemplate = Optional.ofNullable(getById(resources.getApproveNodeId())).orElseGet(ProjectAppTemplate::new);
        ValidationUtil.isNull(projectAppTemplate.getApproveNodeId(), "ProjectAppTemplate", "id", resources.getApproveNodeId());
        projectAppTemplate.copy(resources);
        updateById(projectAppTemplate);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long approveNodeId : ids) {
            projectAppTemplateRepository.deleteById(approveNodeId);
        }
    }

    @Override
    public void download(List<ProjectAppTemplateDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectAppTemplateDto projectAppTemplate : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectAppTemplate.getProjectId());
            map.put("节点id", projectAppTemplate.getNodeId());
            map.put("节点编码", projectAppTemplate.getNodeCode());
            map.put("节点名称", projectAppTemplate.getNodeName());
            map.put("审批子模板主键", projectAppTemplate.getApproveTemplateDetailId());
            map.put("审批主模板主键", projectAppTemplate.getApproveTemplateId());
            map.put("父节点id", projectAppTemplate.getParentId());
            map.put("审批编号", projectAppTemplate.getApproveNum());
            map.put("审批角色", projectAppTemplate.getApproveRole());
            map.put("是否有子集", projectAppTemplate.getHasChild());
            map.put("审批组别", projectAppTemplate.getApproveGroup());
            map.put("审批层级", projectAppTemplate.getApproveLevel());
            map.put("执行方式", projectAppTemplate.getApproveMode());
            map.put("发起方式", projectAppTemplate.getApproveBegin());
            map.put("审批排序", projectAppTemplate.getApproveIndex());
            map.put("该审批角色是否可以修改", projectAppTemplate.getIsModifiable());
            map.put("可修改的字段code", projectAppTemplate.getModifiableCode());
            map.put("创建时间", projectAppTemplate.getCreateTime());
            map.put("创建人", projectAppTemplate.getCreateBy());
            map.put("更新时间", projectAppTemplate.getUpdateTime());
            map.put("更新人", projectAppTemplate.getUpdateBy());
            map.put("是否可用", projectAppTemplate.getIsEnabled());
            map.put("是否删除", projectAppTemplate.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void copyApproveTemplate(ProjectGroup projectGroup, ProjectTemplateApproveRelationDto relationDto) {
        //todo 需增加审批的code插入到审批项目表中用于判断
        LambdaQueryWrapper<ApproveTemplateDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ApproveTemplateDetail.class);
        detailLambdaQueryWrapper.eq(ApproveTemplateDetail::getApproveTemplateId, relationDto.getApproveMatrixId());
        List<ApproveTemplateDetail> list = approveTemplateDetailService.list(detailLambdaQueryWrapper);
        if (list.size() > 0) {
            for (ApproveTemplateDetail detail : list) {
                ProjectAppTemplate projectAppTemplate = new ProjectAppTemplate();
                BeanUtils.copyProperties(detail, projectAppTemplate);
                projectAppTemplate.setProjectId(projectGroup.getProjectId());
                projectAppTemplate.setOrderId(projectGroup.getOrderId());
                projectAppTemplate.setNodeId(projectGroup.getProjectGroupId());
                projectAppTemplate.setNodeCode(projectGroup.getNodeCode());
                projectAppTemplate.setNodeName(projectGroup.getNodeName());
                save(projectAppTemplate);
            }
        }
    }

    @Override
    public Boolean hasApprove(ProjectNodeInfoDto nodeInfo) {
        Boolean flag = Boolean.FALSE;

        LambdaQueryWrapper<ProjectAppTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);

        templateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, nodeInfo.getProjectGroupId());

        int count = (int) count(templateLambdaQueryWrapper);
        if (count > 0) {
            //查询该审批记录是否符合条件
            boolean theConditions = this.getIsMeetTheConditions(nodeInfo);
            if (theConditions) {
                flag = Boolean.TRUE;
            }
        }
        return flag;
    }

    private boolean getIsMeetTheConditions(ProjectNodeInfoDto nodeInfo) {
        boolean isMeetTheConditions = Boolean.TRUE;
        ProjectTemplateApproveRelation meetTheConditions = projectAppTemplateRepository.getIsMeetTheConditions(nodeInfo.getProjectGroupId(),nodeInfo.getProjectId());
        Boolean isModifiable =false;
        if (ObjectUtil.isNotEmpty(meetTheConditions) && ObjectUtil.isNotEmpty(meetTheConditions.getIsModifiable()) && meetTheConditions.getIsModifiable()==1) {
            isModifiable = true;
        }
//          isModifiable = ObjectUtil.isNotEmpty(meetTheConditions) ?  : false;
        if (isModifiable && ObjectUtil.isNotEmpty(meetTheConditions.getModifiableCode())) {
            util.initialize(projectNodeInfoService.getSubmeterProjectId(Long.valueOf(nodeInfo.getProjectId())));

            Gson gson = new Gson(); // 创建Gson对象
            JsonArray jsonArray = gson.fromJson(meetTheConditions.getModifiableCode(), JsonArray.class); // 解析JSON字符串为JsonArray对象
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject jsonObject = jsonArray.get(i).getAsJsonObject(); // 获取JsonArray中的JsonObject对象
                String value = jsonObject.get("value").getAsString();
                String nodeCode = jsonObject.get("nodeCode").getAsString();
                //获取nodeCode的值，逗号隔开 查询当前节点的值和 jsonObject.get("tertiaryKey").getAsString().equals("value") 相同,存在数据就生成审批
                LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, nodeInfo.getProjectId())
                        .in(ProjectNodeInfo::getRemark, value.split(","))
                        .in(ProjectNodeInfo::getNodeCode, nodeCode.split(","));
                Long aLong = projectNodeInfoRepository.selectCount(wrapper);
                if (aLong == 0) {
                    //不符合审批条件，不启动审批流
                    isMeetTheConditions = Boolean.FALSE;
                }
                if (nodeInfo.getNodeCode().equals(JhSystemEnum.twoNodeCodeEnum.NODE_DES125.getKey()) && nodeInfo.getRoleCode().contains(",")) {
                    //样板间验收 负责角色多个的话，不启动审批流
                    isMeetTheConditions = Boolean.FALSE;
                }
            }
        }
        return isMeetTheConditions;
    }

    @Override
    public Boolean hasApprove(OrderNodeInfoDto nodeInfo) {
        Boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectAppTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);
        templateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, nodeInfo.getProjectGroupId());
        int count = (int) count(templateLambdaQueryWrapper);
        if (count > 0) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    @Override
    public Boolean hasApprove(Long projectGroupId) {
        Boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectAppTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);
        templateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, projectGroupId);
        int count = (int) count(templateLambdaQueryWrapper);
        if (count > 0) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    @Override
    public Boolean orderHasApprove(OrderNodeInfoDto nodeInfo) {
        Boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectAppTemplate> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);
        templateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, nodeInfo.getProjectGroupId());
        int count = (int) count(templateLambdaQueryWrapper);
        if (count > 0) {
            flag = Boolean.TRUE;
        }
        return flag;
    }




}