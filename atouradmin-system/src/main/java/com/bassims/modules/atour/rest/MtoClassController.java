/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.MtoClass;
import com.bassims.modules.atour.service.MtoClassService;
import com.bassims.modules.atour.service.dto.MtoClassDto;
import com.bassims.modules.atour.service.dto.MtoClassQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-14
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_mto_class管理")
@RequestMapping("/api/mtoClass")
public class MtoClassController {

    private static final Logger logger = LoggerFactory.getLogger(MtoClassController.class);

    private final MtoClassService mtoClassService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, MtoClassQueryCriteria criteria) throws IOException {
        mtoClassService.download(mtoClassService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<MtoClassDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_mto_class")
    @ApiOperation("查询t_mto_class")
    public ResponseEntity<Object> query(MtoClassQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(mtoClassService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<MtoClassDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_mto_class")
    @ApiOperation("查询t_mto_class")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(mtoClassService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_mto_class")
    @ApiOperation("新增t_mto_class")
    public ResponseEntity<Object> create(@Validated @RequestBody MtoClass resources){
        return new ResponseEntity<>(mtoClassService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_mto_class")
    @ApiOperation("修改t_mto_class")
    public ResponseEntity<Object> update(@Validated @RequestBody MtoClass resources){
        mtoClassService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_mto_class")
    @ApiOperation("删除t_mto_class")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        mtoClassService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<MtoClassDto>>}
     */
    @GetMapping("/queryParent")
    @Log("查询t_mto_class父节点")
    @ApiOperation("查询t_mto_class父节点")
    public ResponseEntity<Object> queryParent(){
        return new ResponseEntity<>(mtoClassService.queryParent(),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<MtoClassDto>>}
     */
    @GetMapping("/queryChild")
    @Log("查询t_mto_class子节点")
    @ApiOperation("查询t_mto_class子节点")
    public ResponseEntity<Object> queryChild(Long id){
        return new ResponseEntity<>(mtoClassService.queryChild(id),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<MtoClassDto>>}
     */
    @GetMapping("/queryChildAll")
    @Log("查询t_mto_class子节点")
    @ApiOperation("查询t_mto_class子节点")
    public ResponseEntity<Object> queryChildAll(){
        return new ResponseEntity<>(mtoClassService.queryChildAll(),HttpStatus.OK);
    }
}