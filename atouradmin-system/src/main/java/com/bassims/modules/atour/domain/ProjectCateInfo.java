/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-01-06
**/
@Data
@TableName(value="t_project_cate_info")
public class ProjectCateInfo implements Serializable {

    @TableId(value = "cate_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long cateId;

    @TableField(value = "cate_template_id")
    @ApiModelProperty(value = "cateTemplateId")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long cateTemplateId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "cate_name")
    @ApiModelProperty(value = "证件类型名称")
    private String cateName;

    @TableField(value = "cate_type")
    @ApiModelProperty(value = "证件类型")
    private String cateType;

    @TableField(value = "recom_finsh_time")
    @ApiModelProperty(value = "建议完成时间")
    private Timestamp recomFinshTime;

    @TableField(value = "cate_process_time")
    @ApiModelProperty(value = "证照审核时间")
    private Timestamp cateProcessTime;

    @TableField(value = "cate_process_status")
    @ApiModelProperty(value = "证照审核状态（码值：）")
    private String cateProcessStatus;

    @TableField(value = "cate_finsh_status")
    @ApiModelProperty(value = "证照完成状态（码值：）")
    private String cateFinshStatus;

    @TableField(value = "cate_finsh_time")
    @ApiModelProperty(value = "证照完成时间")
    private Timestamp cateFinshTime;

    @TableField(value = "cate_files")
    @ApiModelProperty(value = "证照文件")
    private String cateFiles;

    @TableField(value = "three_node_code")
    @ApiModelProperty(value = "关联3级nodeCode")
    private String threeNodeCode;

    @TableField(value = "two_node_code")
    @ApiModelProperty(value = "关联2级nodeCode")
    private String twoNodeCode;

    @TableField(value = "remark_one")
    @ApiModelProperty(value = "预留字段")
    private String remarkOne;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "是否已删除：0 正常，1 已删除")
    private Integer isDelete;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "not_delete")
    @ApiModelProperty(value = "是否可以删除 0可删除；1不可删除")
    private String notDelete;

    public void copy(ProjectCateInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
