/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.TreeNode;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.domain.LocalStorage;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.ProjectGroupRepository;
import com.bassims.modules.atour.repository.ProjectInfoAbarbeitungRepository;
import com.bassims.modules.atour.repository.ProjectNodeInfoRepository;
import com.bassims.modules.atour.service.ProjectInfoAbarbeitungApprovalRejectionService;
import com.bassims.modules.atour.service.ProjectInfoAbarbeitungService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.ProjectApproveDto;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.service.LocalStorageService;
import com.bassims.service.dto.LocalStorageDto;
import com.bassims.service.dto.LocalStorageQueryCriteria;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProjectNodeInfoApprovalRejectionRepository;
import com.bassims.modules.atour.service.ProjectNodeInfoApprovalRejectionService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoApprovalRejectionDto;
import com.bassims.modules.atour.service.dto.ProjectNodeInfoApprovalRejectionQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectNodeInfoApprovalRejectionMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-28
 **/
@Service
public class ProjectNodeInfoApprovalRejectionServiceImpl extends BaseServiceImpl<ProjectNodeInfoApprovalRejectionRepository, ProjectNodeInfoApprovalRejection> implements ProjectNodeInfoApprovalRejectionService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectNodeInfoApprovalRejectionServiceImpl.class);

    @Autowired
    private ProjectNodeInfoApprovalRejectionRepository projectNodeInfoApprovalRejectionRepository;
    @Autowired
    private ProjectNodeInfoApprovalRejectionMapper projectNodeInfoApprovalRejectionMapper;
    @Autowired
    private NoteInfoMappingUtil util;

    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;

    @Autowired
    private LocalStorageService localStorageService;
    @Autowired
    private LocalStorageRepository localStorageRepository;

    @Autowired
    private ProjectInfoAbarbeitungRepository projectInfoAbarbeitungRepository;

    @Autowired
    private ProjectInfoAbarbeitungService projectInfoAbarbeitungService;
    @Autowired
    private ProjectInfoAbarbeitungApprovalRejectionService approvalRejectionService;

    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;


    @Override
    public Map<String, Object> queryAll(ProjectNodeInfoApprovalRejectionQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectNodeInfoApprovalRejection> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectNodeInfoApprovalRejection.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectNodeInfoApprovalRejectionMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectNodeInfoApprovalRejectionDto> queryAll(ProjectNodeInfoApprovalRejectionQueryCriteria criteria) {
        return projectNodeInfoApprovalRejectionMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectNodeInfoApprovalRejection.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoApprovalRejectionDto findById(Long nodeId) {
        ProjectNodeInfoApprovalRejection projectNodeInfoApprovalRejection = Optional.ofNullable(getById(nodeId)).orElseGet(ProjectNodeInfoApprovalRejection::new);
        ValidationUtil.isNull(projectNodeInfoApprovalRejection.getNodeId(), getEntityClass().getSimpleName(), "nodeId", nodeId);
        return projectNodeInfoApprovalRejectionMapper.toDto(projectNodeInfoApprovalRejection);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNodeInfoApprovalRejectionDto create(ProjectNodeInfoApprovalRejection resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setNodeId(snowflake.nextId());
        save(resources);
        return findById(resources.getNodeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectNodeInfoApprovalRejection resources) {
        ProjectNodeInfoApprovalRejection projectNodeInfoApprovalRejection = Optional.ofNullable(getById(resources.getNodeId())).orElseGet(ProjectNodeInfoApprovalRejection::new);
        ValidationUtil.isNull(projectNodeInfoApprovalRejection.getNodeId(), "ProjectNodeInfoApprovalRejection", "id", resources.getNodeId());
        projectNodeInfoApprovalRejection.copy(resources);
        updateById(projectNodeInfoApprovalRejection);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long nodeId : ids) {
            projectNodeInfoApprovalRejectionRepository.deleteById(nodeId);
        }
    }

    @Override
    public void download(List<ProjectNodeInfoApprovalRejectionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectNodeInfoApprovalRejectionDto projectNodeInfoApprovalRejection : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectNodeInfoApprovalRejection.getProjectId());
            map.put("队列id", projectNodeInfoApprovalRejection.getTemplateQueueId());
            map.put("模板主键", projectNodeInfoApprovalRejection.getTemplateId());
            map.put("父节点", projectNodeInfoApprovalRejection.getParentId());
            map.put(" moduleRelationId", projectNodeInfoApprovalRejection.getModuleRelationId());
            map.put("项目版本号", projectNodeInfoApprovalRejection.getProjectVersion());
            map.put("节点编码", projectNodeInfoApprovalRejection.getNodeCode());
            map.put("节点名称", projectNodeInfoApprovalRejection.getNodeName());
            map.put("计划开始时间", projectNodeInfoApprovalRejection.getPlanStartDate());
            map.put("计划结束时间", projectNodeInfoApprovalRejection.getPlanEndDate());
            map.put("预估开始日期", projectNodeInfoApprovalRejection.getPredictStartDate());
            map.put("预估结束日期", projectNodeInfoApprovalRejection.getPredictEndDate());
            map.put("实际完成时间", projectNodeInfoApprovalRejection.getActualEndDate());
            map.put("计划需要完成天数", projectNodeInfoApprovalRejection.getPlanDay());
            map.put("提醒天数", projectNodeInfoApprovalRejection.getNoticeDay());
            map.put("延期天数", projectNodeInfoApprovalRejection.getDelayDay());
            map.put("节点序号", projectNodeInfoApprovalRejection.getNodeWbs());
            map.put("子节点排序", projectNodeInfoApprovalRejection.getNodeIndex());
            map.put("节点等级", projectNodeInfoApprovalRejection.getNodeLevel());
            map.put("节点类型", projectNodeInfoApprovalRejection.getNodeType());
            map.put("节点状态", projectNodeInfoApprovalRejection.getNodeStatus());
            map.put("已完成按钮", projectNodeInfoApprovalRejection.getNodeIsfin());
            map.put("前置任务配置[{type:FS,wbs:11},{type:SS,wbs:12}]", projectNodeInfoApprovalRejection.getFrontWbsConfig());
            map.put("是否是关键节点", projectNodeInfoApprovalRejection.getIsKey());
            map.put("关键节点前置任务", projectNodeInfoApprovalRejection.getKeyFrontWbs());
            map.put("节点备注", projectNodeInfoApprovalRejection.getRemark());
            map.put("关联nodecode", projectNodeInfoApprovalRejection.getRelationCode());
            map.put("关联的类型", projectNodeInfoApprovalRejection.getRelationType());
            map.put("下拉列表角色名称", projectNodeInfoApprovalRejection.getDownCode());
            map.put("干系人角色名称", projectNodeInfoApprovalRejection.getJobCode());
            map.put("使用场景", projectNodeInfoApprovalRejection.getUseCase());
            map.put("节点是否打开", projectNodeInfoApprovalRejection.getIsOpen());
            map.put(" isDelete", projectNodeInfoApprovalRejection.getIsDelete());
            map.put("创建时间", projectNodeInfoApprovalRejection.getCreateTime());
            map.put(" createBy", projectNodeInfoApprovalRejection.getCreateBy());
            map.put("修改时间", projectNodeInfoApprovalRejection.getUpdateTime());
            map.put("修改人", projectNodeInfoApprovalRejection.getUpdateBy());
            map.put("是否可用", projectNodeInfoApprovalRejection.getIsEnabled());
            map.put("dict转码用", projectNodeInfoApprovalRejection.getStartSign());
            map.put("结束标志（甘特图）", projectNodeInfoApprovalRejection.getEndSign());
            map.put("总工期", projectNodeInfoApprovalRejection.getTotalDay());
            map.put("是否是手机端", projectNodeInfoApprovalRejection.getIsMobile());
            map.put("责任人角色code", projectNodeInfoApprovalRejection.getRoleCode());
            map.put("是否可以编辑", projectNodeInfoApprovalRejection.getIsEdit());
            map.put("占位", projectNodeInfoApprovalRejection.getSeat());
            map.put("是否换行", projectNodeInfoApprovalRejection.getIsWrap());
            map.put("是否显示nodeName(1 不显示)", projectNodeInfoApprovalRejection.getNotLabel());
            map.put("小程序标志", projectNodeInfoApprovalRejection.getIcon());
            map.put("公式", projectNodeInfoApprovalRejection.getFormula());
            map.put("影响的code", projectNodeInfoApprovalRejection.getFormulaCode());
            map.put("用户添加的版本", projectNodeInfoApprovalRejection.getAddedVersion());
            map.put("同组最后的code", projectNodeInfoApprovalRejection.getLastCode());
            map.put(" projectNo", projectNodeInfoApprovalRejection.getProjectNo());
            map.put(" brandId", projectNodeInfoApprovalRejection.getBrandId());
            map.put("是否不需要任务信息", projectNodeInfoApprovalRejection.getIsNotTask());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void createAbarbeitung(ProjectApproveDto projectApproveDto) {
        //审批拒绝的话，保存当前二级的审图数据
        ProjectGroup projectGroup = projectGroupRepository.selectById(projectApproveDto.getNodeId());
        if (ObjectUtil.isNotEmpty(projectGroup) && ObjectUtil.isNotEmpty(JhSystemEnum.saveVersionNodeEnum.getSaveVersionNodeEnum(projectGroup.getNodeCode()))) {
            util.initialize(projectNodeInfoService.getSubmeterProjectId(projectApproveDto.getProjectId()));
            //查询当前二级需要保存哪些三级信息
            JhSystemEnum.saveVersionNodeEnum versionNodeEnum = JhSystemEnum.saveVersionNodeEnum.getSaveVersionNodeEnum(projectGroup.getNodeCode());
            LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .and(Wrapper -> Wrapper.in(ProjectNodeInfo::getNodeCode, versionNodeEnum.getNodeCode().split(",")).or().like(ProjectNodeInfo::getNodeCode, "%table%"))
                    .eq(ProjectNodeInfo::getProjectId, projectApproveDto.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, projectGroup.getNodeCode())
                    .orderByAsc(ProjectNodeInfo::getNodeIndex);
            List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(wrapper);
            for (ProjectNodeInfo info : infos) {
                ProjectNodeInfoApprovalRejection nodeInfoApprovalRejection = new ProjectNodeInfoApprovalRejection();
                BeanUtil.copyProperties(info, nodeInfoApprovalRejection, CopyOptions.create().setIgnoreNullValue(true));

                Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                Long approveId = snowflake.nextId();
                nodeInfoApprovalRejection.setNodeId(approveId);
                nodeInfoApprovalRejection.setApproveId(projectApproveDto.getApproveId());
                this.save(nodeInfoApprovalRejection);
                //查询三级的附件，重新保存
                LocalStorageQueryCriteria criteria = new LocalStorageQueryCriteria();
                criteria.setNodeId(info.getNodeId().toString());
                List<LocalStorageDto> storageDtos = localStorageService.queryAll(criteria);
                for (LocalStorageDto storageDto : storageDtos) {
                    LocalStorage localStorage = new LocalStorage();
                    BeanUtil.copyProperties(storageDto, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                    localStorage.setId(null);
                    localStorage.setNodeId(nodeInfoApprovalRejection.getNodeId().toString());
                    localStorageRepository.insert(localStorage);

                    //查询附件相关联的审图整改信息，并保存版本
                    LambdaUpdateWrapper<ProjectInfoAbarbeitung> infoAbarbeitungLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectInfoAbarbeitung.class)
                            .in(ProjectInfoAbarbeitung::getFileNodeId, storageDto.getId())
                            .eq(ProjectInfoAbarbeitung::getProjectId, projectApproveDto.getProjectId());
                    List<ProjectInfoAbarbeitung> abarbeitungList = projectInfoAbarbeitungRepository.selectList(infoAbarbeitungLambdaUpdateWrapper);
                    for (ProjectInfoAbarbeitung infoAbarbeitung : abarbeitungList) {
                        ProjectInfoAbarbeitungApprovalRejection abarbeitungApprovalRejection = new ProjectInfoAbarbeitungApprovalRejection();
                        BeanUtil.copyProperties(infoAbarbeitung, abarbeitungApprovalRejection, CopyOptions.create().setIgnoreNullValue(true));
                        abarbeitungApprovalRejection.setAbarbeitungId(null);
                        abarbeitungApprovalRejection.setFileNodeId(localStorage.getId());
                        approvalRejectionService.save(abarbeitungApprovalRejection);
                    }
                }
            }
        }

    }

}