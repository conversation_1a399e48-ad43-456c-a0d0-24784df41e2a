/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.ProjectGroupExpand;
import com.bassims.modules.atour.service.dto.ProjectGroupExpandDto;
import com.bassims.modules.atour.service.dto.ProjectGroupExpandQueryCriteria;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-12-27
 **/
@Repository
public interface ProjectGroupExpandRepository extends BaseMapper<ProjectGroupExpand> {

   List<ProjectGroupExpandDto> queryAllDeepeningPlanList(ProjectGroupExpandQueryCriteria criteria);

   List<ProjectGroupExpandDto> queryAllDesignTextList(ProjectGroupExpandQueryCriteria criteria);

  List<String> getDrawingStatus(String projectId,String nodeCode,String relevanceNodeCode);

}