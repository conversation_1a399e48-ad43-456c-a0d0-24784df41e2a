package com.bassims.modules.atour.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProjectAreaServiceDto {

    @ExcelProperty({"基础信息", "分公司"})
    private String cityCompany;
    @ExcelProperty({"基础信息", "城市"})
    private String cityName;
    @ExcelProperty({"基础信息", "项目名称"})
    private String projectName;
    @ExcelProperty({"基础信息", "门店名称"})
    private String storeName;
    @ExcelProperty({"基础信息", "门店编码"})
    private String storeNo;
    @ExcelProperty({"基础信息", "门店类型"})
    private String storeType;
    @ExcelProperty({"基础信息", "实际开业时间"})
    private String actualOpenDate;
    @ExcelProperty({"门店服务面积", "服务面积"})
    private BigDecimal serviceArea;
    @ExcelProperty({"门店服务面积", "游乐(童乐园)"})
    private BigDecimal amusement;
    @ExcelProperty({"门店服务面积", "成长试验站"})
    private BigDecimal growthTestStation;
    @ExcelProperty({"门店服务面积", "其他游乐项目"})
    private BigDecimal otherAmusement;
    @ExcelProperty({"门店服务面积", "互动体验"})
    private BigDecimal interactiveExperience;
    @ExcelProperty({"门店服务面积", "孕产体验室(含洽谈室)"})
    private BigDecimal maternityExperienceEoom;
    @ExcelProperty({"门店服务面积", "甄选店-产康"})
    private BigDecimal selectionShop;
    @ExcelProperty({"门店服务面积", "同城"})
    private BigDecimal local;
    @ExcelProperty({"门店服务面积", "服务站"})
    private BigDecimal serviceStation;
    @ExcelProperty({"门店服务面积", "妈妈课堂"})
    private BigDecimal momClass;
    @ExcelProperty({"门店服务面积", "黑金专区"})
    private BigDecimal blackGoldZone;
    @ExcelProperty({"门店服务面积", "备用一"})
    private BigDecimal serviceAreaReserveOne;
    @ExcelProperty({"门店服务面积", "备用二"})
    private BigDecimal serviceAreaReserveTwo;
    @ExcelProperty({"门店服务面积", "备用三"})
    private BigDecimal serviceAreaReserveThree;
}
