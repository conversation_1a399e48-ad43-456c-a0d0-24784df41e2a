/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-03-24
 **/
@Data
public class ProjectInfoInsertStaDto implements Serializable {



    private String projectPlanStart;

    /**
     * 设计公管人员
     */
    private Long designCoManagementPersonnel;

    /** 项目id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;


    /**
     * 项目经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectManager;

    /**
     * 设计师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long designer;

    /**
     * 机电工程师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mechanicalAndElectricalEngineer;

    /**
     * 弱电工程师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long weakCurrentEngineer;


    /**
     * 飞行质检人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long flightQualityInspectionPersonnel;






    /**
     * 软装设计
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long softDecorationDesign;


    /**项目备注*/
    private String remark;

}