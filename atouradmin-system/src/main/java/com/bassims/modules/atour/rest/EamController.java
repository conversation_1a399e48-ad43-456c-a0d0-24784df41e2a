package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.requestParam.IeamUserRequest;
import com.bassims.modules.atour.requestParam.IeamWarehouseRequest;
import com.bassims.modules.atour.service.IeamHttpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 像控制器
 *
 * <AUTHOR>
 * @date 2022/11/10
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "eam接口入口")
@RequestMapping("/api/eamInterface")
public class EamController {

    private static final Logger logger = LoggerFactory.getLogger(EamController.class);

    private final IeamHttpService ieamHttpService;


    /**
     * @real_return {@link ResponseEntity }
     */
    @PostMapping("/queryEamInfo")
    @Log("查询eam用户")
    @ApiOperation("查询eam用户")
    public ResponseEntity<Object> query(@RequestBody IeamUserRequest request) {
        return new ResponseEntity<>(ieamHttpService.queryIeamUserInfo(request), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity }
     */
    @PostMapping("/queryWareHouse")
    @Log("查询eam仓库")
    @ApiOperation("查询eam仓库")
    public ResponseEntity<Object> queryEamWareHouse(@RequestBody IeamWarehouseRequest request) {
        return new ResponseEntity<>(ieamHttpService.queryIeamWareHouse(request.getHrDepartmentCode()), HttpStatus.OK);
    }

}