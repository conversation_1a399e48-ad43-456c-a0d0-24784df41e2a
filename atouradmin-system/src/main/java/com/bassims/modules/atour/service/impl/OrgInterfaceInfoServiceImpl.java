/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.modules.atour.domain.OrgInterfaceInfo;
import com.bassims.utils.*;
import com.bassims.modules.atour.repository.OrgInterfaceInfoRepository;
import com.bassims.modules.atour.service.OrgInterfaceInfoService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.OrgInterfaceInfoDto;
import com.bassims.modules.atour.service.dto.OrgInterfaceInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.OrgInterfaceInfoMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-24
**/
@Service
public class OrgInterfaceInfoServiceImpl extends BaseServiceImpl<OrgInterfaceInfoRepository,OrgInterfaceInfo> implements OrgInterfaceInfoService {

    private static final Logger logger = LoggerFactory.getLogger(OrgInterfaceInfoServiceImpl.class);

    @Value("${finance-info.path}")
    private String financeInfoPath;


    @Autowired
    private OrgInterfaceInfoRepository orgInterfaceInfoRepository;
    @Autowired
    private OrgInterfaceInfoMapper orgInterfaceInfoMapper;

    @Override
    public Map<String,Object> queryAll(OrgInterfaceInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<OrgInterfaceInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(OrgInterfaceInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", orgInterfaceInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<OrgInterfaceInfoDto> queryAll(OrgInterfaceInfoQueryCriteria criteria){
        return orgInterfaceInfoMapper.toDto(list(QueryHelpPlus.getPredicate(OrgInterfaceInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrgInterfaceInfoDto findById(Long id) {
        OrgInterfaceInfo orgInterfaceInfo = Optional.ofNullable(getById(id)).orElseGet(OrgInterfaceInfo::new);
        ValidationUtil.isNull(orgInterfaceInfo.getId(),getEntityClass().getSimpleName(),"id",id);
        return orgInterfaceInfoMapper.toDto(orgInterfaceInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrgInterfaceInfoDto create(OrgInterfaceInfo resources) {
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OrgInterfaceInfo resources) {
        OrgInterfaceInfo orgInterfaceInfo = Optional.ofNullable(getById(resources.getId())).orElseGet(OrgInterfaceInfo::new);
        ValidationUtil.isNull( orgInterfaceInfo.getId(),"OrgInterfaceInfo","id",resources.getId());
        orgInterfaceInfo.copy(resources);
        updateById(orgInterfaceInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            orgInterfaceInfoRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<OrgInterfaceInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OrgInterfaceInfoDto orgInterfaceInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("部门类型", orgInterfaceInfo.getOrgtype());
            map.put("父级部门名称", orgInterfaceInfo.getParentname());
            map.put("部门名称", orgInterfaceInfo.getOrgname());
            map.put("父级部门code", orgInterfaceInfo.getParentcode());
            map.put("部门code", orgInterfaceInfo.getOrgcode());
            map.put("部门类型", orgInterfaceInfo.getOrgproperty());
            map.put(" createTime",  orgInterfaceInfo.getCreateTime());
            map.put(" createBy",  orgInterfaceInfo.getCreateBy());
            map.put(" updateTime",  orgInterfaceInfo.getUpdateTime());
            map.put(" updateBy",  orgInterfaceInfo.getUpdateBy());
            map.put("是否可用", orgInterfaceInfo.getIsEnabled());
            map.put("是否删除", orgInterfaceInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean getOrgInfo() {
        Boolean flag=Boolean.FALSE;
        JSONObject req = JSONUtil.createObj();
        req.set("data","{type : 'orgbudget'}");
        String get = HttpUtil.get(financeInfoPath, req);
        JSONObject entries = JSONUtil.parseObj(get);
        Object data = entries.get("data");
        if(data!=null){
            JSONArray objects = JSONUtil.parseArray(data);
            List<OrgInterfaceInfo> orgInterfaceInfos = JSONUtil.toList(objects, OrgInterfaceInfo.class);
            //数据存库
            if(orgInterfaceInfos.size()>0){
                //先删除原始数据，全部更新
                this.remove(new QueryWrapper<>());
                this.saveBatch(orgInterfaceInfos);
                flag=Boolean.TRUE;
            }
        }

        return flag;
    }

    @Override
    public List<OrgInterfaceInfoDto> getOrgListByName(String orgName) {
        if(StringUtils.isEmpty(orgName)){

        }
        LambdaQueryWrapper<OrgInterfaceInfo> orgInterfaceInfoLambdaQueryWrapper= Wrappers.lambdaQuery(OrgInterfaceInfo.class);
        orgInterfaceInfoLambdaQueryWrapper.like(OrgInterfaceInfo::getOrgname,orgName);
        List<OrgInterfaceInfoDto> list = orgInterfaceInfoMapper.toDto(list(orgInterfaceInfoLambdaQueryWrapper));
        List<OrgInterfaceInfoDto> orgInterfaceInfoDtos =new ArrayList<>();
        for(OrgInterfaceInfoDto dto:list){
            OrgInterfaceInfoDto orgInterfaceInfoDto=new OrgInterfaceInfoDto();
            // BeanUtils.copyProperties(dto,orgInterfaceInfoDto);
            //处理
            orgInterfaceInfoDto.setOrgname(dto.getOrgname());
            orgInterfaceInfoDto.setOrgcode(dto.getOrgcode());
            orgInterfaceInfoDtos.add(orgInterfaceInfoDto);
        }
        return orgInterfaceInfoDtos;
    }

}