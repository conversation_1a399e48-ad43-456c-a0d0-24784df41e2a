/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-11-06
**/
@Data
@TableName(value="t_hlm_project_info_update_brand")
public class HlmProjectInfoUpdateBrand implements Serializable {

    @TableId(value = "update_brand_id")
    @ApiModelProperty(value = "更新项目品牌变更ID")
    private Long updateBrandId;

    @TableField(value = "hotel_id")
    @ApiModelProperty(value = "酒店ID")
    private Integer hotelId;

    @TableField(value = "change_no")
    @ApiModelProperty(value = "变更记录ID")
    private String changeNo;

    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌")
    private Integer brand;

    @TableField(value = "brand_name")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "product")
    @ApiModelProperty(value = "产品")
    private String product;

    @TableField(value = "product_name")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @TableField(value = "change_reason")
    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @TableField(value = "chain_name")
    @ApiModelProperty(value = "变更后酒店名称")
    private String chainName;

    @TableField(value = "design_plan")
    @ApiModelProperty(value = "营建改造方案")
    private String designPlan;


    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    public void copy(HlmProjectInfoUpdateBrand source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}