/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.OrderDetailOldVersion;
import com.bassims.modules.atour.service.OrderDetailOldVersionService;
import com.bassims.modules.atour.service.dto.OrderDetailOldVersionDto;
import com.bassims.modules.atour.service.dto.OrderDetailOldVersionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-04-01
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_order_detail_old_version管理")
@RequestMapping("/api/orderDetailOldVersion")
public class OrderDetailOldVersionController {

    private static final Logger logger = LoggerFactory.getLogger(OrderDetailOldVersionController.class);

    private final OrderDetailOldVersionService orderDetailOldVersionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, OrderDetailOldVersionQueryCriteria criteria) throws IOException {
        orderDetailOldVersionService.download(orderDetailOldVersionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<OrderDetailOldVersionDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_order_detail_old_version")
    @ApiOperation("查询t_order_detail_old_version")
    public ResponseEntity<Object> query(OrderDetailOldVersionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(orderDetailOldVersionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<OrderDetailOldVersionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_order_detail_old_version")
    @ApiOperation("查询t_order_detail_old_version")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(orderDetailOldVersionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_order_detail_old_version")
    @ApiOperation("新增t_order_detail_old_version")
    public ResponseEntity<Object> create(@Validated @RequestBody OrderDetailOldVersion resources){
        return new ResponseEntity<>(orderDetailOldVersionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_order_detail_old_version")
    @ApiOperation("修改t_order_detail_old_version")
    public ResponseEntity<Object> update(@Validated @RequestBody OrderDetailOldVersion resources){
        orderDetailOldVersionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_order_detail_old_version")
    @ApiOperation("删除t_order_detail_old_version")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        orderDetailOldVersionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}