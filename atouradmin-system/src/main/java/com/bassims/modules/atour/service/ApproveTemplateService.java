/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.AppTemplateUpdate;
import com.bassims.modules.atour.domain.ApproveTemplate;
import com.bassims.modules.atour.service.dto.ApproveTemplateDto;
import com.bassims.modules.atour.service.dto.ApproveTemplateQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-03-30
**/
public interface ApproveTemplateService extends BaseService<ApproveTemplate> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ApproveTemplateQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ApproveTemplateDto>
    */
    List<ApproveTemplateDto> queryAll(ApproveTemplateQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param approveTemplateId ID
     * @return ApproveTemplateDto
     */
    ApproveTemplateDto findById(Long approveTemplateId);

    /**
    * 创建
    * @param resources /
    * @return ApproveTemplateDto
    */
    ApproveTemplateDto create(ApproveTemplate resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ApproveTemplate resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ApproveTemplateDto> all, HttpServletResponse response) throws IOException;

    /**
     * 获取所有的审批模板，与当前节点是否有选择的审批模板
     * @param templateId
     * @return
     */
    List<ApproveTemplateDto> getApproveTemplate(Long templateId,Long templateGroupId);

    /**
     * 获取所有的审批模板
     * @return
     */
    List<ApproveTemplate> getApproveTemplate();

    /**
     * 根据审批模板id获取审批模板内部信息
     * @param templateId
     * @return
     */
    AppTemplateUpdate getAppTemplateById(Long templateId);

    /**
     * 更新
     * @return
     */
    Boolean updateAppTemplate(AppTemplateUpdate appTemplateUpdate);

    /**
     *拆分审批配置部分，获取上半部分
     * @param templateId
     * @return
     */
    ApproveTemplateDto getApproveTemplateTitle(Long templateId);



}