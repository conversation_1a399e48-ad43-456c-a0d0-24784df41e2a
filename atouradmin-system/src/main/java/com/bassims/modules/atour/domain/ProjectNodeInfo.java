/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bassims.modules.atour.service.dto.ProjectCompletionReceiptListDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-03-24
 **/
@Data
@TableName(value = "t_project_node_info")
@Accessors(chain = true)
public class ProjectNodeInfo implements Serializable {

    @TableId(value = "node_id")
    @ApiModelProperty(value = "节点id") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long nodeId;

    @TableField(value = "template_id")
    @ApiModelProperty(value = "模板主键") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    @TableField(value = "template_queue_id")
    @ApiModelProperty(value = "队列id") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateQueueId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentId;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号")
    private String projectVersion;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    @TableField(value = "node_name")
    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @TableField(value = "plan_start_date")
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartDate;

    @TableField(value = "plan_end_date")
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndDate;

    @TableField(value = "predict_start_date")
    @ApiModelProperty(value = "预估开始日期")
    private Date predictStartDate;

    @TableField(value = "predict_end_date")
    @ApiModelProperty(value = "预估结束日期")
    private Date predictEndDate;

    @TableField(value = "plan_day")
    @ApiModelProperty(value = "计划需要完成天数")
    private Integer planDay;

    @TableField(value = "actual_end_date")
    @ApiModelProperty(value = "实际完成时间")
    private Date actualEndDate;

    @TableField(value = "notice_day")
    @ApiModelProperty(value = "提醒天数")
    private Integer noticeDay;

    @TableField(value = "delay_day")
    @ApiModelProperty(value = "延期天数")
    private Integer delayDay;

    @TableField(value = "node_wbs")
    @ApiModelProperty(value = "节点序号")
    private Integer nodeWbs;

    @TableField(value = "front_wbs_config")
    @ApiModelProperty(value = "前置任务配置")
    private String frontWbsConfig;

    @TableField(value = "is_key")
    @ApiModelProperty(value = "是否是关键节点")
    private Boolean isKey;

    @TableField(value = "key_front_wbs")
    @ApiModelProperty(value = "关键节点前置任务")
    private String keyFrontWbs;


    @TableField(value = "node_index")
    @ApiModelProperty(value = "子节点排序")
    private double nodeIndex;

    @TableField(value = "node_level")
    @ApiModelProperty(value = "节点等级")
    private Integer nodeLevel;

    @TableField(value = "node_type")
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @TableField(value = "node_status")
    @ApiModelProperty(value = "节点状态")
    private String nodeStatus;

    @TableField(value = "node_isfin")
    @ApiModelProperty(value = "已完成按钮")
    private Boolean nodeIsfin;

    @TableField(value = "remark")
    @ApiModelProperty(value = "节点备注")
    private String remark;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "isDelete")
    private Boolean isDelete;

    @TableField(value = "icon")
    @ApiModelProperty(value = "小程序标志")
    private String icon;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;


    @TableField(value = "total_day")
    @ApiModelProperty(value = "总工期")
    private Integer totalDay;

    @TableField(value = "start_sign")
    @ApiModelProperty(value = "dict转码用")
    private String startSign;

    @TableField(value = "end_sign")
    @ApiModelProperty(value = "结束标志（甘特图）")
    private Integer endSign;

    @TableField(value = "job_code")
    @ApiModelProperty(value = "角色code")
    private String jobCode;

    @TableField(value = "down_code")
    @ApiModelProperty(value = "下拉列表角色名称")
    private String downCode;

    @TableField(value = "is_mobile")
    @ApiModelProperty(value = "是否手机端")
    private Boolean isMobile;
    @TableField(value = "relation_code")
    @ApiModelProperty(value = "关联nodecode")
    private String relationCode;
    @TableField(value = "relation_type")
    @ApiModelProperty(value = "关联的类型")
    private String relationType;
    @TableField(value = "use_case")
    @ApiModelProperty(value = "使用场景")
    private String useCase;

    @TableField(value = "role_code")
    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @TableField(value = "is_edit")
    @ApiModelProperty(value = "是否可以编辑")
    private String isEdit;

    @TableField(value = "seat")
    @ApiModelProperty(value = "占位")
    private String seat;

    @TableField(value = "is_wrap")
    @ApiModelProperty(value = "是否换行")
    private Boolean isWrap;

    @TableField(value = "formula")
    @ApiModelProperty(value = "公式")
    private String formula;

    @TableField(value = "formula_code")
    @ApiModelProperty(value = "影响的code")
    private String formulaCode;

    @TableField(value = "added_version")
    @ApiModelProperty(value = "用户添加的版本")
    private Integer addedVersion;

    @TableField(value = "last_code")
    @ApiModelProperty(value = "同组最后的code")
    private String lastCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "用以标识用户添加的信息")
    private String pieceIndex;

    @TableField(exist = false)
    @ApiModelProperty(value = "用于传用户增加的节点块的字符串nodeId")
    private String fileId;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "合同管理基本信息-大仓")
//    private List<ContractManage> contractManages;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "费用信息-大仓")
//    private List<FeeInfo> feeInfos;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "费用信息-大仓")
//    private List<FeeInfo> otherFeeInfos;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "审定单-大仓")
//    private List<ApprovedForm> approvedForms;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "竣工验收照片信息-大仓")
//    private List<CompletionAcceptancePicture> completionAcceptancePictures;
//
//    @TableField(exist = false)
//    @ApiModelProperty(value = "闭店资产处置物料表格")
//    private List<OrderDetail> orderDetails;


    @TableField(exist = false)
    @ApiModelProperty(value = "预付明细表格")
    private Repay repay;

    @TableField(value = "is_not_task")
    @ApiModelProperty(value = "是否不需要任务信息")
    private Boolean isNotTask;

    @TableField(value = "is_show")
    @ApiModelProperty(value = "是否展示（0展示、1不展示）")
    private Integer isShow;

    @TableField(value = "file_check_format")
    @ApiModelProperty(value = "校验文件格式")
    private String fileCheckFormat;


    /**
     * 筹建启动会-擅自施工
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "筹建启动会-擅自施工")
    private List<UnauthorizedConstruction> unauthorizedConstructions;

    /**
     * 质量管理
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "质量管理")
    private List<QualityControl> qualityControls;

    /**
     * 项目进度
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "项目进度")
    private List<ProjectGroup> nodeInfoDtos;

    /**
     * 施工照片
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "施工照片")
    private List<ConstructionPhotograph> constructionPhotographs;

    /**
     * 设计交底问题汇总
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "设计交底问题汇总")
    private List<ProjectDisclosureProblem> projectDisclosureProblems;
    /**
     * 现场特殊情况暂无法解决事项
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "现场特殊情况暂无法解决事项")
    private List<ProjectInsolubleMatter> projectInsolubleMatter;
    /**
     * 擅自施工与图纸不符部分
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "擅自施工与图纸不符部分")
    private List<ProjectUnauthorizedConstruction> projectUnauthorizedConstructions;

    /**
     *  项目特殊情况说明表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "项目特殊情况说明表")
    private List<ProjectSpecialCaseDescription> projectSpecialCaseDescription;

    /**
     *  项目特殊情况说明表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "项目的特许商特殊需求表")
    private List<ProjectSpecialNeedsFranchisors> projectSpecialNeedsFranchisors;

    /**
     *  样板间确定
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "样板间确定")
    private List<ProjectRoom> projectRoomSure;

    /**
     *  物资管理材料表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "物资管理材料表")
    private List<ProjectMaterialManagement> projectMaterialManagement;

    /**
     *  证照管理集合
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "证照管理集合")
    private List<ProjectCateInfo> projectCateInfoList;

    /**
     *  弱电设施设备信息表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "弱电设施设备信息表")
    private List<ProjectWeakCurrent> projectWeakCurrent;

//    审图额外走【审图处理】的按钮，不走【保存、提交】
//    /**
//     *  项目审图配置表（关于深化、设计审图列表的数据存储）
//     */
//    @TableField(exist = false)
//    @ApiModelProperty(value = "项目审图配置表（关于深化、设计审图列表的数据存储）")
//    private List<ProjectGroupExpand> projectGroupExpands;

    @TableField(exist = false)
    @ApiModelProperty(value = "竣工常规项目自检表")
    private List<QualityControl> routineSelfInspection;

    @TableField(exist = false)
    @ApiModelProperty(value = "竣工系统自检表")
    private List<ProjectSystemSelfInspection> projectSystemSelfInspection;

    @TableField(exist = false)
    @ApiModelProperty(value = "竣工验收验收单保存数据")
    ProjectCompletionReceiptListDto projectCompletionReceiptListDto;

    @TableField(exist = false)
    @ApiModelProperty(value = "问题类型")
    private String problemType;

    @TableField(exist = false)
    @ApiModelProperty(value = "任务节点")
    private String taskNodes;

    @TableField(exist = false)
    @ApiModelProperty(value = "创建时间")
    private String creationTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "整改状态")
    private String rectificationStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "供应商员工")
    private List<SupplierPm> supplierPmList;

    @TableField(exist = false)
    @ApiModelProperty(value = "案例")
    private List<SupplierCase> supplierCaseList;


    @TableField(value = "round_marking")
    @ApiModelProperty(value = "轮次标识")
    private String roundMarking;


    public void copy(ProjectNodeInfo source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
