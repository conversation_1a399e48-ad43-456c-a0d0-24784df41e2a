/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.StoreMasterInfoVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.OrderInfoService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.StoreMasterInfoService;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.MasterStoreOrderDataMapper;
import com.bassims.modules.atour.service.mapstruct.StoreMasterInfoMapper;
import com.bassims.modules.system.domain.Area;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.service.AreaService;
import com.bassims.modules.system.service.UserService;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-11-10
 **/
@Service
public class StoreMasterInfoServiceImpl extends BaseServiceImpl<StoreMasterInfoRepository, StoreMasterInfo> implements StoreMasterInfoService {

    private static final Logger logger = LoggerFactory.getLogger(StoreMasterInfoServiceImpl.class);

    @Autowired
    private StoreMasterInfoRepository storeMasterInfoRepository;
    @Autowired
    private StoreMasterInfoMapper storeMasterInfoMapper;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private MasterPersonInfoRepository masterPersonInfoRepository;
    @Autowired
    private MasterContractInfoRepository masterContractInfoRepository;
    @Autowired
    private MasterDeviceInfoRepository masterDeviceInfoRepository;
    @Autowired
    private MasterEnergyConsumeRepository masterEnergyConsumeRepository;
    @Autowired
    private MasterLicenseInfoRepository masterLicenseInfoRepository;
    @Autowired
    private MasterMaintenanceInfoRepository masterMaintenanceInfoRepository;
    @Autowired
    private MasterOrderDetailRepository masterOrderDetailRepository;
    @Autowired
    private MasterPayInfoRepository masterPayInfoRepository;
    @Autowired
    private MasterSpecialItemRepository masterSpecialItemRepository;
    @Autowired
    private MasterStoreOrderDataRepository masterStoreOrderDataRepository;
    @Autowired
    private MasterStoreOrderDataMapper masterStoreOrderDataMapper;
    @Autowired
    private MasterAreaInfoRepository masterAreaInfoRepository;
    @Autowired
    private MasterDrawingInfoRepository masterDrawingInfoRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private AreaService areaService;


    @Override
    public Map<String, Object> queryAll(StoreMasterInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = projectInfoService.getRolePermission(currentUserId);
        List<Long> storeMasterIds = new LinkedList<>();
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            storeMasterIds = storeMasterInfoRepository.getstoreMasterIdsByCity(currentUserId, Boolean.FALSE);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            storeMasterIds = storeMasterInfoRepository.getstoreMasterIdsBySta(currentUserId, Boolean.FALSE);
        }

        if (ObjectUtil.isNotEmpty(criteria.getPropertyName()) && "其他".equals(criteria.getPropertyName())){
            List<String> noPropertys = Arrays.asList("万达", "吾悦", "永旺", "华润", "龙湖");
            criteria.setPropertyName(null);
            //查询除这些外的 其他物业数据
            criteria.setPropertyNames(noPropertys);
        }
        getPage(pageable);
        PageInfo<StoreMasterInfo> page = new PageInfo<>();
        List<StoreMasterInfoDto> list = new LinkedList<>();
        if (storeMasterIds.size() > 0){
            criteria.setIsDelete(false);
            criteria.setStoreMasterIds(storeMasterIds);

            page = new PageInfo<>(list(QueryHelpPlus.getPredicate(StoreMasterInfo.class, criteria)));
            list = storeMasterInfoMapper.toDto(page.getList());
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", list);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<StoreMasterInfoDto> queryAll(StoreMasterInfoQueryCriteria criteria) {
        return storeMasterInfoMapper.toDto(list(QueryHelpPlus.getPredicate(StoreMasterInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreMasterInfoDto findById(Long storeMasterId) {
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(getById(storeMasterId)).orElseGet(StoreMasterInfo::new);
        ValidationUtil.isNull(storeMasterInfo.getStoreMasterId(), getEntityClass().getSimpleName(), "storeMasterId", storeMasterId);
        return storeMasterInfoMapper.toDto(storeMasterInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreMasterInfoDto create(StoreMasterInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setStoreMasterId(snowflake.nextId());
        save(resources);
        return findById(resources.getStoreMasterId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StoreMasterInfo resources) {
        StoreMasterInfo storeMasterInfo = Optional.ofNullable(getById(resources.getStoreMasterId())).orElseGet(StoreMasterInfo::new);
        ValidationUtil.isNull(storeMasterInfo.getStoreMasterId(), "StoreMasterInfo", "id", resources.getStoreMasterId());
        storeMasterInfo.copy(resources);
        updateById(storeMasterInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long storeMasterId : ids) {
            storeMasterInfoRepository.deleteById(storeMasterId);
        }
    }

    @Override
    public void download(List<StoreMasterInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StoreMasterInfoDto storeMasterInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("经营性质", storeMasterInfo.getBusinessNature());
            map.put("门店名称", storeMasterInfo.getStoreName());
            map.put("门店编码", storeMasterInfo.getStoreNo());
            map.put("门店类型", storeMasterInfo.getStoreType());
            map.put("大区value", storeMasterInfo.getRegion());
            map.put("省份id", storeMasterInfo.getProvince());
            map.put("省份名称", storeMasterInfo.getProvinceName());
            map.put("城市id", storeMasterInfo.getCity());
            map.put("城市名称", storeMasterInfo.getCityName());
            map.put("区县id", storeMasterInfo.getCounty());
            map.put("区县名称", storeMasterInfo.getCountyName());
            map.put("详细地址（不包括省市县）", storeMasterInfo.getProjectAddress());
            map.put("城市公司value", storeMasterInfo.getCityCompany());
            map.put("实际开业日期", storeMasterInfo.getActualOpenDate());
            map.put("装修面积", storeMasterInfo.getDecorateArea());
            map.put("是否生效", storeMasterInfo.getIsActive());
            map.put("创建时间", storeMasterInfo.getCreateTime());
            map.put("创建人", storeMasterInfo.getCreateBy());
            map.put("更新时间", storeMasterInfo.getUpdateTime());
            map.put("更新人", storeMasterInfo.getUpdateBy());
            map.put("是否可用", storeMasterInfo.getIsEnabled());
            map.put("是否删除", storeMasterInfo.getIsDelete());
            map.put("装修等级", storeMasterInfo.getDecorateGrade());
            map.put("门店版本", storeMasterInfo.getStoreVersion());
            map.put("空调末端电费承担", storeMasterInfo.getAirChargeBear());
            map.put("设计定位", storeMasterInfo.getDesignPosition());
            map.put("楼层", storeMasterInfo.getFloor());
            map.put("空调类型", storeMasterInfo.getAirType());
            map.put("门店全称", storeMasterInfo.getStoreFullName());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<StoreMasterInfoDto> getStoreMaster(StoreMasterInfoQueryCriteria criteria) {
        criteria.setStoreStatusClose("store_closed");
        criteria.setIsDelete(false);
        List<StoreMasterInfoDto> list = storeMasterInfoMapper.toDto(list(QueryHelpPlus.getPredicate(StoreMasterInfo.class, criteria)));
        for (StoreMasterInfoDto dto : list) {
            dto.setStoreId(dto.getStoreMasterId());
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreMasterInfoDto getStoreMasterByOrder(StoreMasterInfoQueryCriteria criteria) {
        StoreMasterInfoDto masterInfoDto = findById(criteria.getStoreMasterId());
        Long orderId = criteria.getOrderId();
        OrderInfoDto orderInfoDto = orderInfoService.findById(orderId);
        masterInfoDto.setOrderNo(orderInfoDto.getOrderNo());
        masterInfoDto.setSupName(orderInfoDto.getSupNameCn());
        return masterInfoDto;
    }

    @Override
    public List<StoreMasterInfoVo> getStoreMasterDetail(Long storeId) {
        List<StoreMasterInfoVo> storeMasterInfos = new ArrayList<>();
        LambdaQueryWrapper<ProjectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectInfo::getStoreId, storeId);
        queryWrapper.orderByDesc(ProjectInfo::getActualOpenDate);
        List<ProjectInfo> projectInfos = projectInfoRepository.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(projectInfos)) {
            projectInfos.stream().forEach(projectInfo -> {
                StoreMasterInfoVo storeMasterInfoVo = new StoreMasterInfoVo();
                storeMasterInfoVo.setProjectId(String.valueOf(projectInfo.getProjectId()));
                storeMasterInfoVo.setStoreType(projectInfo.getStoreType());
                storeMasterInfoVo.setBusinessNature(projectInfo.getBusinessNature());
                storeMasterInfoVo.setActualOpenDate(projectInfo.getCreateTime().toString().substring(0,10));
                storeMasterInfoVo.setProjectName(projectInfo.getProjectName());
                storeMasterInfoVo.setFinalSettlementExpenses(projectInfo.getFinalSettlementExpenses());
                storeMasterInfos.add(storeMasterInfoVo);
            });
        }
        return storeMasterInfos;
    }

    @Override
    public StoreMasterInfo getStoreMasterBasic(Long storeId) {
        StoreMasterInfo storeMasterInfo = storeMasterInfoRepository.selectById(storeId);
        return storeMasterInfo;
    }

    @Override
    public Map<String, Object> getStoreMasterDetailInfo(Long storeId, String storeMainFileType) {
        Map<String, Object> mapReturn = Maps.newHashMap();
        //基本信息
        if (KidsSystemEnum.StoreMainFileType.ESSENTIAL_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<StoreMasterInfo> payInfoQueryWrapper = new LambdaQueryWrapper<>();
            payInfoQueryWrapper.eq(StoreMasterInfo::getStoreMasterId, storeId);
            List<StoreMasterInfo> storeMasterInfos = storeMasterInfoRepository.selectList(payInfoQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.ESSENTIAL_INFO.getValue(), storeMasterInfos);
        }
        //人员信息
        if (KidsSystemEnum.StoreMainFileType.PERSONNEL_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterPersonInfo> personInfoQueryWrapper = new LambdaQueryWrapper<>();
            personInfoQueryWrapper.eq(MasterPersonInfo::getStoreId, storeId);
            List<MasterPersonInfoDto> masterPersonInfoDtos = Lists.newArrayList();
            List<MasterPersonInfo> masterPersonInfos = masterPersonInfoRepository.selectList(personInfoQueryWrapper);
            List<Long> userIds = masterPersonInfos.stream().map(MasterPersonInfo::getUserId).distinct().collect(Collectors.toList());
            List<User> users = userService.findByIds(userIds);
            Map<Long, User> userMap = users.stream().collect(Collectors.toMap(User::getId, e -> e));
            masterPersonInfos.forEach(masterPersonInfo -> {
                MasterPersonInfoDto masterPersonInfoDto = new MasterPersonInfoDto();
                BeanUtils.copyProperties(masterPersonInfo, masterPersonInfoDto);
                if (null != masterPersonInfo.getUserId()) {
                    User user = userMap.get(masterPersonInfo.getUserId());
                    if (null != user) {
                        String username = user.getUsername() + "-" + user.getNickName();
                        masterPersonInfoDto.setUserName(username);
                        masterPersonInfoDto.setPhone(user.getPhone());
                    }
                }
                masterPersonInfoDtos.add(masterPersonInfoDto);
            });
            mapReturn.put(KidsSystemEnum.StoreMainFileType.PERSONNEL_INFO.getValue(), masterPersonInfoDtos);
        }
        //付款信息
        else if (KidsSystemEnum.StoreMainFileType.PAYMENT_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterPayInfo> payInfoQueryWrapper = new LambdaQueryWrapper<>();
            payInfoQueryWrapper.eq(MasterPayInfo::getStoreId, storeId);
            List<MasterPayInfo> masterPayInfos = masterPayInfoRepository.selectList(payInfoQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.PAYMENT_INFO.getValue(), masterPayInfos);

        }//合同信息
        else if (KidsSystemEnum.StoreMainFileType.CONTRACT_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterContractInfo> masterContractQueryWrapper = new LambdaQueryWrapper<>();
            masterContractQueryWrapper.eq(MasterContractInfo::getStoreId, storeId);
            List<MasterContractInfo> masterContractInfos = masterContractInfoRepository.selectList(masterContractQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.CONTRACT_INFO.getValue(), masterContractInfos);

        }//证照信息
        else if (KidsSystemEnum.StoreMainFileType.LICENSE_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterLicenseInfo> masterLicenseQueryWrapper = new LambdaQueryWrapper<>();
            masterLicenseQueryWrapper.eq(MasterLicenseInfo::getStoreId, storeId)
                                    .eq(MasterLicenseInfo::getIsDelete,false);
            List<MasterLicenseInfo> masterLicenseInfos = masterLicenseInfoRepository.selectList(masterLicenseQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.LICENSE_INFO.getValue(), masterLicenseInfos);
        }//门店订单数据
        else if (KidsSystemEnum.StoreMainFileType.STORE_ORDER_DATA.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterStoreOrderData> masterStoreOrderDataQueryWrapper = new LambdaQueryWrapper<>();
            masterStoreOrderDataQueryWrapper.eq(MasterStoreOrderData::getStoreId, storeId);
            List<MasterStoreOrderData> masterStoreOrderData = masterStoreOrderDataRepository.selectList(masterStoreOrderDataQueryWrapper);
            List<MasterStoreOrderDataDto> masterStoreOrderDataDtos = masterStoreOrderDataMapper.toDto(masterStoreOrderData);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.STORE_ORDER_DATA.getValue(), masterStoreOrderDataDtos);

        }//能耗信息
        else if (KidsSystemEnum.StoreMainFileType.ENERGY_CONSUMPTION_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterEnergyConsume> masterEnergyConsumeQueryWrapper = new LambdaQueryWrapper<>();
            masterEnergyConsumeQueryWrapper.eq(MasterEnergyConsume::getStoreId, storeId)
                                            .eq(MasterEnergyConsume::getIsDelete,false);
            List<MasterEnergyConsume> masterEnergyConsumes = masterEnergyConsumeRepository.selectList(masterEnergyConsumeQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.ENERGY_CONSUMPTION_INFO.getValue(), masterEnergyConsumes);

        }//设备信息
        else if (KidsSystemEnum.StoreMainFileType.DEVICE_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterDeviceInfo> deviceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            deviceInfoLambdaQueryWrapper.eq(MasterDeviceInfo::getStoreId, storeId)
                                        .eq(MasterDeviceInfo::getIsDelete,false);
            List<MasterDeviceInfo> masterDeviceInfos = masterDeviceInfoRepository.selectList(deviceInfoLambdaQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.DEVICE_INFO.getValue(), masterDeviceInfos);

        }//维修信息
        else if (KidsSystemEnum.StoreMainFileType.MAINTENANCE_INFO.getValue().equals(storeMainFileType)) {
            StoreMasterInfo storeMasterInfo = storeMasterInfoRepository.selectById(storeId);
            LambdaQueryWrapper<MasterMaintenanceInfo> masterMaintenanceQueryWrapper = new LambdaQueryWrapper<>();
            masterMaintenanceQueryWrapper.eq(MasterMaintenanceInfo::getStoreCode, storeMasterInfo.getStoreNo());
            masterMaintenanceQueryWrapper.eq(MasterMaintenanceInfo::getIsDelete,false);
            List<MasterMaintenanceInfo> masterMaintenanceInfos = masterMaintenanceInfoRepository.selectList(masterMaintenanceQueryWrapper);
//            for (MasterMaintenanceInfo maintenanceInfo : masterMaintenanceInfos){
//                Long actualCost = maintenanceInfo.getActualCost();
//                BigDecimal bigDecimal = new BigDecimal(actualCost);
//                long cost = bigDecimal.multiply(BigDecimal.valueOf(0.01)).longValue();
//                maintenanceInfo.setActualCost(cost);
//            }
            mapReturn.put(KidsSystemEnum.StoreMainFileType.MAINTENANCE_INFO.getValue(), masterMaintenanceInfos);
        } //面积信息
        else if (KidsSystemEnum.StoreMainFileType.AREA_INFO.getValue().equals(storeMainFileType)) {
            LambdaQueryWrapper<MasterAreaInfo> masterAreaInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            masterAreaInfoLambdaQueryWrapper.eq(MasterAreaInfo::getStoreId, storeId);
            List<MasterAreaInfo> masterAreaInfos = masterAreaInfoRepository.selectList(masterAreaInfoLambdaQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.AREA_INFO.getValue(), masterAreaInfos);
        }//图纸信息
        else if (KidsSystemEnum.StoreMainFileType.DRAWING_INFO.getValue().equals(storeMainFileType)){
            LambdaQueryWrapper<MasterDrawingInfo> masterDrawingInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            masterDrawingInfoLambdaQueryWrapper.eq(MasterDrawingInfo::getStoreId,storeId);
            List<MasterDrawingInfo> masterDrawingInfos = masterDrawingInfoRepository.selectList(masterDrawingInfoLambdaQueryWrapper);
            mapReturn.put(KidsSystemEnum.StoreMainFileType.DRAWING_INFO.getValue(),masterDrawingInfos);
        }
        return mapReturn;
    }

    @Override
    public Map<String, Object> getProvinceCount() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        LambdaQueryWrapper areaQuery = Wrappers.lambdaQuery(Area.class)
                .eq(Area::getIsDelete,false)
                .eq(Area::getLevel,0);
        List<Area> areaList = areaService.list(areaQuery);
        Map<Long,Area> areaMap = new HashMap<>();
        areaMap = areaList.stream().filter(a -> ObjectUtil.isNotEmpty(a)).collect(Collectors.toMap(Area::getAreaCode,a -> a));
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = projectInfoService.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
        }
        Map<String,Object> resultMap = new HashMap<>();
        Integer provCount = 0;
        Integer cityCount = 0;
        Map<String,Integer> provMap = new HashMap<>();
        Map<String,Integer> cityMap = new HashMap<>();
        for (StoreMasterInfoDto store : storeMasterLongs) {
            if ((ObjectUtil.isNotEmpty(store.getStoreStatus()) && KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(store.getStoreStatus())) || ObjectUtil.isEmpty(store.getStoreStatus())){
                continue;
            }
            Long province = store.getProvince();
            if (ObjectUtil.isNotEmpty(province)){
                Area area = Optional.ofNullable(areaMap.get(province)).orElseGet(Area::new);
                String name = area.getName();
                String cityLevel = area.getCityLevel();
                if (ObjectUtil.isNotEmpty(cityLevel) && "0".equals(cityLevel)){
                    //统计直辖市
                    cityMap.put(name,1);
                }else{
                    //统计省
                    provMap.put(name,1);
                }
                Integer provinceCount = (Integer) resultMap.get(name);
                if (ObjectUtil.isEmpty(provinceCount)){
                    resultMap.put(name,1);
                }else{
                    resultMap.put(name,provinceCount+1);
                }
            }
        }
        resultMap.put("省",ObjectUtil.isNotEmpty(provMap) ? provMap.size() : 0);
        resultMap.put("市",ObjectUtil.isNotEmpty(cityMap) ? cityMap.size() : 0);
        return resultMap;
    }

    @Override
    public Map<String, Object> getStoreStatusCount() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = projectInfoService.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
        }
        Map<Long,Long> projectNewMap = new HashMap<>();
        LambdaQueryWrapper projectQuery = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getIsDelete,false)
                .eq(ProjectInfo::getProjectType,"new");
        List<ProjectInfo> projectList = projectInfoService.list(projectQuery);
        if (ObjectUtil.isNotEmpty(projectList) && projectList.size() > 0){
            projectNewMap = projectList.stream().filter(p -> ObjectUtil.isNotEmpty(p.getStoreId())).collect(Collectors.groupingBy(ProjectInfo::getStoreId,Collectors.counting()));
        }
        String nowDate = DateUtil.getNowDate().substring(2,4);
        Map<String,Object> resultMap = new HashMap<>();
        String newStore = nowDate+"新开店数";
        resultMap.put(newStore,0);
        resultMap.put("营业门店数",0);
        resultMap.put("筹建中门店",0);
        Integer newCount = 0;
        Integer storeCount = 0;
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (StoreMasterInfoDto store : storeMasterLongs) {
            if ((ObjectUtil.isNotEmpty(store.getStoreStatus()) && KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(store.getStoreStatus())) || ObjectUtil.isEmpty(store.getStoreStatus())){
                continue;
            }
            storeCount++;

            String actualOpenDate = store.getActualOpenDate();
            if (ObjectUtil.isNotEmpty(actualOpenDate)) {
//                String format = dateFormat.format(new Date(createTime.getTime()));
                if (nowDate.equals(actualOpenDate.substring(2, 4))) {
                    Long count = projectNewMap.get(store.getStoreMasterId());
                    if (ObjectUtil.isNotEmpty(count)){
                        newCount = newCount + Integer.parseInt(String.valueOf(count));
                    }
                }
            }
            String storeStatus = store.getStoreStatus();
            if (ObjectUtil.isNotEmpty(storeStatus)){
                if (KidsSystemEnum.StoreStatusEnum.BUSINESS.getValue().equals(storeStatus)){
                    storeStatus = "营业门店数";
                }else if (KidsSystemEnum.StoreStatusEnum.PREPARE.getValue().equals(storeStatus)){
                    storeStatus = "筹建中门店";
                }else{
                    continue;
                }
                Integer statusCount = (Integer) resultMap.get(storeStatus);
                if (ObjectUtil.isEmpty(statusCount)){
                    resultMap.put(storeStatus,1);
                }else{
                    resultMap.put(storeStatus,statusCount+1);
                }
            }
        }
        resultMap.put(newStore,newCount);
        resultMap.put("门店数量",storeCount);
        return resultMap;
    }

    @Override
    public Map<String, Object> getStorePropertyCount() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = projectInfoService.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("万达店",0);
        resultMap.put("吾悦店",0);
        resultMap.put("龙湖店",0);
        resultMap.put("永旺店",0);
        resultMap.put("华润店",0);
        resultMap.put("其他",0);
        List<String> properties = Arrays.asList("万达店","吾悦店","龙湖店","永旺店","华润店");
        Integer storeCount = 0;
        for (StoreMasterInfoDto store : storeMasterLongs) {
            if ((ObjectUtil.isNotEmpty(store.getStoreStatus()) && KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(store.getStoreStatus())) || ObjectUtil.isEmpty(store.getStoreStatus())){
                continue;
            }
            storeCount++;

            String propertyName = store.getPropertyName();
            if (ObjectUtil.isNotEmpty(propertyName)){
                propertyName = propertyName + "店";
                if (!properties.contains(propertyName)){
                    propertyName = "其他";
                }
                Integer propertyCount = (Integer) resultMap.get(propertyName);
                if (ObjectUtil.isEmpty(propertyCount)){
                    resultMap.put(propertyName,1);
                }else{
                    resultMap.put(propertyName,propertyCount+1);
                }
            }
        }
        resultMap.put("门店数量",storeCount);
        return resultMap;
    }

    @Override
    public Map<String, Object> getStoreAreaCount() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        LambdaQueryWrapper areaQuery = Wrappers.lambdaQuery(Area.class)
                .eq(Area::getIsDelete,false);
        List<Area> areaList = areaService.list(areaQuery);
        Map<Long,String> areaMap = new HashMap<>();
        areaMap = areaList.stream().filter(a -> ObjectUtil.isNotEmpty(a.getCityLevel())).collect(Collectors.toMap(Area::getAreaCode,Area::getCityLevel));
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = projectInfoService.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("直辖市",0);
        resultMap.put("地级市",0);
        resultMap.put("县级市",0);
        resultMap.put("县城",0);
        Integer storeCount = 0;
        Integer storeLevelCount = 0;
        Map<Long,Integer> storeLevelMap = new HashMap<>();
        List<StoreMasterInfoDto> storeMasterLong = new ArrayList<>();
        for (StoreMasterInfoDto storeMasterInfoDto:storeMasterLongs){
            if (!"store_closed".equals(storeMasterInfoDto.getStoreStatus()) && ObjectUtil.isNotEmpty(storeMasterInfoDto.getStoreStatus())){
                storeMasterLong.add(storeMasterInfoDto);
            }
        }
        for (StoreMasterInfoDto store : storeMasterLong) {
            storeCount++;
            Long province = store.getProvince();
            Long city = store.getCity();
            Long county = store.getCounty();
            if (ObjectUtil.isNotEmpty(province)){
                String value = areaMap.get(province);
                if (ObjectUtil.isNotEmpty(value)){
                    storeLevelMap.put(province,1);
                    String labelStr = KidsSystemEnum.CityLevelEnum.getLabelByValue(value);
                    Integer levelCount = (Integer) resultMap.get(labelStr);
                    if (ObjectUtil.isNotEmpty(levelCount)){
                        resultMap.put(labelStr,levelCount+1);
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(city)){
                String value = areaMap.get(city);
                if (ObjectUtil.isNotEmpty(value)){
                    storeLevelMap.put(city,1);
                    String labelStr = KidsSystemEnum.CityLevelEnum.getLabelByValue(value);
                    Integer levelCount = (Integer) resultMap.get(labelStr);
                    if (ObjectUtil.isNotEmpty(levelCount)){
                        resultMap.put(labelStr,levelCount+1);
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(county)){
                String value = areaMap.get(county);
                if (ObjectUtil.isNotEmpty(value)){
                    storeLevelMap.put(county,1);
                    String labelStr = KidsSystemEnum.CityLevelEnum.getLabelByValue(value);
                    Integer levelCount = (Integer) resultMap.get(labelStr);
                    if (ObjectUtil.isNotEmpty(levelCount)){
                        resultMap.put(labelStr,levelCount+1);
                    }
                }
            }
        }
        resultMap.put("全国门店数量",resultMap.put("门店数量",ObjectUtil.isNotEmpty(storeMasterLong) ? storeMasterLong.size() : 0));
        resultMap.put("直辖、地、县城市数量",ObjectUtil.isNotEmpty(storeLevelMap) ? storeLevelMap.size() : 0);
        return resultMap;
    }
}