/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.StoreTemplateRelation;
import com.bassims.modules.atour.repository.StoreTemplateRelationRepository;
import com.bassims.modules.atour.service.StoreTemplateRelationService;
import com.bassims.modules.atour.service.dto.StoreTemplateRelationDto;
import com.bassims.modules.atour.service.dto.StoreTemplateRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.StoreTemplateRelationMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-03-25
**/
@Service
public class StoreTemplateRelationServiceImpl extends BaseServiceImpl<StoreTemplateRelationRepository,StoreTemplateRelation> implements StoreTemplateRelationService {

    private static final Logger logger = LoggerFactory.getLogger(StoreTemplateRelationServiceImpl.class);

    @Autowired
    private StoreTemplateRelationRepository storeTemplateRelationRepository;
    @Autowired
    private StoreTemplateRelationMapper storeTemplateRelationMapper;

    @Override
    public Map<String,Object> queryAll(StoreTemplateRelationQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<StoreTemplateRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(StoreTemplateRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", storeTemplateRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<StoreTemplateRelationDto> queryAll(StoreTemplateRelationQueryCriteria criteria){
        return storeTemplateRelationMapper.toDto(list(QueryHelpPlus.getPredicate(StoreTemplateRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreTemplateRelationDto findById(Long relationId) {
        StoreTemplateRelation storeTemplateRelation = Optional.ofNullable(getById(relationId)).orElseGet(StoreTemplateRelation::new);
        ValidationUtil.isNull(storeTemplateRelation.getRelationId(),getEntityClass().getSimpleName(),"relationId",relationId);
        return storeTemplateRelationMapper.toDto(storeTemplateRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreTemplateRelationDto create(StoreTemplateRelation resources) {
        save(resources);
        return findById(resources.getRelationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StoreTemplateRelation resources) {
        StoreTemplateRelation storeTemplateRelation = Optional.ofNullable(getById(resources.getRelationId())).orElseGet(StoreTemplateRelation::new);
        ValidationUtil.isNull( storeTemplateRelation.getRelationId(),"StoreTemplateRelation","id",resources.getRelationId());
        storeTemplateRelation.copy(resources);
        updateById(storeTemplateRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long relationId : ids) {
            storeTemplateRelationRepository.deleteById(relationId);
        }
    }

    @Override
    public void download(List<StoreTemplateRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StoreTemplateRelationDto storeTemplateRelation : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("门店类型", storeTemplateRelation.getStoreType());
            map.put("项目类型", storeTemplateRelation.getProjectType());
            map.put("模板id", storeTemplateRelation.getTemplateId());
            map.put("是否可用", storeTemplateRelation.getIsEnabled());
            map.put("是否删除", storeTemplateRelation.getIsDelete());
            map.put("创建时间", storeTemplateRelation.getCreateTime());
            map.put("更新时间", storeTemplateRelation.getUpdateTime());
            map.put(" createBy",  storeTemplateRelation.getCreateBy());
            map.put(" updateBy",  storeTemplateRelation.getUpdateBy());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public StoreTemplateRelation getByTemplateId(Long templateId) {

        return storeTemplateRelationRepository.getByTemplateId(templateId);
    }
}