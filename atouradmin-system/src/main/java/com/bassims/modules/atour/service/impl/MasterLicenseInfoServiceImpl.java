/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.MasterLicenseInfo;
import com.bassims.modules.atour.repository.MasterLicenseInfoRepository;
import com.bassims.modules.atour.service.MasterLicenseInfoService;
import com.bassims.modules.atour.service.dto.MasterLicenseInfoDto;
import com.bassims.modules.atour.service.dto.MasterLicenseInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MasterLicenseInfoMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-22
**/
@Service
public class MasterLicenseInfoServiceImpl extends BaseServiceImpl<MasterLicenseInfoRepository,MasterLicenseInfo> implements MasterLicenseInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MasterLicenseInfoServiceImpl.class);

    @Autowired
    private MasterLicenseInfoRepository masterLicenseInfoRepository;
    @Autowired
    private MasterLicenseInfoMapper masterLicenseInfoMapper;

    @Override
    public Map<String,Object> queryAll(MasterLicenseInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<MasterLicenseInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MasterLicenseInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", masterLicenseInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<MasterLicenseInfoDto> queryAll(MasterLicenseInfoQueryCriteria criteria){
        return masterLicenseInfoMapper.toDto(list(QueryHelpPlus.getPredicate(MasterLicenseInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterLicenseInfoDto findById(Long licenseId) {
        MasterLicenseInfo masterLicenseInfo = Optional.ofNullable(getById(licenseId)).orElseGet(MasterLicenseInfo::new);
        ValidationUtil.isNull(masterLicenseInfo.getLicenseId(),getEntityClass().getSimpleName(),"licenseId",licenseId);
        return masterLicenseInfoMapper.toDto(masterLicenseInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterLicenseInfoDto create(MasterLicenseInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setLicenseId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getLicenseId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterLicenseInfo resources) {
        MasterLicenseInfo masterLicenseInfo = Optional.ofNullable(getById(resources.getLicenseId())).orElseGet(MasterLicenseInfo::new);
        ValidationUtil.isNull( masterLicenseInfo.getLicenseId(),"MasterLicenseInfo","id",resources.getLicenseId());
        masterLicenseInfo.copy(resources);
        updateById(masterLicenseInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long licenseId : ids) {
            masterLicenseInfoRepository.deleteById(licenseId);
        }
    }

    @Override
    public void download(List<MasterLicenseInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterLicenseInfoDto masterLicenseInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("证照名称", masterLicenseInfo.getLicenseName());
            map.put("证照类型", masterLicenseInfo.getLicenseType());
            map.put("门店id", masterLicenseInfo.getStoreId());
            map.put("门店编号", masterLicenseInfo.getStoreNo());
            map.put("实际开始时间", masterLicenseInfo.getActualStart());
            map.put("实际结束时间", masterLicenseInfo.getActualEnd());
            map.put("上传人", masterLicenseInfo.getUploader());
            map.put("上传时间", masterLicenseInfo.getUploadTime());
            map.put(" createTime",  masterLicenseInfo.getCreateTime());
            map.put(" createBy",  masterLicenseInfo.getCreateBy());
            map.put(" updateTime",  masterLicenseInfo.getUpdateTime());
            map.put(" updateBy",  masterLicenseInfo.getUpdateBy());
            map.put("是否删除", masterLicenseInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}