package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectInputDetailReport;
import com.bassims.modules.atour.service.dto.ProjectInputDetailReportQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 工程投入明细报表
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
public interface ProjectInputDetailReportService extends BaseService<ProjectInputDetailReport> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectInputDetailReportQueryCriteria criteria) throws IOException;

    /**
     * 查询时间
     *
     * @param criteria 标准
     * @param pageable
     * @return {@link Object}
     */
    Object queryTime(ProjectInputDetailReportQueryCriteria criteria, Pageable pageable);
}
