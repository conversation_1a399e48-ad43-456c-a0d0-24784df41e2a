/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.Visa;
import com.bassims.modules.atour.service.dto.VisaDto;
import com.bassims.modules.atour.service.dto.VisaQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-09-26
**/
public interface VisaService extends BaseService<Visa> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(VisaQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<VisaDto>
    */
    List<VisaDto> queryAll(VisaQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return VisaDto
     */
    VisaDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return VisaDto
    */
    VisaDto create(Visa resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Visa resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<VisaDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查询数据分页
     * @param visaDto 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryApprovePassInfo(VisaDto visaDto,Pageable pageable);

    /**
     * 创建干系人
     * @param orderId 条件
     * @param roleName
     * @param userId
     */
    void createStakeholders(Long orderId,String roleName,Long userId);

    /**
     * 提交审批
     * @param visaDto 条件
     * @return InstructionSheetDto
     */
    VisaDto submit(VisaDto visaDto);

    /**
     * 导出excel模板
     * @param visaId
     * @param response
     * @throws IOException
     */
    void downloadExcel (Long visaId,HttpServletResponse response)throws IOException;
}