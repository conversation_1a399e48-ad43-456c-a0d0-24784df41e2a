package com.bassims.modules.atour.service.impl;

import com.alibaba.excel.EasyExcel;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ProjectAreaCommodity;
import com.bassims.modules.atour.domain.ProjectInfo;
import com.bassims.modules.atour.repository.ProjectAreaCommodityRepository;
import com.bassims.modules.atour.service.CodeValueConversionService;
import com.bassims.modules.atour.service.ProjectAreaCommodityService;
import com.bassims.modules.atour.service.dto.ProjectAreaCommodityDto;
import com.bassims.modules.atour.service.dto.ProjectAreaCommodityQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectAreaCommodityMapper;
import com.bassims.utils.PageUtil;
import com.bassims.utils.QueryHelpPlus;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProjectAreaCommodityServiceImpl extends BaseServiceImpl<ProjectAreaCommodityRepository, ProjectAreaCommodity> implements ProjectAreaCommodityService {

    @Autowired
    private ProjectAreaCommodityMapper projectAreaCommodityMapper;

    @Autowired
    private CodeValueConversionService codeValueConversionService;

    @Override
    public void download(HttpServletResponse response, ProjectAreaCommodityQueryCriteria criteria) throws IOException {
        String fileName = "商品面积表_" + System.currentTimeMillis() + ".xlsx";

        OutputStream outputStream = response.getOutputStream();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/msexcel;charset=UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String sheetName = "商品面积";
        List<ProjectAreaCommodityDto> projectAreaCommodityDtoList = projectAreaCommodityMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectAreaCommodity.class,criteria)));
        for (ProjectAreaCommodityDto projectAreaCommodityDto:projectAreaCommodityDtoList){
            String cityCompany = codeValueConversionService.getCityCompany(projectAreaCommodityDto.getCityCompany());
            projectAreaCommodityDto.setCityCompany(cityCompany);
        }
        int index = 1;
        EasyExcel.write(outputStream,ProjectAreaCommodityDto.class).sheet(index,sheetName).doWrite(projectAreaCommodityDtoList);
    }

    @Override
    public Map<String, Object> queryAll(ProjectAreaCommodityQueryCriteria criteria, Pageable pageable) {
        List<ProjectAreaCommodity> list = list(QueryHelpPlus.getPredicate(ProjectAreaCommodity.class, criteria));
        PageInfo<ProjectInfo> page = new PageInfo<>();
        if (list.size() > 0){
            List list1 = PageUtil.toPage(pageable.getPageNumber() == 0 ? 1 : (pageable.getPageNumber() - 1), pageable.getPageSize(), list);
            page = new PageInfo<ProjectInfo>(list1);
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content",page.getList());
        map.put("totalElements",list.size());
        return map;
    }
}
