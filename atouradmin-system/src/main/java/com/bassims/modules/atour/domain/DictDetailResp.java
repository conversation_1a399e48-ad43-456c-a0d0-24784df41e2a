package com.bassims.modules.atour.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述：
 */
@Data
public class DictDetailResp implements Serializable {
    @ApiModelProperty(value = "id")
    private Long detailId;

    @ApiModelProperty(value = "字典标签")
    private String label;

    @ApiModelProperty(value = "字典值")
    private String value;
}
