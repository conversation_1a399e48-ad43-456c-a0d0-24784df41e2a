/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.MeasureArea;
import com.bassims.modules.atour.service.dto.MeasureAreaDto;
import com.bassims.modules.atour.service.dto.MeasureAreaQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-12-01
**/
public interface MeasureAreaService extends BaseService<MeasureArea> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(MeasureAreaQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<MeasureAreaDto>
    */
    List<MeasureAreaDto> queryAll(MeasureAreaQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param measureAreaId ID
     * @return MeasureAreaDto
     */
    MeasureAreaDto findById(Long measureAreaId);

    /**
    * 创建
    * @param resources /
    * @return MeasureAreaDto
    */
    MeasureAreaDto create(MeasureArea resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(MeasureArea resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<MeasureAreaDto> all, HttpServletResponse response) throws IOException;

    /**
     * 获取分摊面积信息
     * @param page
     * @return
     */
    Map<String, Object> getMeasure(Page page);

    /**
     * 面积分摊计算
     * @param projectId
     * @return
     */
    boolean culMeasureArea(Long projectId);

    /**
     * 面积分摊计算(新)
     * @param projectId
     * @return
     */
    boolean culMeasureAreaNew(Long projectId, Long templateId);

    /**
     * 面积分摊计算(商户)
     * @param storeId
     * @return
     * @throws Exception
     */
    boolean culStoreIdMeasureArea(Long storeId) throws Exception;

    /**
     * 面积分摊计算(商户所有)
     * @return
     * @throws Exception
     */
    boolean storeCulMeasureArea() throws Exception;

}