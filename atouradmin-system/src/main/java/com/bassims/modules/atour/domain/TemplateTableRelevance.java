/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-25
**/
@Data
@TableName(value="t_template_table_relevance")
public class TemplateTableRelevance implements Serializable {

    @TableId(value = "relevance_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long relevanceId;

    @TableField(value = "template_table_group_id")
    @ApiModelProperty(value = "模版表格分组id")
    private Long templateTableGroupId;

    @TableField(value = "table_id")
    @ApiModelProperty(value = "表格ID")
    private Long tableId;

    public void copy(TemplateTableRelevance source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}