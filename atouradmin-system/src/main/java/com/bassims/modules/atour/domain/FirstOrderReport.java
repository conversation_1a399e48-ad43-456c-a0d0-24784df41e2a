/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-13
**/
@Data
@TableName(value="first_order_report")
public class FirstOrderReport implements Serializable {

//    @TableField(value = "city_company")
    @ApiModelProperty(value = "城市公司value")
    private String cityCompany;

//    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店编码")
    private String storeNo;

//    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

//    @TableField(value = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

//    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "费用类型")
    private String costType;

//    @TableField(value = "actual_open_date")
    @ApiModelProperty(value = "实际开业日期")
    private String actualOpenDate;

//    @TableField(value = "order_no")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

//    @TableField(value = "order_type")
    @ApiModelProperty(value = "订单类型")
    private String orderType;

//    @TableField(value = "sup_name_cn")
    @ApiModelProperty(value = "厂商名称")
    private String supNameCn;

//    @TableField(value = "second_class")
    @ApiModelProperty(value = "物料类型")
    private String secondClass;

//    @TableField(value = "bug_money")
    @ApiModelProperty(value = "采购金额")
    private Double bugMoney;

//    @TableField(value = "settle_money")
    @ApiModelProperty(value = "结算金额")
    private Double settleMoney;

//    @TableField(value = "order_status")
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

//    @TableField(value = "add_time")
    @ApiModelProperty(value = "下单时间")
    private String addTime;

//    @TableField(value = "order_id")
//    @ApiModelProperty(value = "订单主键")
//    private Long orderId;

    public void copy(FirstOrderReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}