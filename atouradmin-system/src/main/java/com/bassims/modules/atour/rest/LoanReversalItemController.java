/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.LoanReversalItem;
import com.bassims.modules.atour.service.LoanReversalItemService;
import com.bassims.modules.atour.service.dto.LoanReversalItemDto;
import com.bassims.modules.atour.service.dto.LoanReversalItemQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-12-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "借款冲销明细管理")
@RequestMapping("/api/loanReversalItem")
public class LoanReversalItemController {

    private static final Logger logger = LoggerFactory.getLogger(LoanReversalItemController.class);

    private final LoanReversalItemService loanReversalItemService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, LoanReversalItemQueryCriteria criteria) throws IOException {
        loanReversalItemService.download(loanReversalItemService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<LoanReversalItemDto>>}
    */
    @GetMapping("/list")
    @Log("查询借款冲销明细")
    @ApiOperation("查询借款冲销明细")
    public ResponseEntity<Object> query(LoanReversalItemQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(loanReversalItemService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<LoanReversalItemDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询借款冲销明细")
    @ApiOperation("查询借款冲销明细")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(loanReversalItemService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增借款冲销明细")
    @ApiOperation("新增借款冲销明细")
    public ResponseEntity<Object> create(@Validated @RequestBody LoanReversalItem resources){
        return new ResponseEntity<>(loanReversalItemService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改借款冲销明细")
    @ApiOperation("修改借款冲销明细")
    public ResponseEntity<Object> update(@Validated @RequestBody LoanReversalItem resources){
        loanReversalItemService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除借款冲销明细")
    @ApiOperation("删除借款冲销明细")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        loanReversalItemService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}