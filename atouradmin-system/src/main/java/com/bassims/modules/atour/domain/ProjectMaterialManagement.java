/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-04
**/
@Data
@TableName(value="t_project_material_management")
public class ProjectMaterialManagement implements Serializable {

    @TableId(value = "project_management_id")
    @ApiModelProperty(value = "项目材料ID") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectManagementId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目ID") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "room_number")
    @ApiModelProperty(value = "房间号")
    private String roomNumber;

    @TableField(value = "material_category")
    @ApiModelProperty(value = "物资类别")
    private String materialCategory;

    @TableField(value = "material_pattern")
    @ApiModelProperty(value = "材料图样")
    private String materialPattern;

    @TableField(value = "material_performance_parameter")
    @ApiModelProperty(value = "材料性能参数")
    private String materialPerformanceParameter;

    @TableField(value = "agree_take")
    @ApiModelProperty(value = "合同约定必采")
    private String agreeTake;

    @TableField(value = "order_time")
    @ApiModelProperty(value = "下单时间")
    private String orderTime;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "create_dept")
    @ApiModelProperty(value = "创建部门")
    private Long createDept;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否已删除：0 正常，1 已删除")
    private Integer isDelete;

    @TableField(value = "tenant_id")
    @ApiModelProperty(value = "租户编号")
    private Long tenantId;

    @TableField(value = "current_template_type")
    @ApiModelProperty(value = "当前模版类型（是否必采）")
    private String currentTemplateType;


    @TableField(value = "platform_purchase_not")
    @ApiModelProperty(value = "是否平台采购(码值：platform_purchase_not)")
    private String   platformPurchaseNot;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;


    @TableField(value = "node_code")
    @ApiModelProperty(value = "三级元素的NodeCode")
    private String nodeCode;

    public void copy(ProjectMaterialManagement source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}