/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.TemplateScheduleRelation;
import com.bassims.modules.atour.repository.TemplateScheduleRelationRepository;
import com.bassims.modules.atour.service.TemplateScheduleRelationService;
import com.bassims.modules.atour.service.dto.TemplateScheduleRelationDto;
import com.bassims.modules.atour.service.dto.TemplateScheduleRelationQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateScheduleRelationMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-04-26
**/
@Service
public class TemplateScheduleRelationServiceImpl extends BaseServiceImpl<TemplateScheduleRelationRepository,TemplateScheduleRelation> implements TemplateScheduleRelationService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateScheduleRelationServiceImpl.class);

    @Autowired
    private TemplateScheduleRelationRepository templateScheduleRelationRepository;
    @Autowired
    private TemplateScheduleRelationMapper templateScheduleRelationMapper;

    @Override
    public Map<String,Object> queryAll(TemplateScheduleRelationQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<TemplateScheduleRelation> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateScheduleRelation.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateScheduleRelationMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateScheduleRelationDto> queryAll(TemplateScheduleRelationQueryCriteria criteria){
        return templateScheduleRelationMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateScheduleRelation.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateScheduleRelationDto findById(Long templateScheduleId) {
        TemplateScheduleRelation templateScheduleRelation = Optional.ofNullable(getById(templateScheduleId)).orElseGet(TemplateScheduleRelation::new);
        ValidationUtil.isNull(templateScheduleRelation.getTemplateScheduleId(),getEntityClass().getSimpleName(),"templateScheduleId",templateScheduleId);
        return templateScheduleRelationMapper.toDto(templateScheduleRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateScheduleRelationDto create(TemplateScheduleRelation resources) {
        save(resources);
        return findById(resources.getTemplateScheduleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateScheduleRelation resources) {
        TemplateScheduleRelation templateScheduleRelation = Optional.ofNullable(getById(resources.getTemplateScheduleId())).orElseGet(TemplateScheduleRelation::new);
        ValidationUtil.isNull( templateScheduleRelation.getTemplateScheduleId(),"TemplateScheduleRelation","id",resources.getTemplateScheduleId());
        templateScheduleRelation.copy(resources);
        updateById(templateScheduleRelation);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateScheduleId : ids) {
            templateScheduleRelationRepository.deleteById(templateScheduleId);
        }
    }

    @Override
    public void download(List<TemplateScheduleRelationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateScheduleRelationDto templateScheduleRelation : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("模板表id", templateScheduleRelation.getTemplateId());
            map.put("模板code", templateScheduleRelation.getTemplateCode());
            map.put("工期表id", templateScheduleRelation.getScheduleId());
            map.put("工期code", templateScheduleRelation.getScheduleCode());
            map.put("是否可用", templateScheduleRelation.getIsEnabled());
            map.put("是否删除", templateScheduleRelation.getIsDelete());
            map.put("创建时间", templateScheduleRelation.getCreateTime());
            map.put("更新时间", templateScheduleRelation.getUpdateTime());
            map.put(" createBy",  templateScheduleRelation.getCreateBy());
            map.put(" updateBy",  templateScheduleRelation.getUpdateBy());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}