/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.alibaba.fastjson.JSONObject;
import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.PreachDatumFile;
import com.bassims.modules.atour.service.PreachDatumFileService;
import com.bassims.modules.atour.service.dto.PreachDatumFileDto;
import com.bassims.modules.atour.service.dto.PreachDatumFileQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-10
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "宣讲资料附件表管理")
@RequestMapping("/api/preachDatumFile")
public class PreachDatumFileController {

    private static final Logger logger = LoggerFactory.getLogger(PreachDatumFileController.class);

    private final PreachDatumFileService preachDatumFileService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, PreachDatumFileQueryCriteria criteria) throws IOException {
        preachDatumFileService.download(preachDatumFileService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<PreachDatumFileDto>>}
    */
    @Log("查询宣讲资料附件表")
    @ApiOperation("查询宣讲资料附件表")
    @GetMapping(value = "/query")
    @MyDataPermission(title = "资料附件配置")
    public ResponseEntity<Object> query(PreachDatumFileQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(preachDatumFileService.queryAll(criteria,pageable),HttpStatus.OK);
    }


    /**
    * @real_return {@link ResponseEntity<PreachDatumFileDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询宣讲资料附件表")
    @ApiOperation("查询宣讲资料附件表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(preachDatumFileService.findById(id),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<PreachDatumFileDto>}
     */
    @GetMapping(value = "/queryByNodeCode")
    @Log("通过node_code查询宣讲资料附件表")
    @ApiOperation("通过node_code查询宣讲资料附件表")
    public ResponseEntity<Object> queryByNodeCode(@RequestParam("nodeCode") String nodeCode){
        return new ResponseEntity<>(preachDatumFileService.queryByNodeCode(nodeCode),HttpStatus.OK);
    }


    @PostMapping("/save")
    @Log("新增宣讲资料附件表")
    @ApiOperation("新增宣讲资料附件表")
    public ResponseEntity<Object> create(@Validated @RequestBody PreachDatumFile resources){
        return new ResponseEntity<>(preachDatumFileService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改宣讲资料附件表")
    @ApiOperation("修改宣讲资料附件表")
    public ResponseEntity<Object> update(@Validated @RequestBody PreachDatumFile resources){
        preachDatumFileService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除宣讲资料附件表")
    @ApiOperation("删除宣讲资料附件表")
//    @MyDataPermission(title = "资料附件配置")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        preachDatumFileService.deleteAll(ids);
        return new ResponseEntity<>("success", HttpStatus.OK);
    }

    @ResponseBody
//    @AnonymousAccess
    @Log("oss上传")
    @ApiOperation("oss上传")
    @PostMapping("/ossCreate")
//    @MyDataPermission(title = "资料附件配置")
    public ResponseEntity<Object> ossCreate(@RequestParam String nodeCode, @RequestParam String name, @RequestParam("uploadFile") MultipartFile file) throws Exception {
        JSONObject json = new JSONObject();
        if (file.isEmpty()) {
            return new ResponseEntity<>("当前文件为空", HttpStatus.CREATED);
        }
        PreachDatumFile preachDatumFile = preachDatumFileService.createOss(nodeCode,name, file, null);
        return new ResponseEntity<>(preachDatumFile, HttpStatus.CREATED);
    }


    @ResponseBody
    @AnonymousAccess
    @Log("oss上传")
    @ApiOperation("oss上传")
    @PostMapping("/updateOss")
    public ResponseEntity<Object> updateOss(@RequestParam Long datumFileId, @RequestParam String name, @RequestParam("uploadFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            return new ResponseEntity<>("当前文件为空", HttpStatus.CREATED);
        }
        PreachDatumFile preachDatumFile = preachDatumFileService.updateOss(datumFileId,name, file);
        return new ResponseEntity<>(preachDatumFile, HttpStatus.CREATED);
    }


    @ApiOperation("oss下载文件")
    @GetMapping(value = "/ossDownloadFile")
//    @MyDataPermission(title = "资料附件配置")
    public void ossDownloadFile(Long localId, HttpServletResponse response, HttpServletRequest request) throws IOException {
        preachDatumFileService.downloadOss(localId, response, request);
    }


}