/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-04
**/
@Data
@TableName(value="t_material_management_expand")
public class MaterialManagementExpand implements Serializable {

    @TableId(value = "management_expand_id")
    @ApiModelProperty(value = "材料拓展表ID")
    private Long managementExpandId;

    @TableField(value = "management_id")
    @ApiModelProperty(value = "材料ID")
    private Long managementId;

    @TableField(value = "price_year")
    @ApiModelProperty(value = "价格年度")
    private Integer priceYear;

    @TableField(value = "unit_price")
    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "create_dept")
    @ApiModelProperty(value = "创建部门")
    private Long createDept;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否已删除：0 正常，1 已删除")
    private Integer isDelete;

    @TableField(value = "tenant_id")
    @ApiModelProperty(value = "租户编号")
    private Long tenantId;

    public void copy(MaterialManagementExpand source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}