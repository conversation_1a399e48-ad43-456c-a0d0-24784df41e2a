/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-04
**/
@Data
public class MaterialManagementExpandDto implements Serializable {

    /** 材料拓展表ID */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long managementExpandId;

    /** 材料ID */
    private Long managementId;

    /** 价格年度 */
    private Integer priceYear;

    /** 含税单价 */
    private BigDecimal unitPrice;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private Long createUser;

    /** 创建部门 */
    private Long createDept;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private Long updateUser;

    /** 是否已删除：0 正常，1 已删除 */
    private Integer isDelete;

    /** 租户编号 */
    private Long tenantId;
}