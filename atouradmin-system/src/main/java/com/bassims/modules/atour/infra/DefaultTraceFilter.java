package com.bassims.modules.atour.infra;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.bassims.modules.atour.infra.utils.TraceUtil;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * 响应header中增加trace_id等内容
 * 
 * <AUTHOR>
 *
 */
public class DefaultTraceFilter extends OncePerRequestFilter {

    private static final String HEADER_NAME_TRACE_ID = "trace_id";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        response.setHeader(HEADER_NAME_TRACE_ID, TraceUtil.getTraceId());
        filterChain.doFilter(request, response);
    }

}
