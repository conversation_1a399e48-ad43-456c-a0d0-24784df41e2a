package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.requestParam.KwWorkFlowApproveDetail;
import com.bassims.modules.atour.service.ProjectApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "中台接入入口")
@RequestMapping("/api/kwThirdInterface")
public class KwThirdController {
    private static final Logger logger = LoggerFactory.getLogger(KwThirdController.class);

    private final ProjectApproveService projectApproveService;

    /**
     * @real_return {@link ResponseEntity < List < InstructionSheetDto >>}
     */
    @PostMapping("/updateApproveDetail")
    @Log("更新第三方接口审批结果")
    @ApiOperation("更新第三方接口审批结果")
    public ResponseEntity<Object> updateApprove(@RequestBody KwWorkFlowApproveDetail request) {
        return new ResponseEntity<>(projectApproveService.createWorkFlowApproveDetail(request), HttpStatus.OK);
    }



}
