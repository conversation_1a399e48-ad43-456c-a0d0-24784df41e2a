/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.TemplateCompletionReceipt;
import com.bassims.modules.atour.domain.TemplateCompletionReceiptRelation;
import com.bassims.modules.atour.domain.vo.TemplateCompletionReceiptRelationVo;
import com.bassims.modules.atour.service.TemplateCompletionReceiptRelationService;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptRelationDto;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptRelationQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-01-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "竣工验收验收单模版和分组关联表管理")
@RequestMapping("/api/templateCompletionReceiptRelation")
@Validated
public class TemplateCompletionReceiptRelationController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateCompletionReceiptRelationController.class);

    private final TemplateCompletionReceiptRelationService templateCompletionReceiptRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateCompletionReceiptRelationQueryCriteria criteria) throws IOException {
        templateCompletionReceiptRelationService.download(templateCompletionReceiptRelationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateCompletionReceiptRelationDto>>}
    */
    @GetMapping("/list")
    @Log("查询竣工验收验收单模版和分组关联表")
    @ApiOperation("查询竣工验收验收单模版和分组关联表")
    public ResponseEntity<Object> query(TemplateCompletionReceiptRelationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateCompletionReceiptRelationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateCompletionReceiptRelationDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询竣工验收验收单模版和分组关联表")
    @ApiOperation("查询竣工验收验收单模版和分组关联表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateCompletionReceiptRelationService.findById(id),HttpStatus.OK);
    }

    @GetMapping("selectByReceiptGroupId")
    @Log("通过selectByReceiptGroupId分组关联表对应的所有仓库")
    @ApiOperation("通过selectByReceiptGroupId分组关联表对应的所有仓库")
    public ResponseEntity<Object> selectByReceiptGroupId(Long receiptGroupId, String totalItem, String project, String content, String subItemContent,String subItem){
        return new ResponseEntity<>(templateCompletionReceiptRelationService.selectByReceiptGroupId(receiptGroupId,totalItem,project,content,subItemContent,subItem),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增竣工验收验收单模版和分组关联表")
    @ApiOperation("新增竣工验收验收单模版和分组关联表")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateCompletionReceiptRelation resources){
        return new ResponseEntity<>(templateCompletionReceiptRelationService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改竣工验收验收单模版和分组关联表")
    @ApiOperation("修改竣工验收验收单模版和分组关联表")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateCompletionReceiptRelation resources){
        templateCompletionReceiptRelationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除竣工验收验收单模版和分组关联表")
    @ApiOperation("删除竣工验收验收单模版和分组关联表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateCompletionReceiptRelationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @PostMapping("/saveRelation")
//    @Log("新增竣工验收验收单模版和分组关联表关系")
//    public ResponseEntity<Object> saveRelation(@RequestBody Map map) {
//        Long receiptGroupId = null;
//        List receiptIds = null;
//        try {
//            receiptGroupId = Long.valueOf(map.get("receiptGroupId").toString());
//            receiptIds = (List)map.get("receiptIds");
//            templateCompletionReceiptRelationService.saveRelation(receiptGroupId,receiptIds);
//        }catch (Exception e){
//            throw new BadRequestException("参数有误");
//        }
//        return new ResponseEntity<>(HttpStatus.OK);
//    }
}