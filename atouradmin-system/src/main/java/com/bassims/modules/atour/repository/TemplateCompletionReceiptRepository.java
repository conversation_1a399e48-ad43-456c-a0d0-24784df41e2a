/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.TemplateCompletionReceipt;
import com.bassims.modules.atour.service.dto.TemplateCompletionReceiptQueryCriteria;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-01-26
**/
@Repository
public interface TemplateCompletionReceiptRepository extends BaseMapper<TemplateCompletionReceipt> {
    List<TemplateCompletionReceipt> getTemplateByGroupId(Long receiptGroupId);
    List<TemplateCompletionReceipt> getSubItemList(Long receiptGroupId);
    List<TemplateCompletionReceipt> selectByCondition(String totalItem, String project, String content,String subItemContent,String subItem);
    List<TemplateCompletionReceipt> queryCondition(TemplateCompletionReceiptQueryCriteria criteria);
}