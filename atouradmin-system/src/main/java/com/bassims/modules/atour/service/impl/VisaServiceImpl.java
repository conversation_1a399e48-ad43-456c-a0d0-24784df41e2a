/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.ProjectInfoSimple;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectApproveDetailMapper;
import com.bassims.modules.atour.service.mapstruct.ProjectApproveMapper;
import com.bassims.modules.atour.service.mapstruct.ProjectGroupMapper;
import com.bassims.modules.atour.service.mapstruct.VisaMapper;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.utils.*;
import com.bassims.utils.excelutil.ExcelUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-09-26
 **/
@Service
public class VisaServiceImpl extends BaseServiceImpl<VisaRepository, Visa> implements VisaService {

    private static final Logger logger = LoggerFactory.getLogger(VisaServiceImpl.class);

    @Autowired
    private VisaRepository visaRepository;
    @Autowired
    private VisaMapper visaMapper;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private InstructionSheetRepository instructionSheetRepository;
    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private ProjectAppTemplateService projectAppTemplateService;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private ApproveTemplateRepository approveTemplateRepository;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private ProjectApproveDetailService projectApproveDetailService;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private InstructionSheetService instructionSheetService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Autowired
    private ProjectApproveDetailRepository projectApproveDetailRepository;

    @Autowired
    private ProjectApproveMapper projectApproveMapper;

    @Autowired
    private ProjectApproveDetailMapper projectApproveDetailMapper;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private VisaService visaService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;

    @Autowired
    private ProjectJointTaskOnfigurationService projectJointTaskOnfigurationService;

    @Override
    public Map<String, Object> queryAll(VisaQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
//        List<Long> ids = visaRepository.getIdsByStakeholders(currentUserId, Boolean.FALSE);
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        List<Long> ids = projectGroupRepository.getAccessByRole("con-00125", roleIds);
        criteria.setVisaId(ids);
        if (ObjectUtils.isEmpty(ids)) {
            criteria.setVisaId(Lists.newArrayList(0L));
        }

//        UserDetails currentUser = SecurityUtils.getCurrentUser();
//        User user = userRepository.findByUsername(currentUser.getUsername());
        LambdaQueryWrapper groupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getTemplateCode, "visa")
                .eq(ProjectGroup::getNodeLevel, 2);
        List<ProjectGroup> groupList = projectGroupService.list(groupQuery);
        Map<Long, ProjectGroup> groupMap = groupList.stream().collect(Collectors.toMap(i -> i.getOrderId(), j -> j, (k1, k2) -> k1));

//        Set<Role> roles = user.getRoles();
//        List<String> roleCodeList = roles.stream().map(Role::getRoleCode).collect(Collectors.toList());
//        if (!roleCodeList.contains(null)){
//            criteria.setCode(currentUser.getUsername());
//        }
        getPage(pageable);
        PageInfo<Visa> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(Visa.class, criteria)));
        List<Visa> visaList = page.getList();
        if (ObjectUtil.isNotEmpty(visaList)) {
            for (Visa visa : visaList) {
                ProjectGroup group = Optional.ofNullable(groupMap.get(visa.getVisaId())).orElseGet(ProjectGroup::new);
                visa.setNodeIsSubmit(group.getIsSubmit());
                visa.setNodeStatus(group.getNodeStatus());
            }
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", visaMapper.toDto(visaList));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<VisaDto> queryAll(VisaQueryCriteria criteria) {
        return visaMapper.toDto(list(QueryHelpPlus.getPredicate(Visa.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisaDto findById(Long visaId) {
        //查询当前人的角色
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        Visa visa = Optional.ofNullable(getById(visaId)).orElseGet(Visa::new);
        ValidationUtil.isNull(visa.getVisaId(), getEntityClass().getSimpleName(), "id", visaId);
        LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(InstructionSheet.class)
                .eq(InstructionSheet::getVisaId, visaId)
                .eq(InstructionSheet::getIsVisa, true);
        List<InstructionSheet> instructionSheets = instructionSheetRepository.selectList(queryWrapper);
        if (ObjectUtils.isNotEmpty(instructionSheets)) {
            instructionSheets.forEach(p -> {
                List<ProjectInfoSimple> info = instructionSheetRepository.getProjectInfoBySheetId(p.getSheetId());
                if (ObjectUtils.isNotEmpty(info)) {
                    p.setSimpleProjects(info);
                }
            });
        }
        visa.setInstructionSheets(instructionSheets);
//        LambdaQueryWrapper projectGroupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
//                .eq(ProjectGroup::getOrderId,visaId)
//                .eq(ProjectGroup::getNodeLevel,2)
//                .eq(ProjectGroup::getIsDelete,false);
//        ProjectGroup projectGroup = projectGroupRepository.selectOne(projectGroupQuery);
        ProjectGroup projectGroup = projectGroupRepository.getLevelTwoNodeInfo(visaId, "con-00125", roleIds);
        if (ObjectUtil.isEmpty(projectGroup)) {
            throw new BadRequestException("参数 sheetId,nodeCode 错误，无法获取数据！");
        }
        visa.setProjectGroupId(projectGroup.getProjectGroupId());
        visa.setRoleCode(projectGroup.getRoleCode());
        //根据rolecode查询任务责任人
        String remark = projectStakeholdersService.getTaskPersonByRoleCode(projectGroup.getRoleCode(), visaId);
        visa.setRemark(remark);
        visa.setNodeStatus(projectGroup.getNodeStatus());
        visa.setNodeIsfin(projectGroup.getNodeIsfin());
        visa.setNodeIsSubmit(projectGroup.getIsSubmit());
        visa.setIsHidden(projectGroup.getIsHidden());
        visa.setIsRead(projectGroup.getIsRead());
        visa.setIsWrite(projectGroup.getIsWrite());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (ObjectUtils.isNotEmpty(projectGroup.getActualEndDate())) {
            visa.setActualEndDate(dateFormat.format(projectGroup.getActualEndDate()));
        }
        visa.setNodeCode(projectGroup.getNodeCode());
        LambdaQueryWrapper approveQuery = Wrappers.lambdaQuery(ProjectApprove.class)
                .eq(ProjectApprove::getOrderId, visaId);
        List<ProjectApprove> approves = projectApproveRepository.selectList(approveQuery);
        if (ObjectUtils.isNotEmpty(approves)) {
            ProjectApprove approve = approves.get(0);
            visa.setNodeId(approve.getNodeId());
        }
        return visaMapper.toDto(visa);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisaDto create(Visa resources) {
        List<InstructionSheet> instructionSheets = resources.getInstructionSheets();
        if (ObjectUtils.isEmpty(instructionSheets) || instructionSheets.size() <= 0) {
            throw new BadRequestException("请先选择指令单！！！");
        }

        Long visaId = updateData(resources);

        return findById(visaId);
    }

    @Override
    public void createStakeholders(Long orderId, String roleName, Long userId) {
        ProjectStakeholders projectStakeholders = new ProjectStakeholders();
        Long uuid = SnowFlakeUtil.getInstance().nextLongId();
        projectStakeholders.setStakeholderId(uuid);
        projectStakeholders.setOrderId(orderId);
        if (userId != 0) {
            projectStakeholders.setUserId(userId);
            //若干系人选择了人，则将干系人状态改为正常
            projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            if (JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(roleName)) {
                projectStakeholders.setIsNotshow(Boolean.TRUE);
            }
        }
        Role role = projectInfoRepository.getRoleByRoleCode(roleName);
        if (role != null) {
            projectStakeholders.setRoleId(role.getId());
            projectStakeholders.setRoleCode(role.getRoleCode());
            projectStakeholders.setRoleName(role.getName());
            //查找之前是否有插入
            LambdaQueryWrapper<ProjectStakeholders> projectStakeholdersLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
            projectStakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getRoleCode, role.getRoleCode())
                    .eq(ProjectStakeholders::getOrderId, orderId)
                    .eq(ProjectStakeholders::getShakeholderStatus, "in_term");
            ProjectStakeholders stakeholdersQuery = projectStakeholdersRepository.selectOne(projectStakeholdersLambdaQueryWrapper);
            if (stakeholdersQuery != null) {
                Long oldUserId = stakeholdersQuery.getUserId();
                if (oldUserId != null) {
                    //当前人跟之前的人不一样
                    if (!oldUserId.equals(userId)) {
                        //若当前操作是换人
                        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
                        //修改状态为离项
                        stakeholdersQuery.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
                        stakeholdersQuery.setReason("系统页面更换干系人");
                        stakeholdersQuery.setLeaveTime(nowTime);
                        projectStakeholdersRepository.updateById(stakeholdersQuery);


                        //新增干系人
                        projectStakeholders.setStakeholderId(null);
                        projectStakeholders.setReason(null);
                        projectStakeholders.setJoinTime(nowTime);
                        projectStakeholders.setIsDelete(Boolean.FALSE);
                        projectStakeholdersRepository.insert(projectStakeholders);
                        //更换node负责人
                        // projectNodeInfoService.updateRoleCode(projectId, userid, oldUserId);
                        //更换当前人的所有任务
                        projectApproveDetailService.changeApproveOrderUser(orderId, userId, oldUserId);

                    }
                } else {
                    //当前操作是把人插入
                    projectStakeholders.setJoinTime(Timestamp.valueOf(DateUtil.parseLocalDateTimeFormatyMd(DateUtil.getNowDate())));
                    projectStakeholders.setIsDelete(false);
                    projectStakeholders.setIsApprove(false);
                    projectStakeholders.setStakeholderId(stakeholdersQuery.getStakeholderId());
                    projectStakeholdersRepository.updateById(projectStakeholders);
                }
            } else {
                projectStakeholders.setJoinTime(Timestamp.valueOf(DateUtil.parseLocalDateTimeFormatyMd(DateUtil.getNowDate())));
                projectStakeholders.setIsDelete(false);
                projectStakeholders.setIsApprove(false);
                projectStakeholdersRepository.insert(projectStakeholders);
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Visa resources) {
        Visa visa = Optional.ofNullable(getById(resources.getVisaId())).orElseGet(Visa::new);
        ValidationUtil.isNull(visa.getVisaId(), "Visa", "id", resources.getVisaId());
        visa.copy(resources);
        updateById(visa);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            visaRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<VisaDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (VisaDto visa : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("申请人姓名", visa.getName());
            map.put("申请人工号", visa.getCode());
            map.put("申请人部门", visa.getDept());
            map.put("申请人岗位", visa.getJob());
            map.put("申请人职级", visa.getRank());
            map.put("联系人电话", visa.getPhone());
            map.put("申请日期", visa.getApplyDate());
            map.put("请示内容", visa.getContent());
            map.put("创建人", visa.getCreateBy());
            map.put("创建时间", visa.getCreateTime());
            map.put("更新人", visa.getUpdateBy());
            map.put("更新时间", visa.getUpdateTime());
            map.put("是否使用", visa.getIsEnabled());
            map.put("是否删除", visa.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Map<String, Object> queryApprovePassInfo(VisaDto visaDto, Pageable pageable) {
        getPage(pageable);
        LambdaQueryWrapper QueryWrapper = Wrappers.lambdaQuery(Visa.class)
                .eq(Visa::getIsDelete, Boolean.FALSE);
        List<Visa> visas = visaRepository.selectList(QueryWrapper);
        List<Visa> list = visas.stream().filter(s -> {
            LambdaQueryWrapper projectGroupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getOrderId, s.getVisaId())
                    .eq(ProjectGroup::getNodeLevel, 2)
                    .eq(ProjectGroup::getIsDelete, false);
            ProjectGroup projectGroup = projectGroupRepository.selectOne(projectGroupQuery);
            s.setProjectGroupId(projectGroup.getProjectGroupId());
            if (s.getIsSignReport()) {
                LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
                        .eq(ProjectApprove::getOrderId, s.getSignReportId())
                        .orderByDesc(ProjectApprove::getApproveStart);
                List<ProjectApprove> approveList = projectApproveRepository.selectList(queryWrapper);
                ProjectApprove approve = approveList.get(0);
                if (ObjectUtils.isNotEmpty(approve) && JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(approve.getApproveResult())) {
                    s.setIsSignReport(false);
                    s.setSignReportId(null);
                    visaRepository.updateById(s);
                    return true;
                }
            }
            if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(projectGroup.getNodeStatus())) {
                LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
                        .eq(ProjectApprove::getOrderId, s.getVisaId());
//                ProjectApprove approvePass = projectApproveRepository.selectOne(queryWrapper);
                List<ProjectApprove> approveList = projectApproveRepository.selectList(queryWrapper);
                List<ProjectApprove> approvePassList = approveList.stream().filter(p -> ObjectUtils.isNotEmpty(p) && JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(p.getApproveResult())).collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(approvePassList) && approvePassList.size() > 0) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        for (Visa visa : list) {
            List<ProjectInfoSimple> info = visaRepository.getProjectInfoByVisaId(visa.getVisaId());
            if (ObjectUtils.isNotEmpty(info)) {
                visa.setSimpleProjects(info);
            }
        }
        PageInfo<Visa> page = new PageInfo<>(list);
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", visaMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    private Long updateData(Visa visa) {
        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        List<InstructionSheet> instructionSheets = visa.getInstructionSheets();
//        if (ObjectUtils.isEmpty(instructionSheets) || instructionSheets.size() <= 0) {
//            throw new BadRequestException("请先选择指令单！！！");
//        }
        Long visaId = visa.getVisaId();
        if (ObjectUtils.isEmpty(visaId)) {
            visaId = snowflake.nextId();
            visa.setVisaId(visaId);

            String key = "QZ" + current;
            Integer value = (Integer) redisUtils.get(key);
            String num;
            if (null == value) {
                redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
                num = "001";
            } else {
                Long increment = redisUtils.increment(key);
                StringBuilder s = new StringBuilder(increment.toString());
                for (int i = s.length(); i < 3; i++) {
                    s.insert(0, "0");
                }
                num = s.toString();
            }
            String applyNo = key + num;
            visa.setApplyNo(applyNo);
            User one = Optional.ofNullable(userRepository.getOne(SecurityUtils.getCurrentUserId())).orElseGet(User::new);
            visa.setName(one.getNickName());
            visa.setCode(one.getCode());
            visa.setCreateBy(one.getId());
            visaId = visa.getVisaId();

            List<ProjectGroup> groupInfoForCount = new LinkedList<>();
            LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(TemplateGroup.class)
                    .eq(TemplateGroup::getTemplateCode, "visa");
            List<TemplateGroup> templateGroupList = templateGroupRepository.selectList(queryWrapper);
            for (TemplateGroup templateGroup : templateGroupList) {
                ProjectGroup projectGroup = new ProjectGroup();
//            projectGroup.setProjectId(projectId);
                projectGroup.setOrderId(visa.getVisaId());
                BeanUtils.copyProperties(templateGroup, projectGroup);
                projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                projectGroup.setIsDelete(false);
                projectGroup.setIsEnabled(true);
                projectGroup.setCreateBy(null);
                projectGroup.setCreateTime(null);
                Snowflake snowflake1 = IdUtil.getSnowflake(1, 1);
                Long projectGroupId = snowflake1.nextId();
                projectGroup.setProjectGroupId(projectGroupId);
                if (templateGroup.getNodeLevel() == 2) {
                    // 审批模板入库
                    List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(templateGroup.getTemplateId(), templateGroup.getTemplateGroupId());
                    //若存在审批节点，则复制当前节点数据
                    if (ObjectUtils.isNotNull(appRelations)) {
                        projectAppTemplateService.copyApproveTemplate(projectGroup, appRelations.get(0));
                    }
                }
                //查看当前二级节点有没有联合关系数据，存在就把 联合关系模版入库
                projectJointTaskOnfigurationService.upJointTask(projectGroup.getNodeCode(),projectGroup.getProjectId());




                groupInfoForCount.add(projectGroup);
            }
            projectGroupService.saveBatch(groupInfoForCount);
        }

        //创建审批干系人
        createStakeholders(visaId, JhSystemEnum.JobEnum.QYJL.getKey(), visa.getAreaManager());
        createStakeholders(visaId, JhSystemEnum.JobEnum.GCJL.getKey(), visa.getProjectManager());

        LambdaUpdateWrapper sheetUpdateOrigin = Wrappers.lambdaUpdate(InstructionSheet.class)
                .eq(InstructionSheet::getVisaId, visaId)
                .eq(InstructionSheet::getIsVisa, Boolean.TRUE)
                .set(InstructionSheet::getVisaId, null).set(InstructionSheet::getIsVisa, Boolean.FALSE);
        instructionSheetService.update(sheetUpdateOrigin);
        if (ObjectUtils.isNotEmpty(instructionSheets) && instructionSheets.size() > 0) {
            for (InstructionSheet p : instructionSheets) {
                LambdaUpdateWrapper sheetUpdate = Wrappers.lambdaUpdate(InstructionSheet.class)
                        .eq(InstructionSheet::getSheetId, p.getSheetId())
                        .eq(InstructionSheet::getIsVisa, Boolean.FALSE)
                        .set(InstructionSheet::getVisaId, visaId).set(InstructionSheet::getIsVisa, Boolean.TRUE);
                instructionSheetService.update(sheetUpdate);
            }
        }

        saveOrUpdate(visa);
        return visaId;
    }

    @Override
    public VisaDto submit(VisaDto visaDto) {
//        if (ObjectUtils.isNotEmpty(visaDto.getIsSubmit()) && visaDto.getIsSubmit()){
//            throw new BadRequestException("已提交，不能再次提交！！！");
//        }

        //保存数据
        Visa visa = visaMapper.toEntity(visaDto);
        Long visaId = updateData(visa);
        visaDto = findById(visaId);

        LambdaQueryWrapper<ProjectGroup> gLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != visaDto.getProjectGroupId(), ProjectGroup::getProjectGroupId, visaDto.getProjectGroupId());
        ProjectGroupDto projectGroupDto = projectGroupMapper.toDto(projectGroupRepository.selectOne(gLambdaQueryWrapper));
        if (null == projectGroupDto || null == projectGroupDto.getNodeLevel() || projectGroupDto.getNodeLevel() != 2) {
            throw new BadRequestException("传递的nodeId 节点参数值 不正确，不是2级节点");
        }

        if (ObjectUtils.isNotEmpty(projectGroupDto.getIsSubmit()) && projectGroupDto.getIsSubmit()) {
            throw new BadRequestException("已提交，不能再次提交！！！");
        }

        //将指令单为已提交
        visaDto.setIsSubmit(true);
        visaRepository.updateById(visaMapper.toEntity(visaDto));
        projectGroupService.updateGroupSubmit(projectGroupMapper.toEntity(projectGroupDto));

        //创建审批
        //查询是否有关联的审批节点
        Boolean hasApprove = projectAppTemplateService.hasApprove(visaDto.getProjectGroupId());
        if (hasApprove) {
            projectApproveService.createOrderApprove(projectGroupDto);
            //新增审批数据
            logger.info(projectGroupDto.getNodeName() + "节点有审批");
        } else {
            projectGroupService.updateGroupNext(projectGroupMapper.toEntity(projectGroupDto));
        }
        return visaDto;
    }

    @Override
    public void downloadExcel(Long visaId, HttpServletResponse response) throws IOException {
        VisaDto visaDto = visaService.findById(visaId);
        VisaExcelDto visaExcelDto = new VisaExcelDto();
        visaExcelDto.setApplyNo(visaDto.getApplyNo());
        visaExcelDto.setApplyDate(visaDto.getApplyDate());
        visaExcelDto.setContent(visaDto.getContent());
        LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(InstructionSheet.class)
                .eq(InstructionSheet::getVisaId, visaId)
                .eq(InstructionSheet::getIsVisa, true);
        List<InstructionSheet> instructionSheets = instructionSheetRepository.selectList(queryWrapper);
        for (InstructionSheet instructionSheet : instructionSheets) {
            Long sheetId = instructionSheet.getSheetId();
            LambdaQueryWrapper infoWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                    .eq(ProjectInfo::getSheetId, sheetId)
                    .eq(ProjectInfo::getIsSheet, true);
            List<ProjectInfo> projectInfos = projectInfoRepository.selectList(infoWrapper);
            for (ProjectInfo projectInfo : projectInfos) {
                if (!Objects.isNull(projectInfo)) {
                    visaExcelDto.setStoreName(projectInfo.getStoreName());
                    visaExcelDto.setProjectName(projectInfo.getProjectName());
                    String projectType = projectInfo.getProjectType();
                    if (!Objects.isNull(projectType)) {
                        if (KidsSystemEnum.ProjectTypeEnum.NEW.getValue().equals(projectType)) {
                            visaExcelDto.setProjectType(KidsSystemEnum.ProjectTypeEnum.NEW.getLabel());
                        }
                        if (KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue().equals(projectType)) {
                            visaExcelDto.setProjectType(KidsSystemEnum.ProjectTypeEnum.MAJOR.getLabel());
                        }
                        if (KidsSystemEnum.ProjectTypeEnum.MINOR.getValue().equals(projectType)) {
                            visaExcelDto.setProjectType(KidsSystemEnum.ProjectTypeEnum.MINOR.getLabel());
                        }
                        if (KidsSystemEnum.ProjectTypeEnum.CLOSE.getValue().equals(projectType)) {
                            visaExcelDto.setProjectType(KidsSystemEnum.ProjectTypeEnum.CLOSE.getLabel());
                        }
                    }
                    Long projectId = projectInfo.getProjectId();
                    LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoWrapper = new LambdaQueryWrapper<>();
                    projectNodeInfoWrapper.eq(ProjectNodeInfo::getProjectId, projectId);
                    List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(projectNodeInfoWrapper);
                    for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
                        if (!Objects.isNull(projectNodeInfo)) {
                            if ("con-0010302".equals(projectNodeInfo.getNodeCode())) {
                                if (projectNodeInfo.getRemark() != null) {
                                    SupplierInfoDto supplierId = supplierInfoService.findById(Long.valueOf(projectNodeInfo.getRemark()));
                                    visaExcelDto.setConstructionUnit(supplierId.getSupNameCn());
                                }
                            }
                        }
                    }
                }
            }
            LambdaQueryWrapper queryApprove = Wrappers.lambdaQuery(ProjectApprove.class)
                    .eq(ProjectApprove::getOrderId, sheetId);
            List<ProjectApproveDto> approve = Optional.ofNullable(projectApproveMapper.toDto(projectApproveRepository.selectList(queryApprove))).orElseGet(LinkedList::new);
            for (ProjectApproveDto projectApproveDto : approve) {
                Long approveId = projectApproveDto.getApproveId();
                LambdaQueryWrapper queryDetail = Wrappers.lambdaQuery(ProjectApproveDetail.class)
                        .eq(ProjectApproveDetail::getApproveId, approveId);
                List<ProjectApproveDetailDto> detailList = projectApproveDetailMapper.toDto(projectApproveDetailRepository.selectList(queryDetail));
                for (ProjectApproveDetailDto projectApproveDetailDto : detailList) {
                    if (null != projectApproveDetailDto.getApproveUser()) {
                        UserDto UserDto = userService.findById(projectApproveDetailDto.getApproveUser());
                        if (null != UserDto) {
                            visaExcelDto.setApproveRole(UserDto.getUsername());
                            if (StringUtils.isNotEmpty(UserDto.getUsername()) && !UserDto.getUsername().equals(UserDto.getNickName())) {
                                visaExcelDto.setApproveRole(UserDto.getUsername() + "-" + UserDto.getNickName());
                            }
                        }
                        if (ObjectUtil.isNotEmpty(projectApproveDetailDto.getApproveRole())) {
                            Role roleByRoleId = roleRepository.findRoleByRoleId(Long.parseLong(projectApproveDetailDto.getApproveRole()));
                            if (roleByRoleId != null) {
                                visaExcelDto.setApproveRoleName(roleByRoleId.getName());
                            }
                        }
                    }
                    String approveStatus = projectApproveDetailDto.getApproveStatus();
                    if (!Objects.isNull(approveStatus)) {
                        if (JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey().equals(approveStatus)) {
                            visaExcelDto.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getSpec());
                        }
                        if (JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey().equals(approveStatus)) {
                            visaExcelDto.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getSpec());
                        }
                    }
                    String approveResult = projectApproveDetailDto.getApproveResult();
                    if (!Objects.isNull(approveResult)) {
                        if (JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getSpec());
                        }
                        if (JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getSpec());
                        }
                        if (JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getSpec());
                        }
                        if (JhSystemEnum.approveResultEnum.APPROVE_REJECT.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_REJECT.getSpec());
                        }
                        if (JhSystemEnum.approveResultEnum.APPROVE_DIS.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_DIS.getSpec());
                        }
                        if (JhSystemEnum.approveResultEnum.APPROVE_STOP.getKey().equals(approveResult)) {
                            visaExcelDto.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_STOP.getSpec());
                        }
                    }
                    visaExcelDto.setApproveEnd(projectApproveDetailDto.getApproveEnd());
                    visaExcelDto.setApproveOption(projectApproveDetailDto.getApproveOption());
                }
            }
        }
        UserDto areaManager = userService.findById(visaDto.getAreaManager());
        visaExcelDto.setName(visaDto.getName());
        visaExcelDto.setAreaManager(areaManager.getNickName());

        List<Map<String, Object>> visaMapList = new ArrayList<>();
        String title = "工程签证单";
        HashMap<String, Object> outerMap = new HashMap<>();
        outerMap.put("title", title);
        Map<String, Object> visaMap = new HashMap<>();
        visaMap.put("applyNo", visaExcelDto.getApplyNo());
        visaMap.put("constructionUnit", visaExcelDto.getConstructionUnit());
        visaMap.put("applyDate", visaExcelDto.getApplyDate());
        visaMap.put("storeName", visaExcelDto.getStoreName());
        visaMap.put("projectName", visaExcelDto.getProjectName());
        visaMap.put("projectType", visaExcelDto.getProjectType());
        visaMap.put("content", visaExcelDto.getContent());
        visaMap.put("approveRoleName", visaExcelDto.getApproveRoleName());
        visaMap.put("approveRole", visaExcelDto.getApproveRole());
        visaMap.put("approveStatus", visaExcelDto.getApproveStatus());
        visaMap.put("approveResult", visaExcelDto.getApproveResult());
        visaMap.put("approveEnd", visaExcelDto.getApproveEnd());
        visaMap.put("approveOption", visaExcelDto.getApproveOption());
        visaMap.put("name", visaExcelDto.getName());
        visaMap.put("areaManager", visaExcelDto.getAreaManager());
        visaMapList.add(visaMap);

        ExcelUtils.exportToTemplate(response, "excel/签证导出模版.xlsx", title, outerMap, visaMapList);
    }
}