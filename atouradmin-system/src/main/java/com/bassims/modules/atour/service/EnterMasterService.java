package com.bassims.modules.atour.service;

import com.bassims.modules.atour.domain.ProjectGroup;

/**
 * 入主档接口
 */
public interface EnterMasterService {

    //合同 证照 人员信息补入主档
    Boolean enterMaster();

    //能源信息补入主档（灯具）
    Boolean enterMasterEnergyConsume();
    Boolean enterMasterEnergyConsume(ProjectGroup projectGroup);

    //空调及其他能源信息补入主档
    Boolean enterMasterEnergyConsumeOther();
    Boolean enterMasterEnergyConsumeOther(ProjectGroup projectGroup);

    //空调能源信息补入主档
    Boolean enterMasterEnergyConsumeAirCondition();
    Boolean enterMasterEnergyConsumeAirCondition(ProjectGroup projectGroup);


    //设备信息补入主档
    Boolean enterMasterDeviceInfo();
    Boolean enterMasterDeviceInfo(ProjectGroup projectGroup);


    //门店订单数据及门店订单详情补入主档
    Boolean enterMasterOrderData();

    Boolean enterMasterOrderData(ProjectGroup projectGroup);

    //图纸信息补入主档
    Boolean enterMasterDrawingInfo();

    Boolean enterMasterDrawingInfo(ProjectGroup projectGroup);

    //证照信息补入主档
    Boolean enterMasterLicenseInfo();
    Boolean enterMasterLicenseInfo(ProjectGroup projectGroup);

}
