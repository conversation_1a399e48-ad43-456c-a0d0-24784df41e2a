/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.HlmProjectInfoOwner;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.HlmProjectInfoOwnerRepository;
import com.bassims.modules.atour.service.HlmProjectInfoOwnerService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.HlmProjectInfoOwnerDto;
import com.bassims.modules.atour.service.dto.HlmProjectInfoOwnerQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.HlmProjectInfoOwnerMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-11-06
**/
@Service
public class HlmProjectInfoOwnerServiceImpl extends BaseServiceImpl<HlmProjectInfoOwnerRepository,HlmProjectInfoOwner> implements HlmProjectInfoOwnerService {

    private static final Logger logger = LoggerFactory.getLogger(HlmProjectInfoOwnerServiceImpl.class);

    @Autowired
    private HlmProjectInfoOwnerRepository hlmProjectInfoOwnerRepository;
    @Autowired
    private HlmProjectInfoOwnerMapper hlmProjectInfoOwnerMapper;

    @Override
    public Map<String,Object> queryAll(HlmProjectInfoOwnerQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<HlmProjectInfoOwner> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(HlmProjectInfoOwner.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", hlmProjectInfoOwnerMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<HlmProjectInfoOwnerDto> queryAll(HlmProjectInfoOwnerQueryCriteria criteria){
        return hlmProjectInfoOwnerMapper.toDto(list(QueryHelpPlus.getPredicate(HlmProjectInfoOwner.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HlmProjectInfoOwnerDto findById(Long ownerId) {
        HlmProjectInfoOwner hlmProjectInfoOwner = Optional.ofNullable(getById(ownerId)).orElseGet(HlmProjectInfoOwner::new);
        ValidationUtil.isNull(hlmProjectInfoOwner.getOwnerId(),getEntityClass().getSimpleName(),"ownerId",ownerId);
        return hlmProjectInfoOwnerMapper.toDto(hlmProjectInfoOwner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HlmProjectInfoOwnerDto create(HlmProjectInfoOwner resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setOwnerId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getOwnerId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(HlmProjectInfoOwner resources) {
        HlmProjectInfoOwner hlmProjectInfoOwner = Optional.ofNullable(getById(resources.getOwnerId())).orElseGet(HlmProjectInfoOwner::new);
        ValidationUtil.isNull( hlmProjectInfoOwner.getOwnerId(),"HlmProjectInfoOwner","id",resources.getOwnerId());
        hlmProjectInfoOwner.copy(resources);
        updateById(hlmProjectInfoOwner);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long ownerId : ids) {
            hlmProjectInfoOwnerRepository.deleteById(ownerId);
        }
    }

    @Override
    public void download(List<HlmProjectInfoOwnerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (HlmProjectInfoOwnerDto hlmProjectInfoOwner : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目id", hlmProjectInfoOwner.getProjectId());
            map.put("酒店ID", hlmProjectInfoOwner.getHotelId());
            map.put("用户名", hlmProjectInfoOwner.getUsername());
            map.put("手机号", hlmProjectInfoOwner.getMobile());
            map.put("邮箱", hlmProjectInfoOwner.getEmail());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}