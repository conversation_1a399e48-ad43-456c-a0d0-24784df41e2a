/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.StoreInterfaceInfo;
import com.bassims.modules.atour.service.StoreInterfaceInfoService;
import com.bassims.modules.atour.service.dto.StoreInterfaceInfoDto;
import com.bassims.modules.atour.service.dto.StoreInterfaceInfoQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-11-23
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_store_interface_info管理")
@RequestMapping("/api/storeInterfaceInfo")
public class StoreInterfaceInfoController {

    private static final Logger logger = LoggerFactory.getLogger(StoreInterfaceInfoController.class);

    private final StoreInterfaceInfoService storeInterfaceInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, StoreInterfaceInfoQueryCriteria criteria) throws IOException {
        storeInterfaceInfoService.download(storeInterfaceInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<StoreInterfaceInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_store_interface_info")
    @ApiOperation("查询t_store_interface_info")
    public ResponseEntity<Object> query(StoreInterfaceInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(storeInterfaceInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<StoreInterfaceInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_store_interface_info")
    @ApiOperation("查询t_store_interface_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(storeInterfaceInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_store_interface_info")
    @ApiOperation("新增t_store_interface_info")
    public ResponseEntity<Object> create(@Validated @RequestBody StoreInterfaceInfo resources){
        return new ResponseEntity<>(storeInterfaceInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_store_interface_info")
    @ApiOperation("修改t_store_interface_info")
    public ResponseEntity<Object> update(@Validated @RequestBody StoreInterfaceInfo resources){
        storeInterfaceInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_store_interface_info")
    @ApiOperation("删除t_store_interface_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        storeInterfaceInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/koaStore")
    @Log("查询koa门店数据")
    @ApiOperation("查询koa门店数据")
    public ResponseEntity<Object> getKoaStore(){
        return new ResponseEntity<>(storeInterfaceInfoService.getStoreMasterInfo(),HttpStatus.OK);
    }

    @GetMapping(value = "/storeInfoList")
    @Log("查询门店临时表数据")
    @ApiOperation("查询门店临时表数据")
    public ResponseEntity<Object> getStoreInfoList(String storeName){
        return new ResponseEntity<>(storeInterfaceInfoService.getStoreListByName(storeName),HttpStatus.OK);
    }

}