/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.SysUsersCity;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.SysUsersCityRepository;
import com.bassims.modules.atour.service.SysUsersCityService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.SysUsersCityDto;
import com.bassims.modules.atour.service.dto.SysUsersCityQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SysUsersCityMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-04-22
**/
@Service
public class SysUsersCityServiceImpl extends BaseServiceImpl<SysUsersCityRepository,SysUsersCity> implements SysUsersCityService {

    private static final Logger logger = LoggerFactory.getLogger(SysUsersCityServiceImpl.class);

    @Autowired
    private SysUsersCityRepository sysUsersCityRepository;
    @Autowired
    private SysUsersCityMapper sysUsersCityMapper;

    @Override
    public Map<String,Object> queryAll(SysUsersCityQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SysUsersCity> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(SysUsersCity.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", sysUsersCityMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SysUsersCityDto> queryAll(SysUsersCityQueryCriteria criteria){
        return sysUsersCityMapper.toDto(list(QueryHelpPlus.getPredicate(SysUsersCity.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUsersCityDto findById(Long userCityId) {
        SysUsersCity sysUsersCity = Optional.ofNullable(getById(userCityId)).orElseGet(SysUsersCity::new);
        ValidationUtil.isNull(sysUsersCity.getUserCityId(),getEntityClass().getSimpleName(),"userCityId",userCityId);
        return sysUsersCityMapper.toDto(sysUsersCity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUsersCityDto create(SysUsersCity resources) {
        save(resources);
        return findById(resources.getUserCityId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUsersCity resources) {
        SysUsersCity sysUsersCity = Optional.ofNullable(getById(resources.getUserCityId())).orElseGet(SysUsersCity::new);
        ValidationUtil.isNull( sysUsersCity.getUserCityId(),"SysUsersCity","id",resources.getUserCityId());
        sysUsersCity.copy(resources);
        updateById(sysUsersCity);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long userCityId : ids) {
            sysUsersCityRepository.deleteById(userCityId);
        }
    }

    @Override
    public void download(List<SysUsersCityDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysUsersCityDto sysUsersCity : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("城市id", sysUsersCity.getAreaCode());
            map.put("用户id", sysUsersCity.getUserId());
            map.put("创建时间", sysUsersCity.getCreateTime());
            map.put("创建人", sysUsersCity.getCreateBy());
            map.put("更新时间", sysUsersCity.getUpdateTime());
            map.put("更新人", sysUsersCity.getUpdateBy());
            map.put("是否可用", sysUsersCity.getIsEnabled());
            map.put("是否删除", sysUsersCity.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}