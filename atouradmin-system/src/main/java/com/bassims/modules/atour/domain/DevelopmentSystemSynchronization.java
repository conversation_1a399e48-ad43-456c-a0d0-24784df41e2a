package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * 开发系统同步表
 */
@Data
@TableName(value="t_development_system_synchronization")
public class DevelopmentSystemSynchronization implements Serializable {

    @TableId(value = "development_system_synchronization_id")
    @ApiModelProperty(value = "主键id")
    private Long developmentSystemSynchronizationId;

    @TableField(value = "project_ID")
    @ApiModelProperty(value = "项目ID")
    private String projectID;

    @TableField(value = "entry_name")
    @ApiModelProperty(value = "项目名称")
    private String entryName;

    @TableField(value = "province")
    @ApiModelProperty(value = "省份")
    private String province;

    @TableField(value = "city")
    @ApiModelProperty(value = "城市")
    private String city;

    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌默认存code，不展示中文")
    private String brand;

    @TableField(value = "executive_standards")
    @ApiModelProperty(value = "执行标准")
    private String executiveStandards;

    @TableField(value = "decision_opinions")
    @ApiModelProperty(value = "决策意见")
    private String decisionOpinions;

    @TableField(value = "description_of_license_situation")
    @ApiModelProperty(value = "证照情况描述")
    private String descriptionOfLicenseSituation;

    @TableField(value = "legal_agreement_on_project_content")
    @ApiModelProperty(value = "法务约定工程内容")
    private String legalAgreementOnProjectContent;

    @TableField(value = "legal_agreement_on_procurement_content")
    @ApiModelProperty(value = "法务约定采购内容")
    private String legalAgreementOnProcurementContent;

    @TableField(value = "number_of_rooms")
    @ApiModelProperty(value = "房间数")
    private BigDecimal numberOfRooms;

    @TableField(value = "signing_time")
    @ApiModelProperty(value = "签约时间")
    private Timestamp signingTime;

    @TableField(value = "red_line_description")
    @ApiModelProperty(value = "红线描述")
    private String redLineDescription;

    @TableField(value = "description_of_special_matters_in_the_construction_memorandum")
    @ApiModelProperty(value = "营建备忘录特殊事项描述")
    private String descriptionOfSpecialMattersInTheConstructionMemorandum;

    @TableField(value = "owner")
    @ApiModelProperty(value = "业主方")
    private String owner;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    public void copy(DevelopmentSystemSynchronization source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
