/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.ProjectSystemSelfInspection;
import com.bassims.modules.atour.service.ProjectSystemSelfInspectionService;
import com.bassims.modules.atour.service.dto.ProjectSystemSelfInspectionDto;
import com.bassims.modules.atour.service.dto.ProjectSystemSelfInspectionQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-01-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "竣工系统自检项目表管理")
@RequestMapping("/api/projectSystemSelfInspection")
public class ProjectSystemSelfInspectionController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSystemSelfInspectionController.class);

    private final ProjectSystemSelfInspectionService projectSystemSelfInspectionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectSystemSelfInspectionQueryCriteria criteria) throws IOException {
        projectSystemSelfInspectionService.download(projectSystemSelfInspectionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProjectSystemSelfInspectionDto>>}
    */
    @GetMapping("/list")
    @Log("查询竣工系统自检项目表")
    @ApiOperation("查询竣工系统自检项目表")
    public ResponseEntity<Object> query(ProjectSystemSelfInspectionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectSystemSelfInspectionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProjectSystemSelfInspectionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询竣工系统自检项目表")
    @ApiOperation("查询竣工系统自检项目表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectSystemSelfInspectionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增竣工系统自检项目表")
    @ApiOperation("新增竣工系统自检项目表")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectSystemSelfInspection resources){
        return new ResponseEntity<>(projectSystemSelfInspectionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改竣工系统自检项目表")
    @ApiOperation("修改竣工系统自检项目表")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectSystemSelfInspection resources){
        projectSystemSelfInspectionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除竣工系统自检项目表")
    @ApiOperation("删除竣工系统自检项目表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectSystemSelfInspectionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/getList")
    @Log("查询竣工系统自检项目表")
    @ApiOperation("查询竣工系统自检项目表")
    public ResponseEntity<Object> getList(ProjectSystemSelfInspectionQueryCriteria criteria){
        return new ResponseEntity<>(projectSystemSelfInspectionService.getList(criteria),HttpStatus.OK);
    }
}