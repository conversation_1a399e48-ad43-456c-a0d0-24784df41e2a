package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.FirstFinanceReport;
import com.bassims.modules.atour.service.dto.FirstFinanceReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 甲供材资产数量报表
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FirstFinanceReportMapper extends BaseMapper<FirstFinanceReportDto, FirstFinanceReport> {

}
