/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ApprovedForm;
import com.bassims.modules.atour.repository.ApprovedFormRepository;
import com.bassims.modules.atour.service.ApprovedFormService;
import com.bassims.modules.atour.service.dto.ApprovedFormDto;
import com.bassims.modules.atour.service.dto.ApprovedFormQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ApprovedFormMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-04-18
**/
@Service
public class ApprovedFormServiceImpl extends BaseServiceImpl<ApprovedFormRepository,ApprovedForm> implements ApprovedFormService {

    private static final Logger logger = LoggerFactory.getLogger(ApprovedFormServiceImpl.class);

    @Autowired
    private ApprovedFormRepository approvedFormRepository;
    @Autowired
    private ApprovedFormMapper approvedFormMapper;

    @Override
    public Map<String,Object> queryAll(ApprovedFormQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ApprovedForm> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ApprovedForm.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", approvedFormMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ApprovedFormDto> queryAll(ApprovedFormQueryCriteria criteria){
        return approvedFormMapper.toDto(list(QueryHelpPlus.getPredicate(ApprovedForm.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovedFormDto findById(Long approvedId) {
        ApprovedForm approvedForm = Optional.ofNullable(getById(approvedId)).orElseGet(ApprovedForm::new);
        ValidationUtil.isNull(approvedForm.getApprovedId(),getEntityClass().getSimpleName(),"approvedId",approvedId);
        return approvedFormMapper.toDto(approvedForm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovedFormDto create(ApprovedForm resources) {
        save(resources);
        return findById(resources.getApprovedId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ApprovedForm resources) {
        ApprovedForm approvedForm = Optional.ofNullable(getById(resources.getApprovedId())).orElseGet(ApprovedForm::new);
        ValidationUtil.isNull( approvedForm.getApprovedId(),"ApprovedForm","id",resources.getApprovedId());
        approvedForm.copy(resources);
        updateById(approvedForm);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long approvedId : ids) {
            approvedFormRepository.deleteById(approvedId);
        }
    }

    @Override
    public void download(List<ApprovedFormDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ApprovedFormDto approvedForm : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("单位名称", approvedForm.getUnitName());
            map.put("决算金额", approvedForm.getFinalMoney());
            map.put("审定金额", approvedForm.getApprovedMoney());
            map.put("附件", approvedForm.getFileId());
            map.put("创建人", approvedForm.getCreateBy());
            map.put("创建时间", approvedForm.getCreateTime());
            map.put("更新人", approvedForm.getUpdateBy());
            map.put("更新时间", approvedForm.getUpdateTime());
            map.put("是否可用", approvedForm.getIsEnabled());
            map.put("是否删除", approvedForm.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}