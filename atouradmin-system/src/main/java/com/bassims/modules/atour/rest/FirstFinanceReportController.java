package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.service.FirstFinanceReportService;
import com.bassims.modules.atour.service.dto.FirstFinanceReportQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 甲供材资产数量报表
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "甲供材资产数量报表")
@RequestMapping("/api/firstFinanceReport")
public class FirstFinanceReportController {

    @Autowired
    private FirstFinanceReportService firstFinanceReportService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, FirstFinanceReportQueryCriteria criteria) throws IOException {
        firstFinanceReportService.download(response, criteria);
    }

    @Log("查询数据")
    @ApiOperation("查询数据")
    @GetMapping(value = "/queryTime")
    public ResponseEntity<Object> queryTime(FirstFinanceReportQueryCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(firstFinanceReportService.queryTime(criteria, pageable));
    }

}
