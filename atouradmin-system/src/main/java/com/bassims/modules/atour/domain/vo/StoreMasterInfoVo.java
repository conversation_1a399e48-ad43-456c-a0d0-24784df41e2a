package com.bassims.modules.atour.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title StoreMasterInfoVo
 * @date 2022/11/22 9:52
 * @description TODO
 */
@Data
public class StoreMasterInfoVo {

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 经营类型
     */
    private String businessNature;

    /**
     * 开店时间
     */
    private String actualOpenDate;

    /**
     * 决算费用
     */
    private BigDecimal finalSettlementExpenses;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目id
     */
    private String projectId;
}
