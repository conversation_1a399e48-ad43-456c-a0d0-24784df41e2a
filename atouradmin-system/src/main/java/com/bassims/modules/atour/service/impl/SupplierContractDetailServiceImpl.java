/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.modules.atour.domain.SupplierContractDetail;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.SupplierContractDetailRepository;
import com.bassims.modules.atour.service.SupplierContractDetailService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.SupplierContractDetailDto;
import com.bassims.modules.atour.service.dto.SupplierContractDetailQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SupplierContractDetailMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-04-01
**/
@Service
public class SupplierContractDetailServiceImpl extends BaseServiceImpl<SupplierContractDetailRepository,SupplierContractDetail> implements SupplierContractDetailService {

    private static final Logger logger = LoggerFactory.getLogger(SupplierContractDetailServiceImpl.class);

    @Autowired
    private SupplierContractDetailRepository supplierContractDetailRepository;
    @Autowired
    private SupplierContractDetailMapper supplierContractDetailMapper;

    @Override
    public Map<String,Object> queryAll(SupplierContractDetailQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SupplierContractDetail> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(SupplierContractDetail.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", supplierContractDetailMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SupplierContractDetailDto> queryAll(SupplierContractDetailQueryCriteria criteria){
        return supplierContractDetailMapper.toDto(list(QueryHelpPlus.getPredicate(SupplierContractDetail.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierContractDetailDto findById(Long id) {
        SupplierContractDetail supplierContractDetail = Optional.ofNullable(getById(id)).orElseGet(SupplierContractDetail::new);
        ValidationUtil.isNull(supplierContractDetail.getId(),getEntityClass().getSimpleName(),"id",id);
        return supplierContractDetailMapper.toDto(supplierContractDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierContractDetailDto create(SupplierContractDetail resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SupplierContractDetail resources) {
        SupplierContractDetail supplierContractDetail = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierContractDetail::new);
        ValidationUtil.isNull( supplierContractDetail.getId(),"SupplierContractDetail","id",resources.getId());
        supplierContractDetail.copy(resources);
        updateById(supplierContractDetail);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            supplierContractDetailRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SupplierContractDetailDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SupplierContractDetailDto supplierContractDetail : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("厂商id", supplierContractDetail.getSupplierId());
            map.put("合同开始时间", supplierContractDetail.getContractStartTime());
            map.put("合同结束时间", supplierContractDetail.getContractEndTime());
            map.put("合同附件", supplierContractDetail.getContractAppendix());
            map.put("创建人", supplierContractDetail.getCreateUser());
            map.put("创建时间", supplierContractDetail.getCreateTime());
            map.put("更新人", supplierContractDetail.getUpdateUser());
            map.put("更新时间", supplierContractDetail.getUpdateTime());
            map.put("是否删除", supplierContractDetail.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void updateData(List<SupplierContractDetailDto> supplierContractDetailDtos) {
        List<SupplierContractDetail> supplierContractDetails = supplierContractDetailMapper.toEntity(supplierContractDetailDtos);
        saveOrUpdateBatch(supplierContractDetails);

    }

    @Override
    public List<SupplierContractDetailDto> queryBySupplierId(Long supplierId) {
        LambdaQueryWrapper<SupplierContractDetail> supplierContractDetailLambdaQueryWrapper = Wrappers.lambdaQuery(SupplierContractDetail.class)
                .eq(SupplierContractDetail::getSupplierId,supplierId);
        List<SupplierContractDetail> supplierContractDetails = supplierContractDetailRepository.selectList(supplierContractDetailLambdaQueryWrapper);
        List<SupplierContractDetailDto> supplierContractDetailDtos = supplierContractDetailMapper.toDto(supplierContractDetails);
        return supplierContractDetailDtos;
    }
}