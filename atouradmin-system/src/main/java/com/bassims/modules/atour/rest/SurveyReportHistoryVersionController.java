/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.SurveyReportHistoryVersion;
import com.bassims.modules.atour.service.SurveyReportHistoryVersionService;
import com.bassims.modules.atour.service.dto.SurveyReportHistoryVersionDto;
import com.bassims.modules.atour.service.dto.SurveyReportHistoryVersionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-19
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "装饰勘测报告审批版本表管理")
@RequestMapping("/api/surveyReportHistoryVersion")
public class SurveyReportHistoryVersionController {

    private static final Logger logger = LoggerFactory.getLogger(SurveyReportHistoryVersionController.class);

    private final SurveyReportHistoryVersionService surveyReportHistoryVersionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, SurveyReportHistoryVersionQueryCriteria criteria) throws IOException {
        surveyReportHistoryVersionService.download(surveyReportHistoryVersionService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<SurveyReportHistoryVersionDto>>}
    */
    @GetMapping("/list")
    @Log("查询装饰勘测报告审批版本表")
    @ApiOperation("查询装饰勘测报告审批版本表")
    public ResponseEntity<Object> query(SurveyReportHistoryVersionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(surveyReportHistoryVersionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<SurveyReportHistoryVersionDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询装饰勘测报告审批版本表")
    @ApiOperation("查询装饰勘测报告审批版本表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(surveyReportHistoryVersionService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增装饰勘测报告审批版本表")
    @ApiOperation("新增装饰勘测报告审批版本表")
    public ResponseEntity<Object> create(@Validated @RequestBody SurveyReportHistoryVersion resources){
        return new ResponseEntity<>(surveyReportHistoryVersionService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改装饰勘测报告审批版本表")
    @ApiOperation("修改装饰勘测报告审批版本表")
    public ResponseEntity<Object> update(@Validated @RequestBody SurveyReportHistoryVersion resources){
        surveyReportHistoryVersionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除装饰勘测报告审批版本表")
    @ApiOperation("删除装饰勘测报告审批版本表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        surveyReportHistoryVersionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @GetMapping("/getVersionList")
    @Log("获取装饰勘测报告审批版本")
    @ApiOperation("获取装饰勘测报告审批版本")
    public ResponseEntity<Object> getVersionList(Long projectId, String nodeCode) {
        return new ResponseEntity<>(surveyReportHistoryVersionService.getVersionList(projectId, nodeCode), HttpStatus.OK);
    }

}