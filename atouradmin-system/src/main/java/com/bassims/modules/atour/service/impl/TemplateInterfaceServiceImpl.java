/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.TemplateInterface;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateInterfaceRepository;
import com.bassims.modules.atour.service.TemplateInterfaceService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateInterfaceDto;
import com.bassims.modules.atour.service.dto.TemplateInterfaceQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateInterfaceMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-10-20
**/
@Service
public class TemplateInterfaceServiceImpl extends BaseServiceImpl<TemplateInterfaceRepository,TemplateInterface> implements TemplateInterfaceService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateInterfaceServiceImpl.class);

    @Autowired
    private TemplateInterfaceRepository templateInterfaceRepository;
    @Autowired
    private TemplateInterfaceMapper templateInterfaceMapper;

    @Override
    public Map<String,Object> queryAll(TemplateInterfaceQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<TemplateInterface> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateInterface.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateInterfaceMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateInterfaceDto> queryAll(TemplateInterfaceQueryCriteria criteria){
        return templateInterfaceMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateInterface.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateInterfaceDto findById(Long templateInterfaceId) {
        TemplateInterface templateInterface = Optional.ofNullable(getById(templateInterfaceId)).orElseGet(TemplateInterface::new);
        ValidationUtil.isNull(templateInterface.getTemplateInterfaceId(),getEntityClass().getSimpleName(),"templateInterfaceId",templateInterfaceId);
        return templateInterfaceMapper.toDto(templateInterface);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateInterfaceDto create(TemplateInterface resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setTemplateInterfaceId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getTemplateInterfaceId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateInterface resources) {
        TemplateInterface templateInterface = Optional.ofNullable(getById(resources.getTemplateInterfaceId())).orElseGet(TemplateInterface::new);
        ValidationUtil.isNull( templateInterface.getTemplateInterfaceId(),"TemplateInterface","id",resources.getTemplateInterfaceId());
        templateInterface.copy(resources);
        updateById(templateInterface);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateInterfaceId : ids) {
            templateInterfaceRepository.deleteById(templateInterfaceId);
        }
    }

    @Override
    public void download(List<TemplateInterfaceDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateInterfaceDto templateInterface : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("节点id", templateInterface.getTemplateId());
            map.put("接口id", templateInterface.getInterfaceInfoId());
            map.put("创建时间", templateInterface.getCreateTime());
            map.put("更新时间", templateInterface.getUpdateTime());
            map.put("创建人", templateInterface.getCreateBy());
            map.put("更新人", templateInterface.getUpdateBy());
            map.put("是否启用", templateInterface.getIsEnabled());
            map.put("是否删除", templateInterface.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    public static void main(String[] args) throws Exception{
        String className = "com.bassims.modules.kids.service.impl.TemplateInterfaceServiceImpl";
        Class clazz = Class.forName(className);
        String methodName = "test";
        Object object = clazz.newInstance();
        //获取方法
        Method m = object.getClass().getDeclaredMethod(methodName, String.class);
        //调用方法
        m.invoke(object,null);

        System.out.println("结束");

    }


    public void test(){
        System.out.println("进来了");
    }

}