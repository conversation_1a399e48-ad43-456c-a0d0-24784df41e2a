package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.rest.AnonymousGetMapping;
import com.bassims.base.QueryRequest;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectGroup;
import com.bassims.modules.atour.service.KwThirdService;
import com.bassims.modules.atour.service.ProjectGroupService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.ProjectTaskService;
import com.bassims.modules.atourWithout.rocket.RocketMQUtil;
import com.bassims.modules.system.domain.TemplateConfig;
import com.bassims.modules.system.service.TemplateConfigService;
import com.bassims.utils.StringUtils;
import com.bassims.utils.excelutil.ExcelUtils;
import com.bassims.utils.excelutil.ValuePosition;
import com.bassims.utils.wordutil.WordUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Api(tags = "测试controller")
@RequestMapping("/api/test")
public class TestController {
    private static final Logger logger = LoggerFactory.getLogger(TestController.class);

    private final ProjectTaskService projectTaskService;
    private final ProjectInfoService projectInfoService;
    private final TemplateConfigService templateConfigService;
    private final WordUtils wordUtils;
    private final KwThirdService kwThirdService;
    private final ProjectGroupService projectGroupService;
    private final RocketMQUtil rocketMQUtil;

    @GetMapping("/createTask")
    @Log("任务相关")
    @ApiOperation("任务相关")
    public ResponseEntity<Object> taskNotiRelation() {
        return new ResponseEntity<>(projectTaskService.createTask(), HttpStatus.OK);
    }

    @GetMapping("/updateAllProject")
    @Log("刷新项目编码")
    @ApiOperation("刷新项目编码")
    public ResponseEntity<Object> updateAllProject() {
        return new ResponseEntity<>(projectInfoService.updateAllProject(), HttpStatus.OK);
    }

    /*@PostMapping("/testDocToHtml")
    @Log("测试word转html")
    @ApiOperation("测试word转html")

    public ResponseEntity<Object> test(@RequestParam("file") MultipartFile file) {
        return new ResponseEntity<>(wordUtils.readeWordToHtml(file) , HttpStatus.OK);
    }*/

    @PostMapping("/testImport")
    @Log("测试导入")
    @ApiOperation("测试导入")
    public ResponseEntity<Object> testImport(QueryRequest queryRequest) throws Exception {
        TemplateConfig byConfigType = templateConfigService.findByConfigType(KidsSystemEnum.TemplateConfigType.CFT001.getValue());
        if (byConfigType == null) {
            throw new BadRequestException("请先配置导入模板");
        }
        List<ValuePosition> valuePositionList = ExcelUtils.importFile(queryRequest.getFile(), byConfigType.getContent(), KidsSystemEnum.TemplateConfigType.CFT001.getValue(),1);
        return new ResponseEntity<>(valuePositionList, HttpStatus.OK);
    }

    @PostMapping("/testDocToHtml")
    @Log("测试word转html")
    @ApiOperation("测试word转html")
    public ResponseEntity<Object> test() {
//        wordUtils.commonExportWord();
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @GetMapping("/submitKoaContractProcess")
    @ApiOperation("submitKoaContractProcess")
    public ResponseEntity<Object> submitKoaContractProcess(Long projectGroupId) {
        ProjectGroup byId = projectGroupService.getById(projectGroupId);
        kwThirdService.submitKoaContractProcess(byId,Boolean.FALSE);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @AnonymousGetMapping("/sendMessage/{content}/{tag}")
    @ApiOperation("sendMessage")
    public ResponseEntity<Object> sendMessage(@PathVariable("content") String content, @PathVariable("tag") String tag) {
        rocketMQUtil.sendMessage(content,tag);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @AnonymousGetMapping("/sendAsyncMessage/{content}/{tag}")
    @ApiOperation("sendAsyncMessage")
    public ResponseEntity<Object> sendAsyncMessage(@PathVariable("content") String content, @PathVariable("tag") String tag) {
        rocketMQUtil.sendAsyncMessage("{\"content\":\"「营建新系统测试」待办提醒：\\n您有新的「营建对接启动」任务。项目名：营建新系统测试，任务结束时间是：2024-03-03,请您尽快登录系统进行处理。\\n系统链接：https://acms.yaduo.com/\",\"operator\":\"acms-backend\",\"targets\":[{\"mobile\":\"17625469193\"}],\"type\":1}",tag);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @AnonymousGetMapping("/sendOneWayMessage/{content}/{tag}")
    @ApiOperation("sendOneWayMessage")
    public ResponseEntity<Object> sendOneWayMessage(@PathVariable("content") String content, @PathVariable("tag") String tag) {
        rocketMQUtil.sendOneWayMessage(content,tag);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 健康检查接口
     * @return
     */
    @AnonymousGetMapping(value = "/healthcheck")
    public ResponseEntity<Object> healthcheck() {
        return new ResponseEntity<>("service is ok", HttpStatus.OK);
    }

    @ApiOperation("测试")
    @AnonymousAccess
    @AnonymousGetMapping(value = "/test_ip")
    public ResponseEntity<Object> test(HttpServletRequest request) {
        return ResponseEntity.ok(StringUtils.getLocalCityInfo(request.getRemoteAddr()));
    }


}
