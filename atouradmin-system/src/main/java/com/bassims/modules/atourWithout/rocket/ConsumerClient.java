package com.bassims.modules.atourWithout.rocket;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @description:
 * @author: shiX
 * @date: 2023/11/16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsumerClient {

    @Value("${rocketmq.name-server}")
    private String serverAddress;

    @Value("${rocketmq.topic}")
    private String topic;


    @Value("${rocketmq.subject-topic}")
    private String topicSubject;

    @Value("${rocketmq.tag}")
    private String tag;

    @Value("${rocketmq.producer.group}")
    private String group;

    @Value("${rocketmq.consumer.access-key}")
    private String accessKey;

    @Value("${rocketmq.consumer.secret-key}")
    private String secretKey;

    @Value("${rocketmq.consumer.thread-num}")
    private String threadNum;

    private final RocketMessageListener messageListener;
    private final RocketSubjectMessageListener rocketSubjectMessageListener;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean buildConsumer() {
        //消费者对象bean
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = new Properties();
        //设置消费者信息
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, serverAddress);
        //设置组id
        properties.setProperty(PropertyKeyConst.GROUP_ID, group);
        //将消费者线程数固定为20个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, threadNum);
        consumerBean.setProperties(properties);

        //配置订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        //当前为订阅一个主题配置（订阅多个topic一样设置）
        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setExpression(tag);
        subscriptionTable.put(subscription, messageListener);

        Subscription subscriptionSubject = new Subscription();
        subscriptionSubject.setTopic(topicSubject);
        subscriptionSubject.setExpression(tag);
        subscriptionTable.put(subscriptionSubject, rocketSubjectMessageListener);
        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

}
