package com.bassims.modules.atourWithout.domain;

import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 接收或发送的用户信息 Atos
 */
@Data
public class AtosUserRequest {
  public String uk;
  public String tk;
  /*0，正常；1，冻结；4:删除*/
  public int dataStatus;
  public int mqType;
  /*手机号*/
  public String mobile;
  /*性别*/
  public int gender;
  /*用户名称*/
  public String userName;
  public String userNote;
  /*邮箱*/
  public String email;
  /*岗位ID*/
  public String jobId;
  /*岗位name*/
  public String jobName;
  /*战区ID*/
  public String firstDepartId;
  /*战区name*/
  public String firstDepartName;


  //酒店ID
  public String pmsDepartmentId;

  public AtosUserExtendInfo extend_info;

  // 用户兼职
  public List<UserPartJobInfo> userPartJobInfoList;


}
