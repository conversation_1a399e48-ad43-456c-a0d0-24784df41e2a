package com.bassims.modules.atourWithout.rocket;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.bassims.modules.atour.service.OwnerService;
import com.bassims.modules.atour.service.SupplierInfoService;
import com.bassims.modules.atourWithout.domain.AtosSubjectRequest;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: shiX
 * @date: 2023/11/16
 */
@Slf4j
@Component
public class RocketSubjectMessageListener implements MessageListener {

    @Autowired
    private SupplierInfoService supplierInfoService;
    @Autowired
    private OwnerService ownerService;


    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 业务处理逻辑
        log.info(">>>>> 【租户消费者】系统时间:[{}]接收消息MsgId=[{}]，接收到的Tag：{}，消息内容:{}", LocalDateTime.now(),message.getMsgID(),message.getTag(),new String(message.getBody(), StandardCharsets.UTF_8));
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            AtosSubjectRequest atosSubjectRequest = JSONUtil.toBean(body, AtosSubjectRequest.class);
            String tk = atosSubjectRequest.getTk();
            Integer dataStaus = atosSubjectRequest.getDataStaus();
            log.info(String.valueOf(atosSubjectRequest));
             if("agent".equals(message.getTag())){
                 if(dataStaus!=4){
                     supplierInfoService.saveOrUpdateFromAtos(atosSubjectRequest);
                 }else {
                     supplierInfoService.deleteFromAtos(atosSubjectRequest);
                 }

            }else if("franchise".equals(message.getTag())){
                 if(dataStaus!=4){
                     ownerService.saveOrUpdateFromAtos(atosSubjectRequest);
                 }else {
                     ownerService.deleteFromAtos(atosSubjectRequest);
                 }
            }
            //添加消息到数据库
            return Action.CommitMessage;
        } catch (Exception e) {
            //消费失败
            log.error(">>>>> 【租户消费者】系统时间:[{}]处理消息MsgId=[{}],失败内容:[{}] 当前消费失败, 开始重试......！",LocalDateTime.now(),message.getMsgID(),e.getMessage());
            return Action.ReconsumeLater;
        }
    }

}

