package com.bassims.modules.feishu.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.feishu.service.NioSsoService;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.modules.system.service.dto.UserQueryCriteria;
import com.bassims.utils.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * nio sso服务impl
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Log4j2
@Service
public class NioSsoServiceImpl implements NioSsoService {

    @Value("${sso.tokenUrl}")
    private String tokenUrl;
    @Value("${sso.codeUrl}")
    private String codeUrl;
    @Value("${sso.appId}")
    private String appId;
    @Value("${sso.secret}")
    private String secret;
    @Value("${sso.redirectUrl}")
    private String redirectUrl;
    @Value("${sso.profileUrl}")
    private String profileUrl;
    @Value("${sso.loginUrl}")
    private String loginUrl;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private UserService userService;

    @Override
    public Object getUserInfo(String code) throws UnsupportedEncodingException {
        if (StringUtils.isNotEmpty(code)) {
            Map<String, Object> tkMap = new HashMap<>(4);
            tkMap.put("code", code);
            //log.info("code==>{}",code);
            tkMap.put("redirect_uri", URLEncoder.encode(redirectUrl,"UTF-8"));
            tkMap.put("client_id", appId);
            tkMap.put("client_secret", secret);
            log.info("获取token的地址==>{}",tokenUrl);
            //log.info("请求accessToken参数===>{}",tkMap);
            String at = HttpUtil.get(tokenUrl, tkMap);
            log.info("获取nio accessToken结果 ===> {}",at);
            Map<String, Object> map = transferToken(at);
//            String accessToken = (String) map.get("access_token");
//            Integer expires = (Integer) map.get("expires");

            log.info("开始获取nio用户信息");
            log.info("请求地址: {}", profileUrl);
            String userInfo = HttpUtil.get(profileUrl, map);
            JSONObject entry = JSONUtil.parseObj(userInfo);
            if (entry.containsKey("error")) {
                throw new BadRequestException(entry.getStr("error"));
            } else {
                JSONArray attributes = entry.getJSONArray("attributes");
                for (int i = 0; i < attributes.size(); i++) {
                    JSONObject attr = attributes.getJSONObject(i);
                    if (attr.containsKey("email")) {
                        String email = attr.getStr("email");
                        log.info("获取到当前登陆人email ===> {}", email);
                        UserQueryCriteria userQueryCriteria = new UserQueryCriteria();
                        userQueryCriteria.setSsoEmail(email);
                        List<UserDto> dtos = userService.queryAll(userQueryCriteria);
                        if (dtos != null && dtos.size() > 0) {
                            String userCode = Base64.encode(dtos.get(0).getUsername());
                            String oss = Base64.encode("oss");
                            return loginUrl+"?username="+userCode+"&pt="+oss;
                        } else {
//                            throw new BadRequestException("暂无该用户信息");
//                            return loginUrl+"?error=暂无该用户信息";

                        }
                    }
                }
            }

        }
        return null;
    }

    @Override
    public String getAuthUrl() throws UnsupportedEncodingException {
        //https://signin.nio.com/oauth2/authorize?client_id=10000&redirect_uri=https%3A%2F%2Fvms.nio.com&response_type=code
        return codeUrl+"?client_id="+appId+"&redirect_uri="+URLEncoder.encode(redirectUrl,"UTF-8")+"&response_type=code";
    }

    private Map<String, Object> transferToken(String var) {
        Map<String, Object> map = new HashMap<>(2);
        String[] split = var.split("&");
        for (String s : split) {
            String first = s.replaceFirst("=", ":");
            String[] param = first.split(":");
            map.put(param[0],param[1]);
        }
        return map;
    }

    public static void main(String[] args) {
        String a  = "access_token=2.0HiWNFQe/wpD7H7l6INhG00nFdx8Sowzxc3uVcRnS4VY=&expires=604764";
        System.out.println("2.0HiWNFQe/wpD7H7l6INhG00nFdx8Sowzxc3uVcRnS4VY=".length());
        String[] split = a.split("&");
        for (String s : split) {
            System.out.println(s);
//            JSONObject object = JSONUtil.pa(s);
//            System.out.println(object.toString());
            String s1 = s.replaceFirst("=", ":");
            System.out.println(s1);
        }
    }
}
