package com.bassims.modules.feishu.rest;

import cn.hutool.json.JSONObject;
import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterSmsVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterTargetsVo;
import com.bassims.modules.atour.domain.vo.SendPhoneVo;
import com.bassims.modules.atour.domain.vo.SendSmsVo;
import com.bassims.modules.atour.service.ProjectTaskService;
import com.bassims.modules.feishu.service.PortalService;
import com.google.gson.JsonElement;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;

@Controller
@RequiredArgsConstructor
@RequestMapping("/api/portal")
public class PortalController {
    private static final Logger logger = LoggerFactory.getLogger(PortalController.class);

    @Autowired
    private PortalService portalService;

    @Autowired
    private ProjectTaskService projectTaskService;

    @Log("portal获取用户")
    @AnonymousAccess
    @ApiOperation("portal获取用户")
    @GetMapping("/getUserInfo")
    public String getUserInfo(HttpServletResponse response, HttpServletRequest request) {
        return "redirect:" + portalService.getUserInfo(request, null);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("portal login")
    @ApiOperation("portal login")
    @GetMapping("/login")
    public String login() throws UnsupportedEncodingException {
        return portalService.getAuthUrl();
    }

    @ResponseBody
    @AnonymousAccess
    @Log("同步供应商所有用户数据")
    @ApiOperation("同步供应商所有用户数据")
    @PostMapping("/syncPortalUserInfo")
    public ResponseEntity<Object> syncUserInfo() {
        return portalService.syncUserInfo();
    }

    private static Map<Long,Integer> callNum = new LinkedHashMap<>();
    @ResponseBody
    @Log("sso 发送短信")
    @AnonymousAccess
    @ApiOperation("sso 发送短信")
    @PostMapping("/sendPhoneCode")
    public JSONObject sendPhoneCode(@Validated @RequestBody SendPhoneVo sendPhoneVo) {
        Integer num = 0;
        Long timeKey = new Date().getTime();
        for(Long key:callNum.keySet()){
            if((new Date().getTime()-key)<60l*1000){
                num = callNum.get(key);
                timeKey = key;
            }else{
                callNum.remove(key);
            }
        }
        if(num>10){
            throw new BadRequestException("短时间调用太频繁,请稍后调用！");
        }
        num++;
        callNum.put(timeKey,num);
        return portalService.sendPhoneCode(sendPhoneVo);
    }


    @ResponseBody
    @Log("sso 短信验证")
    @AnonymousAccess
    @ApiOperation("sso 短信验证")
    @PostMapping("/sendSms")
    public JSONObject sendSms(@Validated @RequestBody SendSmsVo sendSmsVo, HttpServletRequest request) {
        return portalService.sendSms(sendSmsVo, request);
    }


    @ResponseBody
    @Log("发送短信")
    @AnonymousAccess
    @ApiOperation("发送短信")
    @PostMapping("/sendText")
    public String sendText() {

        List<SendNotifyCenterTargetsVo> centerTargetsVos = new ArrayList<>();
        SendNotifyCenterTargetsVo vo = new SendNotifyCenterTargetsVo();
        vo.setMobile("17625469193");
        centerTargetsVos.add(vo);
        SendNotifyCenterTargetsVo vo1 = new SendNotifyCenterTargetsVo();
        vo1.setMobile("15272162089");
        centerTargetsVos.add(vo1);
        SendNotifyCenterSmsVo centerTargetsVo = new SendNotifyCenterSmsVo();
        centerTargetsVo.setTargets(centerTargetsVos);
        projectTaskService.sendAsynchronous(centerTargetsVos, "「营建新系统测试」待办提醒：" +
                "\n您有新的「营建对接启动」任务。项目名：营建新系统测试，酒店ID：100111，任务结束时间是：2024-03-03,请您尽快登录系统进行处理。" +
                "\n系统链接：https://acms.yaduo.com/");
        return "faschg";
    }

}
