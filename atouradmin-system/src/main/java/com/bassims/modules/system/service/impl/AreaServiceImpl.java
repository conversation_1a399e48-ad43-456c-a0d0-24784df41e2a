/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.Brand;
import com.bassims.modules.atour.domain.BrandRegionRelation;
import com.bassims.modules.atour.domain.WarZone;
import com.bassims.modules.atour.domain.vo.AreaVo;
import com.bassims.modules.atour.repository.BrandRepository;
import com.bassims.modules.system.domain.Area;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.repository.AreaRepository;
import com.bassims.modules.system.service.AreaService;
import com.bassims.modules.system.service.dto.AreaDto;
import com.bassims.modules.system.service.dto.AreaQueryCriteria;
import com.bassims.modules.system.service.mapstruct.AreaMapper;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-04-06
 **/
@Service
public class AreaServiceImpl extends BaseServiceImpl<AreaRepository, Area> implements AreaService {

    private static final Logger logger = LoggerFactory.getLogger(AreaServiceImpl.class);

    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private AreaMapper areaMapper;
    @Autowired
    private BrandRepository brandRepository;

    @Override
    public Map<String, Object> queryAll(AreaQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<Area> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(Area.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", areaMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<AreaDto> queryAll(AreaQueryCriteria criteria) {
        return areaMapper.toDto(list(QueryHelpPlus.getPredicate(Area.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AreaDto findById(Long id) {
        Area area = Optional.ofNullable(getById(id)).orElseGet(Area::new);
        ValidationUtil.isNull(area.getId(), getEntityClass().getSimpleName(), "id", id);
        return areaMapper.toDto(area);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AreaDto create(Area resources) {
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Area resources) {
        Area area = Optional.ofNullable(getById(resources.getId())).orElseGet(Area::new);
        ValidationUtil.isNull(area.getId(), "Area", "id", resources.getId());
        area.copy(resources);
        updateById(area);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            areaRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<AreaDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AreaDto area : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("层级", area.getLevel());
            map.put("父级行政代码", area.getParentCode());
            map.put("行政代码", area.getAreaCode());
            map.put("邮政编码", area.getZipCode());
            map.put("区号", area.getCityCode());
            map.put("名称", area.getName());
            map.put("简称", area.getShortName());
            map.put("组合名", area.getMergerName());
            map.put("拼音", area.getPinyin());
            map.put("经度", area.getLng());
            map.put("纬度", area.getLat());
            map.put("大区", area.getRegion());
            map.put("城市公司", area.getCityCompany());
            map.put(" isDelete", area.getIsDelete());
            map.put(" createTime", area.getCreateTime());
            map.put(" createBy", area.getCreateBy());
            map.put(" updateTime", area.getUpdateTime());
            map.put(" updateBy", area.getUpdateBy());
            map.put("是否可用", area.getIsEnabled());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<Area> getProvince(String region) {
        if (StringUtils.isBlank(region)) {
            throw new BadRequestException("大区数据为空，请选择大区后再选择！");
        } else {

        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<Area> areas = areaRepository.getProvince(region, currentUserId);
        return areas;
    }

    @Override
    public List<Area> getCity(String province) {
        if (StringUtils.isBlank(province)) {
            throw new BadRequestException("省份数据为空，请选择省份后再选择！");
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<Area> areas = areaRepository.getCity(province, currentUserId);
        return areas;
    }

    @Override
    public List<Area> getDistrict(String city) {
        if (StringUtils.isBlank(city)) {
            throw new BadRequestException("城市数据为空，请选择城市后再选择！");
        }
        List<Area> areas = areaRepository.getDistrict(city);
        return areas;
    }

    @Override
    public List<Brand> getBrand() {
//        if(StringUtils.isBlank()){
//            throw  new BadRequestException("城市数据为空，请选择城市后再选择！");
//        }
        LambdaQueryWrapper<Brand> brandLambdaQueryWrapper = Wrappers.lambdaQuery(Brand.class);
        List<Brand> brands = brandRepository.selectList(brandLambdaQueryWrapper);
        return brands;
    }

    @Override
    public List<WarZone> getWarZoneByBrand(String region) {
//        if(StringUtils.isBlank(region)){
//            throw  new BadRequestException("战区数据为空，请选择战区后再选择！");
//        }
//        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<WarZone> warZone = areaRepository.getWarZoneByBrand(region);
        return warZone;
    }

    @Override
    public List<DictDetail> getCityCompany(String region, String dict) {
        if (StringUtils.isBlank(region)) {
            throw new BadRequestException("大区数据为空，请选择大区后再选择！");
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<DictDetail> dictDetails = areaRepository.getCityCompany(region, dict, currentUserId);
        return dictDetails;
    }

    @Override
    public List<DictDetail> getRegion(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new BadRequestException("用户数据为空，请重新登录后操作！");
        }
        List<DictDetail> regionDictDetails = areaRepository.getRegionDictDetails(userId);
        return regionDictDetails;
    }

    @Override
    public List<DictDetail> getCityCompanys(String dict) {
        List<DictDetail> dictDetails = areaRepository.getCityCompanys(dict);
        return dictDetails;
    }

    @Override
    public List<Area> getCitysByCityCompany(String cityCompany) {
        List<Area> citys = areaRepository.getCitysByCityCompany(cityCompany);
        return citys;
    }

    @Override
    public List<Area> getAllCitys() {
        List<Area> citys = areaRepository.getAllCitys();
        return citys;
    }

    @Override
    public List<Area> getCitysByCompanyName(String cityCompany) {
        if (StringUtils.isBlank(cityCompany)) {
            throw new BadRequestException("城市公司为空，请重新调用！");
        }
        List<Area> citys = areaRepository.getCitysByCompanyName(cityCompany);
        return citys;
    }

    @Override
    public List<AreaVo> getProvinceAndCitysForUser(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new BadRequestException("用户数据为空，请重新登录后操作！");
        }
        List<AreaVo> list = new LinkedList<>();
        List<DictDetail> regionDictDetails = areaRepository.getRegionDictDetails(userId);
//        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (ObjectUtils.isNotEmpty(regionDictDetails)) {
            for (DictDetail regionDictDetail : regionDictDetails) {
                List<Area> province = areaRepository.getProvince(regionDictDetail.getValue(), Long.parseLong(userId));
                for (Area area : province) {
                    AreaVo areaVo = new AreaVo();
                    areaVo.setId(area.getId());
                    areaVo.setLevel(area.getLevel());
                    areaVo.setParentCode(area.getParentCode());
                    areaVo.setAreaCode(area.getAreaCode());
                    areaVo.setName(area.getName());
                    list.add(areaVo);
                }
            }
        }
        if (list.size() > 0) {
            for (AreaVo areaVo : list) {
                List<Area> citys = areaRepository.getCity(String.valueOf(areaVo.getAreaCode()), Long.parseLong(userId));
                if (citys.size() > 0 && citys.get(0) != null) {
                    List<AreaVo> areaVos = new ArrayList<>();
                    for (Area city : citys) {
                        AreaVo areaVoList = new AreaVo();
                        areaVoList.setId(city.getId());
                        areaVoList.setLevel(city.getLevel());
                        areaVoList.setParentCode(city.getParentCode());
                        areaVoList.setAreaCode(city.getAreaCode());
                        areaVoList.setName(city.getName());
                        areaVos.add(areaVoList);
                    }
                    areaVo.setAreas(areaVos);
                }
            }
        }
        return list;
    }

    @Override
    public List<AreaVo> getProvinceAndCitysForUserNotRegion(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new BadRequestException("用户数据为空，请重新登录后操作！");
        }
        List<AreaVo> list = new LinkedList<>();
        List<Area> province = areaRepository.getProvince(null, Long.parseLong(userId));
        for (Area area : province) {
            AreaVo areaVo = new AreaVo();
            areaVo.setId(area.getId());
            areaVo.setLevel(area.getLevel());
            areaVo.setParentCode(area.getParentCode());
            areaVo.setAreaCode(area.getAreaCode());
            areaVo.setName(area.getName());
            list.add(areaVo);
        }

        if (list.size() > 0) {
            for (AreaVo areaVo : list) {
                List<Area> citys = areaRepository.getCity(String.valueOf(areaVo.getAreaCode()), Long.parseLong(userId));
                if (citys.size() > 0 && citys.get(0) != null) {
                    List<AreaVo> areaVos = new ArrayList<>();
                    for (Area city : citys) {
                        AreaVo areaVoList = new AreaVo();
                        areaVoList.setId(city.getId());
                        areaVoList.setLevel(city.getLevel());
                        areaVoList.setParentCode(city.getParentCode());
                        areaVoList.setAreaCode(city.getAreaCode());
                        areaVoList.setName(city.getName());
                        areaVos.add(areaVoList);
                    }
                    areaVo.setAreas(areaVos);
                }
            }
        }
        return list;
    }

    /**
     * @description: 获取所有区域下的省份和城市
     * @author: zuolei
     * @date: 17:40
     * @param: []
     * @return: java.util.List<com.bassims.modules.atour.domain.vo.AreaVo>
     **/
    @Override
    public List<AreaVo> getAllProvinceAndCity() {
        List<AreaVo> list = new LinkedList<>();
        List<Area> province = areaRepository.getAllProvince();
        province = province.stream().filter(p -> 0 == p.getLevel()).collect(Collectors.toList());
        for (Area area : province) {
            AreaVo areaVo = new AreaVo();
            areaVo.setId(area.getId());
            areaVo.setLevel(area.getLevel());
            areaVo.setParentCode(area.getParentCode());
            areaVo.setAreaCode(area.getAreaCode());
            areaVo.setName(area.getName());
            list.add(areaVo);
        }

        if (!list.isEmpty()) {
            Map<Long, List<Area>> collect = this.list().stream()
                    .filter(a -> ObjectUtil.isNotEmpty(a.getParentCode()) && 1 == a.getLevel())
                    .collect(Collectors.groupingBy(Area::getParentCode));
            for (AreaVo areaVo : list) {
                List<Area> citys = collect.get(areaVo.getAreaCode());
                if (ObjectUtil.isNotEmpty(citys)) {
                    List<AreaVo> areaVos = new ArrayList<>();
                    for (Area city : citys) {
                        AreaVo areaVoList = new AreaVo();
                        areaVoList.setId(city.getId());
                        areaVoList.setLevel(city.getLevel());
                        areaVoList.setParentCode(city.getParentCode());
                        areaVoList.setAreaCode(city.getAreaCode());
                        areaVoList.setName(city.getName());
                        areaVos.add(areaVoList);
                    }
                    areaVo.setAreas(areaVos);
                }
            }
        }
        return list;
    }

    @Override
    public AreaDto getAreaDtoByAreaCode(Long areaCode) {
        if (null == areaCode) {
            return null;
        }
        AreaDto one = areaRepository.getAreaDtoByAreaCode(areaCode, null);
        if (null == one) {
            throw new BadRequestException("areaCode数据异常，未找到相关数据");
        }

        if (one.getLevel() != 1) {
            return areaRepository.getAreaDtoByAreaCode(one.getParentCode(), 1);
        }
        return one;
    }

    @Override
    public List<BrandRegionRelation> getRegionByBrand(String brandName) {
        return areaRepository.getRegionByBrand(brandName);
    }
}