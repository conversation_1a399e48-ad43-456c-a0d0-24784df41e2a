/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.bassims.base.BaseDTO;
import com.bassims.service.dto.LocalStorageDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-23
 */
@Getter
@Setter
public class UserDto extends BaseDTO implements Serializable {
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private Set<RoleSmallDto> roles;

    private Set<JobSmallDto> jobs;

    private Set<AreaSmallDto> citys;

    private DeptSmallDto dept;

    private Long deptId;

    private String username;

    private String nickName;

    private String userStatus;

    private String email;

    private String phone;

    private String gender;

    private String avatarName;

    private String avatarPath;

    @JSONField(serialize = false)
    private String password;

    private Boolean enabled;

    @JSONField(serialize = false)
    private Boolean isAdmin = false;

    private Date pwdResetTime;

    private String businessLicense;
    private LocalStorageDto businessLicenseDto;

    private String legalPerson;

    private String contactNumber;

    private String businessAddress;

    private String contractNumber;

    private Timestamp signTime;

    private String dealerNo;

    private String registerType;

    private String openId;

    private BigDecimal consumeAmount;

    private String emissionLevel;

    private String dealerLevel;

    private String loginLimit;

    private String account;

    private String code;

    private String tenantId;

    private Boolean isDelete;

    private String realName;


    /*岗位名称*/
    private String jobName;

    /*岗位ID*/
    private String jobId;


    /*运营战区名称*/
    private String firstDepartName;

    /*运营战区*/
    private String firstDepartId;
}
