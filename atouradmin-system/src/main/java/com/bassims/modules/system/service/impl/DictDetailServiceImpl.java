/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.service.impl;

import ch.qos.logback.core.joran.util.beans.BeanUtil;
import com.bassims.modules.atour.domain.QualityControl;
import com.bassims.modules.system.domain.Dict;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.DictRepository;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.modules.system.service.dto.DictDetailDto;
import com.bassims.modules.system.service.dto.DictDetailQueryCriteria;
import com.bassims.modules.system.service.mapstruct.DictDetailMapper;
import com.bassims.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.*;

/**
* <AUTHOR> Jie
* @date 2019-04-10
*/
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "dict")
public class DictDetailServiceImpl implements DictDetailService {

    private final DictRepository dictRepository;
    private final DictDetailRepository dictDetailRepository;
    private final DictDetailMapper dictDetailMapper;
    private final RedisUtils redisUtils;

    @Override
    public Map<String,Object> queryAll(DictDetailQueryCriteria criteria, Pageable pageable) {
        Page<DictDetail> page = dictDetailRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(dictDetailMapper::toDto));
    }

    @Override
    public Map<String,Object> queryAll(DictDetailQueryCriteria criteria) {
        List<DictDetailDto> dictDetailDtos = dictDetailMapper.toDto(dictDetailRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            Order order = criteriaBuilder.asc(root.get("dictSort"));
            criteriaQuery.orderBy(order);
            return QueryHelp.getPredicate(root, criteria, criteriaBuilder);
        }));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", dictDetailDtos);
        map.put("totalElements", dictDetailDtos.size());
        return map;
    }
    public void addNewCheckText(String label,String quaityName,String nextName){
        List<DictDetail> descList =  dictDetailRepository.findDictDetailByDictName("check_detail");
            String desc  = label;//检查内容详情
            //内容 completion_content
            String importline = nextName;//重要程度 importance_of_the_problem
            List<DictDetail> querylist = dictDetailRepository.getDictByNameAndParentInfo(desc,quaityName,"completion_content");
            //如果没查出来，则需要在字典表中新增一条
            if(querylist==null||querylist.size()<1){
                DictDetail newOne = new DictDetail();
                newOne.setDictSort(5);
                newOne.setDict(descList.get(0).getDict());
                newOne.setLabel(desc);
                newOne.setValue("detail"+(dictDetailRepository.findMymaxvalue()+1));
                newOne.setParentId(dictDetailRepository.getIdByParentInfo(quaityName,"completion_content"));
                newOne.setNextId(dictDetailRepository.getIdByParentInfo(importline,"importance_of_the_problem"));
                newOne.setCreateBy("admin");
                newOne.setCreateTime(new Timestamp(new Date().getTime()));
                dictDetailRepository.save(newOne);
            }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(DictDetail resources) {
        dictDetailRepository.save(resources);
        // 清理缓存
        delCaches(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DictDetail resources) {
        DictDetail dictDetail = dictDetailRepository.findById(resources.getId()).orElseGet(DictDetail::new);
        ValidationUtil.isNull( dictDetail.getId(),"DictDetail","id",resources.getId());
        resources.setId(dictDetail.getId());
        dictDetailRepository.save(resources);
        // 清理缓存
        delCaches(resources);
    }

    @Override
    //@Cacheable(key = "'name:' + #p0")
    public List<DictDetailDto> getDictByName(String name) {
        List<DictDetail> dictDetailList = dictDetailRepository.findDictDetailByDictName(name);
        List<DictDetailDto> list = new ArrayList<>();
        if(dictDetailList!=null&&dictDetailList.size()>0){
            for(DictDetail detail:dictDetailList){
                DictDetailDto dto = new DictDetailDto();
                dto.setDictSort(detail.getDictSort());
                dto.setId(detail.getId());
                dto.setNextId(detail.getNextId());
                dto.setParentId(detail.getParentId());
                dto.setLabel(detail.getLabel());
                dto.setValue(detail.getValue());
                list.add(dto);
            }
        }
        return list;
    }
    @Override
    public List<DictDetailDto> getDictByNameAndParentInfo(String label,String parentValue,String parentDictName){
        List<DictDetail> dictDetailList = dictDetailRepository.getDictByNameAndParentInfo(label,parentValue,parentDictName);
        List<DictDetailDto> list = new ArrayList<>();
        if(dictDetailList!=null&&dictDetailList.size()>0){
            for(DictDetail detail:dictDetailList){
                DictDetailDto dto = new DictDetailDto();
                dto.setDictSort(detail.getDictSort());
                dto.setId(detail.getId());
                dto.setNextId(detail.getNextId());
                dto.setParentId(detail.getParentId());
                dto.setLabel(detail.getLabel());
                dto.setValue(detail.getValue());
                list.add(dto);
            }
        }
        return list;
    }

    @Override
    public List<DictDetailDto> getDictByDictName(String dictName) {
        return dictDetailMapper.toDto(dictDetailRepository.findDictDetailByDictName(dictName));
    }

    @Override
    public DictDetail getByDictId(Long dictId) {
        DictDetail dictDetail = dictDetailRepository.getByDictId(dictId);
        return dictDetail;
    }
    @Override
    public DictDetail getById(Long dictdetailId){
        return  dictDetailRepository.getOne(dictdetailId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        DictDetail dictDetail = dictDetailRepository.findById(id).orElseGet(DictDetail::new);
        // 清理缓存
        delCaches(dictDetail);
        dictDetailRepository.deleteById(id);
    }

    @Override
    public void delCaches(DictDetail dictDetail){
        Dict dict = dictRepository.findById(dictDetail.getDict().getId()).orElseGet(Dict::new);
        redisUtils.del("dict::name:" + dict.getName());
    }
}