<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.StoreMeasureAreaRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.StoreMeasureArea">
        <id column="store_measure_area_id" property="storeMeasureAreaId"/>
        <result column="store_id" property="storeId"/>
        <result column="label_info_area_id" property="labelInfoAreaId"/>
        <result column="label_info_code" property="labelInfoCode"/>
        <result column="node_code" property="nodeCode"/>
        <result column="measure_area" property="measureArea"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        store_measure_area_id,store_id,label_info_area_id,label_info_code,node_code,measure_area,version,create_time,update_time,create_by,update_by,is_delete
    </sql>

    <select id="getStoreIds" resultType="java.lang.Long">
        select distinct(store_id) from t_store_measure_area
    </select>

</mapper>
