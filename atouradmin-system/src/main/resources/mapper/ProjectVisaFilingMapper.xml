<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectVisaFilingRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectVisaFiling">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="reporting_category" property="reportingCategory"/>
        <result column="report_content" property="reportContent"/>
        <result column="node_code" property="nodeCode"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>


        <result column="presentation_ondition" property="presentationOndition"/>
        <result column="attachment" property="attachment"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,project_id,reporting_category,report_content,node_code,remark,create_time,create_by,update_time,update_by,is_enabled,is_delete,presentation_ondition,attachment
    </sql>

    <select id="getVisaFilingList" resultType="com.bassims.modules.atour.service.dto.ProjectVisaFilingDto">
        select t_project_visa_filing.*,
               t_project_group.project_group_id AS nodeId,
               t_project_group.parent_id AS templateId,
               s_user_info.nick_name AS createByName
        from t_project_visa_filing t_project_visa_filing
                 LEFT JOIN t_project_group t_project_group ON t_project_group.project_id = t_project_visa_filing.id
            AND t_project_group.node_code = t_project_visa_filing.node_code
                 LEFT JOIN s_user_info  s_user_info  ON s_user_info.user_id = SUBSTRING_INDEX(t_project_visa_filing.create_by,":",-1)
        where t_project_visa_filing.project_id = #{projectId}
          and t_project_visa_filing.reporting_category = #{reportingCategory}
        order by t_project_visa_filing.create_time desc
    </select>

    <select id="getVisaFilingOne" resultType="com.bassims.modules.atour.service.dto.ProjectVisaFilingDto">
        select t_project_visa_filing.*,
               t_project_group.project_group_id AS nodeId,
               t_project_group.parent_id AS templateId,
               s_user_info.nick_name AS createByName
        from t_project_visa_filing t_project_visa_filing
                 LEFT JOIN t_project_group t_project_group ON t_project_group.project_id = t_project_visa_filing.id
            AND t_project_group.node_code = t_project_visa_filing.node_code
                 LEFT JOIN s_user_info  s_user_info  ON s_user_info.user_id = SUBSTRING_INDEX(t_project_visa_filing.create_by,":",-1)
        where    t_project_visa_filing.id=#{id}
        order by t_project_visa_filing.create_time desc
    </select>
</mapper>
