<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.PresentationAnnexRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.PresentationAnnex">
                    <id column="annex_id" property="annexId" />
                    <result column="annex_name" property="annexName" />
                    <result column="annex_code" property="annexCode" />
                    <result column="remark" property="remark" />
                    <result column="node_index" property="nodeIndex" />
                    <result column="project_version" property="projectVersion" />
                    <result column="node_wbs" property="nodeWbs" />
                    <result column="node_level" property="nodeLevel" />
                    <result column="node_type" property="nodeType" />
                    <result column="node_status" property="nodeStatus" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_edit" property="isEdit" />
                    <result column="added_version" property="addedVersion" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            annex_id,annex_name,annex_code,remark,node_index,project_version,node_wbs,node_level,node_type,node_status,is_delete,create_time,create_by,update_time,update_by,is_enabled,is_edit,added_version
        </sql>

</mapper>
