<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectStakeholdersRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectStakeholders">
        <id column="stakeholder_id" property="stakeholderId"/>
        <result column="project_id" property="projectId"/>
        <result column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="join_time" property="joinTime"/>
        <result column="shakeholder_status" property="shakeholderStatus"/>
        <result column="leave_time" property="leaveTime"/>
        <result column="reason" property="reason"/>
        <result column="is_approve" property="isApprove"/>
        <result column="is_notshow" property="isNotshow"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>
        <result column="role_code" property="roleCode"/>
        <result column="down_code" property="downCode"/>

        <result column="change_authorizatio_file" property="changeAuthorizatioFile"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        stakeholder_id,project_id,user_id,role_id,role_name,join_time,shakeholder_status,leave_time,reason,is_approve,is_notshow,create_time,create_by,update_time,update_by,is_enabled,is_delete,role_code,down_code,order_id,change_authorizatio_file
    </sql>

    <select id="selectListByOwner" resultType="java.lang.String">
        select s_user_info.email
        from t_project_stakeholders t_project_stakeholders
                 left join s_user_info s_user_info on s_user_info.user_id = t_project_stakeholders.user_id
        where t_project_stakeholders.project_id = #{projectId}
          and t_project_stakeholders.shakeholder_status = 'in_term'
          and t_project_stakeholders.role_code = #{roleCode}
    </select>

    <select id="selectListByRoleCodes" resultType="com.bassims.modules.atour.domain.ProjectStakeholders">
        select a.user_id, a.role_id,b.nick_name
        from t_project_stakeholders a
        left join s_user_info b on a.user_id = b.user_id
        where a.project_id = #{projectId}
          and a.shakeholder_status = 'in_term'
        and a.role_code in
        <foreach collection="roleCodes.split(',')" item="roleCode" index="index" open="(" separator="," close=")">
            #{roleCode}
        </foreach>
    </select>

    <select id="selectListByProjectId" resultType="com.bassims.modules.atour.domain.ProjectStakeholders">
        select
        t_project_stakeholders.user_id,
        s_user_info.username AS username,
        max(t_project_stakeholders.role_code) ,
        max(t_project_stakeholders.role_name)
        from t_project_stakeholders t_project_stakeholders
        left join s_user_info s_user_info on s_user_info.user_id=t_project_stakeholders.user_id
        where t_project_stakeholders.project_id = #{projectId}
        and t_project_stakeholders.shakeholder_status = 'in_term'
        and s_user_info.user_id is not null

        <if test="roleCode != null and roleCode != ''">
            and t_project_stakeholders.role_code = #{roleCode}
        </if>
        <if test="carbonCopyRoleCode != null and carbonCopyRoleCode != ''">
            and FIND_IN_SET(t_project_stakeholders.role_code, #{carbonCopyRoleCode})
        </if>
        GROUP BY  t_project_stakeholders.user_id
    </select>

    <select id="getList" resultType="com.bassims.modules.atour.service.dto.ProjectStakeholdersDto">
        SELECT
            t_project_stakeholders.*,
            s_user_info.nick_name AS userName,
            s_user_info.phone AS phone,
            CASE
                WHEN s_user_info1.nick_name is not null THEN
                    s_user_info1.nick_name
                ELSE
                    t_project_stakeholders.update_by
                END  AS updateName
        FROM
            t_project_stakeholders t_project_stakeholders
                LEFT JOIN s_user_info s_user_info on  t_project_stakeholders.user_id = s_user_info.user_id
                LEFT JOIN s_user_info s_user_info1  ON FIND_IN_SET(s_user_info1.user_id,
                                                                   REPLACE (t_project_stakeholders.update_by, ':', ',' ))
        where t_project_stakeholders.project_id =#{projectId}
        <if test="roleCode != null and roleCode != ''">
            and t_project_stakeholders.role_code =#{roleCode}
        </if>
        <if test="roleName != null and roleName != ''">
            and t_project_stakeholders.role_name like concat('%', #{roleName}, '%')
        </if>
        <if test="userName != null and userName != ''">
            and s_user_info.nick_name like concat('%', #{userName}, '%')
        </if>
        <if test="userId != null and userId != ''">
            and t_project_stakeholders.user_id = #{userId}
        </if>
        <if test="roleCodes != null and roleCodes != ''">
            and t_project_stakeholders.role_code in
            <foreach collection="roleCodes" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
        </if>
         ORDER BY
         (CASE t_project_stakeholders.shakeholder_status
         WHEN 'in_term' THEN
         2
         WHEN 'temporarily_off_term' THEN
         3
         WHEN 'off_term' THEN
         4
         ELSE
         1
         END  );
    </select>

    <select id="getLists" resultType="com.bassims.modules.atour.service.dto.ProjectStakeholdersDto">
        SELECT
            ps.*,
            ui.nick_name,
            pi.project_no,
            pi.project_name,
            pi.remark,
            pi.brand_code,
            pi.product_code,
            pi.region,
            sdd.label region_name,
            sdd2.label product_name,
        CASE
            WHEN
                ui1.nick_name is not null
            THEN
                ui1.nick_name
            ELSE
                ps.update_by
            END AS updateName
        FROM
            t_project_stakeholders ps
        LEFT JOIN s_user_info ui ON ps.user_id = ui.user_id
        LEFT JOIN s_user_info ui1  ON FIND_IN_SET(ui1.user_id, REPLACE (ps.update_by, ':', ',' ))
        LEFT JOIN t_project_info pi ON ps.project_id = pi.project_id
        LEFT JOIN s_sys_dict_detail sdd ON pi.region = sdd.`value`
        LEFT JOIN s_sys_dict_detail sdd2 ON pi.product_code = sdd2.`value`
        where ps.shakeholder_status = 'in_term' and (sdd.dict_id = 8 OR sdd.dict_id is null)
        AND (sdd2.dict_id = 207 OR sdd2.dict_id is null)
        <if test="userId != null and userId != ''">
            and ps.user_id = #{userId}
        </if>
        <if test="projectName != null and projectName != ''">
            and pi.project_name like concat('%',#{projectName} ,'%')
        </if>
        <if test="roleName != null and roleName != ''">
            and ps.role_name = #{roleName}
        </if>
    </select>

    <select id="getListByProjectIdAndUserId" resultType="com.bassims.modules.atour.domain.ProjectStakeholders">
        SELECT
            t_project_stakeholders.*
        FROM
        t_project_stakeholders t_project_stakeholders
        where  t_project_stakeholders.shakeholder_status = 'in_term'
          and t_project_stakeholders.user_id = #{userId}
        <if test="projectIds != null and projectIds.size > 0 ">
            and t_project_stakeholders.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="getMaxId" resultType="Long">
        select max(stakeholder_id) from t_project_stakeholders
    </select>
</mapper>
