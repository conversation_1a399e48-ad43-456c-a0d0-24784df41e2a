<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.HlmProjectInfoConstructAreaManagerRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.HlmProjectInfoConstructAreaManager">
                    <id column="area_manager_id" property="areaManagerId" />
                    <result column="project_id" property="projectId" />
                    <result column="hotel_id" property="hotelId" />
                    <result column="employee_id" property="employeeId" />
                    <result column="flower_name" property="flowerName" />
                    <result column="email" property="email" />
                    <result column="mobile" property="mobile" />
                    <result column="department_id" property="departmentId" />
                    <result column="job_name" property="jobName" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            area_manager_id,project_id,hotel_id,employee_id,flower_name,email,mobile,department_id,job_name
        </sql>

</mapper>
