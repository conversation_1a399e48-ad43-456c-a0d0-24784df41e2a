<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectMtoBaseMaterielRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectMtoBaseMateriel">
        <id column="project_materiel_id" property="projectMaterielId"/>
        <result column="class_id" property="classId"/>
        <result column="materiel_no" property="materielNo"/>
        <result column="materiel_name" property="materielName"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="is_delete" property="isDelete"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="material_pattern" property="materialPattern"/>
        <result column="material_performance_parameter" property="materialPerformanceParameter"/>
        <result column="agree_take" property="agreeTake"/>
        <result column="project_id" property="projectId"/>
        <result column="remark" property="remark"/>
        <result column="is_platform_procurement" property="isPlatformProcurement"/>
        <result column="order_time" property="orderTime"/>
        <result column="materiel_id" property="materielId"/>
        <result column="is_must_mined" property="isMustMined"/>


    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_materiel_id,class_id,materiel_no,materiel_name,create_time,create_user,create_dept,
        update_time,update_user,is_delete,tenant_id,material_pattern,material_performance_parameter,agree_take,project_id,
        remark,is_platform_procurement,order_time,materiel_id,is_must_mined
    </sql>

</mapper>
