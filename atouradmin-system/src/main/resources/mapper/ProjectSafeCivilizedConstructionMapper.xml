<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectSafeCivilizedConstructionRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectSafeCivilizedConstruction">
                    <id column="project_management_id" property="projectManagementId" />
                    <result column="management_name" property="managementName" />
                    <result column="management_code" property="managementCode" />
                    <result column="remark" property="remark" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_edit" property="isEdit" />
                    <result column="added_version" property="addedVersion" />
                    <result column="owner_upload_time" property="ownerUploadTime" />
                    <result column="license_review_time" property="licenseReviewTime" />
                    <result column="certificate_review_status" property="certificateReviewStatus" />
                    <result column="completion_status" property="completionStatus" />
                    <result column="license_completion_time" property="licenseCompletionTime" />
                    <result column="project_id" property="projectId" />
                    <result column="relevancy_node_code" property="relevancyNodeCode" />
                    <result column="relevancy_node_name" property="relevancyNodeName" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            project_management_id,management_name,management_code,remark,is_delete,create_time,create_by,update_time,update_by,is_enabled,is_edit,added_version,owner_upload_time,license_review_time,certificate_review_status,completion_status,license_completion_time,project_id,relevancy_node_code,relevancy_node_name
        </sql>

</mapper>
