<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.PackageInfoRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.PackageInfo">
                    <id column="package_id" property="packageId" />
                    <result column="package_name" property="packageName" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            package_id,package_name,is_delete,create_time,create_by,update_time,update_by
        </sql>
    <select id="findSpackageInfo" resultType="com.bassims.modules.atour.domain.PackageInfo">
        SELECT * FROM s_package_info WHERE 1=1
        and `package_name`=#{name}
        and parent_id=#{parentId}
    </select>
    <select id="findSpackageInfoNeed" resultType="com.bassims.modules.atour.domain.PackageInfo">
        SELECT * FROM s_package_info WHERE 1=1
         and package_id=#{packageId}
    </select>
    <select id="findSpackageInfoDeletes" resultType="java.lang.Long">
        SELECT  storage_id as id,`name`,`path` from s_tool_local_storage where 1=1
        and path like CONCAT('%',#{name},'%')
    </select>


</mapper>
