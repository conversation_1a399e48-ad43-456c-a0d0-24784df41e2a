<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateCompletionReceiptRelationRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateCompletionReceiptRelation">
                    <id column="relation_id" property="relationId" />
                    <result column="receipt_id" property="receiptId" />
                    <result column="receipt_group_id" property="receiptGroupId" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            relation_id,receipt_id,receipt_group_id,create_time,create_by,update_by,update_time,is_enabled,is_delete
        </sql>

        <select id="selectByReceiptGroupId" resultType="com.bassims.modules.atour.domain.vo.TemplateCompletionReceiptRelationVo">
            SELECT
                a.*,
                b.can_input
            FROM
                t_template_completion_receipt a
                LEFT JOIN t_template_completion_receipt_relation b ON a.receipt_id = b.receipt_id
                LEFT JOIN s_sys_dict_detail c ON a.total_item = c.value AND c.dict_id  = 187
              LEFT JOIN  s_sys_dict_detail d ON a.content = d.value AND d.dict_id  = 185 WHERE 1=1
              and b.receipt_group_id = #{receiptGroupId}
            <if test="null != totalItem and totalItem != ''">
                AND c.label LIKE concat('%',#{totalItem},'%')
            </if>
            <if test="null != project and project != ''">
                AND a.project LIKE concat('%',#{project},'%')
            </if>
            <if test="null != content and content != ''">
                AND d.label LIKE concat('%',#{content},'%')
            </if>
            <if test="null != subItemContent and subItemContent != ''">
                AND a.sub_item_content LIKE concat('%',#{subItemContent},'%')
            </if>
            <if test="null != subItem and subItem != ''">
                AND a.sub_item LIKE concat('%',#{subItem},'%')
            </if>
        </select>

</mapper>
