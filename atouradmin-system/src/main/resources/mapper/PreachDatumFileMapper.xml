<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.PreachDatumFileRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.PreachDatumFile">
        <id column="datum_file_id" property="datumFileId"/>
        <result column="real_name" property="realName"/>
        <result column="name" property="name"/>
        <result column="suffix" property="suffix"/>
        <result column="path" property="path"/>
        <result column="type" property="type"/>
        <result column="size" property="size"/>
        <result column="node_id" property="nodeId"/>
        <result column="code" property="code"/>
        <result column="node_code" property="nodeCode"/>

        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="flag" property="flag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        datum_file_id,node_code,real_name,name,suffix,path,type,size,node_id,code,node_code,create_time,create_by,update_time,update_by,flag
    </sql>

    <select id="queryByNodeCode" resultType="com.bassims.modules.atour.service.dto.PreachDatumFileDto">
        select *
        from t_preach_datum_file
        <if test="null != nodeCode and nodeCode != '' ">
            where FIND_IN_SET(#{nodeCode}, node_code)
        </if>
    </select>

    <select id="queryByNodeName" resultType="com.bassims.modules.atour.service.dto.PreachDatumFileDto">
        SELECT a.*,b.node_name FROM t_preach_datum_file a LEFT JOIN (
        SELECT node_code,MAX(node_name) as node_name FROM t_project_template GROUP BY node_code
        ) b ON  a.node_code = b.node_code where 1 = 1
        <if test="null != nodeCode and nodeCode != '' ">
            and a.node_code like concat('%',#{nodeCode},'%')
        </if>
        <if test="null != name and name != '' ">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="null != nodeName and nodeName != '' ">
            and b.node_name like concat('%',#{nodeName},'%')
        </if>
    </select>

</mapper>
