<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectInfoAbarbeitungRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectInfoAbarbeitung">
        <id column="abarbeitung_id" property="abarbeitungId"/>
        <result column="project_id" property="projectId"/>
        <result column="abarbeitung_type" property="abarbeitungType"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>
        <result column="node_code" property="nodeCode"/>
        <result column="file_node_id" property="fileNodeId"/>
        <result column="problem_description" property="problemDescription"/>
        <result column="solution" property="solution"/>
        <result column="status" property="status"/>

        <result column="title" property="title"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        abarbeitung_id,project_id,abarbeitung_type,remark,create_time,create_by,update_time,update_by,is_enabled,is_delete,node_code,file_node_id,problem_description,solution,status,title
    </sql>

</mapper>
