<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateTableRelevanceExpansionRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateTableRelevanceExpansion">
        <id column="expansion_id" property="expansionId"/>
        <result column="relevance_id" property="relevanceId"/>
        <result column="is_must_mined" property="isMustMined"/>
        <result column="construction_type" property="constructionType"/>
        <result column="total_item" property="totalItem"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        expansion_id,relevance_id,is_must_mined,construction_type,total_item
    </sql>

</mapper>
