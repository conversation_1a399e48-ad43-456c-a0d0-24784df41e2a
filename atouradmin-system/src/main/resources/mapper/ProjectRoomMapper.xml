<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectRoomRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectRoom">
                    <id column="id" property="id" />
                    <result column="project_id" property="projectId" />
                    <result column="floor_num" property="floorNum" />
                    <result column="room_num" property="roomNum" />
                    <result column="room_type" property="roomType" />
                    <result column="is_used" property="isUsed" />
                    <result column="is_room_plan_effect_drawing" property="isRoomPlanEffectDrawing" />

        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            id,project_id,floor_num,room_num,room_type,isUsed,is_room_plan_effect_drawing
        </sql>

    <select id="getModelRoom" resultType="com.bassims.modules.atour.domain.ProjectRoom">
        select *
        from t_project_room
        where is_model_room = 1 and project_id =#{projectId}
    </select>



    <select id="getRoomCount" resultType="Integer">
        select count(1)
        from t_project_room
        where   project_id =#{projectId}
    </select>
    <select id="getRoomisModelRoomCount" resultType="Integer">
        select count(1)
        from t_project_room
        where   project_id =#{projectId} and  is_model_room = 1
    </select>



</mapper>
