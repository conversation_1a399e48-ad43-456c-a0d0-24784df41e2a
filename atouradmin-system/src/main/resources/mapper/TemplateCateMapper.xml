<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateCateRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateCate">
                    <id column="cate_template_id" property="cateTemplateId" />
                    <result column="cate_name" property="cateName" />
                    <result column="cate_type" property="cateType" />
                    <result column="cate_shili" property="cateShili" />
                    <result column="cate_stauts" property="cateStauts" />
                    <result column="remark_one" property="remarkOne" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            cate_template_id,cate_name,cate_type,cate_shili,cate_stauts,remark_one,is_delete,create_time,create_by,update_time,update_by
        </sql>

</mapper>
