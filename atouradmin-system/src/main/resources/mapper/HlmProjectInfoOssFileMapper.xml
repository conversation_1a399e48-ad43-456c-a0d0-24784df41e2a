<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.HlmProjectInfoOssFileRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.HlmProjectInfoOssFile">
        <id column="file_id" property="fileId"/>
        <result column="project_id" property="projectId"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="file_name" property="fileName"/>
        <result column="url" property="url"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        file_id,project_id,hotel_id,file_name,url
    </sql>

    <select id="getByProjectId" resultType="com.bassims.modules.atour.service.dto.HlmProjectInfoOssFileVO">
        select *
        from t_hlm_project_info_oss_file
        where project_id = #{projectId}
    </select>

</mapper>
