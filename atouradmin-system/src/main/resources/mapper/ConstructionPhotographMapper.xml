<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ConstructionPhotographRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ConstructionPhotograph">
        <id column="construction_id" property="constructionId"/>
        <result column="project_id" property="projectId"/>
        <result column="show_house_name" property="showHouseName"/>
        <result column="guest_room_aisle" property="guestRoomAisle"/>
        <result column="bedroom_area" property="bedroomArea"/>
        <result column="guest_bathroom" property="guestBathroom"/>
        <result column="wall_space" property="wallSpace"/>
        <result column="guest_shower_room" property="guestShowerRoom"/>
        <result column="suspended_ceiling" property="suspendedCeiling"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>
        <result column="node_code" property="nodeCode"/>
        <result column="building_number" property="buildingNumber"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        construction_id,project_id,show_house_name,guest_room_aisle,bedroom_area,guest_bathroom,wall_space,guest_shower_room,suspended_ceiling,remark,create_time,create_by,update_time,update_by,is_enabled,is_delete,node_code,building_number
    </sql>

    <delete id="deleteByIds">
        delete from  t_construction_photograph where construction_id in
        <foreach collection="collects" item="collect" open="(" separator="," close=")">
            #{collect}
        </foreach>
    </delete>

</mapper>
