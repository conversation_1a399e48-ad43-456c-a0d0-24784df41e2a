<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateTableGroupRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateTableGroup">
        <id column="template_table_group_id" property="templateTableGroupId"/>
        <result column="group_code" property="groupCode"/>
        <result column="relevancy_node_code" property="relevancyNodeCode"/>
        <result column="relevancy_node_name" property="relevancyNodeName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="project_version" property="projectVersion"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_edit" property="isEdit"/>
        <result column="project_type" property="projectType"/>
        <result column="brand" property="brand"/>
        <result column="store_type" property="storeType"/>
        <result column="dynamic_table_type" property="dynamicTableType"/>
        <result column="group_name" property="groupName"/>
        <result column="table_type" property="tableType"/>
        <result column="product_code" property="productCode"/>
        <result column="brand_code" property="brandCode"/>


    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        template_table_group_id,group_code,relevancy_node_code,relevancy_node_name,
        is_delete,project_version,create_time,create_by,update_time,update_by,is_enabled,is_edit,
        project_type,brand,store_type,dynamic_table_type,group_name,table_type,product_code, brand_code

    </sql>


    <resultMap id="getByIdNodeList" type="com.bassims.modules.atour.service.dto.TemplateTableGroupDto">
        <id column="template_table_group_id" property="templateTableGroupId"/>
        <result column="group_code" property="groupCode"/>
        <result column="relevancy_node_code" property="relevancyNodeCode"/>
        <result column="relevancy_node_name" property="relevancyNodeName"/>
        <result column="project_version" property="projectVersion"/>
        <result column="project_type" property="projectType"/>
        <result column="brand" property="brand"/>
        <result column="store_type" property="storeType"/>
        <result column="dynamic_table_type" property="dynamicTableType"/>
        <result column="group_name" property="groupName"/>
        <result column="table_type" property="tableType"/>
        <result column="product_code" property="productCode"/>
        <result column="brand_code" property="brandCode"/>
        <collection property="templateTableList" ofType="com.bassims.modules.atour.domain.TemplateTable">
            <id column="template_table_id" property="templateTableId"/>
            <result column="node_code" property="nodeCode"/>
            <result column="node_wbs" property="nodeWbs"/>
            <result column="node_index" property="nodeIndex"/>
            <result column="node_level" property="nodeLevel"/>
            <result column="node_type" property="nodeType"/>
            <result column="node_status" property="nodeStatus"/>
            <result column="remark" property="remark"/>
            <result column="added_version" property="addedVersion"/>
            <result column="relation_name" property="relationName"/>
            <result column="relation_code" property="relationCode"/>
            <result column="dynamic_table_type" property="dynamicTableType"/>
            <result column="is_must_mined" property="isMustMined"/>
            <result column="construction_type" property="constructionType"/>
        </collection>
    </resultMap>
    <select id="getByIdNodeList" resultMap="getByIdNodeList">
        SELECT t_template_table_group.*,
               t_template_table.template_table_id,
               t_template_table.node_code,
               t_template_table.node_wbs,
               t_template_table.node_index,
               t_template_table.node_level,
               t_template_table.node_type,
               t_template_table.node_status,
               t_template_table.remark,
               t_template_table.added_version,
               t_template_table.relation_name,
               t_template_table.dynamic_table_type,
               t_template_table_relevance_expansion.is_must_mined,
               t_template_table_relevance_expansion.construction_type
        FROM t_template_table_group t_template_table_group
                 LEFT JOIN t_template_table_relevance t_template_table_relevance
                           ON t_template_table_relevance.template_table_group_id =
                              t_template_table_group.template_table_group_id
                 LEFT JOIN t_template_table_relevance_expansion t_template_table_relevance_expansion
                           ON t_template_table_relevance.relevance_id =
                              t_template_table_relevance_expansion.relevance_id
                 LEFT JOIN t_template_table t_template_table
                           ON t_template_table.template_table_id = t_template_table_relevance.table_id
        WHERE t_template_table_group.template_table_group_id = #{templateTableGroupId}

    </select>
</mapper>
