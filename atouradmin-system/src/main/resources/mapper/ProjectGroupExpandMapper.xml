<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectGroupExpandRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectGroupExpand">
                    <id column="expand_id" property="expandId" />
                    <result column="project_id" property="projectId" />
                    <result column="reclassify" property="reclassify" />
                    <result column="review_attribute" property="reviewAttribute" />
                    <result column="file_header" property="fileHeader" />
                    <result column="drawing_file" property="drawingFile" />
                    <result column="group_node_code" property="groupNodeCode" />
                    <result column="node_code" property="nodeCode" />
                    <result column="upload_date" property="uploadDate" />
                    <result column="operator" property="operator" />
                    <result column="drawing_status" property="drawingStatus" />
                    <result column="template_code" property="templateCode" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="describes" property="describes" />
                    <result column="file_check_format" property="fileCheckFormat" />
                    <result column="relation_code" property="relationCode" />
                    <result column="expand_code" property="expandCode" />

                    <result column="design_text" property="designText" />

                    <result column="is_drawing_change" property="isDrawingChange" />
                    <result column="drawing_change_code" property="drawingChangeCode" />

                    <result column="pdf_mandatory" property="pdfMandatory"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            expand_id,project_id,reclassify,review_attribute,file_header,drawing_file,group_node_code,node_code,upload_date,operator,drawing_status,template_code,is_enabled,is_delete,create_time,update_time,create_by,update_by,describes,file_check_format,relation_code,expand_code
            ,designText,is_drawing_change,drawing_change_code,pdf_mandatory
        </sql>

    <select id="queryAllDeepeningPlanList" resultType="com.bassims.modules.atour.service.dto.ProjectGroupExpandDto">
        SELECT
            t_project_group_expand.* ,
            t_project_group.parent_id AS templateId,
            t_project_group.project_group_id AS nodeId
        FROM
            t_project_group_expand t_project_group_expand
                left join t_project_group t_project_group on   t_project_group_expand.group_node_code = t_project_group.node_code and
                                                               t_project_group_expand.project_id = t_project_group.project_id
        WHERE
            t_project_group_expand.project_id = #{projectId} and t_project_group_expand.review_attribute = #{reviewAttribute}
        and (t_project_group_expand.is_drawing_change is null or t_project_group_expand.is_drawing_change = 0)
    </select>
    <select id="queryAllDesignTextList"
            resultType="com.bassims.modules.atour.service.dto.ProjectGroupExpandDto">
        SELECT
            t_project_group_expand.* ,
            t_project_group.parent_id AS templateId,
            t_project_group.project_group_id AS nodeId,
            CASE review_attribute
                WHEN 'deepening_plan' THEN
                    2
                ELSE
                    1
                END  AS type
        FROM
            t_project_group_expand t_project_group_expand
                left join t_project_group t_project_group on   t_project_group_expand.group_node_code = t_project_group.node_code and
                                                               t_project_group_expand.project_id = t_project_group.project_id
        WHERE
            t_project_group_expand.project_id = #{projectId}
        <if test="null != designText and designText != '' ">
          and t_project_group_expand.design_text = #{designText}
        </if>
    </select>


    <select id="getDrawingStatus" resultType="String">
        SELECT
            CONCAT( a.file_header, '：', b.label ) AS projectGroupName
        FROM
            t_project_group_expand a
                LEFT JOIN s_sys_dict_detail b ON a.drawing_status = b.`value`
                AND b.dict_id = 211
        WHERE
            FIND_IN_SET(
                    a.expand_code,(
                SELECT
                    GROUP_CONCAT( t_project_group_expand.relation_code SEPARATOR ',' )
                FROM
                    t_project_group_expand t_project_group_expand
                WHERE
                    t_project_group_expand.project_id = #{projectId}
                  AND t_project_group_expand.`group_node_code` = #{nodeCode}
            ))
          AND a.project_id = #{projectId}
          AND a.group_node_code =  #{relevanceNodeCode}
    </select>
</mapper>
