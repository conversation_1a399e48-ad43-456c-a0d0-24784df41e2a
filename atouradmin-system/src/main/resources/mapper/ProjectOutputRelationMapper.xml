<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectOutputRelationRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectOutputRelation">
                    <id column="project_output_relation_id" property="projectOutputRelationId" />
                    <result column="project_id" property="projectId" />
                    <result column="output_code" property="outputCode" />
                    <result column="template_group_id" property="templateGroupId" />
                    <result column="node_code" property="nodeCode" />
                    <result column="node_name" property="nodeName" />
                    <result column="relevance_template_group_id" property="relevanceTemplateGroupId" />
                    <result column="relevance_node_code" property="relevanceNodeCode" />
                    <result column="relevance_node_name" property="relevanceNodeName" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
                    <result column="second_level_node_code" property="secondLevelNodeCode" />

        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            project_output_relation_id,project_id,output_code,template_group_id,node_code,node_name,relevance_template_group_id,relevance_node_code,relevance_node_name,create_time,update_time,create_by,update_by,is_enabled,is_delete,second_level_node_code
        </sql>

</mapper>
