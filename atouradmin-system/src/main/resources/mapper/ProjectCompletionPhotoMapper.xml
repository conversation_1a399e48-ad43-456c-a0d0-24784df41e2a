<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectCompletionPhotoRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectCompletionPhoto">
                    <id column="project_receipt_id" property="projectReceiptId" />
                    <result column="project_id" property="projectId" />
                    <result column="total_item" property="totalItem" />
                    <result column="sub_item" property="subItem" />
                    <result column="standard_photos" property="standardPhotos" />
                    <result column="remarks" property="remarks" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            project_receipt_id,project_id,total_item,sub_item,standard_photos,remarks,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

    <select id="getPhotoBySubItem"
            resultType="com.bassims.modules.atour.domain.ProjectCompletionPhoto">
        SELECT
            t_project_completion_photo.*
        FROM
            t_project_completion_photo t_project_completion_photo
        WHERE
            t_project_completion_photo.project_id = #{projectId}
        <if test="null != totalItem and totalItem != '' ">
            AND t_project_completion_photo.total_item = #{totalItem}
        </if>
        <if test="null != subItem and subItem != '' ">
            AND t_project_completion_photo.sub_item = #{subItem}
        </if>
          AND t_project_completion_photo.is_delete = 0
    </select>

</mapper>
