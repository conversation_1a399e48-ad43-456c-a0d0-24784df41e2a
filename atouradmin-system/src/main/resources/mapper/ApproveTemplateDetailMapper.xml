<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ApproveTemplateDetailRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ApproveTemplateDetail">
                    <id column="approve_template_detail_id" property="approveTemplateDetailId" />
                    <result column="approve_template_id" property="approveTemplateId" />
                    <result column="parent_id" property="parentId" />
                    <result column="approve_num" property="approveNum" />
                    <result column="approve_role" property="approveRole" />
                    <result column="approve_mode" property="approveMode" />
                    <result column="has_child" property="hasChild" />
                    <result column="approve_group" property="approveGroup" />
                    <result column="approve_level" property="approveLevel" />
                    <result column="approve_begin" property="approveBegin" />
                    <result column="is_modifiable" property="isModifiable" />
                    <result column="modifiable_code" property="modifiableCode" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
                    <result column="approve_index" property="approveIndex" />

                    <result column="modifiable_code_textarea" property="modifiableCodeTextarea" />
                    <result column="is_show" property="isShow" />
                    <result column="is_exist_open_condition" property="isExistOpenCondition" />
                    <result column="approval_opening_condition_node_code" property="approvalOpeningConditionNodeCode" />
                    <result column="approval_opening_condition_value" property="approvalOpeningConditionValue" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            approve_template_detail_id,approve_template_id,parent_id,approve_num,approve_role,approve_mode,has_child,approve_group,approve_level,approve_begin,is_modifiable,modifiable_code,create_time,create_by,update_time,update_by,is_enabled,is_delete,approve_index
            ,modifiable_code_textarea,is_show,is_exist_open_condition,approval_opening_condition_node_code,approval_opening_condition_value
        </sql>
    <select id="getTemplateDetailList" resultType="com.bassims.modules.atour.service.dto.ApproveTemplateDetailDto">
        select d.*,
               j.name role_name,
               j.NAME approveRoleName,
               dict.label mode_name
        from t_approve_template_detail d
            LEFT JOIN s_sys_role j ON d.approve_role=j.role_id
            LEFT JOIN s_sys_dict_detail dict on dict.`value`=d.approve_mode
         where d.approve_template_id=#{templateId}
    </select>

</mapper>
