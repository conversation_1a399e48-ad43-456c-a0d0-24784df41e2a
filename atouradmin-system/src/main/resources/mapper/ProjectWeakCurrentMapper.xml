<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectWeakCurrentRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectWeakCurrent">
                    <id column="project_weak_current_id" property="projectWeakCurrentId" />
                    <result column="project_id" property="projectId" />
                    <result column="facilities_equipment_name" property="facilitiesEquipmentName" />
                    <result column="brand" property="brand" />
                    <result column="model" property="model" />
                    <result column="position" property="position" />
                    <result column="site_photos" property="sitePhotos" />
                    <result column="remarks" property="remarks" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
             project_weak_current_id,project_id,facilities_equipment_name,brand,model,position,site_photos,remarks,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

</mapper>
