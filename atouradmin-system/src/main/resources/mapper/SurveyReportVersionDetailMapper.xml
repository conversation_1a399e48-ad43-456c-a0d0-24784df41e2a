<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SurveyReportVersionDetailRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SurveyReportVersionDetail">
                    <id column="survey_report_version_detail_id" property="surveyReportVersionDetailId" />
                    <result column="survey_report_version_id" property="surveyReportVersionId" />
                    <result column="node_id" property="nodeId" />
                    <result column="node_code" property="nodeCode" />
                    <result column="node_name" property="nodeName" />
                    <result column="node_index" property="nodeIndex" />
                    <result column="node_type" property="nodeType" />
                    <result column="remark" property="remark" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            survey_report_version_detail_id,survey_report_version_id,node_id,node_code,node_name,node_index,node_type,remark,is_delete,create_time,create_by,update_time,update_by,is_enabled
        </sql>

</mapper>
