<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SysUsersRolesRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SysUsersRoles">
                    <id column="user_id" property="userId" />
                    <id column="role_id" property="roleId" />
                    <result column="is_manually_add" property="isManuallyAdd" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            user_id,role_id,is_manually_add
        </sql>

    <select id="findByUserId" resultType="com.bassims.modules.system.domain.Role">
        SELECT r.role_id AS id,u.is_manually_add AS isManuallyAdd ,r.*
        FROM s_sys_role r, s_sys_users_roles u
        WHERE  r.role_id = u.role_id AND u.user_id = #{userId}
    </select>

</mapper>
