<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.MtoClassRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.MtoClass">
                    <id column="id" property="id" />
                    <result column="parent_id" property="parentId" />
                    <result column="class_no" property="classNo" />
                    <result column="name" property="name" />
                    <result column="type" property="type" />
                    <result column="desc" property="desc" />
                    <result column="used_status" property="usedStatus" />
                    <result column="sort" property="sort" />
                    <result column="create_time" property="createTime" />
                    <result column="create_user" property="createUser" />
                    <result column="create_dept" property="createDept" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_user" property="updateUser" />
                    <result column="is_delete" property="isDelete" />
                    <result column="tenant_id" property="tenantId" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            id,parent_id,class_no,`name`,`type`,`desc`,used_status,sort,create_time,create_user,create_dept,update_time,update_user,is_delete,tenant_id
        </sql>

    <select id="selectIdByLevel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mto_class
        where parent_id = (select id from t_mto_class where `name` = #{level1})
        and `name` = #{level2}
    </select>
</mapper>
