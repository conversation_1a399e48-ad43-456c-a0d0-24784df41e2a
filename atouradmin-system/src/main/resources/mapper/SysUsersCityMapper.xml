<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SysUsersCityRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SysUsersCity">
                    <id column="user_city_id" property="userCityId" />
                    <result column="area_code" property="areaCode" />
                    <result column="user_id" property="userId" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            user_city_id,area_code,user_id,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

    <delete id="deleteCityRelationByUserId">
        delete from  s_sys_users_city  where user_id = #{userId}
    </delete>

    <insert id="insertCityRelationByUserId">
        insert into s_sys_users_city(area_code,user_id) values(#{param2},#{param1})
    </insert>

    <select id="addAllCityRelation" resultType="java.math.BigInteger">
        SELECT
            s_sys_area.area_code
        FROM
            s_sys_area s_sys_area
        WHERE
            s_sys_area.`level` = 1
        GROUP BY
            s_sys_area.area_code
    </select>
    <select id="queryUserLimit" resultType="java.lang.Integer">
        select count(*) from s_sys_users_roles a  left join s_sys_roles_menus b on a.role_id = b.role_id left join s_sys_menu c on b.menu_id= c.menu_id
        where a.user_id=#{userId} and FIND_IN_SET(c.title,#{menuTitle})
    </select>

    <select id="getCityRelationByUserFirstDepartId" resultType="java.math.BigInteger">
        SELECT
            s_sys_area.area_code
        FROM
            s_user_info s_user_info
                LEFT JOIN (
                SELECT
                    s_sys_area.*,
                    s_sys_dict_detail.label
                FROM
                    s_sys_area s_sys_area
                        LEFT JOIN s_sys_dict_detail s_sys_dict_detail ON s_sys_dict_detail.`value` = s_sys_area.region
                WHERE
                    s_sys_dict_detail.dict_id = 8
                  AND s_sys_area.`level` = 1
            ) s_sys_area ON FIND_IN_SET( s_sys_area.label, s_user_info.first_depart_name ) > 0
        WHERE
            s_user_info.first_depart_name IS NOT NULL
          AND s_user_info.user_id != 1 AND s_user_info.user_id = #{param1}
        GROUP BY
            s_sys_area.area_code
        HAVING
            s_sys_area.area_code IS NOT NULL

        UNION

        SELECT
            s_sys_area1.area_code
        FROM
            s_user_info s_user_info
                LEFT JOIN (
                SELECT
                    s_sys_area.*,
                    s_sys_dict_detail.label
                FROM
                    s_sys_area s_sys_area
                        LEFT JOIN s_sys_dict_detail s_sys_dict_detail ON s_sys_dict_detail.`value` = s_sys_area.operational_theater
                WHERE
                    s_sys_dict_detail.dict_id = 196
                  AND s_sys_area.`level` = 1
            ) s_sys_area1 ON FIND_IN_SET( s_sys_area1.label, s_user_info.first_depart_name ) > 0
        WHERE
            s_user_info.first_depart_name IS NOT NULL
          AND s_user_info.user_id != 1  AND s_user_info.user_id = #{param1}
        GROUP BY
            s_sys_area1.area_code
        HAVING
            s_sys_area1.area_code IS NOT NULL


    </select>
</mapper>
