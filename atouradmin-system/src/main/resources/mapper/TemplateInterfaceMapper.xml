<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateInterfaceRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateInterface">
                    <id column="template_interface_id" property="templateInterfaceId" />
                    <result column="template_id" property="templateId" />
                    <result column="interface_info_id" property="interfaceInfoId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            template_interface_id,template_id,interface_info_id,create_time,update_time,create_by,update_by,is_enabled,is_delete
        </sql>

</mapper>
