<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.OrderNodeInfoRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.OrderNodeInfo">
                    <id column="node_id" property="nodeId" />
                    <result column="project_id" property="projectId" />
                    <result column="template_queue_id" property="templateQueueId" />
                    <result column="template_id" property="templateId" />
                    <result column="order_id" property="orderId" />
                    <result column="parent_id" property="parentId" />
                    <result column="project_version" property="projectVersion" />
                    <result column="node_code" property="nodeCode" />
                    <result column="node_name" property="nodeName" />
                    <result column="plan_start_date" property="planStartDate" />
                    <result column="plan_end_date" property="planEndDate" />
                    <result column="predict_start_date" property="predictStartDate" />
                    <result column="predict_end_date" property="predictEndDate" />
                    <result column="actual_end_date" property="actualEndDate" />
                    <result column="plan_day" property="planDay" />
                    <result column="notice_day" property="noticeDay" />
                    <result column="delay_day" property="delayDay" />
                    <result column="node_wbs" property="nodeWbs" />
                    <result column="node_index" property="nodeIndex" />
                    <result column="node_level" property="nodeLevel" />
                    <result column="node_type" property="nodeType" />
                    <result column="node_status" property="nodeStatus" />
                    <result column="node_isfin" property="nodeIsfin" />
                    <result column="front_wbs_config" property="frontWbsConfig" />
                    <result column="is_key" property="isKey" />
                    <result column="key_front_wbs" property="keyFrontWbs" />
                    <result column="remark" property="remark" />
                    <result column="relation_code" property="relationCode" />
                    <result column="relation_type" property="relationType" />
                    <result column="down_code" property="downCode" />
                    <result column="job_code" property="jobCode" />
                    <result column="use_case" property="useCase" />
                    <result column="is_open" property="isOpen" />
                    <result column="is_delete" property="isDelete" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="start_sign" property="startSign" />
                    <result column="end_sign" property="endSign" />
                    <result column="total_day" property="totalDay" />
                    <result column="is_mobile" property="isMobile" />
                    <result column="role_code" property="roleCode" />
                    <result column="is_edit" property="isEdit" />
                    <result column="seat" property="seat" />
                    <result column="icon" property="icon" />
                    <result column="formula" property="formula" />
                    <result column="formula_code" property="formulaCode" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            node_id,project_id,template_queue_id,template_id,order_id,parent_id,project_version,node_code,node_name,plan_start_date,plan_end_date,predict_start_date,predict_end_date,actual_end_date,plan_day,notice_day,delay_day,node_wbs,node_index,node_level,node_type,node_status,node_isfin,front_wbs_config,is_key,key_front_wbs,remark,relation_code,relation_type,down_code,job_code,use_case,is_open,is_delete,create_time,create_by,update_time,update_by,is_enabled,start_sign,end_sign,total_day,is_mobile,role_code,is_edit,seat,icon,formula,formula_code
        </sql>
    <select id="getAllNodeInfo" resultType="com.bassims.modules.atour.service.dto.OrderNodeInfoDto">
        select n.*,
        if(sum(j.is_read)>0,1,0) is_read,
        if(sum(j.is_write)>0,1,0) is_write,
        if(sum(j.is_hidden)>0,1,0) is_hidden
        from t_project_group n LEFT JOIN t_job_node_info j on n.template_id=j.node_id
        and n.template_group_id=j.template_group_id
        <if test="null != jobIds and jobIds.size > 0 ">
            AND j.job_id IN
            <foreach collection="jobIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        and j.is_delete=0 where n.order_id=#{orderId}
        <if test="null != useCase and useCase != '' ">
            AND n.use_case = #{useCase}
        </if>
        <if test="null != isMobile and isMobile != '' ">
            AND n.is_mobile =#{isMobile}
        </if>
        GROUP BY n.template_id,n.template_group_id,n.project_group_id;
    </select>
    <select id="getLevelTwoNodeInfo" resultType="com.bassims.modules.atour.service.dto.OrderNodeInfoDto">
         select n.*,if(sum(j.is_read)>0,1,0) is_read, if(sum(j.is_write)>0,1,0) is_write,if(sum(j.is_hidden)>0,1,0) is_hidden
        from t_project_group n LEFT JOIN t_job_node_info j on n.template_id=j.node_id
        and n.template_group_id=j.template_group_id
        <if test="null != jobIds and jobIds.size > 0 ">
            AND j.job_id IN
            <foreach collection="jobIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        and j.is_delete=0 where n.order_id=#{orderId} AND n.node_code=#{nodeCode} GROUP BY n.template_id,n.template_group_id,n.project_group_id;
    </select>

</mapper>
