<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.BrandTemplateRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.BrandTemplate">
                    <id column="id" property="id" />
                    <result column="brand_name" property="brandName" />
                    <result column="brand_code" property="brandCode" />
                    <result column="relation_template" property="relationTemplate" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            id,brand_name,brand_code,relation_template
        </sql>

</mapper>
