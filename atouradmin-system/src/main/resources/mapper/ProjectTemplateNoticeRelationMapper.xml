<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectTemplateNoticeRelationRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectTemplateNoticeRelation">
                    <id column="template_relation_id" property="templateRelationId" />
                    <result column="node_code" property="nodeCode" />
                    <result column="template_id" property="templateId" />
                    <result column="approve_template_detail_id" property="approveTemplateDetailId" />
                    <result column="notice_id" property="noticeId" />
                    <result column="job_id" property="jobId" />
                    <result column="user_id" property="userId" />
                    <result column="user_name" property="userName" />
                    <result column="notice_type" property="noticeType" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            template_relation_id,node_code,template_id,approve_template_detail_id,notice_id,job_id,user_id,user_name,notice_type,create_time,update_time,create_by,update_by,is_enabled,is_delete
        </sql>
    <select id="getTemplateNoticeList"
            resultType="com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto">
        select <include refid="Base_Column_List"/> from t_project_template_notice_relation where template_id=#{templateId}
    </select>
    <select id="getAppTemplateNoticeList"
            resultType="com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto">
        select <include refid="Base_Column_List"/> from t_project_template_notice_relation where approve_template_detail_id =#{templateDetailId}
    </select>

        <select id="getTemplateNoticeListByGroupId"
                resultType="com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto">
            select <include refid="Base_Column_List"/> from t_project_template_notice_relation where template_group_id=#{groupId}
    </select>
</mapper>
