
-- 修改新开店的模版关联
UPDATE `atour-con`.`t_template_collection_relation` SET `condition_code` = 'e,f,g,P,O,N,a,b,c,d,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,B,C,D,E,F,G,H,I,J,K,L,M' WHERE `template_collection_relation_id` = 3;
UPDATE `atour-con`.`t_template_collection_relation` SET `condition_code` = 'h,a,b,c,d,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,B,C,D,E,F,G,H,I,J,K,L,M' WHERE `template_collection_relation_id` = 17;

-- 修改系统模版的排序
UPDATE `atour-con`.`t_template_group` SET `node_index` = 1 where `node_level` = '0' AND `template_code` IN ('engineering', 'engineering(newYDXJD)');
UPDATE `atour-con`.`t_template_group` SET `node_index` = 2 where `node_level` = '0' AND `template_code` IN ('design', 'design(newYDXJD)');
UPDATE `atour-con`.`t_template_group` SET `node_index` = 3 where `node_level` = '0' AND `template_code` IN ('public_area_design', 'public_area_design(newYDXJD)');

UPDATE `atour-con`.`t_project_group` SET `node_index` = 1 where `node_level` = '0' AND `template_code` IN ('engineering', 'engineering(newYDXJD)');
UPDATE `atour-con`.`t_project_group` SET `node_index` = 2 where `node_level` = '0' AND `template_code` IN ('design', 'design(newYDXJD)');
UPDATE `atour-con`.`t_project_group` SET `node_index` = 3 where `node_level` = '0' AND `template_code` IN ('public_area_design', 'public_area_design(newYDXJD)');


-- 修改字典，执行标准的名称存在空格的问题
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵酒店3.0' WHERE `detail_id` = 831;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵酒店3.4' WHERE `detail_id` = 832;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵酒店3.5' WHERE `detail_id` = 833;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵酒店4.0' WHERE `detail_id` = 834;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '轻居酒店2.0' WHERE `detail_id` = 837;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '轻居酒店2.5' WHERE `detail_id` = 838;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '轻居酒店3.0' WHERE `detail_id` = 839;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵S酒店' WHERE `detail_id` = 841;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵S酒店1.1' WHERE `detail_id` = 842;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵S酒店1.4' WHERE `detail_id` = 843;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '亚朵S酒店1.5' WHERE `detail_id` = 844;

-- 修改项目中执行标准的值存在空格的问题
UPDATE `atour-con`.`t_project_node_info_1`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_2`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_3`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_4`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_5`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_6`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_7`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_8`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';
UPDATE `atour-con`.`t_project_node_info_9`  SET `remark` = REGEXP_REPLACE ( remark, ' ', '' ) WHERE	`node_name` = '执行标准';


-- 修改审批下标
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404033;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404034;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404036;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404037;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404039;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404040;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404042;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404043;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404045;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404046;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404048;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404049;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404051;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404052;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404054;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404056;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404057;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404059;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404060;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404062;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404063;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404065;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404069;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404070;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404072;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404073;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404075;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404076;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404078;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 3 WHERE `approve_template_detail_id` = 1782255161247404079;
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_index` = 2 WHERE `approve_template_detail_id` = 1782255161247404181;

--  修改重复的干系人状态为离项状态
UPDATE t_project_stakeholders
SET shakeholder_status = 'off_term'
WHERE stakeholder_id IN ( '1792899469759660033',
                           '1792899398456455169',
                           '1792877091126165505',
                           '1783117859480924161');