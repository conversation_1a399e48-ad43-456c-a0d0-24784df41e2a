-- 调整竣工验收页面布局
DELETE FROM `atour-con`.`t_template_queue` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
UPDATE `atour-con`.`t_template_queue` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_template_queue` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');

DELETE FROM `atour-con`.`t_project_node_info_1` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_2` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_3` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_4` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_5` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_6` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_7` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_8` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;
DELETE FROM `atour-con`.`t_project_node_info_9` WHERE `node_code` LIKE '%eng-00129%' AND `node_name` LIKE '%分类%' ORDER BY `node_index`;

UPDATE `atour-con`.`t_project_node_info_1` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_2` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_3` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_4` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_5` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_6` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_7` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_8` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;
UPDATE `atour-con`.`t_project_node_info_9` SET `node_name` = 'A类资料审核' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '资料审核' ORDER BY `node_index`;

UPDATE `atour-con`.`t_project_node_info_1` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_2` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_3` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_4` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_5` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_6` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_7` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_8` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');
UPDATE `atour-con`.`t_project_node_info_9` SET `node_name` = 'B类资料审核' WHERE node_code in ('eng-00129191','eng-00129195');

-- 修改竣工验收申请-备注  改为 资料审核备注
UPDATE `atour-con`.`t_project_template` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_name` = '资料审核备注' WHERE `node_code` LIKE '%eng-00129%' AND `node_name` = '备注';

-- 竣工验收申请- 施工单位可编辑
UPDATE `atour-con`.`t_project_template` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_1` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_2` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_3` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_4` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_5` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_6` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_7` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_8` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;
UPDATE `atour-con`.`t_project_node_info_9` SET `is_edit` = '1' WHERE (`node_code` IN ('eng-00129030','eng-00129031','eng-00129032','eng-00129033','eng-00129034','eng-00129035','eng-00129036','eng-00129037','eng-00129038','eng-00129039','eng-00129040','eng-00129041','eng-00129042','eng-00129043','eng-00129044')) ;

-- 调整九宫格-设计文本显示
DELETE FROM `atour-con`.`t_template_custom_signage` WHERE `custom_signage_id` = 1717463447995813890;
UPDATE `atour-con`.`t_template_custom_signage` SET `second_name` = '机电' WHERE `custom_signage_id` = 1717463447995813894;
DELETE FROM `atour-con`.`t_template_custom_signage` WHERE `custom_signage_id` = 1717463447995813895;
DELETE FROM `atour-con`.`t_template_custom_signage` WHERE `custom_signage_id` = 1717463447995813896;
DELETE FROM `atour-con`.`t_template_custom_signage` WHERE `custom_signage_id` = 1717463447995813897;

UPDATE `atour-con`.`t_template_custom_signage` SET `is_enabled` = b'1' WHERE `custom_signage_id` = 1717463447995813889;
UPDATE `atour-con`.`t_template_custom_signage` SET `is_enabled` = b'1' WHERE `custom_signage_id` = 1717463447995813891;
UPDATE `atour-con`.`t_template_custom_signage` SET `is_enabled` = b'1' WHERE `custom_signage_id` = 1717463447995813892;
UPDATE `atour-con`.`t_template_custom_signage` SET `is_enabled` = b'1' WHERE `custom_signage_id` = 1717463447995813893;
UPDATE `atour-con`.`t_template_custom_signage` SET `is_enabled` = b'1' WHERE `custom_signage_id` = 1717463447995813894;

-- 调整九宫格-设计文本跳转页面地址
UPDATE `atour-con`.`t_template_custom_signage` SET `jump_address` = '/newKanban/designText/decorate' WHERE `custom_signage_id` = 1717463447995813891;
UPDATE `atour-con`.`t_template_custom_signage` SET `jump_address` = '/newKanban/designText/exteriorFacade' WHERE `custom_signage_id` = 1717463447995813889;
UPDATE `atour-con`.`t_template_custom_signage` SET `jump_address` = '/newKanban/designText/fireControl' WHERE `custom_signage_id` = 1717463447995813892;
UPDATE `atour-con`.`t_template_custom_signage` SET `jump_address` = '/newKanban/designText/weakCurrent' WHERE `custom_signage_id` = 1717463447995813893;
UPDATE `atour-con`.`t_template_custom_signage` SET `jump_address` = '/newKanban/designText/electromechanical' WHERE `custom_signage_id` = 1717463447995813894;


-- 调整图纸和设计文本对应关系
ALTER TABLE `atour-con`.`t_template_group_expand`
    ADD COLUMN `design_text` varchar(255) NULL COMMENT '所属设计文本' AFTER `not_cad`;

ALTER TABLE `atour-con`.`t_project_group_expand`
    ADD COLUMN `design_text` varchar(255) NULL COMMENT '所属设计文本' AFTER `not_cad`;
-- 装饰
UPDATE `atour-con`.`t_template_group_expand` SET `group_node_code` = 'des-00115', `node_code` = 'des-00115026', `design_text` = '装饰' WHERE `expand_id` = 1736292287262101572;
UPDATE `atour-con`.`t_template_group_expand` SET `file_header` = '样板间家具图纸', `design_text` = '装饰'  WHERE `file_header` LIKE '%样板间软装家具方案%';
UPDATE `atour-con`.`t_template_group_expand` SET `design_text` = '装饰' WHERE `file_header` IN ('样板间家具图纸','客房区VI方案','客房区软装家具方案','客房区装饰画（喷绘方案）','客房区材料清单','室外VI方案','室外店招深化方案','室外门头深化方案','公区装饰深化图纸','公区VI方案','公区软装方案（PDF/PPT）','公区装饰画（喷绘方案）','公区固定家具深化方案','公区活动家具深化方案','公区材料列表','公区百货家具深化方案','厨房设备深化方案（平面图）','厨房设备清单','客房平面方案（PPT）','客房平面图（CAD/PDF）','平面隔墙尺寸图（CAD/PDF）','样板间施工图（CAD/PDF）','客房装饰施工图','客房综合天花板图（CAD/PDF）','公区平⾯图（PDF/CAD）','公区概念方案（PPT）','公区效果方案（PDF/PPT）','公区装饰施工图（CAD/PDF）','亚朵百货施工图纸','公区综合天花板（CAD/PDF）','客房效果方案') ;
UPDATE `atour-con`.`t_project_group_expand` SET `group_node_code` = 'des-00115', `node_code` = 'des-00115026', `design_text` = '装饰' WHERE `expand_id` = 1736292287262101572;
UPDATE `atour-con`.`t_project_group_expand` SET `file_header` = '样板间家具图纸', `design_text` = '装饰'  WHERE `file_header` LIKE '%样板间软装家具方案%';
UPDATE `atour-con`.`t_project_group_expand` SET `design_text` = '装饰' WHERE `file_header` IN ('样板间家具图纸','客房区VI方案','客房区软装家具方案','客房区装饰画（喷绘方案）','客房区材料清单','室外VI方案','室外店招深化方案','室外门头深化方案','公区装饰深化图纸','公区VI方案','公区软装方案（PDF/PPT）','公区装饰画（喷绘方案）','公区固定家具深化方案','公区活动家具深化方案','公区材料列表','公区百货家具深化方案','厨房设备深化方案（平面图）','厨房设备清单','客房平面方案（PPT）','客房平面图（CAD/PDF）','平面隔墙尺寸图（CAD/PDF）','样板间施工图（CAD/PDF）','客房装饰施工图','客房综合天花板图（CAD/PDF）','公区平⾯图（PDF/CAD）','公区概念方案（PPT）','公区效果方案（PDF/PPT）','公区装饰施工图（CAD/PDF）','亚朵百货施工图纸','公区综合天花板（CAD/PDF）','客房效果方案' ) ;
-- 弱电
UPDATE `atour-con`.`t_template_group_expand` SET `file_header` = '公区弱电施工图（CAD/PDF）（含厨房）', `design_text` = '弱电'  WHERE `file_header` LIKE '%公区弱电施工图（CAD/PDF）%';
UPDATE `atour-con`.`t_template_group_expand` SET `design_text` = '弱电' WHERE `file_header` in ('客房区弱电深化方案','客房区弱电设备清单','公区弱电深化方案','公区弱电设备清单','客房弱电施工图（CAD/PDF）','公区弱电施工图（CAD/PDF）（含厨房）');
UPDATE `atour-con`.`t_project_group_expand` SET `file_header` = '公区弱电施工图（CAD/PDF）（含厨房）', `design_text` = '弱电'  WHERE `file_header` LIKE '%公区弱电施工图（CAD/PDF）%';
UPDATE `atour-con`.`t_project_group_expand` SET `design_text` = '弱电' WHERE `file_header` in ('客房区弱电深化方案','客房区弱电设备清单','公区弱电深化方案','公区弱电设备清单','客房弱电施工图（CAD/PDF）','公区弱电施工图（CAD/PDF）（含厨房）');
-- 机电
UPDATE `atour-con`.`t_template_group_expand` SET `file_header` = '公区暖通施工图（CAD/PDF）（含厨房）', `design_text` = '机电' WHERE `file_header` LIKE '%公区暖通施工图（CAD/PDF）%' ;
UPDATE `atour-con`.`t_template_group_expand` SET `file_header` = '公区给排水施工图（CAD/PDF）（含厨房）', `design_text` = '机电'  WHERE `file_header` LIKE '%公区给排水施工图（CAD/PDF）%' ;
UPDATE `atour-con`.`t_template_group_expand` SET `file_header` = '公区电气施工图（CAD/PDF）（含厨房）' , `design_text` = '机电' WHERE `file_header` LIKE '%公区电气施工图（CAD/PDF）%' ;
INSERT INTO `atour-con`.`t_template_group_expand` (`expand_id`, `reclassify`, `review_attribute`, `file_header`, `group_node_code`, `node_code`, `template_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `file_check_format`, `expand_code`, `relation_code`, `not_cad`, `design_text`)
VALUES (NULL, 'model_room_deepening', 'deepening_plan', '样板间机电图纸', 'dep-00201', 'dep-00201002', 'deepening_plan', b'1', b'0', '2023-12-23 16:10:13', '2023-12-23 16:10:15', NULL, NULL, 'pdf,PDF', NULL, 'des-0011102803', NULL, '机电');
UPDATE `atour-con`.`t_template_group_expand` SET   `design_text` = '机电'  WHERE `file_header` IN ('客房区热水热源深化方案','客房区空调深化方案','客房区新风深化方案','客房区地暖深化方案','客房区给排水深化方案','厨房排烟深化方案','公区热水热源深化方案','公区暖通深化方案','公区地暖深化方案','公区给排水深化方案','公区强电深化方案','公区其他深化方案','客房电气施工图（CAD/PDF）','客房暖通施工图（CAD/PDF）','客房给排⽔施⼯图（CAD/PDF）','公区电气施工图（CAD/PDF）（含厨房）','公区暖通施工图（CAD/PDF）（含厨房）','公区给排水施工图（CAD/PDF）（含厨房）','客房其他深化方案','样板间机电图纸');
UPDATE `atour-con`.`t_project_group_expand` SET `file_header` = '公区暖通施工图（CAD/PDF）（含厨房）', `design_text` = '机电' WHERE `file_header` LIKE '%公区暖通施工图（CAD/PDF）%' ;
UPDATE `atour-con`.`t_project_group_expand` SET `file_header` = '公区给排水施工图（CAD/PDF）（含厨房）', `design_text` = '机电'  WHERE `file_header` LIKE '%公区给排水施工图（CAD/PDF）%' ;
UPDATE `atour-con`.`t_project_group_expand` SET `file_header` = '公区电气施工图（CAD/PDF）（含厨房）' , `design_text` = '机电' WHERE `file_header` LIKE '%公区电气施工图（CAD/PDF）%' ;
UPDATE `atour-con`.`t_project_group_expand` SET   `design_text` = '机电'  WHERE `file_header` IN ('客房区热水热源深化方案','客房区空调深化方案','客房区新风深化方案','客房区地暖深化方案','客房区给排水深化方案','厨房排烟深化方案','公区热水热源深化方案','公区暖通深化方案','公区地暖深化方案','公区给排水深化方案','公区强电深化方案','公区其他深化方案','客房电气施工图（CAD/PDF）','客房暖通施工图（CAD/PDF）','客房给排⽔施⼯图（CAD/PDF）','公区电气施工图（CAD/PDF）（含厨房）','公区暖通施工图（CAD/PDF）（含厨房）','公区给排水施工图（CAD/PDF）（含厨房）','客房其他深化方案','样板间机电图纸');
INSERT INTO `atour-con`.`t_template_group_expand` (`expand_id`, `reclassify`, `review_attribute`, `file_header`, `group_node_code`, `node_code`, `template_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `file_check_format`, `expand_code`, `relation_code`, `not_cad`, `design_text`) VALUES (NULL, NULL, 'design', '客房综合机电管线图', 'des-00119', 'des-00119042', 'design', b'1', b'0', '2023-12-27 11:53:20', '2023-12-27 11:53:22', NULL, NULL, 'dwg,pdf,DWG,PDF', 'des-0011904205', NULL, NULL, '机电');

-- 外立面及室外
UPDATE `atour-con`.`t_template_group_expand` SET `design_text` = '外立面及室外'   WHERE `file_header` IN ('室外施工图（PDF/CAD）', '室外效果方案（PDF/PPT）');
UPDATE `atour-con`.`t_project_group_expand` SET `design_text` = '外立面及室外'   WHERE `file_header` IN ('室外施工图（PDF/CAD）', '室外效果方案（PDF/PPT）');
-- 消防
UPDATE `atour-con`.`t_template_group_expand` SET `design_text` = '消防' WHERE `file_header` IN ('消防点位对照表、平面图', '消防竣工图（喷淋、报警系统、消火栓、排烟）');
UPDATE `atour-con`.`t_project_group_expand` SET `design_text` = '消防' WHERE `file_header` IN ('消防点位对照表、平面图', '消防竣工图（喷淋、报警系统、消火栓、排烟）');
-- 厨房排烟深化方案 调整关联设计图纸
UPDATE `atour-con`.`t_template_group_expand` SET `relation_code` = 'pad-0013904102' WHERE `file_header` LIKE '%厨房排烟深化方案%';
UPDATE `atour-con`.`t_project_group_expand` SET `relation_code` = 'pad-0013904102' WHERE `file_header` LIKE '%厨房排烟深化方案%';


-- 项目所有确认图纸同步 修改类型
UPDATE `atour-con`.`t_project_template` SET `node_type` = 'skip_page' WHERE `node_code` =  'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_1` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_type` = 'skip_page' WHERE `node_code` = 'eng-00129016';

