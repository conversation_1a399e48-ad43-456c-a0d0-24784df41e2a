-- 开工申请、样板间验收申请、隐蔽验收申请任务去掉项目经理一审
UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1706138142265970688;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1706138551483240448;

UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1706138927095746560;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1706139003541131264;

UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1706139088882634752;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1706139144864010240;

UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1782255161247404181;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1782255161247404182;

UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1782255161247404183;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1782255161247404184;

UPDATE `atour-con`.`t_approve_template_detail` SET `approve_role` = '1260865836018032642' WHERE `approve_template_detail_id` = 1782255161247404185;
DELETE FROM `atour-con`.`t_approve_template_detail` WHERE `approve_template_detail_id` = 1782255161247404186;

UPDATE `atour-con`.`t_approve_template_detail` SET `is_modifiable` = 1, `modifiable_code` = 'eng-00115018' WHERE `approve_template_detail_id` = 1706139088882634752;
UPDATE `atour-con`.`t_approve_template_detail` SET `is_modifiable` = 1, `modifiable_code` = 'eng-00115018' WHERE `approve_template_detail_id` = 1782255161247404185;

ALTER TABLE `atour-con`.`t_project_completion_receipt`
    MODIFY COLUMN `standard_videos` text  NULL AFTER `receipt_group_id`;


ALTER TABLE `atour-con`.`t_project_completion_receipt`
    MODIFY COLUMN `commitment_letter` text  NULL COMMENT '承诺函' AFTER `room_number`,
    MODIFY COLUMN `standard_videos_rectify_reform` text  NULL COMMENT '整改视频' AFTER `standard_videos`,
    MODIFY COLUMN `rectification_document` text  NULL COMMENT '整改文件' AFTER `standard_videos_rectify_reform`;

UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '汉渝开发战区' WHERE `detail_id` = 669;

UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '山河开发战区' WHERE `detail_id` = 672;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '成都开发战区' WHERE `detail_id` = 2030;
UPDATE `atour-con`.`s_sys_dict_detail` SET `label` = '东南开发战区' WHERE `detail_id` = 2032;

INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`)
VALUES (6104, 8, '西北开发战区', '18', 18, 'admin', 'admin', '2025-01-06 13:48:27', '2025-01-06 13:48:32', NULL, NULL);

-- 重新调整开发战区与城市的对应关系
UPDATE `atour-con`.`s_sys_area` SET `region` = NULL WHERE `id` is not null;
UPDATE `atour-con`.`s_sys_area` SET `region` = '9' WHERE `parent_code` IN (140000000000, 370000000000, 410000000000);
UPDATE `atour-con`.`s_sys_area` SET `region` = '2' WHERE (`name` IN ('北京市', '天津市') OR `parent_code` = '130000000000')  AND `level` = '1' ;
UPDATE `atour-con`.`s_sys_area` SET `region` = '14'  WHERE (`parent_code` IN ('350000000000','360000000000','430000000000'))  AND `level` = '1' ;
UPDATE `atour-con`.`s_sys_area` SET `region` = '18'  WHERE (`parent_code` IN ('650000000000','620000000000','640000000000','630000000000','610000000000'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '17'  WHERE (`parent_code` IN ('330000000000'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '5'  WHERE (`parent_code` IN ('420000000000','450000000000','540000000000','500000000000'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '12'  WHERE (`parent_code` IN ('510000000000','530000000000','520000000000'))  AND `level` = '1' ;
UPDATE `atour-con`.`s_sys_area` SET `region` = '6'  WHERE (`parent_code` IN ('230000000000','210000000000','220000000000','150000000000'))  AND `level` = '1' ;
UPDATE `atour-con`.`s_sys_area` SET `region` = '15'  WHERE (`name` IN ('常州市', '南通市', '苏州市', '无锡市','镇江市','扬州市','泰州市'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '3'  WHERE (`name` IN ('广州市','佛山市','湛江市','茂名市','阳江市','云浮市','东莞市','江门市','清远市','韶关市','肇庆市') OR `parent_code` IN ('430000000000'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '16'  WHERE (`name` IN ('南京市','徐州市','连云港市','宿迁市','盐城市','淮安市') OR `parent_code` IN ('340000000000'))  AND `level` = '1';
UPDATE `atour-con`.`s_sys_area` SET `region` = '11'  WHERE (`name` IN ('深圳市','珠海市','汕头市','汕尾市','梅州市','潮州市','揭阳市','中山市','惠州市','河源市'))  AND `level` = '1' ;
UPDATE `atour-con`.`s_sys_area` SET `region` = '1'  WHERE (`name` = '上海市')  AND `level` = '1';

-- 客房其他深化方案
update t_template_group set front_wbs_config='[{"wbs":"des-00103","type":"FS"},{"wbs":"des-00165","type":"FS"}]'  WHERE node_code="dep-00231";

-- 调整系统开工申请、样板间验收申请、隐蔽验收申请、竣工自检申请4个任务的负责人 为项目经理
UPDATE `atour-con`.`t_template_group` SET `role_code` = 'gcjl' WHERE `node_code` IN ('eng-00105', 'eng-00113', 'eng-00115', 'eng-00145');
-- 追加项目开工申请、样板间验收申请、隐蔽验收申请、竣工自检申请4个任务项目经理负责人
update t_project_group t1 set t1.role_code = case when LENGTH(t1.role_code) = 0 then 'gcjl' when FIND_IN_SET('gcjl', t1.role_code) = 0
                                                                                                     and t1.role_code IS NOT NULL then CONCAT(t1.role_code,',gcjl') ELSE t1.role_code end	where t1.node_code IN ('eng-00105', 'eng-00113', 'eng-00115', 'eng-00145') and t1.node_status in ('task_unfin','task_uncommitted','task_submit');