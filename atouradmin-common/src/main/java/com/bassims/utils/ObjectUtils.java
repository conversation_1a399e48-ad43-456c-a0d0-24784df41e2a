package com.bassims.utils;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/31 0031
 */
public class ObjectUtils {

    /**
     * 实现属性 类型转换处理
     * @param type  属性类型，通过Filed获取的
     * @param value 属性的内容，传入的都是字符串，需要将其变为指定类型
     * @return  转换后的数据
     */
    public static Object convertAttributeValue(String type,String value){
        if("long".equals(type) || "java.lang.Long".equals(type)){
            if (StringUtils.isNotEmpty(value)){
                return Long.parseLong(value);
            }else{
                return 0L;
            }
        }else if("int".equals(type) || "java.lang.Integer".equals(type)){

            if (StringUtils.isNotEmpty(value)){
                return Integer.parseInt(value);
            }else{
                return 0;
            }
        }else if("double".equals(type) || "java.lang.Double".equals(type)){
            if (StringUtils.isNotEmpty(value)){
                return Double.parseDouble(value);
            }else{
                return 0;
            }
        }else if("BigDecimal".equals(type)||"java.math.BigDecimal".equals(type)){
            if (StringUtils.isNotEmpty(value)){
                return new BigDecimal(value);
            }else{
                return BigDecimal.ZERO;
            }
        }else{  //字符串
            return value;
        }
    }

}
