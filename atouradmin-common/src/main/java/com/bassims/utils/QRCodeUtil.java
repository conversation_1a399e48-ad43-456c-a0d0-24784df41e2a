package com.bassims.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Base64;
import java.util.Base64.Encoder;
import java.util.Hashtable;

/**
 * @Auther: xiongjb
 * @Date: 2021/4/2 09:01
 * @Description:
 */
public class QRCodeUtil {

    /**
     * 推荐二维码颜色 0xFF 后的六位为16进制的颜色值#A72325
     */
    public static final int QRCODE_COLOR = 0xFF000000;
    /**
     * 推荐二维码背景颜色
     */
    public static final int QRCODE_BACKGROUND_COLOR = 0xFFFFFFFF;
    /**
     * 推荐二维码的白边设置(0-4) 0为无白边
     */
    public static final int QRCODE_MARGIN = 0;
    /**
     * @Title: generalQRCode
     * @Description: 生成二维码并使用Base64编码
     * @param url 要生成的二维码内容
     * @param logoPath logo所在的网络地址
     * @return String 返回的base64格式的图片字符串(png格式)
     * @throws
     */
    public static String generalQRCode(String url,String logoPath) {
        Hashtable<EncodeHintType, Object> hints = new Hashtable<>();
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        hints.put(EncodeHintType.MARGIN, QRCodeUtil.QRCODE_MARGIN);  //设置白边
        byte[] binary = null;
        try {
            //生成二维码矩阵
            BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, 400, 400, hints);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            //二维码中画入logo
            BufferedImage image = writeLogoToQrcode(bitMatrix,logoPath);
            //文件转换为字节数组
            ImageIO.write(image, "png", out);
            byte[] bytes = out.toByteArray();
            //进行base64编码
            Encoder encoder = Base64.getEncoder();
            binary = encoder.encode(bytes);

        } catch (Exception e) {
            e.printStackTrace();
        }
        //返回png格式的base64编码数据 (如果需要其他的，请自行处理)
        assert binary != null;
        return "data:image/png;base64,"+new String(binary);
    }


    /**
     *
     * @Title: toBufferedImage
     * @Description: 二维码矩阵转换为BufferedImage
     * @param matrix
     * @return BufferedImage 返回类型
     * @throws
     */
    public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                //Constant.QRCODE_COLOR和Constant.QRCODE_BACKGROUND_COLOR为二维码颜色和背景颜色
                image.setRGB(x, y, matrix.get(x, y) ? QRCodeUtil.QRCODE_COLOR : QRCodeUtil.QRCODE_BACKGROUND_COLOR);
            }
        }
        return image;
    }
    /**
     *
     * @param matrix 二维码矩阵相关
     * @param logoUrl logo路径
     * @throws IOException
     */
    public static BufferedImage writeLogoToQrcode(BitMatrix matrix,String logoUrl) throws IOException {
        //二维码矩阵转换为BufferedImage
        BufferedImage image = toBufferedImage(matrix);
        //是否传入了logo地址
        if(StringUtils.isNotBlank(logoUrl)){
            URL url = new URL(logoUrl);
            //取得二维码图片的画笔
            Graphics2D gs = image.createGraphics();

            int ratioWidth = image.getWidth()*2/10;
            int ratioHeight = image.getHeight()*2/10;
            //读取logo地址
            Image img = ImageIO.read(url);
            int logoWidth = img.getWidth(null)>ratioWidth?ratioWidth:img.getWidth(null);
            int logoHeight = img.getHeight(null)>ratioHeight?ratioHeight:img.getHeight(null);
            //设置logo图片的位置
            int x = (image.getWidth() - logoWidth) / 2;
            int y = (image.getHeight() - logoHeight) / 2;
            //开画
            //gs.drawImage(Image logo, int logo横坐标, int logo纵坐标, int logo宽, int logo高, null);
            gs.drawImage(img, (int)(x), (int)(y), logoWidth, logoHeight, null);
            gs.dispose();
            img.flush();
        }
        return image;
    }
    public static void main(String[] args) {
        String s= QRCodeUtil.generalQRCode("SS",null);
        System.out.println(s);
    }

}
