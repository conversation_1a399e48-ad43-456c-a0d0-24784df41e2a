package com.bassims.utils;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.Key;
import java.security.Security;


public class AESUtil {

    public static final String KEY_NAME = "AES";
    // 加解密算法/模式/填充方式
    // ECB模式只用密钥即可对数据进行加密解密，CBC模式需要添加一个iv
    public static final String CIPHER_ALGORITHM = "AES/CBC/PKCS7Padding";

    /**
     * 微信 数据解密<br/>
     * 对称解密使用的算法为 AES-128-CBC，数据采用PKCS#7填充<br/>
     * 对称解密的目标密文:encrypted=Base64_Decode(encryptData)<br/>
     * 对称解密秘钥:key = Base64_Decode(session_key),aeskey是16字节<br/>
     * 对称解密算法初始向量:iv = Base64_Decode(iv),同样是16字节<br/>
     *
     * @param encrypted 目标密文
     * @param session_key 会话ID
     * @param iv 加密算法的初始向量
     */
    public static String wxDecrypt(String encrypted, String session_key, String iv) {
        String json = null;
        byte[] encrypted64 = Base64.decodeBase64(encrypted);
        byte[] key64 = Base64.decodeBase64(session_key);
        byte[] iv64 = Base64.decodeBase64(iv);
        byte[] data;
        try {
            init();
            json = new String(decrypt(encrypted64, key64, generateIV(iv64)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json;
    }

    private static final String ALGORITHM = "AES";
    /**  加解密算法/工作模式/填充方式  */
    private static final String ALGORITHM_MODE_PADDING = "AES/ECB/PKCS5Padding";
    /** AES加密  */
    public static String encryptData(String data,String password) throws Exception {
        // 创建密码器
        Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
        SecretKeySpec key = new SecretKeySpec(MD5Utils.MD5(password).toLowerCase().getBytes(), ALGORITHM);
        // 初始化
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return encode(cipher.doFinal(data.getBytes()));
    }
    /** AES解密 */
    public static String decryptData(String base64Data,String password) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
        SecretKeySpec key = new SecretKeySpec(MD5Utils.MD5(password).toLowerCase().getBytes(), ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] decode = decode(base64Data);
        byte[] doFinal = cipher.doFinal(decode);
        return new String(doFinal,"utf-8");
    }

    /**
     * 初始化密钥
     */
    public static void init() throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        KeyGenerator.getInstance(KEY_NAME).init(128);
    }

    /**
     * 生成iv
     */
    public static AlgorithmParameters generateIV(byte[] iv) throws Exception {
        // iv 为一个 16 字节的数组，这里采用和 iOS 端一样的构造方法，数据全为0
        // Arrays.fill(iv, (byte) 0x00);
        AlgorithmParameters params = AlgorithmParameters.getInstance(KEY_NAME);
        params.init(new IvParameterSpec(iv));
        return params;
    }

    /**
     * 生成解密
     */
    public static byte[] decrypt(byte[] encryptedData, byte[] keyBytes, AlgorithmParameters iv)
            throws Exception {
        Key key = new SecretKeySpec(keyBytes, KEY_NAME);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        // 设置为解密模式
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        return cipher.doFinal(encryptedData);
    }


    /**
     * 生成key
     */
    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * AES解密
     */
    public static String decryptData(byte[] b,String secert) throws Exception {
        SecretKeySpec key = new SecretKeySpec(MD5Utils.MD5(secert).toLowerCase().getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
        cipher.init(Cipher.DECRYPT_MODE, key);
        return new String(cipher.doFinal(b));
    }

    /** 解码  */
    public static byte[] decode(String encodedText){
        final java.util.Base64.Decoder decoder = java.util.Base64.getDecoder();
        return decoder.decode(encodedText);
    }
    /** 编码 */
    public static String encode(byte[] data){
        final java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
        return encoder.encodeToString(data);
    }


    public static void main(String[] args) throws Exception {

        String A = "xop9MKC3qW63qRLIrvVaq5IK82mMRM050Ab+fAtNP6VPMa76+ZhUNb5t0OEfEndwcAM2NWhJBTV1IzK3PlRBsl4NkYBR8KVz2zZIvfH+2yhy884vS3kwmgSAL2XPo6sWSbnJ2OPAGBCdVLSMWYzOa/fMzSnVC0ASi/+aXcHHFoxys07Xn18oA4U92hTTipvrxAHerOUkUt9c3YIELld/pYuEYkrfo9OHoUxFCfsW9VgjvfqID6VrrC/QwvgJ5rysJcAzON7ayh68XXGf9aCGT2xsypbKhr7J3OwSrLUEuaaLJ8cL2OSN4o11w4z/+zqgWuCml6KHXLx5W9mekRKHp7vArDaA0JB5/HSQ+U/97ggcz9kvXcBlix7GUOv75YP9rentsPXy6THEBWPGQqybnz0+4McuYNuaiXwAeH14yIsZSiw8MGa1amM47sEaGYBP0LI52lc+B+1K5JVsPxBZaB6CE0Njn/uxI89zELcfByUN1KTAe+ot+sTSlBPppaI1h9oo2BOfRae15LWdKgUv6LH5aJVC8LPbxwtWLc4SFHPr87Fo0fPhR89tH81Zql0SRYEBqAcJj7CbxASIuCE9mq5D7Jztnmb67UqUOB0DpCDruAeJqBWb+XsYguODag2cjS36lYeP3YbQoHgjbA/NPiLU6KlF2C5qB6EUg2aw0yUwkMhIw+pB5ROGDbppsPZJnq5Z8ezQPvwRTO9DMfLEu4djt0EF4B9Kzxs6q6oDhuUkUUhxFSRS718vloAJGwsoucW1GBJJcaWj4uwXxK2AT5awTWX3d6U66Fkim7a5uc/8S32bWKX/3/LxHM99tXR5vVZUsjtW4o+cJhgsXrKZOemT7d3oSc3diNDGlkJiwumI7Pa7Wzq8N9lT773oEcwl0rvEhwdrRbmG4u+A3AJI/hx6R/B+rlQfDRXazp/VfDF3BvYmwFodbhw0nIxe+dmXVJrvx5qvTY6PenOdA+u3LEOv/iYTXaJCQCsp8Rkarr9W895Ogjp9+nnG1OQfz+uTmFy5g7sJCvCJWzGILukRlYM9wndc1MLQmwXC2bf2I5k+BMz3fRjjv6yFNp+tQZZJ";
        String B = AESUtil.decryptData(Base64.decodeBase64(A),"nanjingdiantengkejiyouxiangongsi");
        System.out.println(B);


    }


}
