package com.bassims.utils;

import com.alibaba.fastjson.JSON;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPInputStream;


/**
 * 请求发送方式
 *
 * <AUTHOR>
 */
public class JHRequest {

    private static Logger logger = LoggerFactory.getLogger(Request.class);

    /**
     * post json 方式发送请求
     *
     * @param url
     * @param params
     * @return
     */
    public static String postJson(String url, Object params, String appCode) {
        Long s = System.currentTimeMillis();
        String jsonStr = JSON.toJSONString(params);
        logger.info("postJosn 请求地址:{} 请求参数：{}", url, jsonStr);
        InputStream instr = null;
        try {
            byte[] jsonData = jsonStr.getBytes("UTF-8");
            URL netUrl = new URL(url);
            URLConnection urlCon = netUrl.openConnection();
            urlCon.setDoOutput(true);
            urlCon.setDoInput(true);
            urlCon.setUseCaches(false);
            urlCon.setRequestProperty("Content-Type", "application/json");
            urlCon.setRequestProperty("Content-length", String.valueOf(jsonData.length));
            urlCon.setRequestProperty("Authorization", "APPCODE " + appCode);
            DataOutputStream printout = new DataOutputStream(urlCon.getOutputStream());
            printout.write(jsonData);
            printout.flush();
            printout.close();
            instr = urlCon.getInputStream();
            StringBuffer out = new StringBuffer();
            byte[] bis = new byte[4096];
            int n = 0;
            while ((n = instr.read(bis)) != -1) {
                out.append(new String(bis, 0, n, "UTF-8"));
            }
            logger.info("postJosn 请求返回值={}", out);
            logger.info("postJosn 请求用时={}", System.currentTimeMillis() - s);

            return out.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        } finally {
            try {
                instr.close();
            } catch (Exception ex) {
                return "";
            }
        }
    }

    public static String sendGet(String url, Map<String, Object> params, String appCode) {
        StringBuffer result = new StringBuffer();
        BufferedReader in = null;

        StringBuffer urlNameString = new StringBuffer().append(url).append("?");
        if (params != null) {
            for (String key : params.keySet()) {
                urlNameString.append(key).append("=").append(params.get(key)).append("&");
            }
            url = urlNameString.substring(0, urlNameString.lastIndexOf("&"));
        }

        logger.info("sendGet 请求地址:{}", url);

        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置超时时间
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(15000);
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setRequestProperty("Authorization", "APPCODE " + appCode);
            // 建立实际的连接
            connection.connect();
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String contentEncoding = connection.getContentEncoding();
            if (null != contentEncoding && contentEncoding.indexOf("gzip") != -1) {
                InputStream inputStream = new GZIPInputStream(connection.getInputStream());
                in = new BufferedReader(new InputStreamReader(inputStream));
            }
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            if (in != null) {
                in.close();
            }
        } catch (Exception e) {
        }
        logger.info("sendGet 返回值:{}", result.toString());
        return result.toString();
    }

    public static String sendPost(String url, Map<String, Object> params, String appCode) {
        StringBuffer result = new StringBuffer();
        StringBuffer paramString = new StringBuffer();
        if (params != null) {
            for (String key : params.keySet()) {
                paramString.append(key).append("=").append(params.get(key)).append("&");
            }
        }
        logger.info("sendPost 请求地址:{} 请求参数:{}", url, paramString.toString());
        try {
            PrintWriter out = null;
            BufferedReader in = null;
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置超时时间
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(15000);
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Authorization", "APPCODE " + appCode);
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(paramString.substring(0, paramString.lastIndexOf("&")));
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            if (out != null) {
                out.close();
            }
            if (in != null) {
                in.close();
            }
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
        logger.info("sendPost 返回值:{}", result.toString());
        return result.toString();
    }

    public static String sendGet(String url, String param, String appCode) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);

            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();

            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setRequestProperty("Authorization", "APPCODE " + appCode);
            // 建立实际的连接
            connection.connect();

            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();

            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                logger.info(key + "--->" + map.get(key));
            }

            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            logger.info(result);
        } catch (Exception e) {
            logger.info("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }

        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }


    public static String doPostXmlSSL(String url, String xml, KeyStore keyStore, String secrit) {
        Long start = System.currentTimeMillis();
        try {
            logger.debug("POST_Xml_SSL请求地址:{}",url);
            logger.debug("POST_Xml_SSL请求参数:{}",xml);
            SSLContext sslcontext = SSLContexts.custom()
                    //这里也是写密码的
                    .loadKeyMaterial(keyStore, secrit.toCharArray())
                    .build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                    sslcontext,
                    new String[] { "TLSv1.1" },
                    null,
                    SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(sslsf)
                    .build();
            HttpPost post = new HttpPost(url);
            if (null != xml && !xml.isEmpty()) {
                StringEntity entity = new StringEntity(xml, StandardCharsets.UTF_8);
                post.setEntity(entity);
            }
            CloseableHttpResponse response = httpClient.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();
            if (200 != statusCode) {
                logger.error("POST_Xml_SSL失败, 状态码:{}", statusCode);
                return null;
            }
            String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            response.close();
            httpClient.close();
            logger.debug("POST_Xml_SSL返回数据:{}",result);
            logger.debug("POST_Xml_SSL请求用时:{}ms",System.currentTimeMillis()-start);
            return result;
        } catch (Exception e) {
            logger.error("POST请求异常, EXCEPTION:{}", e);
            return null;
        }
    }


}
