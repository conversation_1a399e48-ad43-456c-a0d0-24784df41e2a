package com.bassims.utils.wordutil;

import com.bassims.config.FileProperties;
import com.bassims.utils.RedisUtils;
import com.bassims.utils.SnowFlakeUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/29 0029
 */
@Slf4j
@Component
public class WordUtils {

    @Autowired
    private FileProperties fileProperties;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 生成word文档
     *
     * @param templateName
     * @param object
     * @return
     */
    public String createDoc(String templateName, Object object) {
        // Configuration用于读取ftl文件
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_0);
        configuration.setDefaultEncoding("UTF-8");
        // 文件名称
        String fileName = SnowFlakeUtil.getInstance().nextId() + ".doc";
        // 输出文档路径及名称
        // 文件的输出位置(输出到本地绝对路径里)
        String templatePath = fileProperties.getPath().getTemplate();
        String outFilePath = fileProperties.getPath().getPath() + fileName;
        Writer out = null;

        File file = new File(outFilePath);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        try {
            // 文件保存10分钟后删除，节约服务器资源
            redisUtils.set(fileName, fileName, 10 * 60);
            out = new BufferedWriter(new OutputStreamWriter(new
                    FileOutputStream(new File(outFilePath)), "UTF-8"), 10240);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 加载文档模板
        Template template = null;
        try {
            //指定路径，例如C:/a.ftl 注意：此处指定ftl文件所在目录的路径，而不是ftl文件的路径
            configuration.setDirectoryForTemplateLoading(new File(templatePath));
            //以utf-8的编码格式读取文件
            template = configuration.getTemplate(templateName, "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("文件模板加载失败！", e);
        }
        // 填充数据
        try {
            template.process(object, out);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("模板数据填充异常！", e);
        } finally {
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("文件输出流关闭异常！", e);
                }
            }
        }
        return outFilePath;
    }

}