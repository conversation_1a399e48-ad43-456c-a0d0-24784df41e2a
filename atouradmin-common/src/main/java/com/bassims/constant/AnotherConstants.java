package com.bassims.constant;

import com.bassims.constant.bsEnum.KidsSystemEnum;

/**
 * <AUTHOR>
 */
public interface AnotherConstants {

    /**
     * 第三方接口状态码
     */
    String THIRD_CODE = "code";
    /**
     * 第三方接口成功状态码
     */
    Integer THIRD_SUCCESS_CODE = 1000;
    /**
     * 第三方接口成功信息
     */
    String THIRD_SUCCESS_MSG = "SUCCESS";
    /**
     * 第三方接口失败信息
     */
    String THIRD_FAIL_MSG = "FAIL";
    /**
     * 第三方接口信息
     */
    String THIRD_MESSAGE = "msg";
    /**
     * 第三方接口参数
     */
    String THIRD_DATA = "data";

    String DISTRIBUTOR_KEY = "distributor-";

    String DOC_KEY = ".docx";

    /**
     * 合同模板
     */
    String CONTRACT_TEMPLATE = "contract.ftl";

    /**
     * 开工通知书
     */
    String NOTICE_TO_PROCEED = "noticeToProceed.ftl";

    String JU_KEY = "ju-";

    /**
     * 超级管理员
     */
    Long ADMIN = 1L;
    /**
     *普通用户
     */
    Long NORMAL_USER = 2L;
    /**
     *经销商
     */
    Long JL = 3L;
    /**
     *直营商
     */
    Long ZL = 4L;
    /**
     *加注站长
     */
    Long JZ = 5L;
    /**
     *加注员
     */
    Long JU = 6L;
    /**
     *客户
     */
    Long USER = 7L;
    /**
     *分销员
     */
    Long FL = 8L;

    String XLSX="xlsx";

    /**
     * excel文件类型
     */
    String[] TEMPLATE_CONFIG_TYPE_LIST = new String[]{
            KidsSystemEnum.TemplateConfigType.CFT001.getValue()};

}
