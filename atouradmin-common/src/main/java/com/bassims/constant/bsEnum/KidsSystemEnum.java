package com.bassims.constant.bsEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
public class KidsSystemEnum {
    @Getter
    @AllArgsConstructor
    public enum ProcessType {
        NEW("新店流程","new"),
        MAJOR("大改造流程","major"),
        MINOR("小改造流程","minor"),
        REFORM("办公室改造流程","reform"),
        CLOSE("闭店流程","close"),
        Adjust("设计小调整","design_adjust");
        private String label;
        private String value;
    }


    /**
     *订单编号name（MM）
     */
    @Getter
    @AllArgsConstructor
    public enum OrderclassType {
        MH("木制货架","MH"),
        GH("钢制货架","GH"),
        DJ("灯具","DJ"),
        DZ("店招","DZ"),
        JL("精灵","JL"),
        CH("仓库货架","CH"),
        HJ("货架","HJ"),
        QT("其他","QT"),
        DD("其他","DD");

        private String label;
        private String value;
    }







    /**
     * 用户状态
     */
    @Getter
    @AllArgsConstructor
    public enum UserStatusEnum {
        /**
         * 激活
         */
        TRUE("激活","true"),
        /**
         * 禁用
         */
        FALSE("禁用","false");
        private String label;
        private String value;
    }

    /**
     * 门店状态
     */
    @Getter
    @AllArgsConstructor
    public enum StoreStatusEnum {
        /**
         * 筹建中
         */
        PREPARE("筹建中","store_prepare"),
        /**
         * 营业中
         */
        BUSINESS("营业中","store_business"),
        /**
         * 已闭店
         */
        CLOSE("已闭店","store_closed");
        private String label;
        private String value;
    }

    /**
     * 部门状态
     */
    @Getter
    @AllArgsConstructor
    public enum DeptStatusEnum {
        /**
         * 启用
         */
        TRUE("启用","true"),
        /**
         * 停用
         */
        FALSE("停用","false");
        private String label;
        private String value;
    }

    /**
     * 岗位状态
     */
    @Getter
    @AllArgsConstructor
    public enum JobStatusEnum {
        /**
         * 启用
         */
        TRUE("启用","true"),
        /**
         * 停用
         */
        FALSE("停用","false");
        private String label;
        private String value;
    }

    /**
     * 项目类型
     */
    @Getter
    @AllArgsConstructor
    public enum ProjectTypeEnum {
        /**
         * 新开店
         */
        NEW("新开店","new"),
        /**
         * 大改造
         */
        MAJOR("大改造","major"),
        /**
         * 小改造
         */
        MINOR("小改造","minor"),
        /**
         * 闭店
         */
        CLOSE("闭店","close");
        private String label;
        private String value;
    }

    /**
     * 项目类型(办公室)
     */
    @Getter
    @AllArgsConstructor
    public enum ProjectTypeOEnum {
        /**
         * 新店
         */
        NEW("新店","new"),
        /**
         * 改造
         */
        REFORM("改造","reform"),
        /**
         * 闭店
         */
        CLOSE("闭店","close");
        private String label;
        private String value;
    }

    /**
     * 经营类型
     */
    @Getter
    @AllArgsConstructor
    public enum ManageTypeOEnum {
        /**
         * 直营店
         */
        DIRECT("直营店","direct"),
        /**
         * 加盟店
         */
        JOIN("加盟店","join");
        private String label;
        private String value;
    }

    /**
     * 租赁形式
     */
    @Getter
    @AllArgsConstructor
    public enum LeaseFormOEnum {
        /**
         * 固定租金
         */
        FIXED_RENT("固定租金","fixed_rent"),
        /**
         * 抽成
         */
        DRAWN("抽成","drawn"),
        /**
         * 固定租金+抽成
         */
        FIXED_DRAWN("固定租金+抽成","fixed_drawn"),
        /**
         * 固定+抽成两者取高
         */
        HIGH_OF_FIXED_DRAWN("固定+抽成两者取高","high_of_fixed_drawn");
        private String label;
        private String value;
    }

    /**
     * 是否与消图纸相符
     */
    @Getter
    @AllArgsConstructor
    public enum IsConsistentDrawingEnum {

    }


    /**
     * 空调类型
     */
    @Getter
    @AllArgsConstructor
    public enum AirConditioningTypeEnum {
        /**
         * 风机盘管
         */
        FAN_COIL("风机盘管","fan_coil"),
        /**
         * 全空气系统
         */
        ALL_AIR_SYSTEM("全空气系统","all_air_system"),
        /**
         * 室内多联机
         */
        INDOOR_MULTI_CONNECTION("室内多联机","indoor_multi_connection"),
        /**
         * 无
         */
        NOTHING("无","nothing"),
        /**
         * 其他
         */
        OTHER("其他","other");
        private String label;
        private String value;
    }

    /**
     * 甲方交付标准
     */
    @Getter
    @AllArgsConstructor
    public enum FirstDeliveryStandardEnum {
        /**
         * 毛胚
         */
        BLANK("毛胚","blank",""),
        /**
         * 精装
         */
        DECORATION("精装","decoration", "con-00103,con-00401,con-00402,con-00403,con-00501,con-00601,con-00404,con-00137,con-00138,con-00113,con-00142,con-00144"),
        /**
         * 半精装
         */
        SEMI_DECORATION("半精装","semi_decoration",""),
        /**
         * 精装-门头
         */
        DECORATION_DOOR("精装-门头","decoration_door","con-00501,con-00601,con-00137,con-00138"),
        /**
         * 半精装+顶+地
         */
        SEMI_TOP_BOTTOM("半精装+顶+地","semi_top_bottom","con-00501,con-00601,con-00137,con-00138"),
        /**
         * 半精装+顶
         */
        SEMI_TOP("半精装+顶","semi_top","con-00501,con-00601,con-00137,con-00138"),
        /**
         * 半精装+地
         */
        SEMI_BOTTOM("半精装+地","semi_bottom","con-00501,con-00601,con-00137,con-00138"),
        /**
         * 毛坯+空调+消防
         */
        BLANK_AIR_FIRE("毛坯+空调+消防","blank_air_fire","con-00501,con-00601,con-00137,con-00138"),
        /**
         * 毛坯+空调
         */
        BLANK_AIR("毛坯+空调","blank_air","con-00501,con-00137"),
        /**
         * 毛坯+消防
         */
        BLANK_FIRE("毛坯+消防","blank_fire","con-00601,con-00138");

        private String label;
        private String value;
        private String finCodes;

        public static String getFinCodeByValue(String value){
            FirstDeliveryStandardEnum[] values = FirstDeliveryStandardEnum.values();
            for (FirstDeliveryStandardEnum first : values) {
                if (first.getValue().equals(value)){
                    return first.getFinCodes();
                }
            }
            return "";
        }
    }

    /**
     * 是否含游乐专用疏散
     */
    @Getter
    @AllArgsConstructor
    public enum IsSpecialEvacuationAmusementEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 消防二次施工
     */
    @Getter
    @AllArgsConstructor
    public enum FireSecondBuildEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 消防维保
     */
    @Getter
    @AllArgsConstructor
    public enum FireMaintenanceEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 空调二次施工
     */
    @Getter
    @AllArgsConstructor
    public enum AirConditionSecondBuildEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 空调维保
     */
    @Getter
    @AllArgsConstructor
    public enum AirConditionMaintenanceEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 空调末端电费承担
     */
    @Getter
    @AllArgsConstructor
    public enum AirEndChargeBearEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 空调责任方
     */
    @Getter
    @AllArgsConstructor
    public enum AirConditioningResponsiblePartyEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 消防责任方
     */
    @Getter
    @AllArgsConstructor
    public enum FireResponsiblePartyEnum {
        /**
         * 甲方
         */
        FIRST("甲方","first");
        private String label;
        private String value;
    }

    /**
     * 项目经理星级
     */
    @Getter
    @AllArgsConstructor
    public enum ProjectManagerStarEnum {

    }

    /**
     * 厂商星级
     */
    @Getter
    @AllArgsConstructor
    public enum ManufacturerStarEnum {

    }

    /**
     * 装修设计等级
     */
    @Getter
    @AllArgsConstructor
    public enum DecorationDesignGradeEnum {
        /**
         * A
         */
        A("A","A"),
        /**
         * B
         */
        B("B","B"),
        /**
         * C
         */
        C("C","C");
        private String label;
        private String value;
    }

    /**
     * 门店版本
     */
    @Getter
    @AllArgsConstructor
    public enum StoreVersionEnum {
        /**
         * G9
         */
        G9("G9","G9");
        private String label;
        private String value;
    }

    /**
     * 消防联动测试结果是否完好
     */
    @Getter
    @AllArgsConstructor
    public enum IsFireLinkageTestResultEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 空调设备是否完好
     */
    @Getter
    @AllArgsConstructor
    public enum IsAirConditionEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 合同归属  第三方 为合同区域
     */
    @Getter
    @AllArgsConstructor
    public enum ContractOwnershipEnum {

        OWNER_101("江","101"),
        OWNER_102("浙","102"),
        OWNER_103("沪","103"),
        OWNER_104("徽","104"),
        OWNER_999("其他","999");
        private String label;
        private String value;
        public static ContractOwnershipEnum getOwnership(String value) {
            ContractOwnershipEnum status=null;
            ContractOwnershipEnum[] values = ContractOwnershipEnum.values();
            for(ContractOwnershipEnum task:values){
                if(task.getValue().equals(value)){
                    status=task;
                }
            }
            return OWNER_999;
        }
    }

    /**
     * 首付对账单类型
     */
    @Getter
    @AllArgsConstructor
    public enum PayContractTypeEnum {
        decorate("装修首付对账单","decorate","con-00404"),
        fire("消防首付对账单","fire","con-00138"),
        air_conditioner("空调首付对账单","air_conditioner","con-00137");
        private String label;
        private String value;
        private String nodeCode;

        public static PayContractTypeEnum getContractType(String value) {
            PayContractTypeEnum status=null;
            PayContractTypeEnum[] values = PayContractTypeEnum.values();
            for(PayContractTypeEnum task:values){
                if(task.getValue().equals(value)){
                    status=task;
                }
            }
            return status;
        }
    }

    /**
     * 合同类型
     */
    @Getter
    @AllArgsConstructor
    public enum ContractTypeEnum {
        decorate("装修合同","decorate","1000","con-00404"),
        fire("消防合同","fire","3000","con-00138"),
        air_conditioner("空调合同","air_conditioner","2000","con-00137"),
        other("其他合同","other","","");
        private String label;
        private String value;
        private String thirdCode;
        private String firstNodeCode;

        public static ContractTypeEnum getContractType(String value) {
            ContractTypeEnum status=null;
            ContractTypeEnum[] values = ContractTypeEnum.values();
            for(ContractTypeEnum task:values){
                if(task.getValue().equals(value)){
                    status=task;
                }
            }
            return status;
        }
    }

    /**
     * 是否续约合同
     */
    @Getter
    @AllArgsConstructor
    public enum IsRenewContractEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 是否付款
     */
    @Getter
    @AllArgsConstructor
    public enum IsPayEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 费用是否在签报/预案限定的标准内
     */
    @Getter
    @AllArgsConstructor
    public enum IsFeeStandardInEnum {
        /**
         * 是
         */
        YES("是","yes"),
        /**
         * 否
         */
        NO("否","no");
        private String label;
        private String value;
    }

    /**
     * 付款方式
     */
    @Getter
    @AllArgsConstructor
    public enum PayTypeEnum {

    }

    /**
     * 付款币别
     */
    @Getter
    @AllArgsConstructor
    public enum PayCurrencyEnum {

    }

    /**
     * 发票类型
     */
    @Getter
    @AllArgsConstructor
    public enum InvoiceTypeEnum {

        PLAIN_INVOICE("普通发票","plain_invoice"),

        VAT_SPECIAL_INVOICE("增值税专用发票","vat_special_invoice"),

        VAT_ORDINARY_INVOICE("增值税普通发票","vat_ordinary_invoice"),

        SPECIAL_ADMINISTRATIVE_RECEIPT("行政专用收据","special_administrative_receipt"),

        ORDINARY_RECEIPT("普通收据","ordinary_receipt");
        private String label;
        private String value;

        public static InvoiceTypeEnum getInvoiceType(String value) {
            InvoiceTypeEnum status=null;
            InvoiceTypeEnum[] values = InvoiceTypeEnum.values();
            for(InvoiceTypeEnum task:values){
                if(task.getValue().equals(value)){
                    status=task;
                }
            }
            return status;
        }


    }

    /**
     * 发票税率
     */
    @Getter
    @AllArgsConstructor
    public enum InvoiceTaxRateEnum {
        PER_0("0%","0","0"),
        PER_1("1%","1","0.01"),
        PER_2("2%","2","0.02"),
        PER_3("3%","3","0.03"),
        PER_4("4%","4","0.04"),
        PER_5("5%","5","0.05"),
        PER_6("6%","6","0.06"),
        PER_7("7%","7","0.07"),
        PER_9("9%","9","0.09"),
        PER_10("10%","10","0.10"),
        PER_11("11%","11","0.11"),
        PER_13("13%","13","0.13"),
        PER_16("16%","16","0.16"),
        PER_17("17%","17","0.17");
        private String label;
        private String value;
        private String thirdCode;

        public static InvoiceTaxRateEnum getTaxRate(String value) {
            InvoiceTaxRateEnum status=null;
            InvoiceTaxRateEnum[] values = InvoiceTaxRateEnum.values();
            for(InvoiceTaxRateEnum task:values){
                if(task.getValue().equals(value)){
                    status=task;
                }
            }
            return status;
        }
    }

    /**
     * 验收类型(材料验收)
     */
    @Getter
    @AllArgsConstructor
    public enum AcceptanceTypeEnum {
        /**
         * 钢材
         */
        STEELS("钢材","steels"),
        /**
         * 板材
         */
        PANEL("板材","panel"),
        /**
         * 电气
         */
        ELECTRIC("电气","electric"),
        /**
         * 其他
         */
        OTHER("其他","other");
        private String label;
        private String value;
    }

    /**
     * 验收类型(隐蔽验收)
     */
    @Getter
    @AllArgsConstructor
    public enum HideAcceptTypeEnum {
        /**
         * 轻钢龙骨吊顶
         */
        LIGHT_KEEL_CEILING("轻钢龙骨吊顶","light_keel_ceiling"),
        /**
         * 轻钢龙骨隔墙
         */
        LIGHT_KEEL_PARTITION("轻钢龙骨隔墙","light_keel_partition"),
        /**
         * 轻质砖墙
         */
        LIGHT_BRICK_WALL("轻质砖墙","light_brick_wall"),
        /**
         * 包柱
         */
        ENVELOPE("包柱","envelope"),
        /**
         * 外立面
         */
        FACADE("外立面","facade"),
        /**
         * 防水
         */
        WATERPROOF("防水","waterproof"),
        /**
         * 电气
         */
        ELECTRIC("电气","electric"),
        /**
         * 隔栅吊顶
         */
        GRID_CEILING("隔栅吊顶","grid_ceiling");
        private String label;
        private String value;
    }

    /**
     * 验收类型(分部分项验收)
     */
    @Getter
    @AllArgsConstructor
    public enum DivisionalSubAcceptTypeEnum {
        /**
         * 顶面
         */
        TOP_SURFACE("顶面","top_surface"),
        /**
         * 地面
         */
        GROUND("地面","ground"),
        /**
         * 隔墙
         */
        PARTITION_WALL("隔墙","partiton_wall"),
        /**
         * 电气
         */
        ELECTRIC("电气","electric"),
        /**
         * 门窗
         */
        DOORS_WINDOWS("门窗","doors_windows"),
        /**
         * 外立面
         */
        FACADE("外立面","facade"),
        /**
         * 卫生间
         */
        TOILET("卫生间","toilet"),
        /**
         * 包柱
         */
        ENVELOPE("包柱","envelope");
        private String label;
        private String value;
    }

    /**
     * 验收类型(空调&消防验收)
     */
    @Getter
    @AllArgsConstructor
    public enum AirFireAcceptTypeEnum {
        /**
         * 空调
         */
        AIR_CONDITION("空调","air_condition"),
        /**
         * 消防
         */
        FIRE_FIGHTING("消防","fire_fighting");
        private String label;
        private String value;
    }

    /**
     * 精装
     */
    @Getter
    @AllArgsConstructor
    public enum DecorationEnum {
        /**
         * 灯具
         */
        LAMP("灯具","lamp"),
        /**
         * 装修
         */
        FIXTURES("装修","fixtures");
        private String label;
        private String value;
    }

    /**
     * 半精装
     */
    @Getter
    @AllArgsConstructor
    public enum SemiDecorationEnum {
        /**
         * 顶
         */
        TOP("顶","top"),
        /**
         * 地
         */
        LAND("地","land"),
        /**
         * 墙
         */
        WALL("墙","wall");
        private String label;
        private String value;
    }

    /**
     * 订单状态
     */
    @Getter
    @AllArgsConstructor
    public enum OrderStatus{
        ORDER_CREATING("创建中","order_creating","con-00901","create"),
        ORDER_APPROVAL("审批中","order_approval","con-00901","submit"),
        ORDER_CHOICE("厂商备货","order_choice","con-00901","finish"),
        ORDER_RETURN("订单退回","order_return","con-00901","back"),
        ORDER_CANCEL("订单作废","order_cancel","con-00903","canel"),
        ORDER_CANCELING("订单作废中","order_canceling","con-00903","submit"),
        ORDER_DELIVER("厂商已发货","order_deliver","con-00905","finish"),
        ORDER_ACCEPTANCE("验收中","order_acceptance","con-00907","submit"),
        ORDER_ACCEPTED("验收通过","order_accepted","con-00907","finish"),
        ORDER_SETTLEMENT("结算中","order_settlement","con-00909","submit"),
        ORDER_PAYMENT("付款中","order_payment","con-00909","finish"),
        ORDER_FINISH("订单完结","order_finish","con-00121","finish");

        private String label;
        private String value;
        private String simplie;
        private String type;


    }

    /**
     * 订单状态流转
     */
    @Getter
    @AllArgsConstructor
    public enum OrderStatusFlow{

        ORDER_01("创建中","order_creating","con-00901","create"),
        ORDER_02("审批中","order_approval","con-00901","submit"),
        ORDER_03("厂商备货","order_choice","con-00901","finish"),
        ORDER_04("订单退回","order_return","con-00901","back"),
        ORDER_05("厂商已发货","order_deliver","con-00905","finish"),
        ORDER_06("验收中","order_acceptance","con-00907","submit"),
        ORDER_07("验收通过","order_accepted","con-00907","finish"),
        ORDER_08("验收中","order_acceptance","con-00907","back"),
        ORDER_09("结算中","order_settlement","con-00909","submit"),
        ORDER_10("付款中","order_payment","con-00909","finish"),
        ORDER_11("验收通过","order_accepted","con-00909","back"),
        ORDER_12("订单完结","order_finish","con-00121","finish"),
        ORDER_13("订单作废","order_cancel","con-00903","canel"),
        ORDER_14("订单作废中","order_canceling","con-00903","submit"),
        ORDER_15("订单作废中","order_canceling","con-00903","back"),
        ORDER_16("订单作废中","order_canceling","con-00903","finish"),
        ORDER_17("付款中","order_payment","con-00121","submit");

        private String label;
        private String value;
        private String simplie;
        private String type;

        public static OrderStatusFlow getOrderStatus(String simplie,String type) {
            OrderStatusFlow status=null;
            OrderStatusFlow[] values = OrderStatusFlow.values();
            for(OrderStatusFlow task:values){
                if(task.getSimplie().equals(simplie)&&task.getType().equals(type)){
                    status=task;
                }
            }
            return status;
        }

    }


    /**
     * 预订单状态流转
     */
    @Getter
    @AllArgsConstructor
    public enum AdvanceOrderStatusFlow{

        ORDER_01("审批中","order_approval","con-00127","submit"),
        ORDER_03("订单退回","order_return","con-00127","back"),
        ORDER_02("订单完结","order_finish","con-00127","finish");

        private String label;
        private String value;
        private String simplie;
        private String type;


        public static AdvanceOrderStatusFlow getOrderStatus(String simplie,String type) {
            AdvanceOrderStatusFlow status=null;
            AdvanceOrderStatusFlow[] values = AdvanceOrderStatusFlow.values();
            for(AdvanceOrderStatusFlow task:values){
                if(task.getSimplie().equals(simplie)&&task.getType().equals(type)){
                    status=task;
                }
            }
            return status;
        }
    }

    /**
     * 指令单类型
     */
    @Getter
    @AllArgsConstructor
    public enum InstructionTypeEnum {
        /**
         * 标准
         */
        STANDARD("标准","standard"),
        /**
         * 非标准
         */
        NON_STANDARD("非标准","non_standard"),
        /**
         * 设计变更
         */
        DESIGN_CHANGE("设计变更","design_change");
        private String label;
        private String value;
    }

    /**
     * 审批模板编码
     */
    @Getter
    @AllArgsConstructor
    public enum ApproveCodeEnum {
        /**
         * 区域经理
         */
        QYJL("区域经理","28"),
        /**
         * 设计经理
         */
        SJJL("设计经理","30");
        private String label;
        private String value;
    }

    /**
     * 订单作废审批模板编码
     */
    @Getter
    @AllArgsConstructor
    public enum OrderCancelApproveCodeEnum{
        /**
         * 设计经理
         */
        SJJL("设计经理","33"),
        /**
         * 甲供材经理
         */
        JGCJL("甲供材经理","41"),
        /**
         * 厂商确认
         */
        CSQR("厂商确认","34");
        private String label;
        private String value;
    }


    /**
     * 任务状态编码
     */
    @Getter
    @AllArgsConstructor
    public enum TaskStatusEnum {
        /**
         * 未完成
         */
        UNFINISH("未完成","task_unfin"),
        /**
         * 已完成
         */
        FINISH("已完成","task_fin");
        private String label;
        private String value;
    }

    /**
     * 任务编码
     */
    @Getter
    @AllArgsConstructor
    public enum TaskTypeEnum {
        /**
         * 待办任务
         */
        TODO("待办任务","todo_task"),
        /**
         * 逾期任务
         */
        OVERDUE("逾期任务","overdue_task"),
        /**
         * 消息通知
         */
        MESSAGE("消息通知","message_notice"),
        /**
         * 提醒任务
         */
        REMIND("提醒任务","remind_task"),
        /**
         * 待办任务 逾期任务
         */
        TODO_OVERDUE("待办任务,逾期任务","todo_task,overdue_task");
        private String label;
        private String value;
    }

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum OrderType{

        ADVANCE_ORDER("预订单","advance_order"),
        PURCHASE_ORDER("采购订单","purchase_order");

        private String label;
        private String value;



    }

    /**
     * 操作类型
     */
    @Getter
    @AllArgsConstructor
    public enum OperationType{
        CREATE("创建","create"),
        SUBMIT("提交","submit"),
        FINISH("完成","finish"),
        BACK("拒绝退回","back"),
        CANEL("报废","canel");

        private String label;
        private String value;



    }


    /**
     * 模板配置类型
     */
    @Getter
    @AllArgsConstructor
    public enum TemplateConfigType {
        /**
         * 预算、发包项目模板
         */
        CFT001("预算项目模板", "CFT001",1),
        CFT002("发包项目模板", "CFT002",1),
        CFT003("决算项目模板","CFT003",1),
        CFT004("工程结算审定单","CFT004",2),
        DW004("结算审定归档","DW004",2),
        ORDER001("订单结算模板","ORDER001",1),
        ORDER002("订单创建模板","ORDER002",1);
        private String label;
        private String value;
        private Integer sheetStart;
    }

    /**
     * 用户状态
     */
    @Getter
    @AllArgsConstructor
    public enum EmailTemplateType{
        /**
         * 生成工程施工委托意向书
         */
        Contract("生成工程施工委托意向书","contract"),
        /**
         * 生成开工通知书
         */
        NoticeToProceed("生成开工通知书","noticeToProceed");
        private String label;
        private String value;
    }

    /**
     * 生成文件类型
     */
    @Getter
    @AllArgsConstructor
    public enum DocType{
        /**
         * 工程施工委托意向书
         */
        Contract("工程施工委托意向书","contract"),
        /**
         * 开工通知书
         */
        NoticeToProceed("开工通知书","noticeToProceed");
        private String label;
        private String value;
    }

    /**
     * 服务范围
     */
    @Getter
    @AllArgsConstructor
    public enum ServiceScopeType{
        /**
         * 装修
         */
        RENOVATION("装修","renovation"),
        /**
         * 空调
         */
        AIR("空调","air"),
        /**
         * 消防
         */
        FIRE("消防","fire");

        private String label;
        private String value;
    }


    /**
     * 门店主档详情
     */
    @Getter
    @AllArgsConstructor
    public enum StoreMainFileType{
        /**
         * 基本信息
         */
        ESSENTIAL_INFO("基本信息","essential_info"),
        /**
         * 人员信息
         */
        PERSONNEL_INFO("人员信息","personnel_info"),
        /**
         * 付款信息
         */
        PAYMENT_INFO("付款信息","payment_info"),
        /**
         * 合同信息
         */
        CONTRACT_INFO("合同信息","contract_info"),
        /**
         * 证照信息
         */
        LICENSE_INFO("证照信息","license_info"),
        /**
         * 图纸信息
         */
        DRAWING_INFO("图纸信息","drawing_info"),
        /**
         * 面积信息
         */
        AREA_INFO("面积信息","area_info"),
        /**
         * 门店订单数据
         */
        STORE_ORDER_DATA("门店订单数据","store_order_data"),
        /**
         * 能耗信息
         */
        ENERGY_CONSUMPTION_INFO("能耗信息","energy_consumption_info"),
        /**
         * 设备信息
         */
        DEVICE_INFO("设备信息","device_info"),
        /**
         * 维修信息
         */
        MAINTENANCE_INFO("维修信息","maintenance_info");

        private String label;
        private String value;
    }

    /**
     * 主档图纸信息字段
     */
    @Getter
    @AllArgsConstructor
    public enum MasterDrawingType{
        ZRED_LINE_CAD("红线图CAD","zred_line_CAD"),
        SIGNED_VERSION_UPLOAD("签字版上传","signed_version_upload"),
        PRED_LINE_CAD("平面图CAD","pred_line_CAD"),
        PLANE_PDF("平面图PDF","plane_PDF"),
        SIGNED_VERSION_PLANE("签字版(平面+信息表)","signed_version_plane"),
        ORIGINAL_BUILDING_DRAWING("物业建筑原始图纸","original_building_drawing"),
        FIRE_APPROVE_CAD("消防报审图CAD版","fire_approve_CAD"),
        CAPITAL_DRAWING_CAD("提资图CAD版","capital_drawing_CAD"),
        PLANE_CONSTRUCTION_CAD("平面施工图CAD版","plane_construction_CAD"),
        FACADE_CONSTRUCTION_CAD("立面施工图含大样节点图CAD版","facade_construction_CAD"),
        WEAK_CURRENT_CAD("弱电施工图CAD版","weak_current_CAD"),
        DOORHEAD_CONSTRUCTION_CAD("门头效果图JPG版","doorhead_construction_CAD"),
        DOORHEAD_EFFECT("门头效果图签字稿JPG版","doorhead_effect"),
        DOORHEAD_EFFECT_SIGNED("门头施工图CAD版","doorhead_effect_signed"),
        JOINT_BRAND_EFFECT("联营效果图","joint_brand_effect"),
        JOINT_BRAND_CONSTRUCTION("联营施工图","joint_brand_construction"),
        BUSINESS_RED_LINE("商户红线图CAD版","business_red_line"),
        BUSINESS_EFFECT("商户效果图JPG版","business_effect"),
        BUSINESS_CONSTRUCTION("商户装修施工图CAD版","business_construction");

        private String label;
        private String value;
    }

    /**
     * 品类code
     */
    @Getter
    @AllArgsConstructor
    public enum CategoryCode{
        /**
         * 商品
         */
        shangpin("商品","shangpin"),
        /**
         * 育儿
         */
        yuer("育儿","yuer"),
        /**
         * 同城
         */
        tongcheng("同城","tongcheng"),
        /**
         * 成长加
         */
        chengzhangjia("成长加","chengzhangjia"),
        /**
         * 游乐
         */
        youle("游乐","youle"),
        /**
         * 互动
         */
        hudong("互动","hudong"),
        /**
         * 黑金
         */
        heijin("黑金","heijin"),
        /**
         * 商品-玩具
         */
        jinrong("金融","jinrong"),
        /**
         * 商品-玩具
         */
        zhaoshang("招商","zhaoshang"),
        /**
         * 商品-玩具
         */
        fentanmianji("分摊面积","fentanmianji");

        private String label;
        private String value;
    }

    /**
     * 标签code
     */
    @Getter
    @AllArgsConstructor
    public enum LabelInfoCode{
        /**
         * 商品-服饰
         */
        fushi("商品-服饰","fushi"),
        /**
         * 商品-玩具
         */
        wanju("商品-玩具","wanju"),
        /**
         * 商品-快消
         */
        kuaixiao("商品-快消","kuaixiao"),
        /**
         * 商品-用品
         */
        yongpin("商品-用品","yongpin"),
        /**
         * 商品-车床椅
         */
        chechuangyi("商品-车床椅","chechuangyi"),
        /**
         * 育儿
         */
        yuer("育儿","yuer"),
        /**
         * 同城
         */
        tongcheng("同城","tongcheng"),
        /**
         * 成长加
         */
        chengzhangjia("成长加","chengzhangjia"),
        /**
         * 游乐
         */
        youle("游乐","youle"),
        /**
         * 互动
         */
        hudong("互动","hudong"),
        /**
         * 黑金
         */
        heijin("黑金","heijin"),
        /**
         * 金融
         */
        jinrong("金融","jinrong"),
        /**
         * 招商
         */
        zhaoshang("招商","zhaoshang"),
        /**
         * 商品-公用
         */
        shangpingongyong("商品-公用","shangpingongyong"),
        /**
         * 公共面积
         */
        gonggongmianji("公共面积","gonggongmianji"),
        /**
         * 特殊面积
         */
        teshumianji("特殊面积","teshumianji");

        private String label;
        private String value;
    }

    /**
     * koa字段
     */
    @Getter
    @AllArgsConstructor
    public enum thirdCode{
        //申请单号  applyNo;
        applyno("申请单号","applyNo"),
        //申请人工号	submitUserCode;
        submitusercode("申请人工号","submitUserCode"),

        //合同类型	contractType;
        contracttype("合同类型","contractType"),

        //合同类型名称	contractTypeName;
        contracttypename("合同类型名称","contractTypeName"),

        //合同名称	contractName;
        contractname("合同名称","contractName"),

        //合同归属编码	contractOwnCode;
        contractowncode("合同归属编码","contractOwnCode"),

        //合同归属名称	contractOwnName;
        contractownname("合同归属名称","contractOwnName"),

        //对方单位名称	supplierName;
        suppliername("对方单位名称","supplierName"),

        //合同开始日期	startDate;
        startdate("合同开始日期","startDate"),

        //合同结束日期	endDate;
        enddate("合同结束日期","endDate"),

        //付款总金额	totalAmount;
        totalamount("付款总金额","totalAmount"),

        //发票类型	invoiceType;
        invoicetype("发票类型","invoiceType"),

        //发票税率	feeFax;
        feefax("发票税率","feeFax"),

        //合同描述	contractexplain;
        contractexplain("合同描述","contractexplain"),

        //合同区域	contractArea;
        contractArea("合同区域","contractArea"),

        //合同电子件	electronicContact;
        electronicContact("合同电子件","electronicContact"),

        //合同送签依据	contactSignBasis;
        contactSignBasis("合同送签依据","contactSignBasis"),
        //是否付款 yes:是;no:否;
        isPay("是否付款","isPay"),
        //费用是否在签报、预案限定的标准内 yes:是;no:否;
        isFeeStandardIn("费用是否在签报、预案限定的标准内","isFeeStandardIn"),
        //是否新店项目 yes:是;no:否;
        isNewProject("是否新店项目","isNewProject"),
        //是否续约合同 yes:是;no:否;
        isRenewContract("是否续约合同","isRenewContract"),
        //付款方
        payer("付款方","payer"),
        //付款方式 accounting_period:账期
        payType("付款方式","payType"),
        //付款币别 rmb:人民币;dollar:美元
        payCurrency("付款币别","payCurrency"),
        //申请人姓名
        name("申请人姓名","name"),
        //申请人部门
        department("申请人部门","department");




        private String label;
        private String value;
    }

    /**
     * 分公司顺序
     */
    @Getter
    @AllArgsConstructor
    public enum CityCompanyEnum{
        NANJING("南京区域","nanjing",1),
        YANGZHEN("扬镇区域","yangzhen",2),
        TONGTAI("通泰区域","tongtai",3),
        CHANGZHOU("常州区域","changzhou",4),
        WUXI("无锡区域","wuxi",5),
        SUZHOU("苏州区域","suzhou",6),
        YANCHENG("盐城区域","yancheng",7),
        XULIAN("徐连区域","xulian",8),
        SUHUAI("宿淮区域","suhuai",9),
        ANHUI("安徽分公司","anhui",10),
        ZHEJIANG("浙江分公司","zhejiang",11),
        SHANGHAI("上海分公司","shanghai",12),
        HUNAN("湖南分公司","hunan",13),
        HUBEI("湖北分公司","hubei",14),
        JIANGXI("江西分公司","jiangxi",15),
        FUJIAN("福建分公司","fujian",16),
        SHANDONG("山东分公司","shandong",17),
        HENAN("河南分公司","henan",18),
        HEBEI("河北分公司","hebei",19),
        TIANJIN("天津分公司","tianjin",20),
        LIAONING("辽宁分公司","liaoning",21),
        SHANXI("陕西分公司","shanxi",22),
        CHONGQING("重庆分公司","chongqing",23),
        GUANGDONG("广东分公司","guangdong",24),
        GUANGXI("广西分公司","guangxi",25),
        SICHUAN("四川分公司","sichuan",26),
        YUNGUI("云贵分公司","yungui",27);

        private String cityCompanyName;
        private String cityCompany;
        private int index;
    }

    /**
     * 直辖、地、县城市码值
     */
    @Getter
    @AllArgsConstructor
    public enum CityLevelEnum{
        /**
         * 直辖市
         */
        CITY_0("直辖市","0"),
        /**
         * 地级市
         */
        CITY_1("地级市","1"),
        /**
         * 县级市
         */
        CITY_2("县级市","2"),
        /**
         * 县城
         */
        CITY_3("县城","3");

        private String label;
        private String value;

        public static String getLabelByValue(String value){
            CityLevelEnum[] values = CityLevelEnum.values();
            for (CityLevelEnum cityLevelEnum : values) {
                if (cityLevelEnum.getValue().equals(value)){
                    return cityLevelEnum.getLabel();
                }
            }
            return "";
        }
    }

    /**
     * 服务范围
     */
    @Getter
    @AllArgsConstructor
    public enum WarehouseDesignEnum{
        /**
         * 名称修改
         */
        NODENAMEUPDATE("名称修改","");

        private String label;
        private String value;
    }

    /**
     * 费用类型
     */
    @Getter
    @AllArgsConstructor
    public enum CostType{

        /**
         * 新店费用
         */
        NEW_FEE("新店费用","new_fee"),
        /**
         * 改造费用
         */
        REFORM_FEE("改造费用","reform_fee"),
        /**
         * 日常费用
         */
        CURRENT_FEE("日常费用","current_fee"),
        /**
         * 采购专项
         */
        SPECIAL_PROCUREMENT("采购专项","special_procurement"),
        /**
         * 店招专项
         */
        STORE_SPECIAL("店招专项","store_special");

        private String label;
        private String value;
    }

    /**
     * 设计定位
     */
    @Getter
    @AllArgsConstructor
    public enum DesignPosition{
        /**
         * 标准店
         */
        STANDARD_STORE("标准店","standard_store"),
        /**
         * 旗舰店
         */
        FLAGSHIP_STORE("旗舰店","flagship_store"),
        /**
         * 优选店
         */
        PREFERRED_STORE("优选店","preferred_store");

        private String label;
        private String value;
    }

    /**
     * 订单详情表格
     */
    @Getter
    @AllArgsConstructor
    public enum OrderDetailTableEnum{

        CREATE_ORDER_DETAIL("con-0090102", "创建订单 订单详情表格"),
        SUPPLIER_ORDER_DETAIL("con-0090502", "厂商发货 订单详情表格"),
        ORDER_ACCEPTANCE_ORDER_DETAIL("con-0090702", "订单验收 订单详情表格"),
        ORDER_SETTLEMENT_ORDER_DETAIL("con-0090902", "订单结算 订单详情表格"),
        ORDER_PAY_ORDER_DETAIL("con-0012102", "订单付款 订单详情表格");

        private String label;
        private String value;
    }
}
